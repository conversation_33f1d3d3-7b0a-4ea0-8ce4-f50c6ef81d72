#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
节假日数据模型
用于存储节假日和工作日信息
"""

import django
from django.db import models
from common.models import BaseModel
from django.db.models import constraints
from utils.column_name import db_comment_kwarg

_db_comment_kwarg = db_comment_kwarg


class PublicHoliday(BaseModel):
    """
    节假日数据表
    存储节假日和工作日信息，用于业务系统的日期判断
    """
    
    date = models.DateField(
        unique=True,
        verbose_name='日期',
        help_text='日期，格式为YYYY-MM-DD',
        **_db_comment_kwarg('日期，格式为YYYY-MM-DD')
    )
    
    WORK_CHOICES = [
        ('1', '工作日'),
        ('0', '节假日'),
    ]

    is_work = models.CharField(
        max_length=1,
        choices=WORK_CHOICES,
        default='1',
        verbose_name='是否工作日',
        help_text='是否工作日，1表示工作日，0表示节假日',
        **_db_comment_kwarg('是否工作日，1表示工作日，0表示节假日')
    )

    class Meta:
        db_table = 'public_holiday'
        verbose_name = '节假日数据表'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['date'], name='idx_pub_holiday_date'),
            models.Index(fields=['is_work'], name='idx_pub_holiday_is_work'),
        ]
        ordering = ['-date']

    def __str__(self):
        work_status = "工作日" if self.is_work == '1' else "节假日"
        return f"{self.date} ({work_status})"


# 动态为 Meta 添加 db_table_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    PublicHoliday.Meta.db_table_comment = '节假日数据表，存储节假日和工作日信息'
