import datetime
import logging
import warnings
from pprint import pprint

import idna
import numpy as np
import pandas as pd
import pymysql
from pandasql import sqldf

from dw import settings
from public.models import PublicTarget
from transfrom.utils.utils import query_sql, sum_or_combine, df_to_dict

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class NhbInsureV5Target():
    # 销售开始日期
    def __init__(self):
        self.DB = settings.DATABASES['default']  # dw数据数据库
        self.sale_start_date = '2025-09-12'
        self.sale_end_date_prev = '2024-12-31'
        self.end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.version = '南京宁惠保-六期'
        self.previous_version = '南京宁惠保-五期'
        self.product_set_code = 'ninghuibaoV6'
        self.product_set_code_prev = 'ninghuibaoV5'

    def adjust_data(self, df):
        """
        调整数据，将12-31（销售截止）之后的数据纳入之前的数据
        :param df: 需要处理的数据
        :return:
        """
        df_out_period = df[df['date'] > self.sale_end_date_prev]
        df_in_period = df[df['date'] <= self.sale_end_date_prev]
        if df_out_period.empty:
            df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
            df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
            return df_in_period
        else:
            # 计算销售期之前的销量每期的占比，再将销售期外的销量加到销售期内
            df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
            df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
            return df_in_period

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def online_adjust_data(self):
        """
        调整线上数据，将销售截止之后的数据纳入之前的数据，只计算个单。
        """
        # 获取上期的线上数据
        try:
            with self.get_connection() as conn:
                sql = query_sql('SQL_DW_INDICATOR_DATA').format(
                    product_set_code=self.product_set_code_prev,
                    statistical_type='当期值', unit='单',
                    freq='日', start_datetime='2000-01-01',
                    end_datetime=self.end_datetime
                )
                df = pd.read_sql(sql, conn)
                df.rename(columns={'end_time': 'date'}, inplace=True)

                # 优化：只筛选一次，并使用映射来分配销售渠道
                channels = {
                    '我的南京': '-销量-线上-我的南京-当期值',
                    '公众号': '-销量-线上-公众号-当期值',
                    '支付宝': '-销量-线上-支付宝-当期值',
                    '合计': '-销量-线上-当期值'
                }

                df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
                for channel_name, filter_str in channels.items():
                    channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
                    channel_df['name'] = channel_name  # 添加销售渠道名称列
                    adjust_channel_df = self.adjust_data(channel_df)  # 调整数据
                    df_onlines = pd.concat([df_onlines, adjust_channel_df])  # 拼接数据
            df_onlines.sort_values(by=['name', 'date'], inplace=True)
            df_onlines.reset_index(drop=True, inplace=True)
            df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
        except Exception as e:
            print(f"Error occurred while processing online data: {e}")

        return df_onlines

    def offline_adjust_data(self):
        """
        调整线下数据，将12-31（销售截止）之后的数据纳入之前的数据，只计算个单
        :param df: 需要处理的数据
        :return:
        """
        # 提取SQL查询逻辑
        sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code_prev,
            statistical_type='当期值',
            unit='单',
            freq='日',
            start_datetime='2000-01-01',
            end_datetime=self.end_datetime
        )

        try:
            # 使用上下文管理器确保连接正确关闭
            with self.get_connection() as conn:
                df = pd.read_sql(sql_query, conn)
                df.rename(columns={'end_time': 'date'}, inplace=True)

                # 定义公司名称列表
                company_names = [
                    ('-销量-线下-个单-中国人保-当期值', '中国人保'),
                    ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
                    ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
                    ('-销量-线下-个单-中华联合-当期值', '中华联合'),
                    ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
                    ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
                    ('-销量-线下-个单-太保产险-当期值', '太保产险'),
                    ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
                    ('-销量-线下-个单-中银保险-当期值', '中银保险'),
                    ('-销量-线下-个单-当期值', '合计')
                ]

                # 循环处理每个公司数据
                dfs = []
                for pattern, name in company_names:
                    filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
                    adjusted_df = self.adjust_data(filtered_df)
                    adjusted_df['name'] = name
                    dfs.append(adjusted_df)

                # 合并所有公司数据
                df_offlines = pd.concat(dfs)
                df_offlines.sort_values(by=['name', 'date'], inplace=True)
                df_offlines.reset_index(drop=True, inplace=True)
                df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
                return df_offlines

        except Exception as e:
            # 异常处理
            print(f"Error occurred while processing data: {e}")

    def online_daily_target(self):
        """
        计算线上每日目标
        """
        with self.get_connection() as conn:
            sql = query_sql('SQL_DW_INDICATOR_DATA').format(
                product_set_code=self.product_set_code,
                statistical_type='当期值', unit='单',
                freq='日', start_datetime='2000-01-01',
                end_datetime=self.end_datetime
            )
            df = pd.read_sql(sql, conn)
            df.rename(columns={'end_time': 'date'}, inplace=True)

            # 优化：只筛选一次，并使用映射来分配销售渠道
            channels = {
                '我的南京': '-销量-线上-我的南京-当期值',
                '公众号': '-销量-线上-公众号-当期值',
                '支付宝': '-销量-线上-支付宝-当期值',
                '合计': '-销量-线上-当期值'
            }

            df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
            for channel_name, filter_str in channels.items():
                channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
                channel_df['name'] = channel_name  # 添加销售渠道名称列
                df_onlines = pd.concat([df_onlines, channel_df])  # 拼接数据
        df_onlines.sort_values(by=['name', 'date'], inplace=True)
        df_onlines.reset_index(drop=True, inplace=True)
        df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
        df_onlines['cumulative_count'] = df_onlines.groupby(['name'])['value'].cumsum()

        # 获取今年的目标数据，计算出目标的每日分布与每日累计分布
        target_online = PublicTarget.objects.filter(type='online', product_set_code=self.product_set_code).values(
            'name',
            'short_name',
            'target')
        df_target_online = pd.DataFrame(target_online)
        number_sum = df_target_online.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        # 保证合计在第一行
        df_target_online = pd.concat([number_sum, df_target_online], axis=0).reset_index(drop=True)
        # 获取去年的目标占比
        df_onlines_prev = self.online_adjust_data()
        df_onlines_prev = pd.merge(df_onlines_prev, df_target_online, on='name', how='left')
        df_onlines_prev['target_value_cumsum'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio_cumsum']
        df_onlines_prev['target_value'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio']
        df_onlines_prev = df_onlines_prev[['name', 'target', 'day', 'target_value', 'target_value_cumsum']]
        df_onlines_prev['target'] = df_onlines_prev['target'].astype(np.int64)
        df_onlines_prev.reset_index(drop=True, inplace=True)
        df_onlines.reset_index(drop=True, inplace=True)
        df = sqldf(
            "select a.*,b.target_value_cumsum ,ifnull(b.target_value,0) target_value,b.target  from df_onlines a left join df_onlines_prev b on a.name=b.name and a.day=b.day")
        df['target_value_cumsum'] = df['target_value_cumsum'].fillna(method='ffill')
        # target 为空，根据name分组，取上一个值
        df['target'] = df['target'].fillna(method='ffill')
        # 根据name分组 取date最大的一条记录
        df_last_record = df.groupby(['name'])['date'].transform('max')
        df_last = df[df['date'] == df_last_record]
        df_last = df_last[['name', 'cumulative_count', 'target']].rename(
            columns={'cumulative_count': '实际', 'target': '目标'})
        # 创建空字典
        result_dict = {}

        # 遍历 DataFrame 并构建字典
        for index, row in df_last.iterrows():
            name = row["name"]
            actual = row["实际"]
            target = row["目标"]
            result_dict[name] = {"实际": actual, "目标": target}

        df_melt_cumsum = df.melt(id_vars=['name', 'day'], value_vars=['cumulative_count', 'target_value_cumsum'],
                                 var_name='type', value_name='count_cumsum').fillna(0)
        df_melt_value = df.melt(id_vars=['name', 'day'], value_vars=['value', 'target_value'],
                                var_name='type', value_name='count').fillna(0)
        df_melt_cumsum['type'] = df_melt_cumsum['type'].map({'cumulative_count': '实际', 'target_value_cumsum': '目标'})
        df_melt_value['type'] = df_melt_value['type'].map({'value': '实际', 'target_value': '目标'})

        df = pd.merge(df_melt_cumsum, df_melt_value, on=['name', 'day', 'type'], how='left')
        df.reset_index(drop=True, inplace=True)
        df.rename(columns={'day': 'x', 'type': 's', 'count': 'y', 'count_cumsum': 'sum'}, inplace=True)
        dict_online = df_to_dict(df, 'name', ['x', 's', 'y', 'sum'])
        dict_online['达成'] = result_dict
        return df, dict_online

    def offline_daily_target(self):
        """
        计算线下每日目标
        """
        # 提取SQL查询逻辑
        sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code,
            statistical_type='当期值',
            unit='单',
            freq='日',
            start_datetime='2000-01-01',
            end_datetime=self.end_datetime
        )

        # 使用上下文管理器确保连接正确关闭
        with self.get_connection() as conn:
            df = pd.read_sql(sql_query, conn)
            df.rename(columns={'end_time': 'date'}, inplace=True)

            # 定义公司名称列表
            company_names = [
                ('-销量-线下-个单-中国人保-当期值', '中国人保'),
                ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
                ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
                ('-销量-线下-个单-中华联合-当期值', '中华联合'),
                ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
                ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
                ('-销量-线下-个单-太保产险-当期值', '太保产险'),
                ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
                ('-销量-线下-个单-中银保险-当期值', '中银保险'),
                ('-销量-线下-个单-当期值', '合计')
            ]

            # 循环处理每个公司数据
            dfs = []
            for pattern, name in company_names:
                filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
                filtered_df['name'] = name
                dfs.append(filtered_df)

        # 合并所有公司数据
        df_offlines = pd.concat(dfs)
        df_offlines.sort_values(by=['name', 'date'], inplace=True)
        df_offlines.reset_index(drop=True, inplace=True)
        df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
        df_offlines['cumulative_count'] = df_offlines.groupby(['name'])['value'].cumsum()

        # 获取今年的目标数据，计算出目标的每日分布与每日累计分布
        target_offline = PublicTarget.objects.filter(type='agent', product_set_code=self.product_set_code).values(
            'name',
            'short_name',
            'target')
        df_target_offline = pd.DataFrame(target_offline)
        df_target_offline.rename(columns={'name': 'full_name', 'short_name': 'name'}, inplace=True)
        number_sum = df_target_offline.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        # 保证合计在第一行
        df_target_offline = pd.concat([number_sum, df_target_offline], axis=0).reset_index(drop=True)
        # 获取去年的目标占比
        df_offlines_prev = self.offline_adjust_data()
        df_offlines_prev = pd.merge(df_offlines_prev, df_target_offline, on='name', how='left')
        df_offlines_prev['target_value_cumsum'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio_cumsum']
        df_offlines_prev['target_value'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio']
        df_offlines_prev = df_offlines_prev[
            ['full_name', 'name', 'day', 'target', 'target_value', 'target_value_cumsum']]
        df_offlines_prev['target'] = df_offlines_prev['target'].astype(np.int64)
        df_offlines_prev.reset_index(drop=True, inplace=True)
        df_offlines.reset_index(drop=True, inplace=True)
        df = sqldf(
            "select a.*,b.target_value_cumsum ,b.full_name,ifnull(b.target_value,0) target_value,b.target target from df_offlines a left join df_offlines_prev b on a.name=b.name and a.day=b.day")
        df['full_name'].fillna(df['name'], inplace=True)
        df['target_value_cumsum'] = df['target_value_cumsum'].fillna(method='ffill')
        # target 为空，根据name分组，取上一个值
        df['target'] = df['target'].fillna(method='ffill')
        # 根据name分组 取date最大的一条记录
        df_last_record = df.groupby(['name'])['date'].transform('max')
        df_last = df[df['date'] == df_last_record]
        df_last = df_last[['name', 'cumulative_count', 'target']].rename(
            columns={'cumulative_count': '实际', 'target': '目标'})
        # 创建空字典
        result_dict = {}

        # 遍历 DataFrame 并构建字典
        for index, row in df_last.iterrows():
            name = row["name"]
            actual = row["实际"]
            target = row["目标"]
            result_dict[name] = {"实际": actual, "目标": target}

        df_melt_cumsum = df.melt(id_vars=['full_name', 'name', 'day'],
                                 value_vars=['cumulative_count', 'target_value_cumsum'], var_name='type',
                                 value_name='count_cumsum').fillna(0)
        df_melt_value = df.melt(id_vars=['full_name', 'name', 'day'], value_vars=['value', 'target_value'],
                                var_name='type', value_name='count').fillna(0)
        df_melt_cumsum['type'] = df_melt_cumsum['type'].map({'cumulative_count': '实际', 'target_value_cumsum': '目标'})
        df_melt_value['type'] = df_melt_value['type'].map({'value': '实际', 'target_value': '目标'})

        df = pd.merge(df_melt_cumsum, df_melt_value, on=['full_name', 'name', 'day', 'type'], how='left')
        df.reset_index(drop=True, inplace=True)
        df.rename(columns={'day': 'x', 'type': 's', 'count': 'y', 'count_cumsum': 'sum', 'full_name': 'seller'},
                  inplace=True)
        df_total = df[df['seller'] == '合计']
        df_other = df[df['seller'] != '合计']
        dict_total = df_to_dict(df_total, 'name', ['x', 's', 'y', 'sum'])
        dict_other = df_to_dict(df_other, 'name', ['name', 'seller', 'x', 's', 'y', 'sum'])
        dict_offline = {**dict_total, **dict_other}
        dict_offline['达成'] = result_dict
        return df, dict_offline

    def daily_target(self):
        df_online_target, data_online = self.online_daily_target()
        df_offline_target, data_offline = self.offline_daily_target()
        online_total = data_online['合计']
        offline_total = data_offline['合计']

        online_total = {
            '实际': [item for item in online_total if item['s'] == '实际'],
            '目标': [item for item in online_total if item['s'] == '目标'],
        }
        offline_total = {
            '实际': [item for item in offline_total if item['s'] == '实际'],
            '目标': [item for item in offline_total if item['s'] == '目标'],
        }

        total = {
            '实际': [],
            '目标': [],
        }

        for index in range(len(online_total['实际'])):
            total['实际'].append({
                'x': online_total['实际'][index]['x'],
                'y': online_total['实际'][index]['y'] + offline_total['实际'][index]['y'],
                'sum': online_total['实际'][index]['sum'] + offline_total['实际'][index]['sum'],
                's': '实际'
            })

        for index in range(len(online_total['目标'])):
            total['目标'].append({
                'x': online_total['目标'][index]['x'],
                'y': online_total['目标'][index]['y'] + offline_total['目标'][index]['y'],
                'sum': online_total['目标'][index]['sum'] + offline_total['目标'][index]['sum'],
                's': '目标'
            })

        data_total = []
        data_total.extend(total['实际'])
        data_total.extend(total['目标'])
        data_total.sort(key=lambda x: x['x'])

        data = {
            '合计': data_total,
            '线上': data_online,
            '线下': data_offline
        }
        return data


if __name__ == '__main__':
    source = NhbInsureV5Target()
    # df_onlines = source.online_adjust_data()
    # print(df_onlines)
    # df_offlines = source.offline_adjust_data()
    # print(df_offlines)
    # df_online_target,dict_online = source.online_daily_target()
    # print(df_online_target)
    # pprint(dict_online)
    # offline_daily_target,dict_offline = source.offline_daily_target()
    # print(offline_daily_target)
    # pprint(dict_offline)
    #
    data = source.daily_target()
    pprint(data)
