from django.db import models
from common.models import BaseModel
from public.models import SystemDict


class SystemDictValue(BaseModel):
    dict_id = models.IntegerField(blank=True, null=True, verbose_name='字典ID')
    position = models.IntegerField(blank=True, null=True, verbose_name='字典排序')
    label = models.CharField(max_length=128, blank=True, null=True,db_index=True, verbose_name='字典标签')
    key = models.CharField(max_length=128, blank=True, null=True, verbose_name='字典键值')
    status = models.IntegerField(default=1, blank=True, null=True, verbose_name='状态（0正常 1停用）')
    description = models.TextField(blank=True, null=True, verbose_name='描述说明')

    def __str__(self):
        return self.label

    class Meta:
        db_table = 'system_dict_value'
        verbose_name = '数据字典值表'
        verbose_name_plural = verbose_name
