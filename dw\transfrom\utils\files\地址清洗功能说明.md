# 地址清洗功能说明

## 📋 概述

地址清洗功能用于处理不规范的地址数据，包括去除多余的空格、标点符号、地址标识符等，提高地址数据的质量和一致性。

## 🔧 功能特性

### 1. 基础地址清洗 (`clean_address_basic`)
- 提取汉字、数字和常用标点
- 保留字母与数字的组合（如 A3）
- 适用于简单的地址清洗需求

### 1.5. 全角转半角 (`convert_fullwidth_to_halfwidth`) 🔄 **优化**
- **Unicode标准转换**：使用Unicode编码范围进行转换，更加标准和全面
- **全角空格转换**：全角空格(12288) → 半角空格(32)
- **全角字符转换**：全角字符(65281-65374) → 半角字符(减去65248)
- **覆盖范围**：数字、字母、标点符号、特殊字符等全面转换

### 2. 增强地址清洗 (`clean_address_enhanced`) ⭐ **推荐** - 核心功能版本

**4个核心功能**：

1. **地址标识符清理**：自动去除"地址："、"详细地址："、"办公地址："等前缀和后缀，包括"址:"和制表符
2. **智能空格和标点规范化**：智能处理空格，保留重要分隔，规范化常见的不规范表述
   - `底 商` → `底商`
   - `1、2 号楼` → `1、2号楼`
   - `3 层 201 室` → `3层201室`
   - `2 栋 3 单元` → `2栋3单元`
   - `5 幢 101 号` → `5幢101号`
   - `18-1 22-1号` → `18-1 22-1号` (保留门牌号空格)
   - `B12  1层` → `B12 1层` (保留建筑标识分隔空格)
   - `经度:121.053345  玮度:41.361883` → `经度:121.053345 玮度:41.361883` (多空格规范化为单空格)
   - `  北京市朝阳区建国门外大街1号  ` → `北京市朝阳区建国门外大街1号` (开头结尾空格清除)
3. **宽松的特殊字符处理**：采用保守策略，保留所有可能有意义的特殊字符和分隔符
   - 地理坐标符号：`°` `′` `″` `'` `"` (度分秒和引号)
   - 连字符变体：`—` `——` `－` (长连字符、双连字符、全角连字符)
   - 范围和分隔符：`~` `_` `/` `*` (波浪号、下划线、斜杠、星号)
   - 其他有意义符号：`#` `;` `,` `，` (井号、分号、逗号)
   - 网址和邮箱清理：自动识别并清除HTTP/HTTPS网址和邮箱地址
4. **智能括号处理**：清除空括号`()`和只含标点的括号，保留有意义的括号

## 📊 清洗效果示例

### 案例1：地址标识符清理
```
原始: "地址：北京市朝阳区建国门外大街1号"
清洗: "北京市朝阳区建国门外大街1号"
```

### 案例2：空格和标点规范化
```
原始: "北京市大兴区黄村镇清源西里 1、2 号楼底 商"
清洗: "北京市大兴区黄村镇清源西里1、2号楼底商"
```

### 案例3：智能特殊字符清理
```
原始: "北京市海淀区中关村大街@#$%^&*()1号A3楼"
清洗: "北京市海淀区中关村大街#1号A3楼"
说明: 清除无意义特殊字符和空括号，保留有意义的#号
```

### 案例4：智能括号处理
```
原始: "北京市朝阳区建国门外大街#1号(A座)15层()"
清洗: "北京市朝阳区建国门外大街#1号(A座)15层"
说明: 保留有意义的括号(A座)，清除空括号()
```

### 案例5：重要分隔符保留
```
原始: "广渠门外南街广渠家园8号楼一层101/102/103单元"
清洗: "广渠门外南街广渠家园8号楼一层101/102/103单元"
说明: 保留斜杠(/)作为单元号分隔符
```

### 案例6：通配符保留
```
原始: "北京市西城区平原里21号楼1层1039、2层23035-2303*9、23050"
清洗: "北京市西城区平原里21号楼1层1039、2层23035-2303*9、23050"
说明: 保留星号(*)作为通配符，避免信息丢失
```

### 案例7：间隔号保留
```
原始: "京投万科·金域公园7号楼"
清洗: "京投万科·金域公园7号楼"
说明: 保留间隔号(·)，避免品牌名称信息丢失
```

### 案例8：全角字母保留
```
原始: "天津市和平区劝业场街南京路258号巨贝大厦Ａ区六层601室"
清洗: "天津市和平区劝业场街南京路258号巨贝大厦Ａ区六层601室"
说明: 保留全角字母(Ａ)，避免区域标识信息丢失
```

### 案例9：中间地址标识符清理
```
原始: "张山营镇靳家堡社区卫生服务站 地址:靳家堡村赵丁路"
清洗: "张山营镇靳家堡社区卫生服务站靳家堡村赵丁路"
说明: 清理文字中间的地址标识符，保持地址连贯性
```

### 案例10：门牌号空格保留
```
原始: "天津市东丽区无瑕街东环路北18-1 22-1号"
清洗: "天津市东丽区无瑕街东环路北18-1 22-1号"
说明: 保留门牌号之间的重要空格，避免门牌号混淆
```

### 案例11：全角转半角
```
原始: "天津市和平区劝业场街南京路２５８号巨贝大厦Ａ区六层６０１室"
转换: "天津市和平区劝业场街南京路258号巨贝大厦A区六层601室"
说明: 全角数字、字母、标点符号全面转换为半角
```

### 案例12：全角标点转换
```
原始: "北京市朝阳区建国门外大街１号（Ｂ座）"
转换: "北京市朝阳区建国门外大街1号(B座)"
说明: 全角括号、数字、字母统一转换为半角
```

### 案例13：智能空格分隔保留
```
原始: "和平南路丽景广场B12  1层01号商铺西侧"
清洗: "和平南路丽景广场B12 1层01号商铺西侧"
说明: 保留建筑标识与楼层之间的重要分隔空格，去除多余空格
```

### 案例14：地理坐标符号保留
```
原始: "钢铁路办事处新华西街轧钢北路咱家小区西200米处北纬40°48'34.45''东经111°36'42.45''"
清洗: "钢铁路办事处新华西街轧钢北路咱家小区西200米处北纬40°48'34.45''东经111°36'42.45''"
说明: 完整保留地理坐标的度分秒符号和引号，避免坐标信息丢失
```

### 案例15：连字符变体保留
```
原始: "青山区幸福南路17#景辰酒店49—1"
清洗: "青山区幸福南路17#景辰酒店49—1"
说明: 保留长连字符(—)，避免门牌号信息丢失
```

### 案例16：范围符号保留
```
原始: "柳州街5号(A01~C09室)"
清洗: "柳州街5号(A01~C09室)"
说明: 保留波浪号(~)作为房间号范围标识
```

### 案例17：双连字符保留
```
原始: "昆明湖街51——27号(6门)"
清洗: "昆明湖街51——27号(6门)"
说明: 保留双连字符(——)，避免门牌号结构破坏
```

### 案例18：下划线分隔符保留
```
原始: "南阳湖街9_1号3门"
清洗: "南阳湖街9_1号3门"
说明: 保留下划线(_)作为门牌号分隔符
```

### 案例19：多空格规范化
```
原始: "义县留龙沟镇大齐沟村经度:121.053345  玮度:41.361883"
清洗: "义县留龙沟镇大齐沟村经度:121.053345 玮度:41.361883"
说明: 将多个空格规范化为单个空格，保留分隔作用
```

### 案例20：网址清理
```
原始: "辽宁省朝阳市建平县北二十家https://code.nhsa.gov.cn/yljg/yljgBaseDataApply/toYljgAuthDetail.html###子镇二十家子村文宣路012号"
清洗: "辽宁省朝阳市建平县北二十家子镇二十家子村文宣路012号"
说明: 自动识别并清除HTTP/HTTPS网址，保留地址内容
```

### 案例21：特殊零字符转换
```
原始: "铜陵市义安区新桥办事处七〇一居委会"
清洗: "铜陵市义安区新桥办事处七0一居委会"
说明: 将中文零字符(〇)转换为数字0，规范化编号格式
```

### 案例22：制表符地址标识符清理
```
原始: "址:\t裕安区西河口乡河口村"
清洗: "裕安区西河口乡河口村"
说明: 清理"址:"标识符和制表符，保持地址纯净
```

### 案例23：中间地址标识符完全清理
```
原始: "十三陵镇昌赤路大宫门村老年活动中心院内地址:十三陵镇昌赤路大宫门村老年活动中心院内"
清洗: "十三陵镇昌赤路大宫门村老年活动中心院内十三陵镇昌赤路大宫门村老年活动中心院内"
说明: 完全清理文字中间的"地址:"标识符，避免地址内容分割
```

### 案例24：邮箱地址清理
```
原始: "***************西张庄村"
清洗: "西张庄村"
说明: 自动识别并完全清除邮箱地址，保持地址纯净
```

### 案例25：复杂邮箱清理
```
原始: "北京市朝阳区建国门外大街test@gmail.com1号楼"
清洗: "北京市朝阳区建国门外大街1号楼"
说明: 清除地址中混入的邮箱地址，保留完整地址信息
```

### 案例26：开头结尾空格清除
```
原始: "  重庆市渝中区解放碑   D6楼  "
清洗: "重庆市渝中区解放碑 D6楼"
说明: 清除开头和结尾的所有空格，中间多空格规范化为单空格
```

## 🚀 使用方法

### 1. 直接调用函数
```python
from transfrom.utils.data_cleaning_config import CommonCleaningFunctions

# 基础清洗
basic_result = CommonCleaningFunctions.clean_address_basic(address)

# 增强清洗（推荐）
enhanced_result = CommonCleaningFunctions.clean_address_enhanced(address)

# 全角转半角（优化版）
halfwidth_result = CommonCleaningFunctions.convert_fullwidth_to_halfwidth(address)
```

### 2. 在ETL流程中使用
地址清洗功能已集成到国家医保局的数据清洗配置中：

```python
# 在 gjyb/pipeline/config/data_cleaning_config.py 中
# 自动对 addr 字段应用增强清洗
df['addr'] = df['addr'].apply(
    lambda x: CommonCleaningFunctions.clean_address_enhanced(x)
    if isinstance(x, str) and len(x) > 10 else x
)
```

### 3. 批量处理DataFrame
```python
import pandas as pd
from transfrom.utils.data_cleaning_config import CommonCleaningFunctions

# 创建测试数据
df = pd.DataFrame({
    'addr': [
        '地址：北京市朝阳区建国门外大街1号',
        '详细地址：上海市黄浦区南京东路100号2 层 201 室',
        '广州市天河区珠江新城CBD地址：'
    ]
})

# 应用增强清洗
df['addr_cleaned'] = df['addr'].apply(CommonCleaningFunctions.clean_address_enhanced)
```

## ⚙️ 配置参数

### 长度阈值
- **基础清洗**：通常用于长度 > 200 的地址
- **增强清洗**：适用于长度 > 10 的地址（更宽松的阈值）

### 支持的地址标识符
- `地址`、`详细地址`、`联系地址`
- `办公地址`、`注册地址`、`通讯地址`
- 支持中英文冒号：`:`、`：`

### 保留的字符类型
- 汉字：`\u4e00-\u9fa5`
- 数字：`\d`
- 常用标点：`-#()（）`
- 地址相关词汇：`层室号楼栋幢座单元弄巷街道路区县市省村镇乡`
- 字母数字组合：`A3`、`B2` 等

## 📈 性能优化

### 1. 批量处理优化
```python
# 使用 pandas apply 进行向量化处理
df['addr'] = df['addr'].apply(CommonCleaningFunctions.clean_address_enhanced)
```

### 2. 条件清洗
```python
# 只对符合条件的地址进行清洗
df['addr'] = df['addr'].apply(
    lambda x: CommonCleaningFunctions.clean_address_enhanced(x)
    if isinstance(x, str) and len(x) > 10 else x
)
```

## 🔍 测试验证

### 运行测试
```python
# 简单测试
from transfrom.utils.data_cleaning_config import CommonCleaningFunctions

test_address = "地址：北京市大兴区黄村镇清源西里 1、2 号楼底 商"
result = CommonCleaningFunctions.clean_address_enhanced(test_address)
print(f"原始: {test_address}")
print(f"清洗: {result}")
```

### 预期效果
- 去除地址标识符
- 规范化空格和标点
- 保留有效的地址信息
- 显著减少字符数量
- 提高地址数据一致性

## 📝 注意事项

1. **数据备份**：在大批量清洗前建议备份原始数据
2. **测试验证**：新的清洗规则应先在小样本上测试
3. **阈值调整**：根据实际数据质量调整长度阈值
4. **特殊情况**：对于特殊格式的地址可能需要自定义处理

## 🔄 版本历史

- **v1.0** - 基础地址清洗功能
- **v2.0** - 增强地址清洗功能，支持更多不规范数据处理
- **v2.1** - 集成到ETL流程，降低处理阈值
- **v3.0** - 简化为核心功能版本，专注于4个主要功能，提高稳定性和可维护性
- **v3.1** - 优化特殊字符清洗，保留有意义的斜杠(/)和星号(*)，避免误删重要地址信息
- **v3.2** - 全面优化地址清洗：保留间隔号(·)、全角字母、门牌号空格，清理中间地址标识符
- **v3.3** - 优化全角转半角功能：采用Unicode标准转换方法，支持更全面的字符转换
- **v3.4** - 优化空格处理策略：采用智能空格保留机制，保护重要的建筑标识分隔空格
- **v3.5** - 采用宽松清洗策略：保留所有有意义的特殊字符，包括地理坐标符号、连字符变体、范围符号等
- **v3.6** - 修复经纬度引号保留：支持单引号(')和双引号(")在地理坐标中的使用
- **v3.7** - 综合修复优化：多空格规范化、网址清理、零字符转换、制表符地址标识符清理
- **v3.8** - 修复中间地址标识符清理：完全清除文字中间的"地址:"、"详细地址:"等标识符
- **v3.9** - 新增邮箱地址清理：自动识别并清除地址中的邮箱地址，支持常见邮箱服务商
- **v3.10** - 优化空格处理逻辑：清除开头结尾空格，保留中间单空格，多空格规范化为单空格

## 🤝 贡献

如发现新的地址不规范模式，请提供案例以便改进清洗规则。
