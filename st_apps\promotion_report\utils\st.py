import pandas as pd
import json
import streamlit as st
from sqlalchemy import text
def submit_and_save_edits(edited_table, file_name, key,file_type = 'excel'):
    """
    提交修改并保存数据到Excel文件。
    :param edited_table: 修改后的数据DataFrame。
    :param file_name: 要保存的Excel文件名。
    :param key: 按钮的key值，用于标识不同的按钮。
    :param file_name:文件类型，默认为excel，可选参数为json
    """
    if st.button('表格提交修改', key=key):
        if edited_table is not None:  # 检查是否有数据被修改并提交
            if file_type == 'excel':
                # 将修改后的数据覆盖原始文件
                edited_table.reset_index(inplace=True)
                edited_table.to_excel(file_name, index=False)
                st.success('数据已成功保存至Excel文件。')
            elif file_type == 'json':
                # 将修改后的数据覆盖原始文件
                with open(file_name, 'w', encoding='utf-8') as file:
                    json.dump(edited_table, file, ensure_ascii=False, indent=4)
                    st.success('数据已成功保存至JSON文件。')
        else:
            st.warning('没有检测到修改后的数据。')


def submit_and_update_table(edited_df, table_name, data_columns, db_columns, unique_index,
                            key='submit_and_update_table'):
    """
    更新编辑后的数据到数据库。
    :param edited_df: 编辑后的 DataFrame。
    :param table_name: 数据库中的表名。
    :param data_columns: 编辑后的 DataFrame 中用于更新的列名列表,顺序为db_columns+unique_index。
    :param db_columns: 数据库中对应的列名列表。
    :param unique_index: 数据库表中的唯一索引列名列表。
    :param key: 按钮的key值，用于标识不同的按钮。
    """
    CONNECTOR_DW = st.connection('dw', type='sql')
    if st.button('表格提交修改', key=key):
        if edited_df is not None:  # 检查是否有数据被修改并提交
            # 准备数据
            data_to_update = edited_df[data_columns].to_dict(orient='records')
            # 构建更新语句模板
            set_clause = ', '.join([f'{col} = "%s"' for col in db_columns])
            where_clause = ' AND '.join([f'{index} = "%s"' for index in unique_index])
            update_query_template = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause} ;"

            # 执行更新
            with CONNECTOR_DW.session as s:
                for params in data_to_update:
                    compiled_stmt = update_query_template % tuple(params.values())
                    # 替换参数列表中的 None 值为 'NULL'，以符合 SQL 语法
                    compiled_stmt_str = str(compiled_stmt).replace('None', 'NULL')
                    s.execute(text(compiled_stmt_str))
                    s.commit()

            st.success('数据已成功更新。')
        else:
            st.warning('没有检测到修改后的数据。')

def modify_dataframe(df,file_name, operation, **kwargs):
    """
    根据指定的操作修改DataFrame。
    参数:
    - df: 要修改的原始DataFrame。
    - file_name: 原始Excel文件名。
    - operation: 要执行的操作，可以是'add_rows', 'add_column', 'delete_rows', 'delete_columns'。
    - **kwargs: 根据操作不同而变化的关键字参数。
    # 使用函数添加行
    df = modify_dataframe(df, 'add_rows', row_name=2)
    # 使用函数添加列
    df = modify_dataframe(df, 'add_column', column_name='NewColumn')
    # 使用函数删除特定的行
    df = modify_dataframe(df, 'delete_rows', rows=[0])
    # 使用函数删除列
    df = modify_dataframe(df, 'delete_column', column_name='Column1')
    返回:
    - 修改后的DataFrame。
    """
    if operation == 'add_rows':
        row_name = kwargs.get('row_name')  # 默认添加0行
        new_rows = pd.DataFrame(index=[row_name], columns=df.columns)
        new_rows.index.name = '名字'
        # 保证合计项在最后一行
        df_not_contain_sum = df[df.index != '合计']
        df_sum = df[df.index == '合计']
        df = pd.concat([df_not_contain_sum, new_rows,df_sum])
        df.reset_index(inplace=True)
        df.to_excel(file_name, index=False)
        return df
    elif operation == 'add_column':
        column_name = kwargs.get('column_name')
        #保证完成率的几列在最后
        df_not_contain_rate = df[df.columns[~df.columns.str.contains('完成率')]]
        df_rate = df[df.columns[df.columns.str.contains('完成率')]]
        df_not_contain_rate[column_name] = None
        df = pd.concat([df_not_contain_rate, df_rate], axis=1)
        df.reset_index(inplace=True)
        df.to_excel(file_name, index=False)
        return df
    elif operation == 'delete_rows':
        rows_to_delete = kwargs.get('rows_to_delete', [])
        if not isinstance(rows_to_delete, list):
            rows_to_delete = [rows_to_delete]
        df.drop(rows_to_delete,inplace=True)
        df.reset_index(inplace=True)
        df.to_excel(file_name, index=False)
        return df
    elif operation == 'delete_columns':
        column_name = kwargs.get('columns_to_delete', [])
        if not isinstance(column_name, list):
            column_name = [column_name]
        df.drop(columns=column_name,inplace=True)
        df.reset_index(inplace=True)
        df.to_excel(file_name, index=False)
        return df
    else:
        raise ValueError("Unsupported operation")


def hide_sidebar():
    """
    隐藏侧边栏
    """
    # 隐藏侧边栏
    hide_sidebar_style = """
        <style>
        .st-emotion-cache-6qob1r.e1dbuyne8 {display: none;}
        .st-emotion-cache-vmpjyt.e1dbuyne0 {display: none;}
        .st-emotion-cache-19ee8pt.e1dbuyne15 {display: none;}
        </style>
    """
    st.markdown(hide_sidebar_style, unsafe_allow_html=True)

def show_sidebar():
    """
    显示侧边栏
    """
    # 显示侧边栏
    show_sidebar_style = """
        <style>
        .st-emotion-cache-6qob1r.e1dbuyne8 {display: block;}
        .st-emotion-cache-vmpjyt.e1dbuyne0 {display: block;}
        .st-emotion-cache-19ee8pt.e1dbuyne15 {display: block;}
        </style>
    """
    st.markdown(show_sidebar_style, unsafe_allow_html=True)


def handle_table_modification(input_cols_keys, buttons_cols_keys, edited_table, file_name):
    """
    对表格数据进行增删处理。
    :param input_cols_keys: 输入框的key值列表。
    :param buttons_cols_keys: 按钮的key值列表。
    :param edited_table: 需要编辑的在线DataFrame。
    :param file_name: 原始Excel文件名。
    """
    input_cols = st.columns([0.15, 0.15, 0.4, 0.4])
    buttons_cols = st.columns([0.15, 0.15, 0.4, 0.4])
    with input_cols[0]:
        row_name = st.text_input('添加人员姓名',key = input_cols_keys[0])
    with buttons_cols[0]:
        if st.button('添加行记录', key=buttons_cols_keys[0]):
            if row_name:
                modify_dataframe(edited_table, file_name, 'add_rows', row_name=row_name)
                st.rerun()
            else:
                st.warning('请输入人员姓名')

    with input_cols[1]:
        column_name = st.text_input('添加产品名称',key = input_cols_keys[1])
    with buttons_cols[1]:
        if st.button('添加列记录', key=buttons_cols_keys[1]):
            if column_name:
                data = [st.text_input(f'Enter value for row {i + 1}:') for i in range(len(edited_table))]
                modify_dataframe(edited_table, file_name, 'add_column', column_name=column_name)
                st.rerun()
            else:
                st.warning('请输入列名')

    with input_cols[2]:
        rows_to_delete = st.multiselect('删除行', options=edited_table.index, default=[],key = input_cols_keys[2])
    with buttons_cols[2]:
        if st.button('删除行记录', key=buttons_cols_keys[2]):
            if rows_to_delete:
                modify_dataframe(edited_table, file_name, 'delete_rows', rows_to_delete=rows_to_delete)
                st.rerun()
            else:
                st.warning('请选择要删除的行')

    with input_cols[3]:
        columns = edited_table.columns.tolist()
        column_to_delete = st.multiselect('删除列', options=columns, default=[],key = input_cols_keys[3])
    with buttons_cols[3]:
        if st.button('删除列记录', key=buttons_cols_keys[3]):
            if column_to_delete:
                modify_dataframe(edited_table, file_name, 'delete_columns', columns_to_delete=column_to_delete)
                st.rerun()
            else:
                st.warning('请选择要删除的列')


def text_write(text,color='#add8e6'):
    """
    创建一个带有颜色色块和加粗文本的HTML字符串。
    :param text : str 要显示的文本。
    :paramcolor : str 色块的背景颜色，可以是颜色名称或者十六进制颜色代码。
    """
    html_code = f'''
        <div style="display: flex; align-items: center;">
            <div style="width: 10px; height: 20px; background-color: {color}; margin-right: 5px; border-radius: 3px;"></div> <!-- 色块 -->
            <strong>{text}</strong> <!-- 加粗文本 -->
        </div>
        '''
    st.markdown(html_code, unsafe_allow_html=True)


def sub_text_write(text,color='#29B09D'):
    """
    创建一个带有颜色色块和加粗文本的HTML字符串。
    :param text : str 要显示的文本。
    :paramcolor : str 色块的背景颜色，可以是颜色名称或者十六进制颜色代码。
    """
    html_code = f'''
        <div style="display: flex; align-items: center;">
            <div style="width: 8px; height: 16px; background-color: {color}; margin-right: 5px; border-radius: 3px;"></div> <!-- 色块 -->
            <span style="font-size: 15px;">{text}</span>
        </div>
        '''
    st.markdown(html_code, unsafe_allow_html=True)

def empty_line(num):
    # 调整布局，换行，保持显示在中间
    for i in range(num):
        st.write('\n')



def set_customer_style():
    """
    保证手机上显示效果
    """
    # 手机上column依然保持在一行,而不是一列
    st.write('''<style>
    [data-testid="column"] {
        width: calc(16.6666% - 1rem) !important;
        flex: 1 1 calc(16.6666% - 1rem) !important;
        min-width: calc(16.6666% - 1rem) !important;

    }
    </style>''', unsafe_allow_html=True)
    # 去掉顶部的padding,使得在手机上的空间更紧致(配合--client.toolbarMode="minimal"使用)
    st.write('''<style>
        [data-testid="stAppViewBlockContainer"] {
            padding: 10px;
        }
        </style>''', unsafe_allow_html=True)


def hide_streamlit_settings():
    """
    隐藏菜单栏和页脚
    """
    hide_menu_style = """
        <style>
        #MainMenu {visibility: hidden;}
        footer {visibility: hidden;}
        </style>
    """
    st.markdown(hide_menu_style, unsafe_allow_html=True)


def query_sql(sql_name_en):
    """
    查询sql
    :param sql_name_en: sql英文名称
    """
    CONNECTOR_DW = st.connection('dw', type='sql')
    record = CONNECTOR_DW.query(
        "select template from public_sql_template where name_en = '{sql_name_en}'".format(sql_name_en=sql_name_en),
        show_spinner='查询中...', ttl=600)
    sql = record['template'].values[0]
    return sql