# Generated by Django 4.2.1 on 2025-05-26 16:56

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="MedicalChineseHerbalDrugBase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="中草药品代码",
                        max_length=128,
                        null=True,
                        verbose_name="中草药品代码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="中草药品名称",
                        max_length=128,
                        null=True,
                        verbose_name="中草药品名称",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        db_comment="药品类别",
                        max_length=64,
                        null=True,
                        verbose_name="药品类别",
                    ),
                ),
                (
                    "category_source",
                    models.CharField(
                        blank=True,
                        db_comment="药品类别来源",
                        max_length=64,
                        null=True,
                        verbose_name="药品类别来源",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        db_comment="药品类型",
                        max_length=64,
                        null=True,
                        verbose_name="药品类型",
                    ),
                ),
                (
                    "dosage",
                    models.CharField(
                        blank=True,
                        db_comment="用量",
                        max_length=255,
                        null=True,
                        verbose_name="用量",
                    ),
                ),
                (
                    "medicinal_material_name",
                    models.CharField(
                        blank=True,
                        db_comment="药材名称",
                        max_length=64,
                        null=True,
                        verbose_name="药材名称",
                    ),
                ),
                (
                    "source_standard_document_name",
                    models.CharField(
                        blank=True,
                        db_comment="来源标准文档名称",
                        max_length=255,
                        null=True,
                        verbose_name="来源标准文档名称",
                    ),
                ),
                (
                    "province_code",
                    models.CharField(
                        blank=True,
                        db_comment="省份编码",
                        max_length=32,
                        null=True,
                        verbose_name="省份编码",
                    ),
                ),
                (
                    "province_name",
                    models.CharField(
                        blank=True,
                        db_comment="省份",
                        max_length=64,
                        null=True,
                        verbose_name="省份",
                    ),
                ),
                (
                    "efficacy_information",
                    models.CharField(
                        blank=True,
                        db_comment="疗效说明",
                        max_length=1024,
                        null=True,
                        verbose_name="疗效说明",
                    ),
                ),
                (
                    "national_healthcare_policy",
                    models.CharField(
                        blank=True,
                        db_comment="国家医保政策",
                        max_length=255,
                        null=True,
                        verbose_name="国家医保政策",
                    ),
                ),
                (
                    "provincial_healthcare_policy",
                    models.CharField(
                        blank=True,
                        db_comment="省级医保政策",
                        max_length=255,
                        null=True,
                        verbose_name="省级医保政策",
                    ),
                ),
                (
                    "medicinal_part",
                    models.CharField(
                        blank=True,
                        db_comment="入药部位",
                        max_length=128,
                        null=True,
                        verbose_name="入药部位",
                    ),
                ),
                (
                    "properties_meridian_tropism",
                    models.CharField(
                        blank=True,
                        db_comment="性味/归经",
                        max_length=255,
                        null=True,
                        verbose_name="性味/归经",
                    ),
                ),
                (
                    "preparation_method",
                    models.CharField(
                        blank=True,
                        db_comment="炮制方法",
                        max_length=255,
                        null=True,
                        verbose_name="炮制方法",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保中草药信息基础表",
                "verbose_name_plural": "医保中草药信息基础表",
                "db_table": "medical_chinese_herbal_drug_base",
                "db_table_comment": "医保中草药信息基础表",
            },
        ),
        migrations.CreateModel(
            name="MedicalDesignatedProviders",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "province_code",
                    models.CharField(
                        blank=True,
                        db_comment="机构省份编码",
                        max_length=32,
                        null=True,
                        verbose_name="机构省份编码",
                    ),
                ),
                (
                    "province_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构省份",
                        max_length=128,
                        null=True,
                        verbose_name="机构省份",
                    ),
                ),
                (
                    "city_code",
                    models.CharField(
                        blank=True,
                        db_comment="机构城市编码",
                        max_length=32,
                        null=True,
                        verbose_name="机构城市编码",
                    ),
                ),
                (
                    "city_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构城市",
                        max_length=128,
                        null=True,
                        verbose_name="机构城市",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="机构编码",
                        max_length=128,
                        null=True,
                        verbose_name="机构编码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="机构名称",
                        max_length=255,
                        null=True,
                        verbose_name="机构名称",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        db_comment="机构分类",
                        max_length=255,
                        null=True,
                        verbose_name="机构分类",
                    ),
                ),
                (
                    "category_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构分类名称",
                        max_length=255,
                        null=True,
                        verbose_name="机构分类名称",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True,
                        db_comment="地址",
                        max_length=512,
                        null=True,
                        verbose_name="地址",
                    ),
                ),
                (
                    "lat",
                    models.CharField(
                        blank=True,
                        db_comment="经度",
                        max_length=512,
                        null=True,
                        verbose_name="经度",
                    ),
                ),
                (
                    "lnt",
                    models.CharField(
                        blank=True,
                        db_comment="纬度",
                        max_length=512,
                        null=True,
                        verbose_name="纬度",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        db_comment="机构类型",
                        max_length=32,
                        null=True,
                        verbose_name="机构类型",
                    ),
                ),
                (
                    "type_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构类型名称",
                        max_length=64,
                        null=True,
                        verbose_name="机构类型名称",
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        blank=True,
                        db_comment="机构等级",
                        max_length=32,
                        null=True,
                        verbose_name="机构等级",
                    ),
                ),
                (
                    "level_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构等级名称",
                        max_length=32,
                        null=True,
                        verbose_name="机构等级名称",
                    ),
                ),
                (
                    "uscc",
                    models.CharField(
                        blank=True,
                        db_comment="统一社会信用代码",
                        max_length=255,
                        null=True,
                        verbose_name="统一社会信用代码",
                    ),
                ),
                (
                    "mobile",
                    models.CharField(
                        blank=True,
                        db_comment="电话号码",
                        max_length=255,
                        null=True,
                        verbose_name="电话号码",
                    ),
                ),
                (
                    "business_scope",
                    models.TextField(
                        blank=True,
                        db_comment="经营范围",
                        null=True,
                        verbose_name="经营范围",
                    ),
                ),
                (
                    "cross_regional_medical_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="异地就医服务状态",
                        null=True,
                        verbose_name="异地就医服务状态",
                    ),
                ),
                (
                    "inpatient_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="住院开通情况",
                        null=True,
                        verbose_name="住院开通情况",
                    ),
                ),
                (
                    "outpatient_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="门诊开通情况",
                        null=True,
                        verbose_name="门诊开通情况",
                    ),
                ),
                (
                    "outpatient_chronic_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="门诊慢特病开通情况",
                        null=True,
                        verbose_name="门诊慢特病开通情况",
                    ),
                ),
                (
                    "outpatient_chronic_disease",
                    models.CharField(
                        blank=True,
                        db_comment="门诊慢特病支持病种编码",
                        max_length=1024,
                        null=True,
                        verbose_name="门诊慢特病支持病种编码",
                    ),
                ),
                (
                    "outpatient_chronic_disease_name",
                    models.CharField(
                        blank=True,
                        db_comment="门诊慢特病支持病种",
                        max_length=1024,
                        null=True,
                        verbose_name="门诊慢特病支持病种",
                    ),
                ),
                (
                    "electronic_prescription_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="电子处方开通状态",
                        null=True,
                        verbose_name="电子处方开通状态",
                    ),
                ),
                (
                    "electronic_certificate_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="电子凭证开通状态",
                        null=True,
                        verbose_name="电子凭证开通状态",
                    ),
                ),
                (
                    "mobile_payment_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="移动支付开通状态",
                        null=True,
                        verbose_name="移动支付开通状态",
                    ),
                ),
                (
                    "insurance_wallet_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="医保钱包开通状态",
                        null=True,
                        verbose_name="医保钱包开通状态",
                    ),
                ),
                (
                    "electronic_billing_status",
                    models.IntegerField(
                        blank=True,
                        db_comment="电子票据开通状态",
                        null=True,
                        verbose_name="电子票据开通状态",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保定点医疗服务机构信息表",
                "verbose_name_plural": "医保定点医疗服务机构信息表",
                "db_table": "medical_designated_providers",
                "db_table_comment": "医保定点医疗服务机构信息表",
            },
        ),
        migrations.CreateModel(
            name="MedicalDrugBase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="药品代码",
                        max_length=128,
                        null=True,
                        verbose_name="药品代码",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        db_comment="药品分类",
                        max_length=32,
                        null=True,
                        verbose_name="药品分类",
                    ),
                ),
                (
                    "type_name",
                    models.CharField(
                        blank=True,
                        db_comment="药品分类名称",
                        max_length=64,
                        null=True,
                        verbose_name="药品分类名称",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        db_comment="药品类别",
                        max_length=32,
                        null=True,
                        verbose_name="药品类别",
                    ),
                ),
                (
                    "market_status",
                    models.CharField(
                        blank=True,
                        db_comment="药品上市状态",
                        max_length=32,
                        null=True,
                        verbose_name="药品上市状态",
                    ),
                ),
                (
                    "registered_name",
                    models.CharField(
                        blank=True,
                        db_comment="注册名称",
                        max_length=128,
                        null=True,
                        verbose_name="注册名称",
                    ),
                ),
                (
                    "registered_dosage_form",
                    models.CharField(
                        blank=True,
                        db_comment="注册剂型",
                        max_length=32,
                        null=True,
                        verbose_name="注册剂型",
                    ),
                ),
                (
                    "registered_specifications",
                    models.CharField(
                        blank=True,
                        db_comment="注册规格",
                        max_length=128,
                        null=True,
                        verbose_name="注册规格",
                    ),
                ),
                (
                    "product_name",
                    models.CharField(
                        blank=True,
                        db_comment="商品名称",
                        max_length=128,
                        null=True,
                        verbose_name="商品名称",
                    ),
                ),
                (
                    "expiration",
                    models.CharField(
                        blank=True,
                        db_comment="有效期",
                        max_length=255,
                        null=True,
                        verbose_name="有效期",
                    ),
                ),
                (
                    "dosage_form",
                    models.CharField(
                        blank=True,
                        db_comment="剂型",
                        max_length=128,
                        null=True,
                        verbose_name="剂型",
                    ),
                ),
                (
                    "specifications",
                    models.CharField(
                        blank=True,
                        db_comment="规格",
                        max_length=255,
                        null=True,
                        verbose_name="规格",
                    ),
                ),
                (
                    "packaging_material",
                    models.CharField(
                        blank=True,
                        db_comment="包装材质",
                        max_length=255,
                        null=True,
                        verbose_name="包装材质",
                    ),
                ),
                (
                    "each_dose",
                    models.CharField(
                        blank=True,
                        db_comment="每次剂量说明",
                        max_length=512,
                        null=True,
                        verbose_name="每次剂量说明",
                    ),
                ),
                (
                    "efficacy_information",
                    models.CharField(
                        blank=True,
                        db_comment="疗效说明",
                        max_length=1024,
                        null=True,
                        verbose_name="疗效说明",
                    ),
                ),
                (
                    "minimum_packaging_count",
                    models.CharField(
                        blank=True,
                        db_comment="最小包装数量",
                        max_length=64,
                        null=True,
                        verbose_name="最小包装数量",
                    ),
                ),
                (
                    "minimum_preparation_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小制剂单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小制剂单位",
                    ),
                ),
                (
                    "minimum_packaging_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小包装单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小包装单位",
                    ),
                ),
                (
                    "minimum_prescription_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小处方单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小处方单位",
                    ),
                ),
                (
                    "otc_flag",
                    models.IntegerField(
                        blank=True,
                        db_comment="是否处方药",
                        null=True,
                        verbose_name="是否处方药",
                    ),
                ),
                (
                    "listed_license_holder",
                    models.CharField(
                        blank=True,
                        db_comment="药品持有企业",
                        max_length=255,
                        null=True,
                        verbose_name="药品持有企业",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "approval_number",
                    models.CharField(
                        blank=True,
                        db_comment="批准文号",
                        max_length=255,
                        null=True,
                        verbose_name="批准文号",
                    ),
                ),
                (
                    "standard_code",
                    models.CharField(
                        blank=True,
                        db_comment="药品本位码",
                        max_length=255,
                        null=True,
                        verbose_name="药品本位码",
                    ),
                ),
                (
                    "categories_national",
                    models.CharField(
                        blank=True,
                        db_comment="甲乙类(国家医保药品目录)",
                        max_length=32,
                        null=True,
                        verbose_name="甲乙类(国家医保药品目录)",
                    ),
                ),
                (
                    "number_national",
                    models.CharField(
                        blank=True,
                        db_comment="编号(国家医保药品目录)",
                        max_length=255,
                        null=True,
                        verbose_name="编号(国家医保药品目录)",
                    ),
                ),
                (
                    "generic_name_national",
                    models.CharField(
                        blank=True,
                        db_comment="药品名称(国家医保药品目录)",
                        max_length=255,
                        null=True,
                        verbose_name="药品名称(国家医保药品目录)",
                    ),
                ),
                (
                    "dosage_form_national",
                    models.CharField(
                        blank=True,
                        db_comment="剂型(国家医保药品目录)",
                        max_length=255,
                        null=True,
                        verbose_name="剂型(国家医保药品目录)",
                    ),
                ),
                (
                    "remark_national",
                    models.CharField(
                        blank=True,
                        db_comment="备注(国家医保药品目录)",
                        max_length=512,
                        null=True,
                        verbose_name="备注(国家医保药品目录)",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保药品信息基础表",
                "verbose_name_plural": "医保药品信息基础表",
                "db_table": "medical_drug_base",
                "db_table_comment": "医保药品信息基础表;包含西药、中成药数据",
            },
        ),
        migrations.CreateModel(
            name="MedicalDrugEntity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="药品代码",
                        max_length=128,
                        null=True,
                        verbose_name="药品代码",
                    ),
                ),
                (
                    "central_code",
                    models.CharField(
                        blank=True,
                        db_comment="中心编码",
                        max_length=128,
                        null=True,
                        verbose_name="中心编码",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        db_comment="药品分类",
                        max_length=32,
                        null=True,
                        verbose_name="药品分类",
                    ),
                ),
                (
                    "category_name",
                    models.CharField(
                        blank=True,
                        db_comment="药品分类名称",
                        max_length=64,
                        null=True,
                        verbose_name="药品分类名称",
                    ),
                ),
                (
                    "province_code",
                    models.CharField(
                        blank=True,
                        db_comment="省份编码",
                        max_length=32,
                        null=True,
                        verbose_name="省份编码",
                    ),
                ),
                (
                    "province_name",
                    models.CharField(
                        blank=True,
                        db_comment="省份",
                        max_length=128,
                        null=True,
                        verbose_name="省份",
                    ),
                ),
                (
                    "city_code",
                    models.CharField(
                        blank=True,
                        db_comment="城市编码",
                        max_length=32,
                        null=True,
                        verbose_name="城市编码",
                    ),
                ),
                (
                    "city_name",
                    models.CharField(
                        blank=True,
                        db_comment="城市",
                        max_length=128,
                        null=True,
                        verbose_name="城市",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        db_comment="药品类别",
                        max_length=32,
                        null=True,
                        verbose_name="药品类别",
                    ),
                ),
                (
                    "registered_name",
                    models.CharField(
                        blank=True,
                        db_comment="注册名称",
                        max_length=128,
                        null=True,
                        verbose_name="注册名称",
                    ),
                ),
                (
                    "registered_specifications",
                    models.CharField(
                        blank=True,
                        db_comment="注册规格",
                        max_length=128,
                        null=True,
                        verbose_name="注册规格",
                    ),
                ),
                (
                    "product_name",
                    models.CharField(
                        blank=True,
                        db_comment="商品名称",
                        max_length=128,
                        null=True,
                        verbose_name="商品名称",
                    ),
                ),
                (
                    "specifications",
                    models.CharField(
                        blank=True,
                        db_comment="规格",
                        max_length=255,
                        null=True,
                        verbose_name="规格",
                    ),
                ),
                (
                    "packaging_material",
                    models.CharField(
                        blank=True,
                        db_comment="包装材质",
                        max_length=255,
                        null=True,
                        verbose_name="包装材质",
                    ),
                ),
                (
                    "minimum_packaging_count",
                    models.CharField(
                        blank=True,
                        db_comment="最小包装数量",
                        max_length=64,
                        null=True,
                        verbose_name="最小包装数量",
                    ),
                ),
                (
                    "minimum_packaging_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小包装单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小包装单位",
                    ),
                ),
                (
                    "minimum_prescription_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小处方单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小处方单位",
                    ),
                ),
                (
                    "otc_flag",
                    models.IntegerField(
                        blank=True,
                        db_comment="是否处方药",
                        null=True,
                        verbose_name="是否处方药",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "approval_number",
                    models.CharField(
                        blank=True,
                        db_comment="批准文号",
                        max_length=255,
                        null=True,
                        verbose_name="批准文号",
                    ),
                ),
                (
                    "categories_national",
                    models.CharField(
                        blank=True,
                        db_comment="甲乙类(国家医保药品目录)",
                        max_length=32,
                        null=True,
                        verbose_name="甲乙类(国家医保药品目录)",
                    ),
                ),
                (
                    "number_national",
                    models.CharField(
                        blank=True,
                        db_comment="编号(国家医保药品目录)",
                        max_length=255,
                        null=True,
                        verbose_name="编号(国家医保药品目录)",
                    ),
                ),
                (
                    "generic_name_national",
                    models.CharField(
                        blank=True,
                        db_comment="药品名称(国家医保药品目录)",
                        max_length=255,
                        null=True,
                        verbose_name="药品名称(国家医保药品目录)",
                    ),
                ),
                (
                    "dosage_form_national",
                    models.CharField(
                        blank=True,
                        db_comment="剂型(国家医保药品目录)",
                        max_length=255,
                        null=True,
                        verbose_name="剂型(国家医保药品目录)",
                    ),
                ),
                (
                    "procure_ceil_price",
                    models.DecimalField(
                        blank=True,
                        db_comment="集中采购上限价",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="集中采购上限价",
                    ),
                ),
                (
                    "payment_upper_limit",
                    models.CharField(
                        blank=True,
                        db_comment="医保支付上限",
                        max_length=255,
                        null=True,
                        verbose_name="医保支付上限",
                    ),
                ),
                (
                    "person_type",
                    models.CharField(
                        blank=True,
                        db_comment="人员类别",
                        max_length=32,
                        null=True,
                        verbose_name="人员类别",
                    ),
                ),
                (
                    "initial_payment_ratio",
                    models.DecimalField(
                        blank=True,
                        db_comment="先行自付比例",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="先行自付比例",
                    ),
                ),
                (
                    "pharmacy_sale_allowed",
                    models.IntegerField(
                        blank=True,
                        db_comment="药店是否可以出售",
                        null=True,
                        verbose_name="药店是否可以出售",
                    ),
                ),
                (
                    "payment_restricted_scope",
                    models.CharField(
                        blank=True,
                        db_comment="医保限定支付范围",
                        max_length=512,
                        null=True,
                        verbose_name="医保限定支付范围",
                    ),
                ),
                (
                    "government_guided_price",
                    models.DecimalField(
                        blank=True,
                        db_comment="政府指导价",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="政府指导价",
                    ),
                ),
                (
                    "begin_date",
                    models.DateField(
                        blank=True,
                        db_comment="开始日期",
                        null=True,
                        verbose_name="开始日期",
                    ),
                ),
                (
                    "end_date",
                    models.DateField(
                        blank=True,
                        db_comment="结束日期",
                        null=True,
                        verbose_name="结束日期",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保药品省市实体信息表",
                "verbose_name_plural": "医保药品省市实体信息表",
                "db_table": "medical_drug_entity",
                "db_table_comment": "医保药品省市实体信息表;包含西药、中成药数据",
            },
        ),
        migrations.CreateModel(
            name="MedicalFieldMapping",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "source_table",
                    models.CharField(
                        blank=True,
                        db_comment="来源表",
                        max_length=64,
                        null=True,
                        verbose_name="来源表",
                    ),
                ),
                (
                    "source_field",
                    models.CharField(
                        blank=True,
                        db_comment="来源字段",
                        max_length=128,
                        null=True,
                        verbose_name="来源字段",
                    ),
                ),
                (
                    "target_table",
                    models.CharField(
                        blank=True,
                        db_comment="目标表",
                        max_length=64,
                        null=True,
                        verbose_name="目标表",
                    ),
                ),
                (
                    "target_field",
                    models.CharField(
                        blank=True,
                        db_comment="目标字段",
                        max_length=128,
                        null=True,
                        verbose_name="目标字段",
                    ),
                ),
                (
                    "default",
                    models.CharField(
                        blank=True,
                        db_comment="默认值",
                        max_length=128,
                        null=True,
                        verbose_name="默认值",
                    ),
                ),
                (
                    "description",
                    models.CharField(
                        blank=True,
                        db_comment="描述",
                        max_length=512,
                        null=True,
                        verbose_name="描述",
                    ),
                ),
                (
                    "dict_table",
                    models.CharField(
                        blank=True,
                        db_comment="字典表",
                        max_length=64,
                        null=True,
                        verbose_name="字典表",
                    ),
                ),
                (
                    "dict_id",
                    models.CharField(
                        blank=True,
                        db_comment="字典分类id",
                        max_length=32,
                        null=True,
                        verbose_name="字典分类id",
                    ),
                ),
                (
                    "dict_code_field",
                    models.CharField(
                        blank=True,
                        db_comment="字典表编码字段",
                        max_length=64,
                        null=True,
                        verbose_name="字典表编码字段",
                    ),
                ),
                (
                    "dict_name_field",
                    models.CharField(
                        blank=True,
                        db_comment="字典表名称字段",
                        max_length=128,
                        null=True,
                        verbose_name="字典表名称字段",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保字段映射表",
                "verbose_name_plural": "医保字段映射表",
                "db_table": "medical_field_mapping",
                "db_table_comment": "医保字段映射表",
            },
        ),
        migrations.CreateModel(
            name="MedicalMedicineDiagnosis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="疾病/手术名称",
                        max_length=128,
                        null=True,
                        verbose_name="疾病/手术名称",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="疾病/手术代码",
                        max_length=128,
                        null=True,
                        verbose_name="疾病/手术代码",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        db_comment="分类",
                        max_length=64,
                        null=True,
                        verbose_name="分类",
                    ),
                ),
                (
                    "type_name",
                    models.CharField(
                        blank=True,
                        db_comment="分类名称",
                        max_length=64,
                        null=True,
                        verbose_name="分类名称",
                    ),
                ),
                (
                    "chapter_code_scope",
                    models.CharField(
                        blank=True,
                        db_comment="章代码范围",
                        max_length=64,
                        null=True,
                        verbose_name="章代码范围",
                    ),
                ),
                (
                    "chapter",
                    models.CharField(
                        blank=True,
                        db_comment="章",
                        max_length=255,
                        null=True,
                        verbose_name="章",
                    ),
                ),
                (
                    "section_code_scope",
                    models.CharField(
                        blank=True,
                        db_comment="节代码范围",
                        max_length=64,
                        null=True,
                        verbose_name="节代码范围",
                    ),
                ),
                (
                    "section",
                    models.CharField(
                        blank=True,
                        db_comment="节",
                        max_length=255,
                        null=True,
                        verbose_name="节",
                    ),
                ),
                (
                    "category_code",
                    models.CharField(
                        blank=True,
                        db_comment="类目代码",
                        max_length=64,
                        null=True,
                        verbose_name="类目代码",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        blank=True,
                        db_comment="类目",
                        max_length=255,
                        null=True,
                        verbose_name="类目",
                    ),
                ),
                (
                    "subcategory_code",
                    models.CharField(
                        blank=True,
                        db_comment="亚目代码",
                        max_length=64,
                        null=True,
                        verbose_name="亚目代码",
                    ),
                ),
                (
                    "subcategory",
                    models.CharField(
                        blank=True,
                        db_comment="亚目",
                        max_length=255,
                        null=True,
                        verbose_name="亚目",
                    ),
                ),
                (
                    "detail_code",
                    models.CharField(
                        blank=True,
                        db_comment="细目代码",
                        max_length=64,
                        null=True,
                        verbose_name="细目代码",
                    ),
                ),
                (
                    "detail",
                    models.CharField(
                        blank=True,
                        db_comment="细目",
                        max_length=255,
                        null=True,
                        verbose_name="细目",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "疾病诊断手术操作信息表",
                "verbose_name_plural": "疾病诊断手术操作信息表",
                "db_table": "medical_medicine_diagnosis",
                "db_table_comment": "疾病诊断手术操作信息表",
            },
        ),
        migrations.CreateModel(
            name="MedicalNationalNegotiatedDrug",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "drug_name",
                    models.CharField(
                        blank=True,
                        db_comment="药品名称",
                        max_length=128,
                        null=True,
                        verbose_name="药品名称",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "sale_flag",
                    models.IntegerField(
                        blank=True,
                        db_comment="出售标识",
                        null=True,
                        verbose_name="出售标识",
                    ),
                ),
                (
                    "mobile",
                    models.CharField(
                        blank=True,
                        db_comment="联系电话",
                        max_length=255,
                        null=True,
                        verbose_name="联系电话",
                    ),
                ),
            ],
            options={
                "verbose_name": "国家谈判药品配备机构目录",
                "verbose_name_plural": "国家谈判药品配备机构目录",
                "db_table": "medical_national_negotiated_drug",
                "db_table_comment": "国家谈判药品配备机构目录",
            },
        ),
        migrations.CreateModel(
            name="MedicalNationalNegotiatedDrugProviders",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "drug_name",
                    models.CharField(
                        blank=True,
                        db_comment="药品名称",
                        max_length=128,
                        null=True,
                        verbose_name="药品名称",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="机构名称",
                        max_length=128,
                        null=True,
                        verbose_name="机构名称",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True,
                        db_comment="机构类型",
                        max_length=255,
                        null=True,
                        verbose_name="机构类型",
                    ),
                ),
                (
                    "province_code",
                    models.CharField(
                        blank=True,
                        db_comment="机构省份编码",
                        max_length=32,
                        null=True,
                        verbose_name="机构省份编码",
                    ),
                ),
                (
                    "province_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构省份",
                        max_length=128,
                        null=True,
                        verbose_name="机构省份",
                    ),
                ),
                (
                    "city_code",
                    models.CharField(
                        blank=True,
                        db_comment="机构城市编码",
                        max_length=32,
                        null=True,
                        verbose_name="机构城市编码",
                    ),
                ),
                (
                    "city_name",
                    models.CharField(
                        blank=True,
                        db_comment="机构城市",
                        max_length=128,
                        null=True,
                        verbose_name="机构城市",
                    ),
                ),
                (
                    "address",
                    models.CharField(
                        blank=True,
                        db_comment="机构地址",
                        max_length=512,
                        null=True,
                        verbose_name="机构地址",
                    ),
                ),
                (
                    "lat",
                    models.CharField(
                        blank=True,
                        db_comment="经度",
                        max_length=128,
                        null=True,
                        verbose_name="经度",
                    ),
                ),
                (
                    "lnt",
                    models.CharField(
                        blank=True,
                        db_comment="纬度",
                        max_length=128,
                        null=True,
                        verbose_name="纬度",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "国家谈判药品销售机构",
                "verbose_name_plural": "国家谈判药品销售机构",
                "db_table": "medical_national_negotiated_drug_providers",
                "db_table_comment": "国家谈判药品销售机构",
            },
        ),
        migrations.CreateModel(
            name="MedicalSelfPreparedDrugBase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="制剂代码",
                        max_length=128,
                        null=True,
                        verbose_name="制剂代码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="制剂名称",
                        max_length=128,
                        null=True,
                        verbose_name="制剂名称",
                    ),
                ),
                (
                    "minimum_packaging_count",
                    models.CharField(
                        blank=True,
                        db_comment="最小包装数量",
                        max_length=64,
                        null=True,
                        verbose_name="最小包装数量",
                    ),
                ),
                (
                    "minimum_preparation_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小制剂单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小制剂单位",
                    ),
                ),
                (
                    "minimum_packaging_unit",
                    models.CharField(
                        blank=True,
                        db_comment="最小包装单位",
                        max_length=64,
                        null=True,
                        verbose_name="最小包装单位",
                    ),
                ),
                (
                    "each_dose",
                    models.CharField(
                        blank=True,
                        db_comment="每次剂量",
                        max_length=255,
                        null=True,
                        verbose_name="每次剂量",
                    ),
                ),
                (
                    "dosage_form",
                    models.CharField(
                        blank=True,
                        db_comment="剂型",
                        max_length=128,
                        null=True,
                        verbose_name="剂型",
                    ),
                ),
                (
                    "specifications",
                    models.CharField(
                        blank=True,
                        db_comment="规格",
                        max_length=255,
                        null=True,
                        verbose_name="规格",
                    ),
                ),
                (
                    "efficacy_information",
                    models.CharField(
                        blank=True,
                        db_comment="疗效说明",
                        max_length=1024,
                        null=True,
                        verbose_name="疗效说明",
                    ),
                ),
                (
                    "packaging_material",
                    models.CharField(
                        blank=True,
                        db_comment="包装材质",
                        max_length=255,
                        null=True,
                        verbose_name="包装材质",
                    ),
                ),
                (
                    "license_number",
                    models.CharField(
                        blank=True,
                        db_comment="许可证号",
                        max_length=128,
                        null=True,
                        verbose_name="许可证号",
                    ),
                ),
                (
                    "approval_number",
                    models.CharField(
                        blank=True,
                        db_comment="批准文号",
                        max_length=255,
                        null=True,
                        verbose_name="批准文号",
                    ),
                ),
                (
                    "approval_begin_date",
                    models.DateField(
                        blank=True,
                        db_comment="批准文号开始日期",
                        null=True,
                        verbose_name="批准文号开始日期",
                    ),
                ),
                (
                    "elder_medication_precautions",
                    models.CharField(
                        blank=True,
                        db_comment="老年患者用药注意事项",
                        max_length=512,
                        null=True,
                        verbose_name="老年患者用药注意事项",
                    ),
                ),
                (
                    "child_medication_precautions",
                    models.CharField(
                        blank=True,
                        db_comment="儿童患者用药注意事项",
                        max_length=512,
                        null=True,
                        verbose_name="儿童患者用药注意事项",
                    ),
                ),
                (
                    "hospital_name",
                    models.CharField(
                        blank=True,
                        db_comment="医疗机构名称",
                        max_length=255,
                        null=True,
                        verbose_name="医疗机构名称",
                    ),
                ),
                (
                    "hospital_contact_person",
                    models.CharField(
                        blank=True,
                        db_comment="医疗机构联系人",
                        max_length=64,
                        null=True,
                        verbose_name="医疗机构联系人",
                    ),
                ),
                (
                    "hospital_address",
                    models.CharField(
                        blank=True,
                        db_comment="医疗机构地址",
                        max_length=255,
                        null=True,
                        verbose_name="医疗机构地址",
                    ),
                ),
                (
                    "hospital_regional_code",
                    models.CharField(
                        blank=True,
                        db_comment="医疗机构区域编码",
                        max_length=64,
                        null=True,
                        verbose_name="医疗机构区域编码",
                    ),
                ),
                (
                    "hospital_mobile",
                    models.CharField(
                        blank=True,
                        db_comment="医疗机构电话号码",
                        max_length=255,
                        null=True,
                        verbose_name="医疗机构电话号码",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "production_company_address",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业地址",
                        max_length=512,
                        null=True,
                        verbose_name="生产企业地址",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保自制药信息基础表",
                "verbose_name_plural": "医保自制药信息基础表",
                "db_table": "medical_self_prepared_drug_base",
                "db_table_comment": "医保自制药信息基础表",
            },
        ),
        migrations.CreateModel(
            name="MedicalServiceBase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="服务项目代码",
                        max_length=128,
                        null=True,
                        verbose_name="服务项目代码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="服务项目名称",
                        max_length=128,
                        null=True,
                        verbose_name="服务项目名称",
                    ),
                ),
                (
                    "charge_item_code",
                    models.CharField(
                        blank=True,
                        db_comment="收费项目代码",
                        max_length=128,
                        null=True,
                        verbose_name="收费项目代码",
                    ),
                ),
                (
                    "charge_item_name",
                    models.CharField(
                        blank=True,
                        db_comment="收费项目名称",
                        max_length=128,
                        null=True,
                        verbose_name="收费项目名称",
                    ),
                ),
                (
                    "admin_region_code",
                    models.CharField(
                        blank=True,
                        db_comment="行政区域编码",
                        max_length=32,
                        null=True,
                        verbose_name="行政区域编码",
                    ),
                ),
                (
                    "admin_region_name",
                    models.CharField(
                        blank=True,
                        db_comment="行政区域名称",
                        max_length=128,
                        null=True,
                        verbose_name="行政区域名称",
                    ),
                ),
                (
                    "treatment_item_content",
                    models.CharField(
                        blank=True,
                        db_comment="诊疗项目内涵",
                        max_length=1024,
                        null=True,
                        verbose_name="诊疗项目内涵",
                    ),
                ),
                (
                    "treatment_item_description",
                    models.CharField(
                        blank=True,
                        db_comment="诊疗项目说明",
                        max_length=1024,
                        null=True,
                        verbose_name="诊疗项目说明",
                    ),
                ),
                (
                    "treatment_excluded_content",
                    models.CharField(
                        blank=True,
                        db_comment="诊疗除外内容",
                        max_length=1024,
                        null=True,
                        verbose_name="诊疗除外内容",
                    ),
                ),
                (
                    "pricing_unit",
                    models.CharField(
                        blank=True,
                        db_comment="计价单位",
                        max_length=32,
                        null=True,
                        verbose_name="计价单位",
                    ),
                ),
                (
                    "begin_date",
                    models.DateField(
                        blank=True,
                        db_comment="开始日期",
                        null=True,
                        verbose_name="开始日期",
                    ),
                ),
                (
                    "end_data",
                    models.DateField(
                        blank=True,
                        db_comment="结束日期",
                        null=True,
                        verbose_name="结束日期",
                    ),
                ),
                (
                    "remark",
                    models.CharField(
                        blank=True,
                        db_comment="备注",
                        max_length=512,
                        null=True,
                        verbose_name="备注",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "医疗服务项目分类基础表",
                "verbose_name_plural": "医疗服务项目分类基础表",
                "db_table": "medical_service_base",
                "db_table_comment": "医疗服务项目分类基础表",
            },
        ),
        migrations.CreateModel(
            name="MedicalServiceEntity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="服务项目代码",
                        max_length=128,
                        null=True,
                        verbose_name="服务项目代码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="服务项目名称",
                        max_length=64,
                        null=True,
                        verbose_name="服务项目名称",
                    ),
                ),
                (
                    "charge_item_code",
                    models.CharField(
                        blank=True,
                        db_comment="收费项目代码",
                        max_length=128,
                        null=True,
                        verbose_name="收费项目代码",
                    ),
                ),
                (
                    "charge_item_name",
                    models.CharField(
                        blank=True,
                        db_comment="收费项目名称",
                        max_length=128,
                        null=True,
                        verbose_name="收费项目名称",
                    ),
                ),
                (
                    "central_code",
                    models.CharField(
                        blank=True,
                        db_comment="中心编码",
                        max_length=128,
                        null=True,
                        verbose_name="中心编码",
                    ),
                ),
                (
                    "level_type",
                    models.CharField(
                        blank=True,
                        db_comment="层级类型",
                        max_length=32,
                        null=True,
                        verbose_name="层级类型",
                    ),
                ),
                (
                    "province_code",
                    models.CharField(
                        blank=True,
                        db_comment="省份编码",
                        max_length=32,
                        null=True,
                        verbose_name="省份编码",
                    ),
                ),
                (
                    "province_name",
                    models.CharField(
                        blank=True,
                        db_comment="省份",
                        max_length=128,
                        null=True,
                        verbose_name="省份",
                    ),
                ),
                (
                    "city_code",
                    models.CharField(
                        blank=True,
                        db_comment="城市编码",
                        max_length=32,
                        null=True,
                        verbose_name="城市编码",
                    ),
                ),
                (
                    "city_name",
                    models.CharField(
                        blank=True,
                        db_comment="城市",
                        max_length=128,
                        null=True,
                        verbose_name="城市",
                    ),
                ),
                (
                    "treatment_item_content",
                    models.CharField(
                        blank=True,
                        db_comment="诊疗项目内涵",
                        max_length=1024,
                        null=True,
                        verbose_name="诊疗项目内涵",
                    ),
                ),
                (
                    "treatment_item_description",
                    models.CharField(
                        blank=True,
                        db_comment="诊疗项目说明",
                        max_length=1024,
                        null=True,
                        verbose_name="诊疗项目说明",
                    ),
                ),
                (
                    "treatment_excluded_content",
                    models.CharField(
                        blank=True,
                        db_comment="诊疗除外内容",
                        max_length=1024,
                        null=True,
                        verbose_name="诊疗除外内容",
                    ),
                ),
                (
                    "pricing_unit",
                    models.CharField(
                        blank=True,
                        db_comment="计价单位",
                        max_length=32,
                        null=True,
                        verbose_name="计价单位",
                    ),
                ),
                (
                    "payment_type",
                    models.CharField(
                        blank=True,
                        db_comment="医保支付类别",
                        max_length=32,
                        null=True,
                        verbose_name="医保支付类别",
                    ),
                ),
                (
                    "payment_upper_limit",
                    models.CharField(
                        blank=True,
                        db_comment="医保支付上限",
                        max_length=255,
                        null=True,
                        verbose_name="医保支付上限",
                    ),
                ),
                (
                    "price",
                    models.DecimalField(
                        blank=True,
                        db_comment="供应价格",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="供应价格",
                    ),
                ),
                (
                    "price_composition",
                    models.CharField(
                        blank=True,
                        db_comment="价格构成",
                        max_length=1024,
                        null=True,
                        verbose_name="价格构成",
                    ),
                ),
                (
                    "service_output",
                    models.CharField(
                        blank=True,
                        db_comment="服务产出",
                        max_length=1024,
                        null=True,
                        verbose_name="服务产出",
                    ),
                ),
                (
                    "initial_payment_ratio",
                    models.DecimalField(
                        blank=True,
                        db_comment="先行自付比例",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="先行自付比例",
                    ),
                ),
                (
                    "limited_payment",
                    models.CharField(
                        blank=True,
                        db_comment="限定支付",
                        max_length=255,
                        null=True,
                        verbose_name="限定支付",
                    ),
                ),
                (
                    "begin_date",
                    models.DateField(
                        blank=True,
                        db_comment="开始日期",
                        null=True,
                        verbose_name="开始日期",
                    ),
                ),
                (
                    "additional_info",
                    models.TextField(
                        blank=True,
                        db_comment="附加信息",
                        null=True,
                        verbose_name="附加信息",
                    ),
                ),
            ],
            options={
                "verbose_name": "医疗服务项目省市实体表",
                "verbose_name_plural": "医疗服务项目省市实体表",
                "db_table": "medical_service_entity",
                "db_table_comment": "医疗服务项目省市实体表;主要用于记录省级、市级的数据，避免与国家目录干扰",
            },
        ),
        migrations.CreateModel(
            name="MedicalSuppliesBase",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="耗材代码",
                        max_length=128,
                        null=True,
                        verbose_name="耗材代码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="耗材名称",
                        max_length=128,
                        null=True,
                        verbose_name="耗材名称",
                    ),
                ),
                (
                    "general_name",
                    models.CharField(
                        blank=True,
                        db_comment="医保通用名称",
                        max_length=128,
                        null=True,
                        verbose_name="医保通用名称",
                    ),
                ),
                (
                    "first_type",
                    models.CharField(
                        blank=True,
                        db_comment="一级分类（学科、品类）",
                        max_length=64,
                        null=True,
                        verbose_name="一级分类（学科、品类）",
                    ),
                ),
                (
                    "second_type",
                    models.CharField(
                        blank=True,
                        db_comment="二级分类（用途、品目）",
                        max_length=64,
                        null=True,
                        verbose_name="二级分类（用途、品目）",
                    ),
                ),
                (
                    "third_type",
                    models.CharField(
                        blank=True,
                        db_comment="三级分类（部位、功能能、品种）",
                        max_length=64,
                        null=True,
                        verbose_name="三级分类（部位、功能能、品种）",
                    ),
                ),
                (
                    "type_path",
                    models.CharField(
                        blank=True,
                        db_comment="分类明细",
                        max_length=255,
                        null=True,
                        verbose_name="分类明细",
                    ),
                ),
                (
                    "material",
                    models.CharField(
                        blank=True,
                        db_comment="耗材材质",
                        max_length=64,
                        null=True,
                        verbose_name="耗材材质",
                    ),
                ),
                (
                    "specifications",
                    models.CharField(
                        blank=True,
                        db_comment="规格（特征、参数）",
                        max_length=255,
                        null=True,
                        verbose_name="规格（特征、参数）",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "rid",
                    models.CharField(
                        blank=True,
                        db_comment="记录标识",
                        max_length=255,
                        null=True,
                        verbose_name="记录标识",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保耗材分类基础表",
                "verbose_name_plural": "医保耗材分类基础表",
                "db_table": "medical_supplies_base",
                "db_table_comment": "医保耗材分类基础表",
            },
        ),
        migrations.CreateModel(
            name="MedicalSuppliesEntity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="耗材代码",
                        max_length=128,
                        null=True,
                        verbose_name="耗材代码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        db_comment="耗材名称",
                        max_length=128,
                        null=True,
                        verbose_name="耗材名称",
                    ),
                ),
                (
                    "central_code",
                    models.CharField(
                        blank=True,
                        db_comment="中心编码",
                        max_length=128,
                        null=True,
                        verbose_name="中心编码",
                    ),
                ),
                (
                    "registered_name",
                    models.CharField(
                        blank=True,
                        db_comment="注册名称",
                        max_length=128,
                        null=True,
                        verbose_name="注册名称",
                    ),
                ),
                (
                    "registered_number",
                    models.CharField(
                        blank=True,
                        db_comment="注册证号",
                        max_length=128,
                        null=True,
                        verbose_name="注册证号",
                    ),
                ),
                (
                    "model",
                    models.CharField(
                        blank=True,
                        db_comment="型号",
                        max_length=1024,
                        null=True,
                        verbose_name="型号",
                    ),
                ),
                (
                    "specifications",
                    models.CharField(
                        blank=True,
                        db_comment="规格",
                        max_length=1024,
                        null=True,
                        verbose_name="规格",
                    ),
                ),
                (
                    "production_company_name",
                    models.CharField(
                        blank=True,
                        db_comment="生产企业名称",
                        max_length=255,
                        null=True,
                        verbose_name="生产企业名称",
                    ),
                ),
                (
                    "initial_payment_ratio",
                    models.DecimalField(
                        blank=True,
                        db_comment="先行自付比例",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="先行自付比例",
                    ),
                ),
                (
                    "payment_upper_limit",
                    models.DecimalField(
                        blank=True,
                        db_comment="医保支付上限",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="医保支付上限",
                    ),
                ),
                (
                    "tender_number",
                    models.CharField(
                        blank=True,
                        db_comment="招标号",
                        max_length=255,
                        null=True,
                        verbose_name="招标号",
                    ),
                ),
                (
                    "tender_price",
                    models.DecimalField(
                        blank=True,
                        db_comment="招标价格",
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        verbose_name="招标价格",
                    ),
                ),
                (
                    "packaging_unit",
                    models.CharField(
                        blank=True,
                        db_comment="包装单位",
                        max_length=64,
                        null=True,
                        verbose_name="包装单位",
                    ),
                ),
                (
                    "begin_date",
                    models.DateField(
                        blank=True,
                        db_comment="开始日期",
                        null=True,
                        verbose_name="开始日期",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保耗材省市实体表",
                "verbose_name_plural": "医保耗材省市实体表",
                "db_table": "medical_supplies_entity",
                "db_table_comment": "医保耗材省市实体表",
            },
        ),
        migrations.CreateModel(
            name="MedicalSuppliesRegisterMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        db_comment="创建时间",
                        help_text="由数据库自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        db_comment="更新时间",
                        help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_comment="耗材代码",
                        max_length=128,
                        null=True,
                        verbose_name="耗材代码",
                    ),
                ),
                (
                    "registration_number",
                    models.CharField(
                        blank=True,
                        db_comment="注册编码",
                        max_length=255,
                        null=True,
                        verbose_name="注册编码",
                    ),
                ),
                (
                    "single_product_name",
                    models.CharField(
                        blank=True,
                        db_comment="单件产品名称",
                        max_length=255,
                        null=True,
                        verbose_name="单件产品名称",
                    ),
                ),
            ],
            options={
                "verbose_name": "医保耗材注册信息表",
                "verbose_name_plural": "医保耗材注册信息表",
                "db_table": "medical_supplies_register_message",
                "db_table_comment": "医保耗材注册信息表",
            },
        ),
    ]
