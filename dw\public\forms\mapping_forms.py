from django import forms
from public.models import PublicMapping, SystemDictValue
from django.forms.models import ModelChoiceField


class MappingChoiceField(ModelChoiceField):
    # 重写label_from_instance方法，使得下拉框显示中文
    def label_from_instance(self, obj):
        return obj.key


class MappingForm(forms.ModelForm):
    class Meta:
        model = PublicMapping
        fields = '__all__'

    type = MappingChoiceField(
        queryset=SystemDictValue.objects.filter(dict_id=3),
        to_field_name='label',
        label='分类',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
