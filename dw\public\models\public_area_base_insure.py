from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class PublicAreaBaseInsure(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='地区名称')
    code = models.CharField(max_length=64, blank=True, null=True, verbose_name='地区编码')
    count = models.IntegerField(blank=True, null=True, verbose_name='基本医保人数')
    employee_count = models.IntegerField(blank=True, null=True, verbose_name='职工医保人数')
    resident_count = models.IntegerField(blank=True, null=True, verbose_name='居民医保人数')
    target = models.IntegerField(blank=True, null=True, verbose_name='销售目标')

    class Meta:
        db_table = 'public_area_base_insure'
        verbose_name = '地区基础参保信息'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'name'],
                name='unique_public_area_base_insure_combination'
            ),
        ]
