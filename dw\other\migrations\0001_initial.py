# Generated by Django 4.2.13 on 2024-07-19 12:37

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="OtherYbStatisticNhb",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_code",
                    models.CharField(
                        blank=True, max_length=200, null=True, verbose_name="产品编码"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=200, null=True, verbose_name="类型"
                    ),
                ),
                (
                    "content",
                    models.JSONField(blank=True, null=True, verbose_name="内容数据"),
                ),
                (
                    "is_post",
                    models.BooleanField(
                        blank=True, default=False, null=True, verbose_name="是否已推送"
                    ),
                ),
            ],
            options={
                "verbose_name": "医保高铁-宁惠保理赔报表",
                "verbose_name_plural": "医保高铁-宁惠保理赔报表",
                "db_table": "other_yb_statistic_nhb",
            },
        ),
    ]
