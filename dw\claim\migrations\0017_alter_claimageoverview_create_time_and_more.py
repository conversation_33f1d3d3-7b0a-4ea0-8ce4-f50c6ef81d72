# Generated by Django 4.2.1 on 2025-05-26 16:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("claim", "0016_claimxuepingxian_product_set_code_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="claimageoverview",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimageoverview",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimagerangepay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimagerangepay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimamountrangepay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimamountrangepay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimareapay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimareapay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimgenderpay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimgenderpay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimgrouppay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimgrouppay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimliabilitypay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimliabilitypay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimmonthlypay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimmonthlypay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimpayoverview",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimpayoverview",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimpaytype",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimpaytype",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimpreexistingcondition",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimpreexistingcondition",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimproductpay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimproductpay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimprotonheavyion",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimprotonheavyion",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimsellerpay",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimsellerpay",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimtopcaseinfo",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimtopcaseinfo",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
        migrations.AlterField(
            model_name="claimxuepingxian",
            name="create_time",
            field=models.DateTimeField(db_comment="创建时间", verbose_name="创建时间"),
        ),
        migrations.AlterField(
            model_name="claimxuepingxian",
            name="update_time",
            field=models.DateTimeField(db_comment="更新时间", verbose_name="更新时间"),
        ),
    ]
