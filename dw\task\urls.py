from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import datav_hxx_insure,datav_nhb_insure,datav_nxb_insure,datav_ghb_insure,datav_bzyhb_insure,datav_dzhmb_insure,\
    datav_njdha_insure,datav_jsop_insure,datav_njnhb_insure

urlpatterns = [
    path('datavhxxinsure/', datav_hxx_insure, name='datav_hxx_insure'),
    path('datavnhbinsure/', datav_nhb_insure, name='datav_nhb_insure'),
    path('datavnxbinsure/', datav_nxb_insure, name='datav_nxb_insure'),
    path('datavghbinsure/', datav_ghb_insure, name='datav_ghb_insure'),
    path('datavbzyhbinsure/', datav_bzyhb_insure, name='datav_bzyhb_insure'),
    path('datavdzhmbinsure/', datav_dzhmb_insure, name='datav_dzhmb_insure'),
    path('datavnjdhainsure/', datav_njdha_insure, name='datav_njdha_insure'),
    path('datavjsopinsure/', datav_jsop_insure, name='datav_jsop_insure'),
    path('datavnjnhbinsure/', datav_njnhb_insure, name='datav_njnhb_insure'),
]
