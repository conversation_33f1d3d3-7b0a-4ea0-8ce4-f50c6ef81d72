# Generated by Django 3.2.12 on 2024-11-13 13:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('insure', '0004_auto_20241017_1152'),
    ]

    operations = [
        migrations.CreateModel(
            name='InsureMobile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_short_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品类编码')),
                ('channel_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='渠道名称')),
                ('mobile', models.Char<PERSON>ield(blank=True, max_length=32, null=True, verbose_name='手机号码')),
                ('additional_info', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=128, null=True, verbose_name='补充信息')),
            ],
            options={
                'verbose_name': '健康险渠道手机号码',
                'verbose_name_plural': '健康险渠道手机号码',
                'db_table': 'insure_mobile',
            },
        ),
    ]
