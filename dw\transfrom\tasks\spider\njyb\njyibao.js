require('./loader')

// 直接引入node-fetch作为备选方案
const fetch = require('node-fetch');

// 尝试从window.__o获取request和processResponse函数
// 如果失败，则使用node-fetch作为备选方案
let request, processResponse;

try {
    // 尝试从window.__o获取函数
    const module = window.__o("7d92");
    request = module.request;
    processResponse = module.processResponse;
    console.log('成功从window.__o获取request和processResponse函数');
} catch (error) {
    console.log('无法从window.__o获取函数，使用node-fetch作为备选方案');
    // 使用node-fetch作为备选方案
    request = async function(options) {
        try {
            console.log(`正在发送请求到: ${options.url}`);

            // 准备fetch选项
            const fetchOptions = {
                method: options.method || 'GET',
                headers: options.headers || {},
                timeout: options.timeout || 30000
            };

            // 如果有请求体数据，添加到选项中
            if (options.data) {
                fetchOptions.body = JSON.stringify(options.data);
            }

            // 发送请求
            const response = await fetch(options.url, fetchOptions);

            // 解析响应
            const data = await response.json();

            // 返回与axios兼容的响应格式
            return {
                data: data,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers
            };
        } catch (error) {
            console.error('请求出错:', error.message);
            throw error;
        }
    };

    processResponse = function(response) {
        if (response && response.data) {
            return response.data;
        }
        return response;
    };
}

// 常用浏览器的User-Agent列表
const userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15'
];

// 随机获取User-Agent
function getRandomUserAgent() {
    const randomIndex = Math.floor(Math.random() * userAgents.length);
    return userAgents[randomIndex];
}

async function fetchMedicalData(maxPages = 5, apiUrl = 'https://wb.njybjyybz.org.cn:9091/hsa-pss-pw-nj/web/pw/wsgs/ypmlcx', data = {}, startPage = 1, filename = 'medical_data') {
    if (maxPages < 1) {
        throw new Error('页数必须大于0');
    }
    if (!apiUrl) {
        throw new Error('API URL不能为空');
    }

    const fs = require('fs');
    const path = require('path');
    const downloadDir = path.join(__dirname, 'download', 'json');
    fs.mkdirSync(downloadDir, { recursive: true });
    const batchSize = 2; // 每批次处理的页数
    const allData = [];
    const savedFiles = [];
    const maxRetries = 10; // 最大重试次数
    const initialRetryDelay = 5000; // 初始重试延迟时间（毫秒）
    const maxRetryDelay = 60000; // 最大重试延迟时间（毫秒）
    const requestTimeout = 60000; // 单个请求超时时间（毫秒）
    let totalRecords = 0; // 记录总数据条数

    // 计算需要爬取的批次数
    const remainingPages = maxPages - startPage + 1;
    const batchCount = Math.ceil(remainingPages / batchSize);

    console.log(`开始从第${startPage}页爬取，共需爬取${remainingPages}页，分${batchCount}个批次`);

    try {
        for (let batch = 0; batch < batchCount; batch++) {
            const currentStartPage = startPage + batch * batchSize;
            const endPage = Math.min(currentStartPage + batchSize - 1, maxPages);
            console.log(`开始爬取第${currentStartPage}页到第${endPage}页数据...`);

            var reqs = [];
            for (let i = currentStartPage; i <= endPage; i++) {
                console.log(`正在获取第${i}页数据...`);
                // 如果不是第一页，添加随机等待，爬虫友好操作
                if (i > startPage) {
                    const delay = Math.floor(Math.random() * 3000) + 3000; // 3000-6000毫秒的随机延迟
                    await new Promise(resolve => setTimeout(resolve, delay));
                }

                // 构建请求参数
                var requestParams = {
                    ...data,
                    pageNum: i,
                    pageSize: 100
                };

                // 添加重试逻辑
                let retryCount = 0;
                const makeRequest = async () => {
                    try {
                        // 使用从webpack模块中提取的request函数
                        const response = await request({
                            url: apiUrl,
                            method: 'POST',
                            data: requestParams,
                            headers: {
                                'Accept': 'application/json',
                                'Content-Type': 'application/json',
                                'User-Agent': getRandomUserAgent(),
                                'Host': 'wb.njybjyybz.org.cn:9091',
                                'Connection': 'keep-alive',
                                'Cookie': 'SESSION_FLAG=1'
                            },
                            timeout: requestTimeout
                        });

                        // 使用从webpack模块中提取的processResponse函数处理响应
                        // 如果没有特殊处理，可以直接返回response
                        return processResponse ? processResponse(response) : response;
                    } catch (error) {
                        if (retryCount < maxRetries) {
                            retryCount++;
                            console.log(`第${i}页请求失败，正在进行第${retryCount}次重试...`);
                            const retryDelay = Math.min(initialRetryDelay * Math.pow(2, retryCount - 1), maxRetryDelay);
                            console.log(`等待${retryDelay}毫秒后进行重试...`);
                            await new Promise(resolve => setTimeout(resolve, retryDelay)); // 指数退避策略
                            return makeRequest();
                        }
                        throw error;
                    }
                };

                reqs.push(makeRequest());
            }

            // 处理当前批次的请求
            const responses = await Promise.all(reqs);

            // 批次间增加较长的等待时间，避免触发服务器限制
            if (batch < batchCount - 1) {
                const batchDelay = Math.floor(Math.random() * 5000) + 5000; // 5000-10000毫秒
                console.log(`批次${batch + 1}完成，等待${batchDelay}毫秒后继续下一批次...`);
                await new Promise(resolve => setTimeout(resolve, batchDelay));
            }

            // 解析数据并保存
            const batchData = [];
            responses.forEach((result, index) => {
                if (result && result.data && result.data.list && Array.isArray(result.data.list)) {
                    const pageData = result.data.list;
                    batchData.push(...pageData);
                    allData.push(...pageData);
                    totalRecords += pageData.length;
                }
            });

            // 保存当前批次数据到文件
            const batchFilePath = path.join(downloadDir, `${filename}_${currentStartPage}_${endPage}.json`);
            fs.writeFileSync(batchFilePath, JSON.stringify(batchData, null, 2));
            savedFiles.push(batchFilePath);
            console.log(`第${currentStartPage}页到第${endPage}页数据已保存到${batchFilePath}`);
        }

        // 保存所有数据到一个总文件
        const filePath = path.join(downloadDir, `${filename}.json`);
        fs.writeFileSync(filePath, JSON.stringify(allData, null, 2));
        const expectedTotal = maxPages * 100; // 预期总数据量
        console.log(`所有数据获取完成：
        实际获取：${totalRecords}条记录
        预期总量：${expectedTotal}条记录
        完整率：${((totalRecords/expectedTotal)*100).toFixed(2)}%
        数据已保存到${filePath}`);

        // 返回保存的文件列表，以便Python脚本处理
        return { allDataFile: `${filename}.json`, batchFiles: savedFiles };
    } catch (err) {
        console.error('获取数据时发生错误:', err);
        throw err;
    }
}

// 示例用法：
fetchMedicalData(maxPages = 3, apiUrl = 'https://wb.njybjyybz.org.cn:9091/hsa-pss-pw-nj/web/pw/wsgs/ypmlcx', data = {
    regName: "阿莫西林"
})
  .catch(console.error);

// module.exports = { fetchMedicalData };