import logging
import urllib3
import requests
import asyncio
import pandas as pd
import os
import math
from tqdm import tqdm
import random
import glob

# 禁用SSL证书验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

async def search_med_supplies_by_post(use_name, page=1, page_size=100):
    """通过POST请求查询耗材信息"""
    url = "https://wb.njybjyybz.org.cn:9091/hsa-pss-pw-nj/web/pw/wsgs/clcx"
    
    headers = {
        "Host": "wb.njybjyybz.org.cn:9091",
        "Connection": "keep-alive",
        "sec-ch-ua-platform": "Windows",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "application/json",
        "channel": "web",
        "Content-Type": "application/json",
        "Cookie": "SESSION_FLAG=1"
    }
    
    payload = {
        "useName": use_name,
        "pageNum": page,
        "pageSize": page_size
    }
    
    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            verify=False  # 忽略SSL证书验证
        )
        response.raise_for_status()
        
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0 and data.get('message') == '成功':
                drug_list = data.get('data', {}).get('list', [])
                total = data.get('data', {}).get('total', 0)
                logging.info(f"成功获取第{page}页耗材数据，共{total}条记录")
                return drug_list, total
            logging.warning(f"API返回异常: {data}")
        return None, 0
        
    except Exception as e:
        logging.error(f"POST请求失败: {str(e)}")
        return None, 0

async def fetch_all_med_supplies_data(supplies_name, page_size=100, max_retries=3):
    """获取所有页面的耗材数据"""
    # 获取第一页数据以获取总记录数
    first_page_data, total = await search_med_supplies_by_post(supplies_name, page=1, page_size=page_size)
    if first_page_data is None:
        return None

    # 计算总页数
    total_pages = math.ceil(total / page_size)
    all_data = first_page_data

    if total_pages > 1:
        # 创建剩余页面的任务
        tasks = []
        for page in range(2, total_pages + 1):
            await asyncio.sleep(random.uniform(1, 3))
            logging.info(f"等待 {random.uniform(1, 3):.2f} 秒后继续请求第 {page} 页数据")
            tasks.append(search_med_supplies_by_post(supplies_name, page=page, page_size=page_size))

        # 使用tqdm显示进度
        with tqdm(total=len(tasks), desc="获取数据进度") as pbar:
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            for page_data, _ in results:
                if page_data:
                    all_data.extend(page_data)
                pbar.update(1)

    return all_data


def clean_filename(name) :
    """
    清洗文件名，只保留中英文、数字和常用符号，其余替换为下划线。
    """
    import re
    # 只允许中英文、数字、下划线、横线、点
    return re.sub(r"[^\u4e00-\u9fa5a-zA-Z0-9_\-.]", '_', name)

def process_excel_files(excel_files,excel_name):
    """
    处理所有Excel文件，合并数据并保存到一个新的Excel文件中。
    :param excel_files: 要处理的Excel文件列表
    :param excel_name: Excel 文件名
    """
    df_total = pd.DataFrame()
    save_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'download')

    # 遍历每个 Excel 文件
    for file_path in excel_files:
        try:
            # 读取 Excel 文件，指定编码格式
            # 读取Excel文件时不需要指定encoding参数
            df = pd.read_excel(file_path,dtype='str')
            # 合并数据
            df_total = pd.concat([df_total, df], ignore_index=True)
        except Exception as e:
            try:
                print(repr(f'处理文件 {file_path} 时出错: {str(e)}'))
            except UnicodeEncodeError:
                print(repr(f'处理文件 {file_path} 时出错: {str(e)}'))
    # 去重
    df_total = df_total.drop_duplicates()
    
    # 保存合并后的文件，使用openpyxl引擎
    # output_file = 'merged_med_service_excel.xlsx'
    output_file = os.path.join(save_dir, excel_name)
    df_total.to_excel(output_file, index=False, engine='openpyxl')
    print(repr(f'\n所有文件处理完成！合并结果已保存至: {output_file}'))
    print(repr(f'总记录数: {len(df_total)}'))


if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    # 保存到Excel文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    download_dir = os.path.join(script_dir, 'med_supplies_download')
    # 获取所有页面的数据
    df_drug =  pd.read_excel(os.path.join(os.path.dirname(__file__), 'files', 'processed_supplies_name.xlsx'))
    for supplies_name in df_drug['supplies_name']:
        print(supplies_name)
        results = asyncio.run(fetch_all_med_supplies_data(supplies_name))
        
        if results:
            df = pd.DataFrame(results)
            # time_columns = df.columns[df.columns.str.contains('Time|time|date', case=True)]
            # if not time_columns.empty:
            #     # 修改处理逻辑，使用pd.to_numeric安全转换数值
            #     df[time_columns] = df[time_columns].apply(lambda x: pd.to_numeric(x, errors='coerce')).fillna('')
            #     # 对非空值进行整数转换
            #     df[time_columns] = df[time_columns].apply(lambda x: x.apply(lambda y: str(int(y)) if pd.notnull(y) and y != '' else ''))
            other_columns = df.columns[df.columns.str.contains('Cod|Ddln|form|code', case=True)]
            if not other_columns.empty:
                df[other_columns] = df[other_columns].apply(lambda x: x.apply(lambda y: str(y) if pd.notnull(y) and y != '' else ''))
            print(df)
            print(f"共获取到 {len(results)} 条记录")
            
            os.makedirs(download_dir, exist_ok=True)  # 创建download文件夹（如果不存在）
            supplies_name = clean_filename(supplies_name)
            excel_file = os.path.join(download_dir, f'{supplies_name}.xlsx')
            
            df.to_excel(excel_file, index=False)
            print(f"数据已保存到: {excel_file}")
        else:
            print("获取数据失败")
    # 将数据合并成一个excel
    # 获取目录下所有 Excel 文件（包括 .xlsx 和 .xls）
    excel_files = glob.glob(os.path.join(download_dir, '*.xlsx')) + glob.glob(os.path.join(download_dir, '*.xls'))
    process_excel_files(excel_files,'医用耗材-完整数据.xlsx')