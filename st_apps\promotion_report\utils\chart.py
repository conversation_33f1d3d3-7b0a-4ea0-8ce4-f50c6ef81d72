from pyecharts import options as opts
from pyecharts.charts import Tree, PictorialBar
from streamlit_echarts import st_echarts


def picbar_gender(key_list, value_list):
    """
    生成象形柱状图，显示男女各自的数量
    :param key_list: 分类名称列表
    :param value_list: 分类数量列表,顺序需要与key统一
    :return: 象形柱状图
    """
    import json
    with open("pic_templates/gender.json", "r", encoding="utf-8") as f:
        symbols = json.load(f)
    # 象形柱状图
    pictorialBar = (
        PictorialBar()
        # x轴系列数据
        .add_xaxis(key_list)
        # y轴系列数据
        .add_yaxis(
            # 系列名称
            "",
            # 系列数据
            [
                {"value": int(value_list[0]),
                 "symbol": symbols["male"]},
                {"value": int(value_list[1]),
                 "symbol": symbols["female"]},
            ],
            # 标签配置项，参考 `series_options.LabelOpts`
            label_opts=opts.LabelOpts(is_show=True, position="outside", distance=-25, font_size=15, formatter="{c}人"),
            ## 图形的大小
            symbol_size=30,
            # 指定图形元素是否重复
            symbol_repeat="fixed",
            # 图形相对于原本位置的偏移
            symbol_offset=[0, 0],
            # 是否剪裁图形
            is_symbol_clip=True,
        )

        # 反转轴
        .reversal_axis()
        # 全局配置项
        .set_global_opts(
            # x轴设置
            xaxis_opts=opts.AxisOpts(is_show=False),
            # y轴设置
            yaxis_opts=opts.AxisOpts(
                axistick_opts=opts.AxisTickOpts(is_show=False),
                axisline_opts=opts.AxisLineOpts(
                    linestyle_opts=opts.LineStyleOpts(opacity=0)
                ),
            ),
        )
    )
    return pictorialBar


def tree_chart(name_list):
    """
    绘制单层树状图，一个主节点，包含两个子节点
    :param name_list: 节点名称列表,第一个值为主节点名称，第二个值为第一个子节点名称，第三个值为第二个子节点名称
    :return:
    """
    data = {
        "name": name_list[0],
        "label": opts.LabelOpts(color="#0068C9"),
        "children": [
            {
                "name": name_list[1]
            },
            {
                "name": name_list[2]
            },
        ],
    }

    tree = Tree()
    tree.add("", [data], orient="TB")
    return tree


def display_donut_chart(data,name='类型',is_click_back = False, inner_radius='50%', outer_radius='75%', center=['50%', '50%']):
    """
    生成并显示环形图,会对点击的数据回传。

    :param data: 数据列表，每个元素是一个元组 (分类名称, 数值)
    :param name:图例显示名称
    :param is_click_back: 是否开启点击事件，开启后会返回点击的数据
    :param inner_radius: 环形图的内半径，可以是字符串百分比或数值
    :param outer_radius: 环形图的外半径，可以是字符串百分比或数值
    :param center: 环形图的中心位置，如 ['50%', '50%']
    """
    # 构建饼图数据
    pie_data = [{"name": name, "value": value} for name, value in data]

    # 配置选项
    option = {
        "tooltip": {"trigger": "item"},
        "series": [
            {
                "name": name,
                "type": "pie",
                "radius": [inner_radius, outer_radius],
                "center": center,
                "data": pie_data,
                "label": {
                    "show": True,
                    "formatter": "{b}\n{c}",  # 显示标签和值，{b}是标签，{c}是值
                },
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowOffsetX": 0,
                        "shadowColor": "rgba(0, 0, 0, 0.5)",
                    }
                },
            }
        ],
    }
    if is_click_back:
        # 事件配置，这里仅展示点击事件的处理逻辑，你可以根据需要调整
        events = {
            "click": "function(params) { console.log(params.name); return params.name }",
        }

        # 使用st_echarts展示环形图
        value = st_echarts(option, events=events)
        return value
    else:
        st_echarts(option)
