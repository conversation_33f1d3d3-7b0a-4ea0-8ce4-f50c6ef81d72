# Generated by Django 3.2.12 on 2024-10-23 14:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0007_auto_20240724_1718'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherManagementStaff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('short_name', models.CharField(blank=True, max_length=32, null=True, verbose_name='保司简称')),
                ('agent_id', models.IntegerField(blank=True, null=True, verbose_name='人员id')),
                ('name', models.CharField(blank=True, max_length=32, null=True, verbose_name='人员姓名')),
                ('type', models.CharField(blank=True, max_length=32, null=True, verbose_name='人员类型')),
                ('additional_info', models.CharField(blank=True, max_length=128, null=True, verbose_name='补充信息')),
            ],
            options={
                'verbose_name': '保司管理层人员信息',
                'verbose_name_plural': '保司管理层人员信息',
                'db_table': 'other_management_staff',
            },
        ),
    ]
