#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医保药品实体表字段更新程序
使用以下表的数据更新medical_drug_entity表的type、type_name、category_name、number_national字段：
1. medical_drug_base表 - 更新基础药品的type、type_name、category_name、number_national字段
2. medical_chinese_herbal_drug_base表 - 更新中草药的type='traditional_chinese_medicine'、type_name='中草药'
3. medical_self_prepared_drug_base表 - 更新自制药的type='selfprep_medicine'、type_name='自制药'

适用于Celery调度的独立Python程序
"""

import os
import sys
import logging
import django
from datetime import datetime

# 添加项目路径到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..', '..')
sys.path.insert(0, project_root)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dw.settings')
django.setup()

from django.db import connection, transaction
from django.utils import timezone

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('drug_entity_update.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DrugEntityFieldUpdater:
    """医保药品实体表字段更新器"""

    def __init__(self):
        # 基础药品表更新SQL
        self.update_sql = """
        UPDATE medical_drug_entity e
        JOIN medical_drug_base b ON b.code = e.code
        SET
            e.type = b.type,
            e.type_name = b.type_name,
            e.category_name = b.category_name,
            e.number_national = b.number_national,
            e.update_time = %s
        WHERE b.code IS NOT NULL
        """

        # 中草药更新SQL
        self.chinese_herbal_update_sql = """
        UPDATE medical_drug_entity e
        JOIN medical_chinese_herbal_drug_base b ON b.code = e.code
        SET
            e.type = 'traditional_chinese_medicine',
            e.type_name = '中草药',
            e.update_time = %s
        WHERE e.type IS NULL AND b.code IS NOT NULL
        """

        # 自制药更新SQL
        self.self_prepared_update_sql = """
        UPDATE medical_drug_entity e
        JOIN medical_self_prepared_drug_base b ON b.code = e.code
        SET
            e.type = 'selfprep_medicine',
            e.type_name = '自制药',
            e.update_time = %s
        WHERE e.type IS NULL AND b.code IS NOT NULL
        """

        self.count_sql = """
        SELECT COUNT(*)
        FROM medical_drug_entity e
        JOIN medical_drug_base b ON b.code = e.code
        WHERE (e.type != b.type OR e.type IS NULL AND b.type IS NOT NULL)
          OR (e.type_name != b.type_name OR e.type_name IS NULL AND b.type_name IS NOT NULL)
          OR (e.category_name != b.category_name OR e.category_name IS NULL AND b.category_name IS NOT NULL)
          OR (e.number_national != b.number_national OR e.number_national IS NULL AND b.number_national IS NOT NULL)
        """

        # 中草药需要更新的记录数量统计SQL
        self.chinese_herbal_count_sql = """
        SELECT COUNT(*)
        FROM medical_drug_entity e
        JOIN medical_chinese_herbal_drug_base b ON b.code = e.code
        WHERE e.type IS NULL or e.type != 'traditional_chinese_medicine'
        """

        # 自制药需要更新的记录数量统计SQL
        self.self_prepared_count_sql = """
        SELECT COUNT(*)
        FROM medical_drug_entity e
        JOIN medical_self_prepared_drug_base b ON b.code = e.code
        WHERE e.type IS NULL or e.type != 'selfprep_medicine'
        """

        self.verify_sql = """
        SELECT COUNT(*)
        FROM medical_drug_entity e
        JOIN medical_drug_base b ON b.code = e.code
        WHERE (e.type = b.type OR (e.type IS NULL AND b.type IS NULL))
          AND (e.type_name = b.type_name OR (e.type_name IS NULL AND b.type_name IS NULL))
          AND (e.category_name = b.category_name OR (e.category_name IS NULL AND b.category_name IS NULL))
          AND (e.number_national = b.number_national OR (e.number_national IS NULL AND b.number_national IS NULL))
        """

    def get_update_count(self):
        """获取需要更新的记录数量"""
        try:
            with connection.cursor() as cursor:
                # 获取基础药品表需要更新的记录数量
                cursor.execute(self.count_sql)
                base_count = cursor.fetchone()[0]

                # 获取中草药需要更新的记录数量
                cursor.execute(self.chinese_herbal_count_sql)
                herbal_count = cursor.fetchone()[0]

                # 获取自制药需要更新的记录数量
                cursor.execute(self.self_prepared_count_sql)
                self_prepared_count = cursor.fetchone()[0]

                return {
                    'base': base_count,
                    'chinese_herbal': herbal_count,
                    'self_prepared': self_prepared_count,
                    'total': base_count + herbal_count + self_prepared_count
                }
        except Exception as e:
            logger.error(f"获取更新记录数量失败: {str(e)}")
            raise

    def get_sample_data(self, limit=5):
        """获取示例数据"""
        sample_sql = """
        SELECT
            e.code,
            e.type as old_type,
            b.type as new_type,
            e.type_name as old_type_name,
            b.type_name as new_type_name,
            e.category_name as old_category,
            b.category_name as new_category,
            e.number_national as old_number_national,
            b.number_national as new_number_national
        FROM medical_drug_entity e
        JOIN medical_drug_base b ON b.code = e.code
        WHERE b.code IS NOT NULL
        LIMIT %s
        """

        try:
            with connection.cursor() as cursor:
                cursor.execute(sample_sql, [limit])
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取示例数据失败: {str(e)}")
            return []

    def get_chinese_herbal_sample_data(self, limit=5):
        """获取中草药示例数据"""
        sample_sql = """
        SELECT
            e.code,
            e.type as old_type,
            'traditional_chinese_medicine' as new_type,
            e.type_name as old_type_name,
            '中草药' as new_type_name
        FROM medical_drug_entity e
        JOIN medical_chinese_herbal_drug_base b ON b.code = e.code
        WHERE e.type IS NULL
        LIMIT %s
        """

        try:
            with connection.cursor() as cursor:
                cursor.execute(sample_sql, [limit])
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取中草药示例数据失败: {str(e)}")
            return []

    def get_self_prepared_sample_data(self, limit=5):
        """获取自制药示例数据"""
        sample_sql = """
        SELECT
            e.code,
            e.type as old_type,
            'selfprep_medicine' as new_type,
            e.type_name as old_type_name,
            '自制药' as new_type_name
        FROM medical_drug_entity e
        JOIN medical_self_prepared_drug_base b ON b.code = e.code
        WHERE e.type IS NULL
        LIMIT %s
        """

        try:
            with connection.cursor() as cursor:
                cursor.execute(sample_sql, [limit])
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取自制药示例数据失败: {str(e)}")
            return []

    def execute_update(self):
        """执行更新操作"""
        try:
            # 获取需要更新的记录数量
            update_count = self.get_update_count()
            logger.info(f"找到需要更新的记录数量:")
            logger.info(f"  基础药品表: {update_count['base']} 条")
            logger.info(f"  中草药表: {update_count['chinese_herbal']} 条")
            logger.info(f"  自制药表: {update_count['self_prepared']} 条")
            logger.info(f"  总计: {update_count['total']} 条")

            if update_count['total'] == 0:
                logger.info("没有找到需要更新的记录")
                return True, 0

            # 显示基础药品示例数据
            if update_count['base'] > 0:
                sample_data = self.get_sample_data()
                if sample_data:
                    logger.info("\n基础药品示例更新数据（前5条）:")
                    logger.info("药品代码 | 旧type -> 新type | 旧type_name -> 新type_name | 旧category -> 新category | 旧number_national -> 新number_national")
                    logger.info("-" * 150)
                    for row in sample_data:
                        code, old_type, new_type, old_type_name, new_type_name, old_category, new_category, old_number_national, new_number_national = row
                        logger.info(
                            f"{code} | {old_type} -> {new_type} | {old_type_name} -> {new_type_name} | "
                            f"{old_category} -> {new_category} | {old_number_national} -> {new_number_national}"
                        )

            # 显示中草药示例数据
            if update_count['chinese_herbal'] > 0:
                herbal_sample_data = self.get_chinese_herbal_sample_data()
                if herbal_sample_data:
                    logger.info("\n中草药示例更新数据（前5条）:")
                    logger.info("药品代码 | 旧type -> 新type | 旧type_name -> 新type_name")
                    logger.info("-" * 80)
                    for row in herbal_sample_data:
                        code, old_type, new_type, old_type_name, new_type_name = row
                        logger.info(f"{code} | {old_type} -> {new_type} | {old_type_name} -> {new_type_name}")

            # 显示自制药示例数据
            if update_count['self_prepared'] > 0:
                self_prepared_sample_data = self.get_self_prepared_sample_data()
                if self_prepared_sample_data:
                    logger.info("\n自制药示例更新数据（前5条）:")
                    logger.info("药品代码 | 旧type -> 新type | 旧type_name -> 新type_name")
                    logger.info("-" * 80)
                    for row in self_prepared_sample_data:
                        code, old_type, new_type, old_type_name, new_type_name = row
                        logger.info(f"{code} | {old_type} -> {new_type} | {old_type_name} -> {new_type_name}")

            # 执行更新操作
            logger.info("\n开始执行更新操作...")
            total_affected_rows = 0

            with transaction.atomic():
                with connection.cursor() as cursor:
                    current_time = timezone.now()

                    # 1. 更新基础药品表数据
                    if update_count['base'] > 0:
                        logger.info("正在更新基础药品表数据...")
                        cursor.execute(self.update_sql, [current_time])
                        base_affected_rows = cursor.rowcount
                        total_affected_rows += base_affected_rows
                        logger.info(f"基础药品表更新完成！更新了 {base_affected_rows} 条记录")

                    # 2. 更新中草药数据
                    if update_count['chinese_herbal'] > 0:
                        logger.info("正在更新中草药数据...")
                        cursor.execute(self.chinese_herbal_update_sql, [current_time])
                        herbal_affected_rows = cursor.rowcount
                        total_affected_rows += herbal_affected_rows
                        logger.info(f"中草药更新完成！更新了 {herbal_affected_rows} 条记录")

                    # 3. 更新自制药数据
                    if update_count['self_prepared'] > 0:
                        logger.info("正在更新自制药数据...")
                        cursor.execute(self.self_prepared_update_sql, [current_time])
                        self_prepared_affected_rows = cursor.rowcount
                        total_affected_rows += self_prepared_affected_rows
                        logger.info(f"自制药更新完成！更新了 {self_prepared_affected_rows} 条记录")

                    logger.info(f"\n所有更新操作完成！总共更新了 {total_affected_rows} 条记录")

                    # 验证更新结果
                    cursor.execute(self.verify_sql)
                    verified_count = cursor.fetchone()[0]

                    logger.info(f"验证完成：{verified_count} 条记录的字段已正确更新")

                    return True, total_affected_rows

        except Exception as e:
            logger.error(f"更新操作失败: {str(e)}")
            return False, 0

    def run(self):
        """运行更新任务"""
        logger.info("=" * 60)
        logger.info("开始执行医保药品实体表字段更新任务")
        logger.info(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)
        
        try:
            success, affected_rows = self.execute_update()
            
            if success:
                logger.info("=" * 60)
                logger.info(f"任务执行成功！共更新 {affected_rows} 条记录")
                logger.info("=" * 60)
                return True
            else:
                logger.error("=" * 60)
                logger.error("任务执行失败！")
                logger.error("=" * 60)
                return False
                
        except Exception as e:
            logger.error(f"任务执行异常: {str(e)}")
            return False


def main():
    """主函数"""
    updater = DrugEntityFieldUpdater()
    success = updater.run()
    
    # 返回退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
