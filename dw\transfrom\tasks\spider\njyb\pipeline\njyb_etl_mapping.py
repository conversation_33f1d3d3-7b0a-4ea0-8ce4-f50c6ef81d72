#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
南京医保局(njyb)ETL映射处理
核心清洗逻辑在data_cleaning.py中
表配置在table_configs.py中
字段配置在field_mapping.py中
"""

# 设置项目路径
import sys
import os

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = current_file
# 从当前文件向上6级到达dw根目录
for _ in range(6):
    project_root = os.path.dirname(project_root)

if project_root not in sys.path:
    sys.path.insert(0, project_root)

import logging
import warnings

import pandas as pd

# 导入njyb配置（自动注册到全局配置管理器）
import transfrom.tasks.spider.njyb.pipeline.config

# 导入表配置
from transfrom.tasks.spider.njyb.pipeline.config.table_configs import (
    NJYB_TABLE_CONFIGS as TABLE_CONFIGS,
    PROCESSING_MODES,
    OPERATION_MODES,
    get_table_config,
    get_all_table_configs
)

# 导入通用ETL模块
from transfrom.utils import (
    # 数据库相关
    get_connection,
    DEFAULT_DB,

    # ETL处理器
    BatchOperationManager,

    # 字段映射相关
    FieldMappingManager,
    DictMappingManager,
    DataTransformer
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 忽略pandas警告
warnings.filterwarnings('ignore', category=pd.errors.SettingWithCopyWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 默认数据库连接
DEFALUT_DB = DEFAULT_DB

# 创建管理器实例
batch_manager = BatchOperationManager()
field_mapping_manager = FieldMappingManager()
dict_mapping_manager = DictMappingManager()
data_transformer = DataTransformer()

# 配置已从table_configs.py导入


def get_custom_dict_mapping(dict_table, dict_code_field, dict_name_field):
    """获取自定义字典表的映射关系 - 向后兼容函数"""
    return dict_mapping_manager.get_custom_dict_mapping(dict_table, dict_code_field, dict_name_field)

def apply_field_mapping(source_df, mapping_df):
    """应用字段映射转换 - 向后兼容函数"""
    return field_mapping_manager.apply_field_mapping(source_df, mapping_df)

def apply_dict_mapping(df, mapping_row, dict_mapping_df=None):
    """应用字典映射转换 - 向后兼容函数"""
    return dict_mapping_manager.apply_dict_mapping(df, mapping_row, dict_mapping_df)

def validate_mapping(source_table, target_table):
    """验证映射配置的完整性和正确性 - 向后兼容函数"""
    return field_mapping_manager.validate_mapping(source_table, target_table)


def execute_etl_transform(source_table, target_table, limit=None,operation_mode='upsert',
                          unique_fields=None, batch_size=10000, excel_save_operations=[], target_where_clause=None, save_format='csv'):
    """
    执行ETL转换的主函数

    Args:
        source_table (str): 源表名
        target_table (str): 目标表名
        limit (int): 限制处理的记录数，None表示处理所有
        operation_mode (str): 操作模式，'upsert' 或 'traditional'
        unique_fields (list): 唯一性字段列表
        batch_size (int): 批处理大小
        excel_save_operations (list): 需要保存Excel的操作类型列表，可选值：['update', 'delete', 'insert']
                                    None表示不保存任何Excel文件，[]表示不保存，['update', 'delete']表示只保存更新和删除
        target_where_clause (str): 目标表数据过滤条件，可选
        save_format (str): 保存格式，可选值：'csv', 'excel', 'both'，默认'csv'

    Returns:
        bool: 执行是否成功
    """
    try:
        logger.info(f"开始ETL转换: {source_table} -> {target_table}")

        # 处理Excel保存操作类型参数
        if excel_save_operations is None:
            excel_save_operations = []  # 默认不保存任何Excel文件

        if not excel_save_operations:
            logger.info("Excel保存功能已禁用，将使用CSV保存更新对比信息")
        else:
            logger.info(f"Excel保存已启用，将保存以下操作类型: {excel_save_operations}")

        # 🚀 使用通用ETL处理器，传递excel_save_operations参数
        # ⚙️ 数据处理的关键逻辑在这里
        # 核心清洗逻辑在data_cleaning.py中

        # 创建专用的ETL处理器实例，控制Excel保存行为和输出目录
        from transfrom.utils.etl_base import ETLProcessor
        import os

        # 设置输出目录为当前文件的目录
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_file_dir, "output")

        etl_processor = ETLProcessor(output_dir=output_dir, excel_save_operations=excel_save_operations, save_format=save_format)

        stats = etl_processor.process_etl(
            source_table=source_table,
            target_table=target_table,
            unique_fields=unique_fields,
            operation_mode=operation_mode,
            batch_size=batch_size,
            limit=limit,
            target_where_clause=target_where_clause,
            province='njyb'  # 指定为南京医保局
        )

        logger.info(f"ETL转换完成: 新增 {stats.get('inserted', 0)} 条, "
                   f"更新 {stats.get('updated', 0)} 条, "
                   f"删除 {stats.get('deleted', 0)} 条, "
                   f"错误 {stats.get('errors', 0)} 条")

        return stats.get('errors', 0) == 0

    except Exception as e:
        logger.error(f"ETL转换失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False


def get_source_data(source_table, limit=None):
    """
    获取源表数据

    Args:
        source_table (str): 源表名
        limit (int): 限制返回的行数

    Returns:
        pd.DataFrame: 源表数据
    """
    sql = f"SELECT * FROM {source_table}"
    if limit:
        sql += f" LIMIT {limit}"

    with get_connection(DEFALUT_DB) as conn:
        df = pd.read_sql(sql, conn)

    return df


def run_njyb_service_facilities_etl(limit=None, operation_mode='upsert', batch_size=10000):
    """
    执行南京医保局医疗服务项目ETL转换

    Args:
        limit (int): 限制处理的记录数，None表示处理所有
        operation_mode (str): 操作模式，'upsert' 或 'traditional'
        batch_size (int): 批处理大小

    Returns:
        bool: 执行是否成功
    """
    config = TABLE_CONFIGS["南京医疗服务项目"]

    return execute_etl_transform(
        source_table=config["source_table"],
        target_table=config["target_table"],
        unique_fields=config["unique_fields"],
        limit=limit,
        operation_mode=operation_mode,
        batch_size=batch_size,
        target_where_clause=config["target_where_clause"]
    )


def interactive_etl():
    """
    交互式ETL执行界面
    """
    print("\n" + "="*80)
    print("[NJYB] 南京医保局 ETL数据转换工具")
    print("="*80)

    # 显示可用的表
    print("\n[TABLE] 可用的数据表:")
    for i, (key, config) in enumerate(TABLE_CONFIGS.items(), 1):
        icon = config.get('icon', '[DATA]')
        print(f"   {i}. {icon} {key}")
        print(f"      源表: {config['source_table']}")
        print(f"      目标表: {config['target_table']}")
        if config.get('target_where_clause'):
            print(f"      过滤条件: {config['target_where_clause']}")
        print()

    # 选择表
    while True:
        try:
            choice = input("请选择要处理的表 (输入数字): ").strip()
            table_index = int(choice) - 1
            table_keys = list(TABLE_CONFIGS.keys())
            if 0 <= table_index < len(table_keys):
                selected_table = table_keys[table_index]
                break
            else:
                print("[ERROR] 无效选择，请重新输入")
        except ValueError:
            print("[ERROR] 请输入有效数字")

    config = TABLE_CONFIGS[selected_table]
    source_table = config["source_table"]
    target_table = config["target_table"]
    unique_fields = config["unique_fields"]
    target_where_clause = config.get("target_where_clause")
    icon = config.get('icon', '[DATA]')

    # 选择处理模式
    print(f"\n[MODE] 处理模式:")
    for key, mode_config in PROCESSING_MODES.items():
        print(f"   {key}. {mode_config['icon']} {mode_config['name']} - {mode_config['description']}")

    while True:
        try:
            mode_choice = input("请选择处理模式 (输入数字): ").strip()
            if mode_choice in PROCESSING_MODES:
                processing_config = PROCESSING_MODES[mode_choice]
                limit = processing_config["limit"]
                break
            else:
                print("[ERROR] 无效选择，请重新输入")
        except ValueError:
            print("[ERROR] 请输入有效数字")

    # 选择操作模式
    print(f"\n[OP] 操作模式:")
    for key, op_config in OPERATION_MODES.items():
        print(f"   {key}. {op_config['icon']} {op_config['name']} - {op_config['description']}")

    while True:
        try:
            op_choice = input("请选择操作模式 (输入数字): ").strip()
            if op_choice in OPERATION_MODES:
                operation_config = OPERATION_MODES[op_choice]
                operation_mode = operation_config["mode"]
                break
            else:
                print("[ERROR] 无效选择，请重新输入")
        except ValueError:
            print("[ERROR] 请输入有效数字")

    # 批处理大小
    batch_size = 10000

    # 显示执行摘要
    print(f"\n[摘要] 执行摘要:")
    print(f"   表名: {icon} {selected_table}")
    print(f"   源表: {source_table}")
    print(f"   目标表: {target_table}")
    if target_where_clause:
        print(f"   目标表过滤: {target_where_clause}")
    print(f"   处理模式: {processing_config['icon']} {processing_config['name']}")
    print(f"   操作模式: {operation_config['icon']} {operation_config['name']}")
    print(f"   批处理大小: {batch_size:,}")
    if limit:
        print(f"   处理数量: {limit:,} 条")
    else:
        print(f"   处理数量: 全量数据")

    # 执行ETL转换
    print(f"\n[START] 开始执行ETL转换...")
    success = execute_etl_transform(
        source_table=source_table,
        target_table=target_table,
        limit=limit,
        operation_mode=operation_mode,
        unique_fields=unique_fields,
        batch_size=batch_size,
        target_where_clause=target_where_clause,
    )

    if success:
        print(f"\n[SUCCESS] ETL转换成功完成!")
    else:
        print(f"\n[FAIL] ETL转换失败，请查看日志了解详细信息")

    return success


if __name__ == "__main__":
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dw.settings')

    # 导入Django设置
    import django
    django.setup()

    # 运行交互式ETL
    interactive_etl()