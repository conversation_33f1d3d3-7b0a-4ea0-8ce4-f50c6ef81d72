import os
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_rid_in_json_files(directory_path, target_rid):
    """
    在指定目录下的所有JSON文件中查找包含特定rid的数据
    
    Args:
        directory_path: JSON文件所在目录的路径
        target_rid: 要查找的rid值
    
    Returns:
        包含目标rid的文件名列表和对应的数据
    """
    found_files = []
    found_data = []
    
    # 确保目录存在
    if not os.path.exists(directory_path):
        logger.error(f"目录不存在: {directory_path}")
        return found_files, found_data
    
    # 获取目录中的所有文件
    files = [f for f in os.listdir(directory_path) if f.endswith('.json')]
    total_files = len(files)
    logger.info(f"开始在{total_files}个JSON文件中搜索rid: {target_rid}")
    
    # 遍历每个JSON文件
    for i, file_name in enumerate(files):
        file_path = os.path.join(directory_path, file_name)
        if (i+1) % 100 == 0:
            logger.info(f"已处理 {i+1}/{total_files} 个文件...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                # 检查是列表还是字典
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and item.get('rid') == target_rid:
                            found_files.append(file_name)
                            found_data.append(item)
                            logger.info(f"在文件 {file_name} 中找到匹配的rid")
                elif isinstance(data, dict):
                    if data.get('rid') == target_rid:
                        found_files.append(file_name)
                        found_data.append(data)
                        logger.info(f"在文件 {file_name} 中找到匹配的rid")
        except Exception as e:
            logger.error(f"处理文件 {file_name} 时出错: {str(e)}")
    
    if not found_files:
        logger.info(f"未找到包含rid {target_rid} 的文件")
    else:
        logger.info(f"共找到 {len(found_files)} 个包含目标rid的文件")
    
    return found_files, found_data

if __name__ == "__main__":
    # 设置要搜索的目录和目标rid
    json_directory = r"D:\work\dw_shize\dw\dw\transfrom\tasks\spider\gjyb\fuwu_nhsa\download\json"
    target_rid = "100000000000202305261619553451880657"
    
    # 执行搜索
    found_files, found_data = find_rid_in_json_files(json_directory, target_rid)
    
    # 输出结果
    if found_files:
        logger.info("找到以下文件包含目标rid:")
        for i, file_name in enumerate(found_files):
            logger.info(f"{i+1}. {file_name}")
            logger.info(f"数据内容: {json.dumps(found_data[i], ensure_ascii=False, indent=2)}")
    else:
        logger.info("未找到包含目标rid的文件")