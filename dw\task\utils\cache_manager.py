from django.core.cache import cache
import logging
import time
from typing import Optional, Any

logger = logging.getLogger(__name__)

class CacheManager():
    def __init__(self):
        self.cached = {}
        self._redis_available = None
        self._last_redis_check = 0
        self._redis_check_interval = 60  # 每60秒检查一次Redis状态

    @property
    def cache_key_prefix(self):
        return f''

    def _check_redis_availability(self) -> bool:
        """检查Redis是否可用"""
        current_time = time.time()

        # 如果距离上次检查时间不足间隔时间，直接返回缓存的状态
        if (self._redis_available is not None and
            current_time - self._last_redis_check < self._redis_check_interval):
            return self._redis_available

        try:
            # 使用一个简单的测试键来检查Redis连接
            test_key = f'redis_health_check_{int(current_time)}'
            cache.set(test_key, 'test_value', 1)
            result = cache.get(test_key)

            if result == 'test_value':
                cache.delete(test_key)  # 清理测试键
                self._redis_available = True
                logger.debug("Redis缓存连接正常")
            else:
                raise Exception("Redis读写测试失败")

        except Exception as e:
            self._redis_available = False
            logger.warning(f"Redis缓存不可用，将使用内存缓存: {e}")

        self._last_redis_check = current_time
        return self._redis_available

    def clear_cache(self):
        """清理缓存"""
        self.cached = {}
        try:
            if self._check_redis_availability():
                cache.delete_pattern(f'{self.cache_key_prefix}*')
                logger.info("Redis缓存清理成功")
        except Exception as e:
            logger.warning(f"Redis缓存清理失败: {e}")

    def get_function_cache_key(self, func_name):
        return f'data_source:{type(self).__name__}:{func_name}'

    def get_from_cache(self, method, timeout=3600 * 6, use_local=True):
        """
        从缓存获取数据，支持Redis容错处理
        优先级：Redis缓存 > 本地内存缓存 > 数据库查询

        Args:
            method: 要调用的方法名
            timeout: 缓存超时时间（秒）
            use_local: 是否使用本地内存缓存

        Returns:
            缓存的数据或通过方法获取的新数据
        """
        key = self.get_function_cache_key(method)

        # 1. 优先尝试从Redis缓存获取数据
        if self._check_redis_availability():
            try:
                redis_data = cache.get(key)
                if redis_data is not None:
                    # Redis缓存命中，同时更新本地缓存
                    if use_local:
                        self.cached[key] = redis_data
                    logger.debug(f'从Redis缓存获取数据: {key}')
                    return redis_data
            except Exception as e:
                logger.warning(f'Redis缓存读取失败，将尝试本地缓存: {key}, 错误: {e}')
                self._redis_available = False  # 标记Redis不可用

        # 2. Redis不可用或未命中，尝试本地内存缓存
        if use_local:
            data = self.cached.get(key, None)
            if data is not None:
                logger.debug(f'从本地缓存获取数据: {key}')
                return data

        # 3. 缓存完全未命中，从数据库获取数据
        logger.info(f'缓存未命中，从数据库获取数据: {key}')
        try:
            data = getattr(self, method)()

            # 4. 优先将数据存储到Redis缓存
            redis_stored = False
            if self._check_redis_availability():
                try:
                    cache.set(key, data, timeout)
                    redis_stored = True
                    logger.debug(f'数据已存储到Redis缓存: {key}')
                except Exception as e:
                    logger.warning(f'Redis缓存写入失败: {key}, 错误: {e}')
                    self._redis_available = False

            # 5. 将数据存储到本地缓存（作为备份或Redis不可用时的主缓存）
            if use_local:
                self.cached[key] = data
                if redis_stored:
                    logger.debug(f'数据已同步到本地缓存: {key}')
                else:
                    logger.info(f'Redis不可用，数据仅保存在本地缓存: {key}')

            return data

        except Exception as e:
            logger.error(f'数据获取失败: {method}, 错误: {e}')
            raise

    def update_cache(self, key, data, timeout=3600 * 6):
        """
        更新缓存数据，支持Redis容错处理
        优先写入Redis，然后同步到本地缓存

        Args:
            key: 缓存键
            data: 要缓存的数据
            timeout: 缓存超时时间（秒）
        """
        cache_key = self.get_function_cache_key(key)
        logger.info(f'更新缓存: {cache_key}')

        redis_updated = False

        # 1. 优先尝试更新Redis缓存
        if self._check_redis_availability():
            try:
                cache.set(cache_key, data, timeout)
                redis_updated = True
                logger.debug(f'Redis缓存更新成功: {cache_key}')
            except Exception as e:
                logger.warning(f'Redis缓存更新失败: {cache_key}, 错误: {e}')
                self._redis_available = False

        # 2. 更新本地内存缓存
        self.cached[cache_key] = data

        if redis_updated:
            logger.debug(f'本地缓存已同步: {cache_key}')
        else:
            logger.info(f'Redis不可用，数据仅保存在本地缓存: {cache_key}')

        return redis_updated  # 返回Redis更新状态，便于调用方监控

    def force_redis_sync(self, timeout=3600 * 6):
        """
        强制将本地缓存同步到Redis
        用于Redis恢复后的数据同步

        Args:
            timeout: 缓存超时时间（秒）

        Returns:
            dict: 同步结果统计
        """
        if not self._check_redis_availability():
            logger.warning("Redis不可用，无法执行强制同步")
            return {'success': False, 'reason': 'Redis不可用'}

        sync_stats = {'total': 0, 'success': 0, 'failed': 0, 'errors': []}

        for cache_key, data in self.cached.items():
            sync_stats['total'] += 1
            try:
                cache.set(cache_key, data, timeout)
                sync_stats['success'] += 1
                logger.debug(f'强制同步到Redis成功: {cache_key}')
            except Exception as e:
                sync_stats['failed'] += 1
                sync_stats['errors'].append(f'{cache_key}: {str(e)}')
                logger.warning(f'强制同步到Redis失败: {cache_key}, 错误: {e}')

        logger.info(f'Redis强制同步完成: 总计{sync_stats["total"]}, 成功{sync_stats["success"]}, 失败{sync_stats["failed"]}')
        return sync_stats

    def get_cache_status(self) -> dict:
        """
        获取缓存状态信息

        Returns:
            包含缓存状态的字典
        """
        redis_available = self._check_redis_availability()

        # 统计本地缓存中各类数据的数量
        local_cache_keys = list(self.cached.keys())

        return {
            'redis_available': redis_available,
            'local_cache_size': len(self.cached),
            'local_cache_keys': local_cache_keys[:10] if len(local_cache_keys) > 10 else local_cache_keys,  # 最多显示10个键
            'last_redis_check': self._last_redis_check,
            'redis_check_interval': self._redis_check_interval,
            'cache_strategy': 'Redis优先' if redis_available else '本地缓存降级'
        }