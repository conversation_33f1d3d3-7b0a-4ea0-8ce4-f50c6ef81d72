# Generated by Django 3.2.12 on 2024-12-25 09:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0011_otherproductproton_past_symptom_pay_person'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherStreamlitKeyMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_serial_code', models.Char<PERSON>ield(blank=True, max_length=128, null=True, verbose_name='产品系列')),
                ('type', models.Char<PERSON>ield(blank=True, max_length=128, null=True, verbose_name='类型')),
                ('client_id', models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=256, null=True, verbose_name='app编码')),
                ('client_secret', models.CharField(blank=True, max_length=256, null=True, verbose_name='app密钥')),
                ('url', models.CharField(blank=True, max_length=256, null=True, verbose_name='url地址')),
            ],
            options={
                'verbose_name': 'ST应用信息映射',
                'verbose_name_plural': 'ST应用信息映射',
                'db_table': 'other_streamlit_key_mapping',
            },
        ),
    ]
