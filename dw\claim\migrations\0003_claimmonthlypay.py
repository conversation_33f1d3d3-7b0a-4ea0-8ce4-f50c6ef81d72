# Generated by Django 3.2.12 on 2025-03-04 10:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0002_auto_20241212_1110'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimMonthlyPay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('pay_month', models.DateField(blank=True, null=True, verbose_name='赔付月份')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
            ],
            options={
                'verbose_name': '理赔-月度赔付情况',
                'verbose_name_plural': '理赔-月度赔付情况',
                'db_table': 'claim_monthly_pay',
            },
        ),
    ]
