from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class OtherSentryReplayFiles(BaseModel):
    file_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='压缩后的文件名')
    order_list = models.TextField(blank=True, null=True, verbose_name='对应的订单id')  # 添加 LONGTEXT 字段
    product_set_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='产品集')
    type  = models.CharField(max_length=128, blank=True, null=True, verbose_name='压缩类型')
    description  = models.CharField(max_length=256, blank=True, null=True, verbose_name='描述信息')

    class Meta:
        db_table = 'other_sentry_replay_files'
        verbose_name = '健康险销售sentry回放文件'
        verbose_name_plural = verbose_name
