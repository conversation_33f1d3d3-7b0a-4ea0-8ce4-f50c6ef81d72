"""
安全的迁移命令，可以跳过已经处理过的表
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from common.db_migration import DatabaseMigrationManager
from common.db_utils import DatabaseCompatibilityUtils


class Command(BaseCommand):
    help = '安全的时间戳默认值迁移，跳过已处理的表'

    def add_arguments(self, parser):
        parser.add_argument(
            '--app-names',
            type=str,
            nargs='*',
            help='要处理的app名称列表，如：insure claim other'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要执行的SQL，不实际执行'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制处理所有表，即使已有默认值'
        )

    def handle(self, *args, **options):
        app_names = options.get('app_names', ['insure', 'claim', 'other', 'public', 'promotion'])
        dry_run = options.get('dry_run', False)
        force = options.get('force', False)
        
        self.stdout.write("开始安全迁移...")
        
        try:
            migration_manager = DatabaseMigrationManager()
            db_utils = DatabaseCompatibilityUtils()
            
            # 获取所有符合条件的表
            all_tables = self._get_tables_with_timestamps(connection)
            target_tables = self._filter_tables(all_tables, app_names)
            
            self.stdout.write(f"发现 {len(target_tables)} 个符合条件的表")
            
            # 检查每个表的状态
            need_processing = []
            already_processed = []
            
            for table_name in target_tables:
                create_default, create_on_update = migration_manager.check_column_has_default(table_name, 'create_time')
                update_default, update_on_update = migration_manager.check_column_has_default(table_name, 'update_time')
                
                if force or not (create_default and update_default):
                    need_processing.append(table_name)
                    status = "需要处理"
                    if create_default:
                        status += " (create_time已有默认值)"
                    if update_default:
                        status += " (update_time已有默认值)"
                else:
                    already_processed.append(table_name)
                    status = "已处理"
                
                self.stdout.write(f"  {table_name}: {status}")
            
            self.stdout.write(f"\n需要处理: {len(need_processing)} 个表")
            self.stdout.write(f"已经处理: {len(already_processed)} 个表")
            
            if not need_processing:
                self.stdout.write(self.style.SUCCESS("所有表都已处理完成！"))
                return
            
            if dry_run:
                self.stdout.write(self.style.WARNING("\n=== 预览模式 ==="))
                for table_name in need_processing:
                    sql_statements = db_utils.generate_timestamp_columns_sql(table_name)
                    self.stdout.write(f"\n表 {table_name}:")
                    for sql in sql_statements:
                        self.stdout.write(f"  {sql}")
                return
            
            # 实际执行
            self.stdout.write(f"\n开始处理 {len(need_processing)} 个表...")
            
            successful = []
            failed = []
            
            for i, table_name in enumerate(need_processing, 1):
                try:
                    self.stdout.write(f"[{i}/{len(need_processing)}] 处理表 {table_name}...")
                    
                    success = migration_manager.add_timestamp_defaults(table_name)
                    if success:
                        self.stdout.write(self.style.SUCCESS(f"  [OK] 表 {table_name} 处理成功"))
                        successful.append(table_name)
                    else:
                        self.stdout.write(self.style.ERROR(f"  [ERROR] 表 {table_name} 处理失败"))
                        failed.append(table_name)
                        
                except KeyboardInterrupt:
                    self.stdout.write(self.style.WARNING(f"\n用户中断执行，已处理 {len(successful)} 个表"))
                    break
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"  [ERROR] 表 {table_name} 处理出错: {str(e)}"))
                    failed.append(table_name)
            
            # 输出最终结果
            self.stdout.write(f"\n=== 处理完成 ===")
            self.stdout.write(f"成功: {len(successful)} 个表")
            self.stdout.write(f"失败: {len(failed)} 个表")
            self.stdout.write(f"跳过: {len(already_processed)} 个表")
            
            if successful:
                self.stdout.write(f"\n成功的表: {', '.join(successful)}")
            
            if failed:
                self.stdout.write(self.style.ERROR(f"\n失败的表: {', '.join(failed)}"))
                
        except Exception as e:
            raise CommandError(f"执行失败: {str(e)}")

    def _get_tables_with_timestamps(self, connection):
        """获取所有包含时间戳字段的表"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT table_name 
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND column_name IN ('create_time', 'update_time')
                GROUP BY table_name 
                HAVING COUNT(DISTINCT column_name) = 2
            """)
            return [row[0] for row in cursor.fetchall()]

    def _filter_tables(self, all_tables, app_names):
        """根据app名称过滤表"""
        if not app_names:
            return all_tables
        
        filtered_tables = []
        for app_name in app_names:
            filtered_tables.extend([t for t in all_tables if t.startswith(f"{app_name}_")])
        
        return list(set(filtered_tables))
