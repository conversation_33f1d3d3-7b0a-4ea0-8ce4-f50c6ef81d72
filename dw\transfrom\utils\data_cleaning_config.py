#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗配置管理基础框架
提供配置管理的基础能力，不包含具体的业务配置
"""

import logging
import re
from typing import Dict, List, Optional, Callable
from abc import ABC, abstractmethod
import pandas as pd

logger = logging.getLogger(__name__)


class BaseDataCleaningConfig(ABC):
    """
    数据清洗配置基础类
    各省份可以继承此类实现自己的清洗配置
    """

    def __init__(self):
        """初始化配置管理器"""
        # 支持的清洗策略
        self._supported_strategies = {
            'address_cleaning',      # 地址清洗
            'type_validation',       # 类型验证清洗
            'phone_cleaning',        # 电话号码清洗
            'email_cleaning',        # 邮箱清洗
            'id_number_cleaning',    # 身份证号清洗
            'custom_cleaning'        # 自定义清洗
        }

    @abstractmethod
    def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
        """
        获取表级别的清洗规则配置
        子类必须实现此方法

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {规则配置}}
        """
        pass

    @abstractmethod
    def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
        """
        获取字段级别的清洗规则配置
        子类必须实现此方法

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {字段名: {规则配置}}}
        """
        pass

    def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
        """
        获取自定义清洗函数
        子类可以重写此方法提供自定义清洗函数

        Returns:
            Dict[str, Callable]: 自定义清洗函数字典，格式为 {函数名: 函数对象}
        """
        return {}

    def get_supported_strategies(self) -> set:
        """
        获取支持的清洗策略列表

        Returns:
            set: 支持的策略集合
        """
        return self._supported_strategies.copy()


class DataCleaningConfigManager:
    """
    数据清洗配置管理器
    负责管理和协调不同省份的清洗配置
    """

    def __init__(self):
        """初始化配置管理器"""
        self._configs = {}  # 存储各省份的配置实例

    def register_config(self, province: str, config: BaseDataCleaningConfig):
        """
        注册省份清洗配置

        Args:
            province (str): 省份标识
            config (BaseDataCleaningConfig): 配置实例
        """
        self._configs[province] = config
        logger.info(f"已注册省份 '{province}' 的数据清洗配置")

    def get_config(self, province: str) -> Optional[BaseDataCleaningConfig]:
        """
        获取指定省份的清洗配置

        Args:
            province (str): 省份标识

        Returns:
            Optional[BaseDataCleaningConfig]: 配置实例，如果不存在则返回None
        """
        return self._configs.get(province)

    def get_table_cleaning_rules(self, province: str, table_name: str) -> Dict[str, any]:
        """
        获取指定省份和表的清洗规则

        Args:
            province (str): 省份标识
            table_name (str): 表名

        Returns:
            Dict[str, any]: 清洗规则配置
        """
        config = self.get_config(province)
        if config:
            table_rules = config.get_table_cleaning_rules()
            return table_rules.get(table_name, {})
        return {}

    def get_field_cleaning_rules(self, province: str, table_name: str, field_name: str = None) -> Dict[str, any]:
        """
        获取指定省份、表和字段的清洗规则

        Args:
            province (str): 省份标识
            table_name (str): 表名
            field_name (str, optional): 字段名，如果为None则返回该表所有字段的规则

        Returns:
            Dict[str, any]: 清洗规则配置
        """
        config = self.get_config(province)
        if config:
            field_rules = config.get_field_cleaning_rules()
            table_field_rules = field_rules.get(table_name, {})

            if field_name:
                return table_field_rules.get(field_name, {})
            else:
                return table_field_rules
        return {}

    def get_custom_cleaning_functions(self, province: str) -> Dict[str, Callable]:
        """
        获取指定省份的自定义清洗函数

        Args:
            province (str): 省份标识

        Returns:
            Dict[str, Callable]: 自定义清洗函数字典
        """
        config = self.get_config(province)
        if config:
            return config.get_custom_cleaning_functions()
        return {}

    def has_cleaning_rules(self, province: str, table_name: str) -> bool:
        """
        检查指定省份和表是否有清洗规则

        Args:
            province (str): 省份标识
            table_name (str): 表名

        Returns:
            bool: 是否有清洗规则
        """
        table_rules = self.get_table_cleaning_rules(province, table_name)
        field_rules = self.get_field_cleaning_rules(province, table_name)
        return bool(table_rules or field_rules)


# 创建全局配置管理器实例
data_cleaning_config_manager = DataCleaningConfigManager()


# 提供便捷的函数接口
def get_table_cleaning_rules(province: str, table_name: str) -> Dict[str, any]:
    """获取表级别清洗规则的便捷函数"""
    return data_cleaning_config_manager.get_table_cleaning_rules(province, table_name)


def get_field_cleaning_rules(province: str, table_name: str, field_name: str = None) -> Dict[str, any]:
    """获取字段级别清洗规则的便捷函数"""
    return data_cleaning_config_manager.get_field_cleaning_rules(province, table_name, field_name)


def get_custom_cleaning_functions(province: str) -> Dict[str, Callable]:
    """获取自定义清洗函数的便捷函数"""
    return data_cleaning_config_manager.get_custom_cleaning_functions(province)


def has_cleaning_rules(province: str, table_name: str) -> bool:
    """检查是否有清洗规则的便捷函数"""
    return data_cleaning_config_manager.has_cleaning_rules(province, table_name)


def register_province_cleaning_config(province: str, config: BaseDataCleaningConfig) -> None:
    """注册省份清洗配置的便捷函数"""
    data_cleaning_config_manager.register_config(province, config)


# 通用清洗函数（可被各省份配置引用）
class CommonCleaningFunctions:
    """
    通用清洗函数集合
    提供各种常用的数据清洗功能
    """

    @staticmethod
    def clean_address_basic(address):
        """
        基础地址清洗函数 - 提取汉字、数字和常用标点

        Args:
            address: 原始地址字符串

        Returns:
            清洗后的地址字符串
        """
        if not isinstance(address, str):
            return ""

        # 修改正则表达式，保留字母与数字的组合（如 A3）
        pattern = re.compile(r'[\u4e00-\u9fa5\d\-\#\(\)（）层室号楼栋幢座]+|[A-Za-z]+\d+')
        result = pattern.findall(address)
        return "".join(result)

    @staticmethod
    def clean_address_enhanced(address):
        """
        增强版地址清洗函数 - 核心功能版本

        主要功能：
        1. 地址标识符清理
        2. 空格和标点规范化
        3. 智能特殊字符清理
        4. 智能括号处理

        Args:
            address: 原始地址字符串

        Returns:
            清洗后的地址字符串
        """
        if not isinstance(address, str):
            return ""

        # 1. 去除首尾空格
        address = address.strip()

        # 2. 地址标识符清理
        # 清理地址末尾的标识符
        address_suffixes = [
            r'地址[:：]?$',
            r'详细地址[:：]?$',
            r'联系地址[:：]?$',
            r'办公地址[:：]?$',
            r'注册地址[:：]?$',
            r'通讯地址[:：]?$'
        ]
        for suffix_pattern in address_suffixes:
            address = re.sub(suffix_pattern, '', address, flags=re.IGNORECASE)

        # 清理地址开头的标识符（包括制表符和空格）
        address_prefixes = [
            r'^地址[:：]?\s*',
            r'^详细地址[:：]?\s*',
            r'^联系地址[:：]?\s*',
            r'^办公地址[:：]?\s*',
            r'^注册地址[:：]?\s*',
            r'^通讯地址[:：]?\s*',
            r'^址[:：]?\s*'  # 新增：处理"址:"的情况
        ]
        for prefix_pattern in address_prefixes:
            address = re.sub(prefix_pattern, '', address, flags=re.IGNORECASE)

        # 清理文字中间的地址标识符（新增）
        # 使用更精确的模式，完全清除地址标识符
        # 注意：这里要按照从长到短的顺序匹配，避免部分匹配
        address_middle_patterns = [
            # 完整的地址标识符（优先匹配）
            r'详细地址[:：]\s*',
            r'联系地址[:：]\s*',
            r'办公地址[:：]\s*',
            r'注册地址[:：]\s*',
            r'通讯地址[:：]\s*',
            # 带空格的地址标识符
            r'\s+详细地址[:：]\s*',
            r'\s+联系地址[:：]\s*',
            r'\s+办公地址[:：]\s*',
            r'\s+注册地址[:：]\s*',
            r'\s+通讯地址[:：]\s*',
            # 简单的地址标识符
            r'\s+地址[:：]\s*',
            r'地址[:：]\s*'
        ]

        # 统一处理：将匹配的内容替换为空字符串
        for middle_pattern in address_middle_patterns:
            address = re.sub(middle_pattern, '', address, flags=re.IGNORECASE)

        # 清理网址（新增）
        # 匹配 http://、https://、www. 开头的网址，更精确的匹配
        url_patterns = [
            r'https?://[^\s\u4e00-\u9fa5]+',  # http:// 或 https:// 开头到中文字符前
            r'www\.[^\s\u4e00-\u9fa5]+',      # www. 开头到中文字符前
        ]
        for url_pattern in url_patterns:
            address = re.sub(url_pattern, '', address, flags=re.IGNORECASE)

        # 清理邮箱地址（新增）
        # 使用更精确的邮箱匹配，避免误删地址中的@符号
        email_patterns = [
            # 标准邮箱格式：用户名@域名.后缀（必须包含点和域名后缀）
            r'[a-zA-Z0-9._%+-]{2,}@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            # 常见邮箱服务商格式（更严格的匹配）
            r'[a-zA-Z0-9._%+-]+@(gmail|yahoo|hotmail|outlook|163|126|qq|sina|sohu|foxmail)\.(com|cn|net|org)',
        ]
        for email_pattern in email_patterns:
            address = re.sub(email_pattern, '', address, flags=re.IGNORECASE)

        # 3. 智能空格和标点规范化
        # 采用更保守的空格处理策略，只处理明确需要规范化的情况

        # 特殊字符映射和规范化
        # 将特殊的零字符转换为数字0
        address = address.replace('〇', '0')  # 中文零字符转换为数字0

        # 处理常见的不规范表述（只处理明确的模式）
        address = re.sub(r'底\s+商', '底商', address)  # "底 商" -> "底商"（多个空格）
        address = re.sub(r'(\d+)、(\d+)\s+号', r'\1、\2号', address)  # "1、2 号楼" -> "1、2号楼"

        # 只规范化明确的建筑相关空格表述（保持保守）
        space_patterns = [
            (r'(\d+)\s+层\s+(\d+)\s+室', r'\1层\2室'),      # "3 层 201 室" -> "3层201室"（多个空格）
            (r'(\d+)\s+号\s+楼', r'\1号楼'),               # "1 号 楼" -> "1号楼"（多个空格）
            (r'(\d+)\s+栋\s+(\d+)\s+单元', r'\1栋\2单元'),   # "2 栋 3 单元" -> "2栋3单元"（多个空格）
            (r'(\d+)\s+幢\s+(\d+)\s+号', r'\1幢\2号'),      # "5 幢 101 号" -> "5幢101号"（多个空格）
        ]

        for pattern, replacement in space_patterns:
            address = re.sub(pattern, replacement, address)

        # 智能空格处理：保留重要的分隔空格
        # 保护门牌号之间的空格
        while re.search(r'(\d+-\d+)\s+(\d+-\d+)', address):
            address = re.sub(r'(\d+-\d+)\s+(\d+-\d+)', r'\1【SPACE】\2', address)

        # 保护其他重要的分隔空格（字母数字组合与楼层标识之间）
        # 如 "B12 1层", "D6 B1层" 中的空格
        address = re.sub(r'([A-Za-z]\d+)\s+(\d*[层楼号])', r'\1【SPACE】\2', address)
        address = re.sub(r'([A-Za-z]\d+)\s+([A-Za-z]\d*[层楼号])', r'\1【SPACE】\2', address)  # "D6 B1层"

        # 保护建筑标识与其他部分之间的空格
        # 如 "商铺西侧" 前的空格
        address = re.sub(r'(\d+号[^【]*?)\s+([东西南北][侧边])', r'\1【SPACE】\2', address)

        # 智能空格规范化：
        # 1. 将多个空格替换为单个空格（中间的多空格 → 单空格）
        address = re.sub(r'\s{2,}', ' ', address)  # 将2个以上的连续空格替换为单个空格
        # 2. 去除开头和结尾的所有空格（包括单个空格）
        address = address.strip()  # 去除开头和结尾的空格

        # 恢复被保护的重要空格
        address = address.replace('【SPACE】', ' ')

        # 4. 智能特殊字符清理
        # 保护重要的分隔符和特殊字符
        address = address.replace(';', '【SEMICOLON】')
        address = address.replace(',', '【COMMA】')
        address = address.replace('，', '【CHINESE_COMMA】')
        address = address.replace('/', '【SLASH】')  # 保护斜杠
        address = address.replace('*', '【ASTERISK】')  # 保护星号
        address = address.replace('°', '【DEGREE】')  # 保护度符号
        address = address.replace('′', '【MINUTE】')  # 保护分符号
        address = address.replace('″', '【SECOND】')  # 保护秒符号
        address = address.replace('—', '【DASH】')  # 保护中文连字符
        address = address.replace('——', '【DOUBLE_DASH】')  # 保护双连字符
        address = address.replace('~', '【TILDE】')  # 保护波浪号
        address = address.replace('_', '【UNDERSCORE】')  # 保护下划线
        address = address.replace('－', '【FULLWIDTH_DASH】')  # 保护全角连字符

        # 提取有效字符（汉字、数字、常用标点和字母组合）
        # 扩展字符集，包含更多有意义的字符
        # 添加间隔号·、全角字母、地理坐标符号、各种分隔符、引号、@符号支持
        pattern = re.compile(r'[\u4e00-\u9fa5\d\-\#\(\)（）、。，；\.·°′″—_～\'\"@层室号楼栋幢座单元弄巷街道路区县市省村镇乡里园区科技工业开发部分【】A-Za-z\uFF21-\uFF3A\uFF41-\uFF5A\s]+')
        result = pattern.findall(address)
        cleaned_address = "".join(result)

        # 恢复重要的分隔符和特殊字符
        cleaned_address = cleaned_address.replace('【SEMICOLON】', ';')
        cleaned_address = cleaned_address.replace('【COMMA】', ',')
        cleaned_address = cleaned_address.replace('【CHINESE_COMMA】', '，')
        cleaned_address = cleaned_address.replace('【SLASH】', '/')  # 恢复斜杠
        cleaned_address = cleaned_address.replace('【ASTERISK】', '*')  # 恢复星号
        cleaned_address = cleaned_address.replace('【DEGREE】', '°')  # 恢复度符号
        cleaned_address = cleaned_address.replace('【MINUTE】', '′')  # 恢复分符号
        cleaned_address = cleaned_address.replace('【SECOND】', '″')  # 恢复秒符号
        cleaned_address = cleaned_address.replace('【DASH】', '—')  # 恢复中文连字符
        cleaned_address = cleaned_address.replace('【DOUBLE_DASH】', '——')  # 恢复双连字符
        cleaned_address = cleaned_address.replace('【TILDE】', '~')  # 恢复波浪号
        cleaned_address = cleaned_address.replace('【UNDERSCORE】', '_')  # 恢复下划线
        cleaned_address = cleaned_address.replace('【FULLWIDTH_DASH】', '－')  # 恢复全角连字符

        # 5. 智能括号处理
        # 清除空括号
        cleaned_address = re.sub(r'\(\s*\)', '', cleaned_address)  # 清除空的英文括号
        cleaned_address = re.sub(r'（\s*）', '', cleaned_address)  # 清除空的中文括号

        # 清除只含标点的括号
        cleaned_address = re.sub(r'\([#\-]{1,2}\)', '', cleaned_address)  # 清除 (#) (-) 等
        cleaned_address = re.sub(r'（[#\-]{1,2}）', '', cleaned_address)  # 清除 （#） （-） 等

        # 6. 最终清理
        cleaned_address = cleaned_address.strip('-、。，')

        return cleaned_address

    @staticmethod
    def clean_phone_number(phone):
        """
        清洗电话号码（基础版本）

        Args:
            phone: 原始电话号码

        Returns:
            清洗后的电话号码
        """
        if not isinstance(phone, str):
            return ""

        # 移除所有非数字字符，保留数字、+、-、()
        cleaned = re.sub(r'[^\d\+\-\(\)]', '', phone)
        return cleaned

    @staticmethod
    def clean_phone_number_advanced(phone):
        """
        高级电话号码清洗函数
        处理各种复杂的电话号码格式，包括：
        - 全角字符转半角
        - 提取多个电话号码
        - 移除非电话内容（姓名、地址等）
        - 标准化格式

        Args:
            phone: 原始电话号码字符串

        Returns:
            清洗后的电话号码，多个号码用分号分隔
        """
        if not isinstance(phone, str) or not phone.strip():
            return ""

        original_phone = phone

        # 0. 预验证：对于纯数字输入，检查长度合理性
        # 先进行全角转半角，然后再验证
        phone_for_validation = CommonCleaningFunctions._convert_fullwidth_to_halfwidth(phone.strip())

        # 检查是否为纯数字输入（只包含数字和常见分隔符）
        # 移除数字和常见分隔符后，如果剩余内容很少，则认为是纯数字输入
        phone_without_digits_and_separators = re.sub(r'[\d\-\s\(\)\+/~.,;、；，。转]', '', phone_for_validation)

        # 如果移除数字和分隔符后剩余字符很少（少于3个），则认为是纯数字输入，需要进行长度验证
        if len(phone_without_digits_and_separators) < 3:
            phone_digits_only = re.sub(r'[^\d]', '', phone_for_validation)
            if phone_digits_only:  # 确保有数字
                length = len(phone_digits_only)

                # 只对明显无效的长度进行过滤，避免误杀包含多个号码的情况
                # 过滤明显无效的长度：太短（<7位）或太长（>25位）
                if length < 7:
                    return ""  # 太短，明显无效
                elif length > 25:
                    return ""  # 太长，明显无效
                elif length in [15, 17]:
                    # 明显无效的长度：15位、17位
                    # 注意：18位可能是7位固定电话+11位手机号，19位可能是8位固定电话+11位手机号，20位可能是共享区号格式，所以不过滤
                    return ""
                elif 13 <= length <= 21 and length != 22:
                    # 其他13-21位之间的数字，可能是多个号码组合，允许通过
                    pass
                elif length == 22:
                    # 特殊处理：检查是否为两个连续的11位手机号或手机号+固定电话组合
                    mobile1 = phone_digits_only[:11]
                    mobile2 = phone_digits_only[11:]

                    # 检查是否为有效的组合
                    mobile1_is_mobile = mobile1.startswith('1') and mobile1[1] in '3456789'
                    mobile2_is_mobile = mobile2.startswith('1') and mobile2[1] in '3456789'

                    if mobile1_is_mobile or mobile2_is_mobile:
                        # 至少有一个是手机号，允许通过
                        pass
                    else:
                        return ""  # 都不是有效的手机号
                # 其他长度（7-12位，23-25位）允许通过，由后续逻辑处理

        # 1. 全角字符转半角
        phone = CommonCleaningFunctions._convert_fullwidth_to_halfwidth(phone)

        # 2. 移除明显的非电话内容
        phone = CommonCleaningFunctions._remove_non_phone_content(phone)

        # 3. 提取所有可能的电话号码
        phone_numbers = CommonCleaningFunctions._extract_phone_numbers(phone)

        # 4. 验证和标准化电话号码
        valid_phones = []
        for num in phone_numbers:
            cleaned = CommonCleaningFunctions._standardize_phone_number(num)
            if cleaned and CommonCleaningFunctions._is_valid_phone_number(cleaned):
                valid_phones.append(cleaned)

        # 5. 去重并返回
        if valid_phones:
            return ';'.join(list(dict.fromkeys(valid_phones)))  # 保持顺序去重
        else:
            return ""

    @staticmethod
    def convert_fullwidth_to_halfwidth(text):
        """
        将全角字符转换为半角字符的公开方法
        
        Args:
            text: 原始文本
        
        Returns:
            转换后的文本
        """
        return CommonCleaningFunctions._convert_fullwidth_to_halfwidth(text)

    @staticmethod
    def _convert_fullwidth_to_halfwidth(text):
        """
        将全角字符转换为半角字符

        使用Unicode编码范围进行转换，更加标准和全面：
        - 全角空格(12288) -> 半角空格(32)
        - 全角字符(65281-65374) -> 半角字符(减去65248)

        Args:
            text: 原始文本

        Returns:
            转换后的文本
        """
        if not isinstance(text, str):
            return text

        result = ""
        for char in text:
            inside_code = ord(char)
            if inside_code == 12288:  # 全角空格
                inside_code = 32
            elif 65281 <= inside_code <= 65374:  # 全角字符范围
                inside_code -= 65248
            result += chr(inside_code)

        return result

    @staticmethod
    def _remove_non_phone_content(text):
        """移除明显的非电话内容"""
        # 移除常见的非电话前缀
        prefixes_to_remove = [
            r'联系电话[：:]\s*',
            r'联系方式[：:]\s*',
            r'电话[：:]\s*',
            r'手机[：:]\s*',
            r'传真电话[：:]\s*',
            r'身份证[：:]\s*\d{15,18}\s*',
            # 注释掉这个模式，因为它会误删电话号码前的字母前缀
            # r'[a-zA-Z]{2,}\d+\s*',  # 车牌号等
            r'^[：:]\s*',  # 开头的冒号
        ]

        for pattern in prefixes_to_remove:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)

        # 移除明显的姓名（2-4个汉字）
        text = re.sub(r'^[\u4e00-\u9fa5]{2,4}\s*', '', text)

        # 移除地址信息（包含"区"、"街道"、"路"、"号"等的长文本）
        if len(text) > 50 and any(keyword in text for keyword in ['区', '街道', '路', '号', '市', '县', '镇']):
            # 尝试从地址中提取电话号码
            phone_pattern = r'1[3-9]\d{9}'
            matches = re.findall(phone_pattern, text)
            if matches:
                return ' '.join(matches)
            else:
                return ""

        return text

    @staticmethod
    def _remove_id_numbers(text):
        """移除身份证号码，避免误识别为电话号码"""
        if not isinstance(text, str):
            return text

        # 先检查是否包含连续的手机号，如果是则不进行身份证过滤
        # 连续手机号模式：两个11位手机号连在一起（如：1394899708315804827269）
        consecutive_mobile_pattern = r'1[3-9]\d{9}1[3-9]\d{9}'
        if re.search(consecutive_mobile_pattern, text):
            # 如果检测到连续手机号，先提取它们，然后处理剩余部分
            def replace_consecutive_mobiles(match):
                full_match = match.group()
                mobile1 = full_match[:11]
                mobile2 = full_match[11:]
                return f'{mobile1} {mobile2}'  # 用空格分隔

            text = re.sub(consecutive_mobile_pattern, replace_consecutive_mobiles, text)

        # 身份证号码模式：15位或18位数字（18位可能以X结尾）
        # 但要避免误删连续手机号，所以增加更严格的条件
        id_patterns = [
            r'(?<!\d)\d{15}(?!\d)',         # 15位身份证（前后都不能是数字）
            r'(?<!\d)\d{17}[\dXx](?!\d)',   # 18位身份证（前后都不能是数字）
        ]

        for pattern in id_patterns:
            # 将身份证号码替换为空格，保持文本结构
            text = re.sub(pattern, ' ', text)

        return text

    @staticmethod
    def _normalize_separators(text):
        """标准化各种分隔符为统一格式"""
        if not isinstance(text, str):
            return text

        # 将全角破折号转换为半角破折号
        text = text.replace('—', '-')
        text = text.replace('－', '-')

        # 将波浪号转换为破折号
        text = text.replace('~', '-')

        # 处理中文分隔符
        text = text.replace('；', ';')  # 全角分号转半角分号
        text = text.replace('，', ',')  # 全角逗号转半角逗号
        text = text.replace('、', ',')  # 顿号转逗号
        text = text.replace('。', '.')  # 全角句号转半角句号

        # 处理共享区号的多号码格式（如：0531-81769739/81769209）
        # 匹配模式：0xxx-xxxxxxx/xxxxxxx，但需要区分第二个号码是否为手机号
        shared_area_pattern = r'(0\d{2,3})-(\d{6,8})/(\d{6,11})'
        def expand_shared_area(match):
            area_code, num1, num2 = match.groups()
            # 判断第二个号码是否为手机号（11位且以1开头）
            if len(num2) == 11 and num2.startswith('1'):
                # 第二个号码是手机号，不添加区号
                return f'{area_code}-{num1} {num2}'
            else:
                # 第二个号码是固定电话，添加区号
                return f'{area_code}-{num1} {area_code}-{num2}'
        text = re.sub(shared_area_pattern, expand_shared_area, text)

        # 处理斜杠分隔的号码（按优先级顺序处理）
        # 1. 手机号和手机号混合：1xxxxxxxxxx/1xxxxxxxxxx（优先处理，避免被其他规则误匹配）
        mobile_mobile_slash_pattern = r'(1[3-9]\d{9})/(1[3-9]\d{9})'
        def separate_mobile_mobile(match):
            mobile1, mobile2 = match.groups()
            return f'{mobile1} {mobile2}'  # 用空格分隔
        text = re.sub(mobile_mobile_slash_pattern, separate_mobile_mobile, text)

        # 2. 手机号和固定电话混合（斜杠分隔）：1xxxxxxxxxx/0xxx-xxxxxxx
        mobile_landline_slash_pattern = r'(1[3-9]\d{9})/(0\d{2,3}[-]?\d{6,8})'
        def separate_mobile_landline(match):
            mobile, landline = match.groups()
            return f'{mobile} {landline}'  # 用空格分隔
        text = re.sub(mobile_landline_slash_pattern, separate_mobile_landline, text)

        # 3. 固定电话和手机号混合：需要验证前面的数字是否为有效固定电话
        landline_mobile_pattern = r'(\d{7,8}|0\d{10})/(1[3-9]\d{9})'
        def separate_landline_mobile(match):
            landline, mobile = match.groups()

            # 验证前面的数字是否为有效的固定电话
            if CommonCleaningFunctions._is_valid_landline(landline):
                # 有效固定电话，保留两个号码
                return f'{landline} {mobile}'
            else:
                # 无效数字串，只保留手机号
                return mobile

        text = re.sub(landline_mobile_pattern, separate_landline_mobile, text)

        # 4. 带区号的固定电话：0xxx/xxxxxxx
        text = re.sub(r'(0\d{2,3})/(\d{6,8})', r'\1-\2', text)

        # 处理其他分隔符组合
        # 5. 手机号-固定电话（破折号分隔，但不是标准格式）
        mobile_dash_landline_pattern = r'(1[3-9]\d{9})-(0\d{2,3})\.?(\d{6,8})'
        def separate_mobile_dash_landline(match):
            mobile, area_code, number = match.groups()
            return f'{mobile} {area_code}-{number}'
        text = re.sub(mobile_dash_landline_pattern, separate_mobile_dash_landline, text)

        # 6. 句号分隔的号码（按优先级处理）
        # 6.1 手机号.手机号（句号分隔）
        mobile_dot_mobile_pattern = r'(1[3-9]\d{9})\.(1[3-9]\d{9})'
        def separate_mobile_dot_mobile(match):
            mobile1, mobile2 = match.groups()
            return f'{mobile1} {mobile2}'
        text = re.sub(mobile_dot_mobile_pattern, separate_mobile_dot_mobile, text)

        # 6.2 手机号.固定电话（句号分隔）
        mobile_dot_landline_pattern = r'(1[3-9]\d{9})\.(\d{3,4}[-]?\d{6,8})'
        def separate_mobile_dot_landline(match):
            mobile, landline = match.groups()
            # 如果landline没有破折号，添加破折号
            if '-' not in landline:
                if len(landline) == 10:  # 3位区号+7位号码
                    landline = landline[:3] + '-' + landline[3:]
                elif len(landline) == 11:  # 4位区号+7位号码
                    landline = landline[:4] + '-' + landline[4:]
            return f'{mobile} {landline}'
        text = re.sub(mobile_dot_landline_pattern, separate_mobile_dot_landline, text)

        # 6.3 固定电话.手机号（句号分隔）
        landline_dot_mobile_pattern = r'(\d{7,8})\.(1[3-9]\d{9})'
        def separate_landline_dot_mobile(match):
            landline, mobile = match.groups()
            return f'{landline} {mobile}'
        text = re.sub(landline_dot_mobile_pattern, separate_landline_dot_mobile, text)

        # 7. 逗号分隔的号码（支持7-8位固定电话）
        comma_separated_pattern = r'((?:1[3-9]\d{9}|0\d{2,3}[-]?\d{6,8}|\d{7,8})),\s*((?:1[3-9]\d{9}|0\d{2,3}[-]?\d{6,8}|\d{7,8}))'
        def separate_comma_numbers(match):
            num1, num2 = match.groups()
            return f'{num1} {num2}'
        text = re.sub(comma_separated_pattern, separate_comma_numbers, text)

        # 8. 分号分隔的号码（支持7-8位固定电话）
        semicolon_separated_pattern = r'((?:1[3-9]\d{9}|0\d{2,3}[-]?\d{6,8}|\d{7,8}));?\s*((?:1[3-9]\d{9}|0\d{2,3}[-]?\d{6,8}|\d{7,8}))'
        def separate_semicolon_numbers(match):
            num1, num2 = match.groups()
            return f'{num1} {num2}'
        text = re.sub(semicolon_separated_pattern, separate_semicolon_numbers, text)

        # 清理多个连续的破折号为单个破折号
        text = re.sub(r'-{2,}', '-', text)

        # 清理破折号周围的多余空格
        text = re.sub(r'\s*-\s*', '-', text)

        return text

    @staticmethod
    def _remove_letter_prefixes(text):
        """移除电话号码前的字母前缀"""
        if not isinstance(text, str):
            return text

        # 移除电话号码前的字母前缀（如：QIN0537-5179865）
        # 匹配模式：字母+数字开头的电话号码，保留完整的电话号码
        text = re.sub(r'[A-Za-z]+(?=0\d{2,3}[-~]?\d{6,8})', '', text)

        # 处理更复杂的情况，确保区号不丢失
        # 匹配：字母+区号+分隔符+号码
        text = re.sub(r'[A-Za-z]+(0\d{2,3}[-~]?\d{6,8})', r'\1', text)

        return text

    @staticmethod
    def _extract_phone_numbers(text):
        """提取所有可能的电话号码，按在文本中出现的顺序"""
        # 0. 预处理：移除身份证号码，避免误识别
        text = CommonCleaningFunctions._remove_id_numbers(text)

        # 0.1 预处理：移除字母前缀
        text = CommonCleaningFunctions._remove_letter_prefixes(text)

        # 0.2 预处理：标准化分隔符
        text = CommonCleaningFunctions._normalize_separators(text)

        # 使用一个列表来存储所有匹配结果，包含位置信息
        all_matches = []

        # 1. 包含"转"字的电话号码（优先处理，避免被其他规则截断）
        transfer_pattern = r'0\d{2,3}[-]?\d{6,8}转\d+'
        for match in re.finditer(transfer_pattern, text):
            all_matches.append((match.start(), match.group()))

        # 2. 手机号码模式 (11位，1开头)
        # 2.1 连续的手机号
        mobile_pattern = r'1[3-9]\d{9}'
        for match in re.finditer(mobile_pattern, text):
            all_matches.append((match.start(), match.group()))

        # 2.2 带空格或分隔符的手机号 (如: 138 1234 5678, 137 96403161)
        # 使用更精确的模式，避免跨越独立号码
        mobile_with_spaces_pattern = r'(?<!\d)1[3-9]\d[\s-]\d{4}[\s-]\d{4}(?!\d)'
        for match in re.finditer(mobile_with_spaces_pattern, text):
            all_matches.append((match.start(), match.group()))

        # 2.3 特殊格式：3位数字 + 空格 + 8位数字 (如: 137 96403161)
        # 这种格式可能是手机号的特殊分隔方式
        special_mobile_pattern = r'(?<!\d)(1[3-9]\d)\s+(\d{8})(?!\d)'
        for match in re.finditer(special_mobile_pattern, text):
            # 组合成完整的11位手机号
            prefix, suffix = match.groups()
            full_mobile = prefix + suffix
            # 验证是否为有效手机号
            if len(full_mobile) == 11 and full_mobile.startswith('1') and full_mobile[1] in '3456789':
                all_matches.append((match.start(), full_mobile))

        # 3. 固定电话模式 (带区号) - 优先匹配完整格式
        # 3位区号：010、020、021、022、023、024、025、027、028、029
        three_digit_area_codes = ['010', '020', '021', '022', '023', '024', '025', '027', '028', '029']

        # 3.1 匹配3位区号的固定电话
        for area_code in three_digit_area_codes:
            # 格式：010-xxxxxxxx 或 (010)xxxxxxxx 或 010 xxxxxxxx 或 010/xxxxxxxx 或 010~xxxxxxxx
            pattern = rf'\(?{area_code}\)?[-\s/~]?\d{{7,8}}'
            for match in re.finditer(pattern, text):
                all_matches.append((match.start(), match.group()))

        # 3.2 匹配4位区号的固定电话
        # 3.2.1 带分隔符的格式 (0xxx-xxxxxxx, (0xxx)xxxxxxx等)
        four_digit_pattern = r'(?<!\d)0\d{3}[-/~]\d{7,8}(?!\d)|(?<!\d)0\d{3}\s\d{7,8}(?=\s|$)'
        for match in re.finditer(four_digit_pattern, text):
            digits_only = re.sub(r'[^\d]', '', match.group())
            if len(digits_only) >= 10:
                area_code = digits_only[:3]
                if area_code not in three_digit_area_codes:
                    all_matches.append((match.start(), match.group()))

        # 3.2.2 括号格式的固定电话 ((0xxx)xxxxxxx)
        bracket_pattern = r'\(0\d{3}\)\d{7,8}'
        for match in re.finditer(bracket_pattern, text):
            digits_only = re.sub(r'[^\d]', '', match.group())
            if len(digits_only) >= 10:
                area_code = digits_only[:3]
                if area_code not in three_digit_area_codes:
                    all_matches.append((match.start(), match.group()))

        # 3.2.3 连续11位固定电话 (0xxxxxxxxxx)
        consecutive_landline_pattern = r'(?<!\d)0\d{10}(?!\d)'
        for match in re.finditer(consecutive_landline_pattern, text):
            digits_only = match.group()
            area_code = digits_only[:3]
            if area_code not in three_digit_area_codes:
                all_matches.append((match.start(), match.group()))

        # 4. 国际号码 (+86开头)
        international_pattern = r'\+86[-\s]?1[3-9]\d{9}'
        for match in re.finditer(international_pattern, text):
            clean_match = re.sub(r'\+86[-\s]?', '', match.group())
            all_matches.append((match.start(), clean_match))

        # 5. 处理多个空格分隔的号码
        # 5.1 手机号+多个空格+手机号的模式（如：15694731238   15332717337）
        mobile_mobile_pattern = r'(1[3-9]\d{9})\s{2,}(1[3-9]\d{9})'
        for match in re.finditer(mobile_mobile_pattern, text):
            mobile1, mobile2 = match.groups()
            all_matches.append((match.start(), mobile1))
            all_matches.append((match.start() + len(mobile1), mobile2))

        # 5.2 手机号+多个空格+固话的模式（如：13588654567      85915211）
        mobile_landline_pattern = r'(1[3-9]\d{9})\s{2,}(\d{7,8})'
        for match in re.finditer(mobile_landline_pattern, text):
            mobile, landline = match.groups()
            all_matches.append((match.start(), mobile))
            all_matches.append((match.start() + len(mobile), landline))

        # 6. 处理独立的7-8位固定电话号码
        # 提取独立的7-8位号码，但要避免与已匹配的号码重叠
        short_number_pattern = r'\b\d{7,8}\b'
        for match in re.finditer(short_number_pattern, text):
            # 检查是否与已有匹配重叠
            overlaps = False
            for existing_pos, existing_phone in all_matches:
                existing_end = existing_pos + len(existing_phone)
                if not (match.end() <= existing_pos or match.start() >= existing_end):
                    overlaps = True
                    break

            if not overlaps:
                all_matches.append((match.start(), match.group()))

        # 按位置排序并去重
        all_matches.sort(key=lambda x: x[0])
        phone_numbers = []
        used_ranges = []  # 记录已使用的文本范围

        for pos, phone in all_matches:
            phone_digits = re.sub(r'[^\d]', '', phone)
            phone_end = pos + len(phone)

            # 检查是否与已有范围重叠
            overlaps = False
            for used_start, used_end in used_ranges:
                if not (phone_end <= used_start or pos >= used_end):
                    overlaps = True
                    break

            # 如果不重叠且号码不重复，则添加
            if not overlaps and phone_digits not in [re.sub(r'[^\d]', '', p) for p in phone_numbers]:
                phone_numbers.append(phone)
                used_ranges.append((pos, phone_end))

        return phone_numbers

    @staticmethod
    def _standardize_phone_number(phone):
        """标准化电话号码格式"""
        if not phone:
            return ""

        # 特殊处理：包含"转"字的电话号码，保持原样
        if '转' in phone:
            return phone

        # 移除所有非数字字符
        digits_only = re.sub(r'[^\d]', '', phone)

        # 3位区号列表
        three_digit_area_codes = ['010', '020', '021', '022', '023', '024', '025', '027', '028', '029']

        # 处理不同长度的号码
        if len(digits_only) == 11 and digits_only.startswith('1'):
            # 11位手机号
            return digits_only
        elif len(digits_only) == 10 and digits_only.startswith('0'):
            # 10位固话，判断是3位区号还是4位区号
            area_code_3 = digits_only[:3]
            if area_code_3 in three_digit_area_codes:
                # 3位区号 + 7位号码：010-1234567
                return digits_only[:3] + '-' + digits_only[3:]
            else:
                # 4位区号 + 6位号码：0571-123456
                return digits_only[:4] + '-' + digits_only[4:]
        elif len(digits_only) == 11 and digits_only.startswith('0'):
            # 11位固话，判断是3位区号还是4位区号
            area_code_3 = digits_only[:3]
            if area_code_3 in three_digit_area_codes:
                # 3位区号 + 8位号码：010-12345678
                return digits_only[:3] + '-' + digits_only[3:]
            else:
                # 4位区号 + 7位号码：0571-1234567
                return digits_only[:4] + '-' + digits_only[4:]
        elif len(digits_only) == 12 and digits_only.startswith('0'):
            # 12位固话，4位区号 + 8位号码：0571-12345678
            return digits_only[:4] + '-' + digits_only[4:]
        elif 7 <= len(digits_only) <= 8:
            # 7-8位本地号码，保持原样
            return digits_only
        else:
            # 其他情况，返回原始数字
            return digits_only

    @staticmethod
    def _is_valid_phone_number(phone):
        """验证电话号码是否有效"""
        if not phone:
            return False

        # 特殊处理：包含"转"字的电话号码
        if '转' in phone:
            # 对于包含"转"字的号码，保持原样
            return True

        # 移除分隔符进行验证
        digits_only = re.sub(r'[^\d]', '', phone)

        # 手机号验证 (11位，1开头)
        if len(digits_only) == 11 and digits_only.startswith('1'):
            return True

        # 固话验证 (7-12位)
        if 7 <= len(digits_only) <= 12:
            return True

        # 过滤明显无效的号码
        if len(digits_only) < 7 or len(digits_only) > 15:
            return False

        # 过滤全是相同数字的号码
        if len(set(digits_only)) == 1:
            return False

        return True

    @staticmethod
    def _is_valid_landline(landline):
        """验证是否为有效的固定电话号码"""
        if not landline:
            return False

        # 移除分隔符进行验证
        digits_only = re.sub(r'[^\d]', '', landline)

        # 3位区号列表
        three_digit_area_codes = ['010', '020', '021', '022', '023', '024', '025', '027', '028', '029']

        # 7-8位本地号码（无区号）
        if 7 <= len(digits_only) <= 8:
            # 检查是否为常见的有效号码模式
            # 避免明显无效的号码（如全是相同数字、明显的随机数字等）
            if len(set(digits_only)) == 1:  # 全是相同数字
                return False

            # 检查是否以常见的固定电话开头
            # 大多数固定电话不会以0、1开头（本地号码部分）
            if digits_only.startswith('0') or digits_only.startswith('1'):
                return False

            # 检查是否为明显的无效模式（只保留最明显的无效模式）
            invalid_patterns = [
                r'^1234',  # 12345678 这种连续数字
                r'^9999',  # 99999999 这种重复数字
                r'^0000',  # 00000000 这种全零数字
            ]

            for pattern in invalid_patterns:
                if re.match(pattern, digits_only):
                    return False

            return True

        # 10-12位带区号的固定电话
        elif 10 <= len(digits_only) <= 12 and digits_only.startswith('0'):
            area_code_3 = digits_only[:3]

            # 3位区号
            if area_code_3 in three_digit_area_codes:
                return True

            # 4位区号（0xxx格式，但不在3位区号列表中）
            if len(digits_only) >= 10 and area_code_3 not in three_digit_area_codes:
                return True

        return False

    @staticmethod
    def clean_email(email):
        """
        清洗邮箱地址

        Args:
            email: 原始邮箱地址

        Returns:
            清洗后的邮箱地址，如果格式不正确返回空字符串
        """
        if not isinstance(email, str):
            return ""

        email = email.strip().lower()

        # 简单的邮箱格式验证
        email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        if email_pattern.match(email):
            return email
        else:
            return ""

    @staticmethod
    def clean_id_number(id_number):
        """
        清洗身份证号码

        Args:
            id_number: 原始身份证号码

        Returns:
            清洗后的身份证号码
        """
        if not isinstance(id_number, str):
            return ""

        # 移除所有非字母数字字符
        cleaned = re.sub(r'[^a-zA-Z0-9]', '', id_number.upper())

        # 验证长度（15位或18位）
        if len(cleaned) in [15, 18]:
            return cleaned
        else:
            return ""

    @staticmethod
    def remove_extra_spaces(text):
        """
        移除多余的空格

        Args:
            text: 原始文本

        Returns:
            清洗后的文本
        """
        if not isinstance(text, str):
            return text

        # 将多个连续空格替换为单个空格
        return re.sub(r'\s+', ' ', text.strip())

    @staticmethod
    def remove_special_characters(text, keep_chars=''):
        """
        移除特殊字符

        Args:
            text: 原始文本
            keep_chars: 要保留的特殊字符

        Returns:
            清洗后的文本
        """
        if not isinstance(text, str):
            return text

        # 保留汉字、字母、数字和指定的特殊字符
        pattern = f'[^\u4e00-\u9fa5a-zA-Z0-9{re.escape(keep_chars)}]'
        return re.sub(pattern, '', text)

