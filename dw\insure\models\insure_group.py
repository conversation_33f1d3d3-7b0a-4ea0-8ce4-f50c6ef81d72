from django.db import models
from common.models import BaseModel


class InsureGroup(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    seller_name = models.CharField(max_length=256, blank=True, null=True, verbose_name='保司名称')
    company_name = models.CharField(max_length=256, blank=True, null=True, verbose_name='公司名称')
    name = models.CharField(max_length=256, blank=True, null=True, verbose_name='被保人姓名')
    credential_number = models.CharField(max_length=64, blank=True, null=True, verbose_name='证件号码')
    product_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='产品编码')

    class Meta:
        db_table = 'insure_group'
        verbose_name = '健康险团单数据'
        verbose_name_plural = verbose_name

