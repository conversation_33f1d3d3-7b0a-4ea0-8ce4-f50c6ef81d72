from django.db import models
import django


def _db_comment_kwarg(comment: str):
    """
    为字段添加数据库注释的辅助函数
    只在Django 4.2+版本中添加db_comment参数
    """
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class BaseModel(models.Model):
    """
    抽象基类模型，包含创建时间和更新时间字段。

    注意：这些字段现在使用Django的auto_now_add和auto_now机制来自动更新时间戳。
    请确保在数据库中正确设置了这些字段的默认值。

    重要：请不要随意修改这些字段的定义，以避免产生不必要的迁移文件。
    如需修改，请先在开发环境中充分测试。
    """
    # 使用Django级别的自动时间戳处理
    # 字段定义已固定，请勿随意修改
    create_time = models.DateTimeField(
        verbose_name='创建时间',
        help_text='由Django自动设置，INSERT时自动填充当前时间',
        auto_now_add=True,
        **_db_comment_kwarg('创建时间')
    )
    update_time = models.DateTimeField(
        verbose_name='更新时间',
        help_text='由Django自动维护，INSERT和UPDATE时自动更新为当前时间',
        auto_now=True,
        **_db_comment_kwarg('更新时间')
    )

    class Meta:
        abstract = True  # 标记为抽象类，不会生成数据库表


class DatabaseMigrationLog(models.Model):
    """
    数据库迁移日志模型，用于记录迁移操作
    """
    migration_name = models.CharField(max_length=255, verbose_name='迁移名称')
    database_type = models.CharField(max_length=50, verbose_name='数据库类型')
    sql_executed = models.TextField(verbose_name='执行的SQL')
    status = models.CharField(max_length=20, choices=[
        ('success', '成功'),
        ('failed', '失败'),
        ('rollback', '已回滚')
    ], verbose_name='状态')
    error_message = models.TextField(blank=True, null=True, verbose_name='错误信息')
    executed_at = models.DateTimeField(auto_now_add=True, verbose_name='执行时间')

    class Meta:
        db_table = 'database_migration_log'
        verbose_name = '数据库迁移日志'
        verbose_name_plural = '数据库迁移日志'
        ordering = ['-executed_at']