# Generated by Django 4.2.1 on 2025-05-27 14:05

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("medical", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelTableComment(
            name="medicalchineseherbaldrugbase",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicaldesignatedproviders",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicaldrugbase",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicaldrugentity",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalfieldmapping",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalmedicinediagnosis",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalnationalnegotiateddrug",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalnationalnegotiateddrugproviders",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalselfprepareddrugbase",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalservicebase",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalserviceentity",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalsuppliesbase",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalsuppliesentity",
            table_comment=None,
        ),
        migrations.AlterModelTableComment(
            name="medicalsuppliesregistermessage",
            table_comment=None,
        ),
    ]
