# 南京医保局爬虫数据清洗规则说明

## 概述

本文档详细说明了南京医保局爬虫数据到目标表的ETL清洗规则，包括表级别清洗、字段级别清洗和自定义清洗函数的配置。

## 支持的数据表

| 源表 | 目标表 | 描述 | 唯一性字段 |
|------|--------|------|------------|
| `spider_njyb_service_facilities` | `medical_service_entity` | 南京医疗服务项目市级实体 | `code`, `central_code` |
| `spider_njyb_medical_supplies` | `medical_supplies_entity` | 南京医用耗材市级实体 | `code`, `central_code` |
| `spider_njyb_drug` | `medical_drug_entity` | 南京医保药品市级实体 | `code`, `central_code` |

## 清洗规则分类

### 1. 表级别清洗规则

#### 1.1 南京医疗服务项目 (`spider_njyb_service_facilities`)
- **唯一键约束**: 使用table_configs中配置的unique_fields: `code`, `central_code`
- **源表字段映射**: `medListCodg` → `code`, `centCodg` → `central_code`
- **字段映射**: 通过medical_field_mapping表进行字段映射，包括固定值字段
- **additional_info处理**: 将来源表字段转换为字典存储，key为驼峰转下划线格式
- **地理位置标识**: 通过medical_field_mapping表自动添加南京市的省份和城市信息
- **数据过滤**: 按照table_configs中的unique_fields进行过滤，确保配置一致性
- **数据类型转换**: 价格和比例字段的数值类型转换
- **table_config限制**: `province_name='江苏省' AND level_type='city' AND city_name='南京市'`

#### 1.2 南京医用耗材 (`spider_njyb_medical_supplies`)
- **唯一键约束**: 使用table_configs中配置的unique_fields: `code`, `central_code`
- **源表字段映射**: `medListCodg` → `code`, `centCodg` → `central_code`
- **字段映射**: 通过medical_field_mapping表进行字段映射，包括固定值字段
- **地理位置标识**: 通过medical_field_mapping表自动添加南京市的省份和城市信息
- **数据类型转换**: 自动处理Decimal和Date字段类型转换，解决数据类型不匹配问题
- **空值清理**: 标准化处理空字符串、特殊符号等无效值
- **table_config限制**: `province_name='江苏省' AND level_type='city' AND city_name='南京市'`

#### 1.3 南京医保药品 (`spider_njyb_drug`)
- **唯一键约束**: 使用table_configs中配置的unique_fields: `code`, `central_code`
- **源表字段映射**: `medListCodg` → `code`, `centCodg` → `central_code`
- **字段映射**: 通过medical_field_mapping表进行字段映射，包括固定值字段
- **地理位置标识**: 通过medical_field_mapping表自动添加南京市的省份和城市信息
- **数据类型转换**: 自动处理Decimal精度标准化和Date字段类型转换
- **空值清理**: 标准化处理空字符串、特殊符号等无效值
- **table_config限制**: `province_name='江苏省' AND level_type='city' AND city_name='南京市'`

### 2. 字段映射详情

#### 2.1 医疗服务项目字段映射 (`spider_njyb_service_facilities`)

##### 基础信息字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `medListCodg` | `code` | 服务项目代码 |
| `itemname` | `name` | 服务项目名称 |
| `pricCodg` | `charge_item_code` | 收费项目代码 |
| `centCodg` | `central_code` | 中心编码 |

##### 支付和价格字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `lv1Pric` | `price` | 供应价格 |
| `selfpayProp` | `initial_payment_ratio` | 先行自付比例 |

#### 2.2 医用耗材字段映射 (`spider_njyb_medical_supplies`)

##### 基础信息字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `medListCodg` | `code` | 耗材代码 |
| `useName` | `name` | 耗材名称 |
| `centCodg` | `central_code` | 中心编码 |
| `regcertName` | `registered_name` | 注册名称 |
| `regcertNo` | `registered_number` | 注册证号 |
| `mol` | `model` | 型号 |
| `spec` | `specifications` | 规格 |
| `prodentp` | `production_company_name` | 生产企业名称 |

##### 价格和支付字段
| 源字段 | 目标字段 | 说明 | 数据类型转换 |
|--------|----------|------|-------------|
| `matnSelfpayProp` | `initial_payment_ratio` | 先行自付比例 | float → Decimal |
| `matnPayUplmt` | `payment_upper_limit` | 医保支付上限 | float → Decimal |
| `mdbidprice` | `tender_price` | 招标价格 | float → Decimal |
| `begntime` | `begin_date` | 开始日期 | string → date |

##### 其他字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `mdbidNo` | `tender_number` | 招标号 |
| `prcunt` | `packaging_unit` | 包装单位 |

#### 2.3 医保药品字段映射 (`spider_njyb_drug`)

##### 基础信息字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `medListCodg` | `code` | 药品代码 |
| `drugProdname` | `product_name` | 产品名称 |
| `centCodg` | `central_code` | 中心编码 |
| `drugGenname` | `generic_name_national` | 通用名称 |
| `regName` | `registered_name` | 注册名称 |
| `spec` | `specifications` | 规格 |
| `prodentpName` | `production_company_name` | 生产企业名称 |

##### 价格和支付字段
| 源字段 | 目标字段 | 说明 | 数据类型转换 |
|--------|----------|------|-------------|
| `govPric` | `government_guided_price` | 政府指导价 | Decimal精度标准化 |
| `selfpayProp` | `initial_payment_ratio` | 先行自付比例 | Decimal精度标准化 |
| `matnPayUplmt` | `payment_upper_limit` | 医保支付上限 | Decimal精度标准化 |
| `provPurcUplmtPric` | `procure_ceil_price` | 采购上限价格 | Decimal精度标准化 |
| `begntime` | `begin_date` | 开始日期 | datetime → date |

##### 其他字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `drugAprvno` | `approval_number` | 批准文号 |
| `stdtPayType` | `categories_national` | 国家支付类别 |
| `provListCodg` | `number_national` | 国家编码 |
| `minPacCnt` | `minimum_packaging_count` | 最小包装数量 |
| `prcunt` | `minimum_packaging_unit` | 最小包装单位 |
| `minPrcunt` | `minimum_prescription_unit` | 最小处方单位 |
| `otcFlag` | `otc_flag` | OTC标识 |
| `pacmatl` | `packaging_material` | 包装材质 |
| `hiQuaPayScp` | `payment_restricted_scope` | 限定支付范围 |
| `phacDsabFlag` | `pharmacy_sale_allowed` | 药店销售标识 |

#### 2.4 地理位置字段（自动生成）
| 目标字段 | 默认值 | 说明 |
|----------|--------|------|
| `province_code` | `320000` | 江苏省编码 |
| `province_name` | `江苏省` | 省份名称 |
| `city_code` | `320100` | 南京市编码 |
| `city_name` | `南京市` | 城市名称 |
| `level_type` | `city` | 层级类型 |

### 3. 数据类型转换规则

#### 3.1 数据类型不匹配问题解决

南京医保局数据在ETL过程中遇到的主要问题是**数据类型不匹配**，导致系统误判所有记录都需要更新。

##### 问题原因
1. **Decimal精度差异**: 源表 `Decimal('0.0000')` vs 目标表 `Decimal('0.00')`
2. **日期类型差异**: 源表 `datetime` vs 目标表 `date`
3. **数值类型差异**: 源表 `float` vs 目标表 `Decimal`

##### 解决方案
通过 `data_normalization.py` 模块自动进行数据类型转换：

**医用耗材表 (`medical_supplies_entity`) 转换配置**:
```python
decimal_fields_config = {
    'medical_supplies_entity': [
        'initial_payment_ratio',  # 先行自付比例
        'payment_upper_limit',    # 医保支付上限
        'tender_price'            # 招标价格
    ]
}

date_fields_config = {
    'medical_supplies_entity': [
        'begin_date'  # 开始日期
    ]
}
```

**医保药品表 (`medical_drug_entity`) 转换配置**:
```python
decimal_fields_config = {
    'medical_drug_entity': [
        'government_guided_price',  # 政府指导价
        'initial_payment_ratio',    # 先行自付比例
        'payment_upper_limit',      # 医保支付上限
        'procure_ceil_price'        # 采购上限价格
    ]
}

date_fields_config = {
    'medical_drug_entity': [
        'begin_date'  # 开始日期
    ]
}
```

#### 3.2 转换效果示例

##### Decimal字段转换
- **转换前**: `0.2` (float) → **转换后**: `Decimal('0.20')` (Decimal)
- **转换前**: `Decimal('0.0000')` → **转换后**: `Decimal('0.00')` (精度标准化)

##### Date字段转换
- **转换前**: `'2024-10-15 00:00:00'` (string) → **转换后**: `date(2024, 10, 15)` (date)
- **转换前**: `datetime(2025, 3, 1, 0, 0)` → **转换后**: `date(2025, 3, 1)` (date)

### 4. 自定义清洗函数详解

#### 4.1 南京医疗服务项目清洗 (`_clean_njyb_service_facilities_data`)

**处理字段**: 根据实际源表字段进行完整映射

#### 4.2 医用耗材和药品标准清洗

**医用耗材** (`spider_njyb_medical_supplies`) 和 **医保药品** (`spider_njyb_drug`) 使用标准清洗策略：

##### 标准清洗流程
1. **字段映射**: 通过 `medical_field_mapping` 表进行自动字段映射
2. **空值清理**: 处理空字符串、特殊符号等无效值
3. **数据类型转换**: 自动转换 Decimal 和 Date 字段类型
4. **地理位置注入**: 自动添加南京市地理位置信息
5. **唯一性过滤**: 按照 `unique_fields` 配置进行去重

##### 无效值清理规则
```python
null_values = ['-', '--', '—', '/', '\\', '', 'nan', 'NaN', 'null', 'NULL']
```

##### 驼峰转下划线转换
**功能**: 将驼峰命名转换为下划线分割格式
**处理逻辑**:
```python
def camel_to_snake(name):
    """将驼峰命名转换为下划线分割"""
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
```

##### additional_info字段处理（重要）
**功能**: 将来源表字段转换为字典存储，key为驼峰转下划线格式
**格式要求**:
```json
{
  "field_name_one": "value1",
  "field_name_two": 123.45,
  "another_field": "value3"
}
```

**处理逻辑**:
```python
def build_additional_info(row):
    additional_info = {}
    for field_name, field_value in row.items():
        # 跳过目标表已有的标准字段
        if field_name in ['code', 'name', 'charge_item_code', ...]:
            continue
        
        # 将驼峰命名转换为下划线分割
        snake_case_key = camel_to_snake(field_name)
        
        # 处理字段值
        if pd.notna(field_value) and str(field_value).strip() not in ['-', '--', '—', '/', '\\', '']:
            try:
                if isinstance(field_value, (int, float)):
                    additional_info[snake_case_key] = field_value
                else:
                    try:
                        additional_info[snake_case_key] = float(field_value)
                    except (ValueError, TypeError):
                        additional_info[snake_case_key] = str(field_value).strip()
            except (ValueError, TypeError):
                additional_info[snake_case_key] = str(field_value).strip()
    
    return json.dumps(additional_info, ensure_ascii=False) if additional_info else '{}'
```

##### 地理位置信息添加
**功能**: 为南京市数据添加标准的地理位置标识
**处理逻辑**:
- 省份编码: 固定为 `32` (江苏省)
- 省份名称: 固定为 `江苏省`
- 城市编码: 固定为 `3201` (南京市)
- 城市名称: 固定为 `南京市`
- 层级类型: 标识为 `city`

##### 按唯一键过滤和去重
**功能**: 按照可用的唯一键进行过滤和去重处理
**处理逻辑**:
```python
# 按唯一键过滤 - 确保关键字段不为空
df = df[
    (df['code'].notna() & (df['code'] != '')) |
    (df['charge_item_code'].notna() & (df['charge_item_code'] != ''))
]

# 按唯一键去重
unique_fields = []
if 'code' in df.columns and df['code'].notna().any():
    unique_fields.append('code')
if 'charge_item_code' in df.columns and df['charge_item_code'].notna().any():
    unique_fields.append('charge_item_code')
if 'name' in df.columns and df['name'].notna().any():
    unique_fields.append('name')

if unique_fields:
    df = df.drop_duplicates(subset=unique_fields, keep='first')
```

## 使用方法

### 自动执行（推荐）

#### 执行所有ETL任务
```bash
cd dw/transfrom/tasks/spider/njyb/pipeline
python njyb_etl_mapping.py
```

#### 单独执行各表ETL
```bash
# 医疗服务项目
python -c "from njyb_etl_mapping import run_njyb_service_facilities_etl; run_njyb_service_facilities_etl()"

# 医用耗材
python -c "from njyb_etl_mapping import run_njyb_medical_supplies_etl; run_njyb_medical_supplies_etl()"

# 医保药品
python -c "from njyb_etl_mapping import run_njyb_drug_etl; run_njyb_drug_etl()"
```

### 编程调用

#### 各表ETL函数
```python
from transfrom.tasks.spider.njyb.pipeline.njyb_etl_mapping import (
    run_njyb_service_facilities_etl,
    run_njyb_medical_supplies_etl,
    run_njyb_drug_etl
)

# 南京医疗服务项目ETL
success1 = run_njyb_service_facilities_etl()

# 南京医用耗材ETL
success2 = run_njyb_medical_supplies_etl()

# 南京医保药品ETL
success3 = run_njyb_drug_etl()
```

## 清洗效果监控

### 验证方法

#### 医疗服务项目数据验证
```sql
-- 检查南京医疗服务项目目标表数据质量
SELECT COUNT(*) FROM medical_service_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市';

-- 检查字段映射完整性
SELECT
    COUNT(CASE WHEN charge_item_code IS NOT NULL THEN 1 END) as with_charge_code,
    COUNT(CASE WHEN code IS NOT NULL THEN 1 END) as with_service_code,
    COUNT(CASE WHEN price IS NOT NULL THEN 1 END) as with_price
FROM medical_service_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市';

-- 检查additional_info字段
SELECT additional_info FROM medical_service_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市'
AND additional_info IS NOT NULL AND additional_info != '{}'
LIMIT 5;
```

#### 医用耗材数据验证
```sql
-- 检查南京医用耗材目标表数据质量
SELECT COUNT(*) FROM medical_supplies_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市';

-- 检查数据类型转换效果
SELECT
    COUNT(CASE WHEN initial_payment_ratio IS NOT NULL THEN 1 END) as with_payment_ratio,
    COUNT(CASE WHEN payment_upper_limit IS NOT NULL THEN 1 END) as with_upper_limit,
    COUNT(CASE WHEN begin_date IS NOT NULL THEN 1 END) as with_begin_date
FROM medical_supplies_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市';
```

#### 医保药品数据验证
```sql
-- 检查南京医保药品目标表数据质量
SELECT COUNT(*) FROM medical_drug_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市';

-- 检查Decimal精度标准化效果
SELECT
    COUNT(CASE WHEN government_guided_price IS NOT NULL THEN 1 END) as with_gov_price,
    COUNT(CASE WHEN initial_payment_ratio IS NOT NULL THEN 1 END) as with_payment_ratio,
    COUNT(CASE WHEN begin_date IS NOT NULL THEN 1 END) as with_begin_date
FROM medical_drug_entity
WHERE province_name='江苏省' AND level_type='city' AND city_name='南京市';
```

## 注意事项

1. **地理位置标识**: 所有数据自动标识为南京市市级数据
2. **驼峰转下划线**: additional_info中的key都会从驼峰命名转换为下划线分割
3. **table_config限制**: 目标表数据限制为 `province_name='江苏省' AND level_type='city' AND city_name='南京市'`
4. **数据完整性**: 清洗过程优先保护数据完整性
5. **字段映射**: 使用medical_field_mapping表进行字段映射，包括固定值和地理位置字段
6. **数据类型转换**: 自动处理Decimal精度和Date类型转换，避免ETL误判更新
7. **唯一性约束**: 所有表都使用 `code` + `central_code` 作为唯一性字段

## 核心配置说明

### 数据类型转换配置
通过 `data_normalization.py` 模块自动处理数据类型不匹配问题：

#### 医用耗材表配置
```python
decimal_fields_config = {
    'medical_supplies_entity': [
        'initial_payment_ratio',  # 先行自付比例
        'payment_upper_limit',    # 医保支付上限
        'tender_price'            # 招标价格
    ]
}

date_fields_config = {
    'medical_supplies_entity': [
        'begin_date'  # 开始日期
    ]
}
```

#### 医保药品表配置
```python
decimal_fields_config = {
    'medical_drug_entity': [
        'government_guided_price',  # 政府指导价
        'initial_payment_ratio',    # 先行自付比例
        'payment_upper_limit',      # 医保支付上限
        'procure_ceil_price'        # 采购上限价格
    ]
}

date_fields_config = {
    'medical_drug_entity': [
        'begin_date'  # 开始日期
    ]
}
```

### 地理位置字段
- **level_type**: 固定值 `city`
- **province_code**: 固定值 `320000`
- **province_name**: 固定值 `江苏省`
- **city_code**: 固定值 `320100`
- **city_name**: 固定值 `南京市`

### additional_info字段（仅医疗服务项目）
- 包含所有不在标准字段映射中的源表字段
- key为驼峰转下划线格式
- value保持原始数据类型（数值或字符串）

## 版本历史

### v1.2 (数据类型转换优化)
- **新增表支持**: 添加医用耗材和医保药品表的清洗规则
- **数据类型转换**: 解决Decimal精度和Date类型不匹配问题
- **自动转换配置**: 在data_normalization.py中配置自动类型转换
- **字段映射完善**: 完善所有表的字段映射关系说明
- **清洗策略统一**: 医用耗材和药品使用标准清洗策略
- **文档更新**: 完善各表的字段映射和清洗规则说明

### v1.1 (配置统一优化)
- **配置一致性修复**: 统一使用table_configs中的unique_fields配置
- **字段映射优化**: 修正源表字段名与目标表字段名的映射关系
- **唯一性字段调整**: 从`['code', 'charge_item_code', 'name']`调整为`['code', 'central_code']`
- **源表字段映射**: `medListCodg` → `code`, `centCodg` → `central_code`
- **验证配置同步**: 更新VALIDATION_CONFIG使用源表字段名
- **数据过滤逻辑**: 使用table_configs配置进行动态字段映射和过滤

### v1.0 (初始版本)
- 南京医疗服务项目ETL框架
- 驼峰转下划线字段名转换
- additional_info字典存储
- 地理位置自动标识
- 数据清洗和验证逻辑
- table_config地理位置限制
