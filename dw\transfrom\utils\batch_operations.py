#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量数据库操作模块
提供高效的批量插入、更新、删除操作
"""

import logging
import time
import pandas as pd
from datetime import datetime
from .database_utils import DatabaseConnectionManager
from .data_normalization import values_are_different
from .data_cleaning import default_cleaner
from .performance_config import PerformanceConfig, performance_monitor, optimize_for_large_dataset

logger = logging.getLogger(__name__)


class BatchOperationManager:
    """
    批量操作管理器
    提供高效的批量数据库操作功能
    """

    def __init__(self, db_config=None, save_comparison_excel=True, output_dir=None, excel_save_operations=None, save_format='csv'):
        """
        初始化批量操作管理器

        Args:
            db_config (dict): 数据库配置，如果为None则使用默认配置
            save_comparison_excel (bool): 是否保存更新对比信息到Excel，默认True（向后兼容）
            output_dir (str): 输出目录路径，如果为None则自动检测执行文件目录
            excel_save_operations (list): 需要保存Excel的操作类型列表，可选值：['update', 'delete', 'insert']
                                        None表示使用save_comparison_excel参数控制，[]表示不保存任何Excel文件
            save_format (str): 保存格式，可选值：'csv', 'excel', 'both'，默认'csv'
        """
        self.connection_manager = DatabaseConnectionManager(db_config)
        self.db_config = db_config
        self.output_dir = output_dir
        self.save_format = save_format  # 新增保存格式控制

        # 处理Excel保存控制参数
        if excel_save_operations is not None:
            # 使用新的精细控制参数
            self.excel_save_operations = excel_save_operations
            self.save_comparison_excel = bool(excel_save_operations)  # 向后兼容
        else:
            # 使用旧的布尔参数（向后兼容）
            self.save_comparison_excel = save_comparison_excel
            self.excel_save_operations = ['update', 'delete', 'insert'] if save_comparison_excel else []

    def upsert_dataframe(self, df, target_table, unique_fields, batch_size=10000, target_where_clause=None):
        """
        对DataFrame执行高性能优化的upsert操作
        实现真正的全量查询后分批处理逻辑：
        1. 一次性查询出爬虫表与目标表的所有数据（支持WHERE条件过滤）
        2. 根据唯一索引判定出删除、更新、新增的数据集
        3. 对这三个结果集分批次处理目标表（删除→更新→新增）

        Args:
            df (pd.DataFrame): 要处理的数据（爬虫表数据）
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表，用于判断记录是否存在
            batch_size (int): 批处理大小，默认10000
            target_where_clause (str): 目标表数据过滤条件，可选

        Returns:
            dict: 操作统计信息
        """
        if df.empty:
            logger.warning("没有数据需要处理")
            return {'total': 0, 'inserted': 0, 'updated': 0, 'deleted': 0, 'errors': 0}

        # 验证唯一性字段是否存在
        missing_fields = [field for field in unique_fields if field not in df.columns]
        if missing_fields:
            raise ValueError(f"数据中缺少唯一性字段: {missing_fields}")

        data_size = len(df)
        logger.info(f"开始执行高性能upsert操作: {data_size:,} 条数据到表 {target_table}")
        logger.info(f"唯一性字段: {unique_fields}, 批处理大小: {batch_size}")

        # 性能优化：为中等及以上数据集启用优化设置
        if data_size > 10000:  # 降低阈值从50000到10000
            logger.info(f"检测到中等数据集({data_size:,}条)，启用性能优化...")
            optimize_for_large_dataset()

            # 应用内存优化
            original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
            logger.info(f"原始数据内存使用: {original_memory:.2f}MB")

            # 获取最优配置
            optimal_config = PerformanceConfig.get_optimal_config_for_data_size(data_size)
            batch_size = optimal_config.get('batch_size', batch_size)
            logger.info(f"使用优化批处理大小: {batch_size:,}")

        # 将所有nan统一转换为None，防止MySQL写入报错
        df = df.where(pd.notnull(df), None)

        # 使用全量查询后分批处理模式
        return self._process_full_query_batch_operation(df, target_table, unique_fields, batch_size, target_where_clause)

    def _get_all_target_table_data(self, cursor, target_table, where_clause=None):
        """
        一次性获取目标表的所有数据（支持WHERE条件过滤）

        Args:
            cursor: 数据库游标
            target_table (str): 目标表名
            where_clause (str): WHERE条件，可选

        Returns:
            pd.DataFrame: 目标表的数据
        """
        try:
            # 首先检查表是否存在以及是否有数据
            logger.info(f"开始查询目标表 {target_table} 的数据...")

            # 构建查询条件 - 确保where_clause不为空字符串
            where_condition = f" WHERE {where_clause}" if where_clause and where_clause.strip() else ""
            condition_desc = f" (条件: {where_clause})" if where_clause and where_clause.strip() else ""

            # 先查询表的记录数量
            count_query = f"SELECT COUNT(*) as count FROM `{target_table}`{where_condition}"
            cursor.execute(count_query)
            count_result = cursor.fetchone()
            record_count = count_result[0] if count_result else 0

            logger.info(f"目标表 {target_table}{condition_desc} 共有 {record_count} 条记录")

            if record_count == 0:
                logger.info(f"目标表{condition_desc}为空，跳过数据查询")
                # 获取表结构
                structure_query = f"SELECT * FROM `{target_table}` LIMIT 0"
                cursor.execute(structure_query)
                columns = [desc[0] for desc in cursor.description]
                return pd.DataFrame(columns=columns)

            # 如果有数据，则查询所有数据
            logger.info(f"开始获取目标表的 {record_count} 条数据{condition_desc}...")
            query = f"SELECT * FROM `{target_table}`{where_condition}"
            cursor.execute(query)

            # 转换为DataFrame
            columns = [desc[0] for desc in cursor.description]
            results = cursor.fetchall()

            target_df = pd.DataFrame(results, columns=columns)
            logger.info(f"成功获取目标表数据: {len(target_df)} 条记录")
            return target_df

        except Exception as e:
            logger.error(f"获取目标表所有数据失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            # 如果查询失败，返回空DataFrame，让后续逻辑按新增处理
            return pd.DataFrame()

    def _classify_data_changes(self, source_df, target_df, unique_fields, target_table=None):
        """
        高性能数据比对分类 - 使用向量化操作替代逐行遍历
        性能提升：65万数据从几分钟优化到几秒钟

        Args:
            source_df: 源数据DataFrame
            target_df: 目标数据DataFrame
            unique_fields: 唯一性字段列表
            target_table: 目标表名，用于生成对比信息
        """
        logger.info("开始高性能数据比对分类...")
        start_time = time.time()

        # 如果目标表为空，所有源数据都是新增
        if target_df.empty:
            logger.info(f"目标表为空，所有 {len(source_df)} 条数据标记为新增")
            to_insert = [row for _, row in source_df.iterrows()]
            return to_insert, [], []

        # 使用向量化操作创建唯一键
        source_df_copy = source_df.copy()
        target_df_copy = target_df.copy()

        # 创建复合唯一键列
        source_df_copy['__unique_key__'] = self._create_vectorized_unique_key(source_df_copy, unique_fields)
        target_df_copy['__unique_key__'] = self._create_vectorized_unique_key(target_df_copy, unique_fields)

        # 使用pandas merge进行高效比对
        logger.info("执行向量化数据合并...")

        # 1. 找出新增数据（源数据中有，目标数据中没有）
        source_keys = set(source_df_copy['__unique_key__'])
        target_keys = set(target_df_copy['__unique_key__'])

        insert_keys = source_keys - target_keys
        delete_keys = target_keys - source_keys
        common_keys = source_keys & target_keys

        logger.info(f"键值比对完成: 新增 {len(insert_keys)} 条, 删除 {len(delete_keys)} 条, 共同 {len(common_keys)} 条")

        # 2. 获取新增和删除数据
        to_insert = source_df_copy[source_df_copy['__unique_key__'].isin(insert_keys)].drop('__unique_key__', axis=1)
        to_delete = target_df_copy[target_df_copy['__unique_key__'].isin(delete_keys)].drop('__unique_key__', axis=1)

        # 3. 对共同键值进行更新检查（使用向量化比较）
        to_update, update_comparison_data = self._vectorized_update_check(
            source_df_copy, target_df_copy, common_keys, unique_fields
        )

        # 转换为列表格式（保持与原接口兼容）
        to_insert_list = [row for _, row in to_insert.iterrows()]
        to_update_list = [row for _, row in to_update.iterrows()]
        to_delete_list = [row for _, row in to_delete.iterrows()]

        # 保存所有操作对比信息（根据excel_save_operations配置）
        if self.excel_save_operations:
            # 合并所有操作的对比信息
            all_comparison_data = []

            # 添加更新对比信息
            if 'update' in self.excel_save_operations and update_comparison_data:
                all_comparison_data.extend(update_comparison_data)

            # 添加删除记录信息
            if 'delete' in self.excel_save_operations:
                delete_comparison_data = self._create_delete_comparison_data(to_delete_list, target_table, unique_fields)
                if delete_comparison_data:
                    all_comparison_data.extend(delete_comparison_data)

            # 添加新增记录信息
            if 'insert' in self.excel_save_operations:
                insert_comparison_data = self._create_insert_comparison_data(to_insert_list, target_table)
                if insert_comparison_data:
                    all_comparison_data.extend(insert_comparison_data)

            # 保存合并后的对比信息
            if all_comparison_data:
                self._save_update_comparison_to_excel(all_comparison_data)
        else:
            # 统计信息（Excel保存已禁用）
            total_changes = len(update_comparison_data) if update_comparison_data else 0
            total_changes += len(to_delete_list) + len(to_insert_list)
            if total_changes > 0:
                logger.info(f"检测到 {total_changes} 条操作记录（更新:{len(update_comparison_data) if update_comparison_data else 0}, 删除:{len(to_delete_list)}, 新增:{len(to_insert_list)}）（Excel保存已禁用）")

        elapsed_time = time.time() - start_time
        logger.info(f"高性能数据比对完成: 新增 {len(to_insert_list)} 条, 更新 {len(to_update_list)} 条, "
                   f"删除 {len(to_delete_list)} 条, 耗时 {elapsed_time:.2f} 秒")

        return to_insert_list, to_update_list, to_delete_list

    def _create_vectorized_unique_key(self, df, unique_fields):
        """
        使用向量化操作创建唯一键
        比逐行遍历快100倍以上
        """
        # 处理NaN值，转换为字符串
        key_parts = []
        for field in unique_fields:
            if field in df.columns:
                # 将NaN转换为空字符串，其他值转换为字符串
                field_values = df[field].fillna('').astype(str)
                key_parts.append(field_values)
            else:
                # 如果字段不存在，用空字符串填充
                key_parts.append(pd.Series([''] * len(df), index=df.index))

        # 使用分隔符连接所有字段值
        unique_key = key_parts[0]
        for part in key_parts[1:]:
            unique_key = unique_key + '|' + part

        return unique_key

    def _vectorized_update_check(self, source_df, target_df, common_keys, unique_fields):
        """
        使用向量化操作检查更新
        比逐行比较快50倍以上
        """
        logger.info(f"开始向量化更新检查，共 {len(common_keys)} 条记录...")
        start_time = time.time()

        if not common_keys:
            return pd.DataFrame(), []

        # 筛选出需要比较的数据
        source_common = source_df[source_df['__unique_key__'].isin(common_keys)].copy()
        target_common = target_df[target_df['__unique_key__'].isin(common_keys)].copy()

        # 按唯一键排序，确保对应关系正确
        source_common = source_common.sort_values('__unique_key__').reset_index(drop=True)
        target_common = target_common.sort_values('__unique_key__').reset_index(drop=True)

        # 获取需要比较的字段（排除唯一性字段和临时字段）
        compare_fields = [col for col in source_common.columns
                         if col not in unique_fields and col != '__unique_key__']

        # 确保两个DataFrame有相同的列
        common_compare_fields = [col for col in compare_fields if col in target_common.columns]

        if not common_compare_fields:
            logger.info("没有可比较的字段，跳过更新检查")
            return pd.DataFrame(), []

        # 使用向量化比较（确保索引从0开始）
        needs_update_mask = pd.Series([False] * len(source_common), index=range(len(source_common)))
        update_comparison_data = []

        # 分批处理比较字段，避免内存过大
        batch_size = 10  # 每次比较10个字段
        field_batches = [common_compare_fields[i:i + batch_size]
                        for i in range(0, len(common_compare_fields), batch_size)]

        for field_batch in field_batches:
            for field in field_batch:
                # 获取原始值（不填充空字符串）
                source_values = source_common[field].reset_index(drop=True)
                target_values = target_common[field].reset_index(drop=True)

                # 使用智能比较逻辑进行向量化比较（传递字段名）
                field_different = self._vectorized_smart_compare(source_values, target_values, field)
                needs_update_mask |= field_different

                # 收集变更详情（仅对有差异的记录）
                if field_different.any():
                    different_indices = field_different[field_different].index
                    for idx in different_indices:
                        source_val = source_values.iloc[idx]
                        target_val = target_values.iloc[idx]

                        # 再次验证是否真的不同（使用精确比较，传递字段名）
                        if values_are_different(source_val, target_val, field):
                            # 获取唯一键信息
                            unique_key_info = {}
                            for unique_field in unique_fields:
                                if unique_field in source_common.columns:
                                    unique_key_info[unique_field] = str(source_common[unique_field].iloc[idx])

                            comparison_record = {
                                '字段名': field,
                                '爬虫数据': str(source_val) if pd.notna(source_val) else '',
                                '目标数据': str(target_val) if pd.notna(target_val) else '',
                                '变更类型': '更新'
                            }
                            comparison_record.update(unique_key_info)
                            update_comparison_data.append(comparison_record)

        # 获取需要更新的记录
        to_update = source_common[needs_update_mask].drop('__unique_key__', axis=1)

        elapsed_time = time.time() - start_time
        logger.info(f"向量化更新检查完成: 需要更新 {len(to_update)} 条, 无需更新 {len(source_common) - len(to_update)} 条, "
                   f"耗时 {elapsed_time:.2f} 秒")

        return to_update, update_comparison_data

    def _vectorized_smart_compare(self, source_values, target_values, field_name=None):
        """
        智能向量化比较，处理数值类型的比较问题

        Args:
            source_values (pd.Series): 源数据列
            target_values (pd.Series): 目标数据列
            field_name (str): 字段名，用于字段感知比较

        Returns:
            pd.Series: 布尔值序列，True表示不同
        """
        # 转换为numpy数组来完全避免pandas索引问题
        import numpy as np

        # 获取原始值并转换为numpy数组
        source_array = source_values.values
        target_array = target_values.values

        # 检查长度是否一致，如果不一致则进行调整
        if len(source_array) != len(target_array):
            logger.warning(f"字段 {field_name} 的源数据长度 ({len(source_array)}) 与目标数据长度 ({len(target_array)}) 不一致")
            # 取较短的长度，避免索引越界
            min_length = min(len(source_array), len(target_array))
            source_array = source_array[:min_length]
            target_array = target_array[:min_length]

        # 处理NaN值，转换为字符串
        source_str_array = np.array([str(x) if pd.notna(x) else '' for x in source_array])
        target_str_array = np.array([str(x) if pd.notna(x) else '' for x in target_array])

        # 进行字符串比较
        string_different_array = (source_str_array != target_str_array)

        # 转换回pandas Series，使用连续索引
        string_different = pd.Series(string_different_array, index=range(len(string_different_array)))

        if not string_different.any():
            # 如果字符串比较都相同，直接返回
            return string_different

        # 对于可能不同的值，进行智能比较
        result = pd.Series([False] * len(source_array), index=range(len(source_array)))

        # 获取可能不同的索引
        different_indices = string_different[string_different].index

        for idx in different_indices:
            source_val = source_array[idx] if idx < len(source_array) else None
            target_val = target_array[idx] if idx < len(target_array) else None

            # 使用精确的比较逻辑（传递字段名以支持字段感知比较）
            if values_are_different(source_val, target_val, field_name):
                result.iloc[idx] = True

        return result

    def _is_row_different(self, source_row, target_row, unique_fields):
        """
        比较两行数据是否不同（排除唯一性字段）
        只有当非唯一性字段的值确实不同时才返回True，避免不必要的更新
        """
        # 获取需要比较的字段（排除唯一性字段）
        compare_fields = [col for col in source_row.index if col not in unique_fields]

        for col in compare_fields:
            # 处理源数据值
            source_val = source_row[col]
            if pd.isna(source_val):
                source_val = None

            # 处理目标数据值
            if col in target_row.index:
                target_val = target_row[col]
                if pd.isna(target_val):
                    target_val = None
            else:
                # 目标表中没有这个字段，视为None
                target_val = None

            # 比较值是否不同（传递字段名以支持字段感知比较）
            if values_are_different(source_val, target_val, col):
                logger.debug(f"字段 '{col}' 值不同: 源='{source_val}' vs 目标='{target_val}'")
                return True

        return False

    def _collect_update_comparison(self, source_row, target_row, unique_fields):
        """
        收集更新对比信息，返回每个不同字段的对比数据

        Args:
            source_row: 源数据行
            target_row: 目标数据行
            unique_fields: 唯一性字段列表

        Returns:
            list: 包含对比信息的字典列表
        """
        comparison_data = []

        # 获取唯一性字段的值作为标识
        unique_key_info = {}
        for field in unique_fields:
            unique_key_info[field] = source_row[field] if pd.notna(source_row[field]) else None

        # 比较非唯一性字段
        compare_fields = [col for col in source_row.index if col not in unique_fields]

        for field in compare_fields:
            # 处理源数据值
            source_val = source_row[field] if pd.notna(source_row[field]) else None

            # 处理目标数据值
            if field in target_row.index:
                target_val = target_row[field] if pd.notna(target_row[field]) else None
            else:
                target_val = None

            # 如果值不同，记录对比信息（传递字段名以支持字段感知比较）
            if values_are_different(source_val, target_val, field):
                comparison_record = {
                    '字段名': field,
                    '爬虫数据': str(source_val) if source_val is not None else '',
                    '目标数据': str(target_val) if target_val is not None else '',
                    '变更类型': '更新'
                }

                # 添加唯一性字段信息
                for unique_field, unique_value in unique_key_info.items():
                    comparison_record[unique_field] = str(unique_value) if unique_value is not None else ''

                comparison_data.append(comparison_record)

        return comparison_data

    def _save_update_comparison_to_excel(self, comparison_data):
        """
        将更新对比信息保存到文件（优先Excel，失败则保存为CSV）

        Args:
            comparison_data: 对比信息列表
        """
        if not comparison_data:
            logger.info("没有更新对比数据需要保存")
            return

        try:
            import os

            # 创建DataFrame
            df_comparison = pd.DataFrame(comparison_data)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 确定输出目录
            if self.output_dir:
                # 使用用户指定的输出目录
                output_dir = self.output_dir
                logger.info(f"使用指定的输出目录: {output_dir}")
            else:
                # 自动检测执行文件的目录
                import inspect
                frame = inspect.currentframe()
                try:
                    # 向上查找调用栈，找到非utils目录的文件
                    caller_frame = frame
                    caller_file = None
                    while caller_frame:
                        caller_frame = caller_frame.f_back
                        if caller_frame and caller_frame.f_code.co_filename:
                            file_path = caller_frame.f_code.co_filename
                            # 如果不是utils目录下的文件，就使用这个文件的目录
                            if 'utils' not in file_path and file_path.endswith('.py'):
                                caller_file = file_path
                                break

                    if caller_file:
                        # 使用执行文件的目录
                        current_dir = os.path.dirname(os.path.abspath(caller_file))
                        output_dir = os.path.join(current_dir, "output")
                        logger.info(f"使用执行文件目录: {current_dir}")
                    else:
                        # 回退到当前工作目录
                        current_dir = os.getcwd()
                        output_dir = os.path.join(current_dir, "output")
                        logger.info(f"回退到当前工作目录: {current_dir}")
                finally:
                    del frame
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                logger.info(f"创建输出目录: {output_dir}")

            # 根据save_format参数决定保存格式
            if self.save_format in ['csv', 'both']:
                # 保存CSV格式
                csv_filename = f"数据更新对比_{timestamp}.csv"
                csv_filepath = os.path.join(output_dir, csv_filename)

                try:
                    df_comparison.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
                    logger.info(f"更新对比信息已保存到: {csv_filepath}")
                    logger.info(f"共保存 {len(df_comparison)} 条字段变更记录")
                    print(f"  [INFO] 更新对比信息已保存到: {csv_filepath}")
                    print(f"  [INFO] 共保存 {len(df_comparison)} 条字段变更记录")
                except Exception as csv_error:
                    logger.error(f"CSV保存失败: {csv_error}")
                    print(f"  [ERROR] CSV保存失败: {csv_error}")
                    if self.save_format == 'csv':  # 如果只要求CSV格式，失败则抛出异常
                        raise csv_error

            if self.save_format in ['excel', 'both']:
                # 保存Excel格式
                excel_filename = f"数据更新对比_{timestamp}.xlsx"
                excel_filepath = os.path.join(output_dir, excel_filename)

                try:
                    df_comparison.to_excel(excel_filepath, index=False, engine='openpyxl')
                    logger.info(f"Excel版本已保存到: {excel_filepath}")
                    print(f"  [INFO] Excel版本已保存到: {excel_filepath}")
                except Exception as excel_error:
                    logger.warning(f"Excel保存失败: {excel_error}")
                    print(f"  [WARNING] Excel保存失败: {excel_error}")
                    if self.save_format == 'excel':  # 如果只要求Excel格式，失败则抛出异常
                        raise excel_error

        except Exception as e:
            logger.error(f"保存更新对比信息完全失败: {str(e)}")
            print(f"  [ERROR] 保存更新对比信息失败: {str(e)}")

            # 最后的备用方案：在控制台输出前几条记录
            try:
                if comparison_data:
                    logger.info("作为备用方案，输出前5条变更记录到日志:")
                    print("  [INFO] 作为备用方案，输出前5条变更记录:")
                    for i, record in enumerate(comparison_data[:5]):
                        logger.info(f"  记录{i+1}: {record}")
                        print(f"    记录{i+1}: {record}")
                    if len(comparison_data) > 5:
                        logger.info(f"  ... 还有 {len(comparison_data)-5} 条记录未显示")
                        print(f"    ... 还有 {len(comparison_data)-5} 条记录未显示")
            except Exception as log_error:
                logger.error(f"连日志输出都失败了: {log_error}")
                print(f"  [ERROR] 连日志输出都失败了: {log_error}")

    def _process_full_query_batch_operation(self, df, target_table, unique_fields, batch_size, target_where_clause=None):
        """
        全量查询后分批处理模式：
        1. 一次性查询出目标表的所有数据（支持WHERE条件过滤）
        2. 与爬虫表数据进行比对，分类为：新增、更新、删除
        3. 对三个数据集分批次处理（删除→更新→新增）
        4. 在处理过程中不再进行查询操作，只有删除、更新、新增操作

        Args:
            df (pd.DataFrame): 爬虫表数据
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表
            batch_size (int): 批处理大小
            target_where_clause (str): 目标表数据过滤条件，可选

        Returns:
            dict: 操作统计信息
        """
        stats = {'total': len(df), 'inserted': 0, 'updated': 0, 'deleted': 0, 'errors': 0}
        conn = None
        cursor = None

        try:
            # 获取连接并优化配置
            conn = self.connection_manager.get_connection()
            cursor = conn.cursor()
            from .database_utils import optimize_mysql_session
            optimize_mysql_session(cursor)

            logger.info(f"开始全量查询后分批处理模式: {len(df)} 条爬虫数据")

            # 步骤1: 一次性获取目标表的所有数据（支持WHERE条件过滤）
            if target_where_clause:
                logger.info(f"步骤1: 一次性查询目标表数据 (条件: {target_where_clause})...")
                print(f"  [INFO] 步骤1: 开始查询目标表数据 (条件: {target_where_clause})...")
            else:
                logger.info("步骤1: 一次性查询目标表所有数据...")
                print("  [INFO] 步骤1: 开始查询目标表数据...")

            target_data = self._get_all_target_table_data(cursor, target_table, target_where_clause)
            logger.info(f"目标表现有数据: {len(target_data)} 条")
            print(f"  [SUCCESS] 目标表查询完成: {len(target_data)} 条记录")

            # 步骤2: 数据比对分类
            logger.info("步骤2: 进行数据比对分类...")
            print("  [INFO] 步骤2: 开始数据比对分类...")
            to_insert, to_update, to_delete = self._classify_data_changes(df, target_data, unique_fields, target_table)
            logger.info(f"数据分类完成: 新增 {len(to_insert)} 条, 更新 {len(to_update)} 条, 删除 {len(to_delete)} 条")
            print(f"  [SUCCESS] 数据分类完成: 新增 {len(to_insert)} 条, 更新 {len(to_update)} 条, 删除 {len(to_delete)} 条")

            # 步骤3: 数据清洗（在更新和新增前进行）
            if to_update or to_insert:
                logger.info("步骤3: 对更新和新增数据进行清洗...")
                print("  [INFO] 步骤3: 开始数据清洗...")
                to_update = default_cleaner.clean_data_for_operations(to_update)
                to_insert = default_cleaner.clean_data_for_operations(to_insert)
                print("  [SUCCESS] 数据清洗完成")

            # 步骤4: 分批次处理三个数据集（删除→更新→新增）
            print("  [INFO] 步骤4: 开始分批次处理数据...")

            # 4.1 分批删除操作
            if to_delete:
                logger.info(f"步骤4.1: 开始分批删除操作，共 {len(to_delete)} 条记录")
                print(f"  [INFO] 步骤4.1: 开始分批删除操作，共 {len(to_delete)} 条记录")
                delete_stats = self._batch_process_deletes(cursor, to_delete, target_table, unique_fields, batch_size)
                stats['deleted'] = delete_stats['deleted']
                stats['errors'] += delete_stats['errors']
                print(f"  [SUCCESS] 删除操作完成: 删除 {delete_stats['deleted']} 条")

            # 4.2 分批更新操作
            if to_update:
                logger.info(f"步骤4.2: 开始分批更新操作，共 {len(to_update)} 条记录")
                print(f"  [INFO] 步骤4.2: 开始分批更新操作，共 {len(to_update)} 条记录")
                update_stats = self._batch_process_updates(cursor, to_update, target_table, unique_fields, batch_size)
                stats['updated'] = update_stats['updated']
                stats['errors'] += update_stats['errors']
                print(f"  [SUCCESS] 更新操作完成: 更新 {update_stats['updated']} 条")

            # 关闭当前连接，为to_sql操作准备新连接
            try:
                if cursor:
                    cursor.close()
                    cursor = None  # 标记为已关闭
                if conn:
                    conn.close()
                    conn = None  # 标记为已关闭
            except Exception as close_error:
                logger.warning(f"关闭数据库连接时出错: {close_error}")

            # 4.3 分批新增操作（使用to_sql方法）
            if to_insert:
                logger.info(f"步骤4.3: 开始分批新增操作，共 {len(to_insert)} 条记录")
                print(f"  [INFO] 步骤4.3: 开始分批新增操作，共 {len(to_insert)} 条记录")
                insert_stats = self._batch_process_inserts(to_insert, target_table, batch_size)
                stats['inserted'] = insert_stats['inserted']
                stats['errors'] += insert_stats['errors']
                print(f"  [SUCCESS] 新增操作完成: 新增 {insert_stats['inserted']} 条")

            logger.info(f"全量查询后分批处理完成: 新增 {stats['inserted']} 条, "
                       f"更新 {stats['updated']} 条, 删除 {stats['deleted']} 条, "
                       f"错误 {stats['errors']} 条")

        except Exception as e:
            logger.error(f"全量查询后分批处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            stats['errors'] = len(df)
        finally:
            # 确保连接被正确关闭（只关闭未关闭的连接）
            try:
                if cursor is not None:
                    cursor.close()
            except Exception as e:
                logger.debug(f"关闭游标时出错（可能已关闭）: {str(e)}")

            try:
                if conn is not None:
                    conn.close()
            except Exception as e:
                logger.debug(f"关闭连接时出错（可能已关闭）: {str(e)}")

        return stats

    def _batch_process_deletes(self, cursor, to_delete, target_table, unique_fields, batch_size):
        """
        分批处理删除操作

        Args:
            cursor: 数据库游标
            to_delete (list): 要删除的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表
            batch_size (int): 批处理大小

        Returns:
            dict: 删除统计信息
        """
        import time

        stats = {'deleted': 0, 'errors': 0}

        if not to_delete:
            return stats

        # 开始计时
        start_time = time.time()
        print(f"  [DELETE] 开始删除操作: {len(to_delete)} 条记录")

        # 保存删除记录信息（如果启用）
        excel_start = time.time()
        if 'delete' in self.excel_save_operations:
            self._save_delete_records_to_excel(to_delete, target_table, unique_fields)
            excel_time = time.time() - excel_start
            print(f"  [TIMING] Excel保存耗时: {excel_time:.2f}秒")
        else:
            logger.info(f"检测到 {len(to_delete)} 条删除记录（Excel保存已禁用）")

        # 分批删除
        total_batches = (len(to_delete) + batch_size - 1) // batch_size
        print(f"  [BATCH] 分为 {total_batches} 批次处理，每批 {batch_size} 条")

        for batch_idx in range(total_batches):
            batch_start = time.time()
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(to_delete))
            batch_data = to_delete[start_idx:end_idx]

            logger.info(f"删除批次 {batch_idx + 1}/{total_batches}: 处理 {len(batch_data)} 条记录")

            batch_stats = self._batch_delete_records(cursor, batch_data, target_table, unique_fields)
            stats['deleted'] += batch_stats['deleted']
            stats['errors'] += batch_stats['errors']

            batch_time = time.time() - batch_start
            print(f"  [BATCH] 批次 {batch_idx + 1}/{total_batches} 完成: 删除 {batch_stats['deleted']} 条, 耗时 {batch_time:.2f}秒")

        # 计算耗时并输出
        elapsed_time = time.time() - start_time
        logger.info(f"删除操作完成: 删除 {stats['deleted']} 条记录, 耗时 {elapsed_time:.2f} 秒")
        print(f"  [TIMING] 删除操作总耗时: {elapsed_time:.2f}秒")

        return stats

    def _batch_process_updates(self, cursor, to_update, target_table, unique_fields, batch_size):
        """
        分批处理更新操作

        Args:
            cursor: 数据库游标
            to_update (list): 要更新的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表
            batch_size (int): 批处理大小

        Returns:
            dict: 更新统计信息
        """
        import time

        stats = {'updated': 0, 'errors': 0, 'method': 'unknown'}

        if not to_update:
            return stats

        # 开始计时
        start_time = time.time()
        print(f"  [UPDATE] 开始更新操作: {len(to_update)} 条记录")

        # 分批更新
        total_batches = (len(to_update) + batch_size - 1) // batch_size
        print(f"  [BATCH] 分为 {total_batches} 批次处理，每批 {batch_size} 条")

        for batch_idx in range(total_batches):
            batch_start = time.time()
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(to_update))
            batch_data = to_update[start_idx:end_idx]

            logger.info(f"更新批次 {batch_idx + 1}/{total_batches}: 处理 {len(batch_data)} 条记录")

            batch_stats = self._batch_update_records_optimized(cursor, batch_data, target_table, unique_fields)
            stats['updated'] += batch_stats['updated']
            stats['errors'] += batch_stats['errors']

            # 记录使用的方法
            if 'method' in batch_stats:
                stats['method'] = batch_stats['method']

            batch_time = time.time() - batch_start
            method_info = f" ({batch_stats.get('method', 'unknown')})" if 'method' in batch_stats else ""
            print(f"  [BATCH] 批次 {batch_idx + 1}/{total_batches} 完成: 更新 {batch_stats['updated']} 条{method_info}, 耗时 {batch_time:.2f}秒")

        # 计算耗时并输出
        elapsed_time = time.time() - start_time
        method_info = f" (方法: {stats.get('method', 'unknown')})" if 'method' in stats else ""
        logger.info(f"更新操作完成: 更新 {stats['updated']} 条记录, 耗时 {elapsed_time:.2f} 秒")
        print(f"  [TIMING] 更新操作总耗时: {elapsed_time:.2f}秒{method_info}")

        return stats

    def _batch_process_inserts(self, to_insert, target_table, batch_size):
        """
        分批处理插入操作

        Args:
            to_insert (list): 要插入的数据列表
            target_table (str): 目标表名
            batch_size (int): 批处理大小

        Returns:
            dict: 插入统计信息
        """
        import time

        stats = {'inserted': 0, 'errors': 0, 'method': 'unknown'}

        if not to_insert:
            return stats

        # 开始计时
        start_time = time.time()
        print(f"  [INSERT] 开始插入操作: {len(to_insert)} 条记录")

        # 保存新增记录信息（如果启用）
        excel_start = time.time()
        if 'insert' in self.excel_save_operations:
            self._save_insert_records_to_excel(to_insert, target_table)
            excel_time = time.time() - excel_start
            print(f"  [TIMING] Excel保存耗时: {excel_time:.2f}秒")
        else:
            logger.info(f"检测到 {len(to_insert)} 条新增记录（Excel保存已禁用）")

        # 分批插入
        total_batches = (len(to_insert) + batch_size - 1) // batch_size
        print(f"  [BATCH] 分为 {total_batches} 批次处理，每批 {batch_size} 条")

        for batch_idx in range(total_batches):
            batch_start = time.time()
            start_idx = batch_idx * batch_size
            end_idx = min((batch_idx + 1) * batch_size, len(to_insert))
            batch_data = to_insert[start_idx:end_idx]

            logger.info(f"插入批次 {batch_idx + 1}/{total_batches}: 处理 {len(batch_data)} 条记录")

            batch_stats = self._batch_insert_with_tosql(batch_data, target_table)
            stats['inserted'] += batch_stats['inserted']
            stats['errors'] += batch_stats['errors']

            # 记录使用的方法
            if 'method' in batch_stats:
                stats['method'] = batch_stats['method']

            batch_time = time.time() - batch_start
            method_info = f" ({batch_stats.get('method', 'unknown')})" if 'method' in batch_stats else ""
            print(f"  [BATCH] 批次 {batch_idx + 1}/{total_batches} 完成: 插入 {batch_stats['inserted']} 条{method_info}, 耗时 {batch_time:.2f}秒")

        # 计算耗时并输出
        elapsed_time = time.time() - start_time
        method_info = f" (方法: {stats.get('method', 'unknown')})" if 'method' in stats else ""
        logger.info(f"新增操作完成: 新增 {stats['inserted']} 条记录, 耗时 {elapsed_time:.2f} 秒")
        print(f"  [TIMING] 新增操作总耗时: {elapsed_time:.2f}秒{method_info}")

        return stats

    def _save_delete_records_to_excel(self, to_delete, target_table, unique_fields):
        """
        将删除记录信息保存到文件（优先Excel，失败则保存为CSV）

        Args:
            to_delete: 删除记录列表
            target_table: 目标表名
            unique_fields: 唯一性字段列表
        """
        if not to_delete:
            logger.info("没有删除记录需要保存")
            return

        try:
            import os
            from datetime import datetime

            # 创建删除记录数据
            delete_records = []
            for record in to_delete:
                delete_record = {
                    '操作类型': '删除',
                    '目标表': target_table
                }

                # 添加唯一性字段信息
                for unique_field in unique_fields:
                    if hasattr(record, unique_field):
                        delete_record[unique_field] = str(getattr(record, unique_field)) if getattr(record, unique_field) is not None else ''
                    elif isinstance(record, dict) and unique_field in record:
                        delete_record[unique_field] = str(record[unique_field]) if record[unique_field] is not None else ''

                # 添加所有字段信息（用于记录被删除的完整数据）
                if hasattr(record, 'to_dict'):
                    record_dict = record.to_dict()
                elif isinstance(record, dict):
                    record_dict = record
                else:
                    # 如果是pandas Series，转换为字典
                    record_dict = record.to_dict() if hasattr(record, 'to_dict') else {}

                for field, value in record_dict.items():
                    if field not in delete_record:  # 避免重复添加唯一性字段
                        delete_record[field] = str(value) if value is not None else ''

                delete_records.append(delete_record)

            # 创建DataFrame
            df_delete = pd.DataFrame(delete_records)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 确定输出目录
            if self.output_dir:
                output_dir = self.output_dir
            else:
                # 自动检测执行文件目录
                import inspect
                frame = None
                try:
                    # 获取调用栈，找到执行文件
                    for frame_info in inspect.stack():
                        frame = frame_info.frame
                        caller_file = frame.f_globals.get('__file__')
                        if caller_file and not caller_file.endswith(('batch_operations.py', 'etl_base.py')):
                            break

                    if caller_file:
                        # 使用执行文件的目录
                        current_dir = os.path.dirname(os.path.abspath(caller_file))
                        output_dir = os.path.join(current_dir, "output")
                        logger.info(f"使用执行文件目录: {current_dir}")
                    else:
                        # 回退到当前工作目录
                        current_dir = os.getcwd()
                        output_dir = os.path.join(current_dir, "output")
                        logger.info(f"回退到当前工作目录: {current_dir}")
                finally:
                    del frame

            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                logger.info(f"创建输出目录: {output_dir}")

            # 根据save_format参数决定保存格式
            if self.save_format in ['csv', 'both']:
                # 保存CSV格式
                csv_filename = f"数据删除记录_{target_table}_{timestamp}.csv"
                csv_filepath = os.path.join(output_dir, csv_filename)

                try:
                    df_delete.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
                    logger.info(f"删除记录信息已保存到: {csv_filepath}")
                    logger.info(f"共保存 {len(df_delete)} 条删除记录")
                    print(f"  [INFO] 删除记录信息已保存到: {csv_filepath}")
                    print(f"  [INFO] 共保存 {len(df_delete)} 条删除记录")
                except Exception as csv_error:
                    logger.error(f"CSV保存失败: {csv_error}")
                    print(f"  [ERROR] CSV保存失败: {csv_error}")
                    if self.save_format == 'csv':
                        raise csv_error

            if self.save_format in ['excel', 'both']:
                # 保存Excel格式
                excel_filename = f"数据删除记录_{target_table}_{timestamp}.xlsx"
                excel_filepath = os.path.join(output_dir, excel_filename)

                try:
                    df_delete.to_excel(excel_filepath, index=False, engine='openpyxl')
                    logger.info(f"Excel版本已保存到: {excel_filepath}")
                    print(f"  [INFO] Excel版本已保存到: {excel_filepath}")
                except Exception as excel_error:
                    logger.warning(f"Excel保存失败: {excel_error}")
                    print(f"  [WARNING] Excel保存失败: {excel_error}")
                    if self.save_format == 'excel':
                        raise excel_error

        except Exception as e:
            logger.error(f"保存删除记录信息完全失败: {str(e)}")
            print(f"  [ERROR] 保存删除记录信息失败: {str(e)}")

            # 最后的备用方案：在控制台输出前几条记录
            try:
                if to_delete:
                    logger.info("作为备用方案，输出前5条删除记录到日志:")
                    print("  [INFO] 作为备用方案，输出前5条删除记录:")
                    for i, record in enumerate(to_delete[:5]):
                        logger.info(f"  记录{i+1}: {record}")
                        print(f"    记录{i+1}: {record}")
                    if len(to_delete) > 5:
                        logger.info(f"  ... 还有 {len(to_delete)-5} 条记录未显示")
                        print(f"    ... 还有 {len(to_delete)-5} 条记录未显示")
            except Exception as log_error:
                logger.error(f"连日志输出都失败了: {log_error}")
                print(f"  [ERROR] 连日志输出都失败了: {log_error}")

    def _save_insert_records_to_excel(self, to_insert, target_table):
        """
        将新增记录信息保存到文件（优先Excel，失败则保存为CSV）

        Args:
            to_insert: 新增记录列表
            target_table: 目标表名
        """
        if not to_insert:
            logger.info("没有新增记录需要保存")
            return

        try:
            import os
            from datetime import datetime

            # 创建新增记录数据
            insert_records = []
            for record in to_insert:
                insert_record = {
                    '操作类型': '新增',
                    '目标表': target_table
                }

                # 添加所有字段信息
                if hasattr(record, 'to_dict'):
                    record_dict = record.to_dict()
                elif isinstance(record, dict):
                    record_dict = record
                else:
                    # 如果是pandas Series，转换为字典
                    record_dict = record.to_dict() if hasattr(record, 'to_dict') else {}

                for field, value in record_dict.items():
                    insert_record[field] = str(value) if value is not None else ''

                insert_records.append(insert_record)

            # 创建DataFrame
            df_insert = pd.DataFrame(insert_records)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 确定输出目录
            if self.output_dir:
                output_dir = self.output_dir
            else:
                # 自动检测执行文件目录
                import inspect
                frame = None
                try:
                    # 获取调用栈，找到执行文件
                    for frame_info in inspect.stack():
                        frame = frame_info.frame
                        caller_file = frame.f_globals.get('__file__')
                        if caller_file and not caller_file.endswith(('batch_operations.py', 'etl_base.py')):
                            break

                    if caller_file:
                        # 使用执行文件的目录
                        current_dir = os.path.dirname(os.path.abspath(caller_file))
                        output_dir = os.path.join(current_dir, "output")
                        logger.info(f"使用执行文件目录: {current_dir}")
                    else:
                        # 回退到当前工作目录
                        current_dir = os.getcwd()
                        output_dir = os.path.join(current_dir, "output")
                        logger.info(f"回退到当前工作目录: {current_dir}")
                finally:
                    del frame

            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                logger.info(f"创建输出目录: {output_dir}")

            # 根据save_format参数决定保存格式
            if self.save_format in ['csv', 'both']:
                # 保存CSV格式
                csv_filename = f"数据新增记录_{target_table}_{timestamp}.csv"
                csv_filepath = os.path.join(output_dir, csv_filename)

                try:
                    df_insert.to_csv(csv_filepath, index=False, encoding='utf-8-sig')
                    logger.info(f"新增记录信息已保存到: {csv_filepath}")
                    logger.info(f"共保存 {len(df_insert)} 条新增记录")
                    print(f"  [INFO] 新增记录信息已保存到: {csv_filepath}")
                    print(f"  [INFO] 共保存 {len(df_insert)} 条新增记录")
                except Exception as csv_error:
                    logger.error(f"CSV保存失败: {csv_error}")
                    print(f"  [ERROR] CSV保存失败: {csv_error}")
                    if self.save_format == 'csv':
                        raise csv_error

            if self.save_format in ['excel', 'both']:
                # 保存Excel格式
                excel_filename = f"数据新增记录_{target_table}_{timestamp}.xlsx"
                excel_filepath = os.path.join(output_dir, excel_filename)

                try:
                    df_insert.to_excel(excel_filepath, index=False, engine='openpyxl')
                    logger.info(f"Excel版本已保存到: {excel_filepath}")
                    print(f"  [INFO] Excel版本已保存到: {excel_filepath}")
                except Exception as excel_error:
                    logger.warning(f"Excel保存失败: {excel_error}")
                    print(f"  [WARNING] Excel保存失败: {excel_error}")
                    if self.save_format == 'excel':
                        raise excel_error

        except Exception as e:
            logger.error(f"保存新增记录信息完全失败: {str(e)}")
            print(f"  [ERROR] 保存新增记录信息失败: {str(e)}")

            # 最后的备用方案：在控制台输出前几条记录
            try:
                if to_insert:
                    logger.info("作为备用方案，输出前5条新增记录到日志:")
                    print("  [INFO] 作为备用方案，输出前5条新增记录:")
                    for i, record in enumerate(to_insert[:5]):
                        logger.info(f"  记录{i+1}: {record}")
                        print(f"    记录{i+1}: {record}")
                    if len(to_insert) > 5:
                        logger.info(f"  ... 还有 {len(to_insert)-5} 条记录未显示")
                        print(f"    ... 还有 {len(to_insert)-5} 条记录未显示")
            except Exception as log_error:
                logger.error(f"连日志输出都失败了: {log_error}")
                print(f"  [ERROR] 连日志输出都失败了: {log_error}")

    def _create_delete_comparison_data(self, to_delete_list, target_table, unique_fields):
        """
        创建删除记录的对比信息

        Args:
            to_delete_list: 要删除的记录列表
            target_table: 目标表名
            unique_fields: 唯一性字段列表

        Returns:
            list: 删除记录的对比信息列表
        """
        if not to_delete_list:
            return []

        comparison_data = []
        for record in to_delete_list:
            # 获取唯一性字段信息
            unique_key_info = {}
            for unique_field in unique_fields:
                if hasattr(record, unique_field):
                    unique_key_info[unique_field] = str(getattr(record, unique_field)) if getattr(record, unique_field) is not None else ''
                elif isinstance(record, dict) and unique_field in record:
                    unique_key_info[unique_field] = str(record[unique_field]) if record[unique_field] is not None else ''
                elif hasattr(record, 'get') and record.get(unique_field) is not None:
                    unique_key_info[unique_field] = str(record.get(unique_field))
                else:
                    unique_key_info[unique_field] = ''

            comparison_record = {
                '字段名': '整条记录',
                '爬虫数据': '',
                '目标数据': '已删除',
                '变更类型': '删除',
                '目标表': target_table
            }
            comparison_record.update(unique_key_info)
            comparison_data.append(comparison_record)

        return comparison_data

    def _create_insert_comparison_data(self, to_insert_list, target_table):
        """
        创建新增记录的对比信息

        Args:
            to_insert_list: 要新增的记录列表
            target_table: 目标表名

        Returns:
            list: 新增记录的对比信息列表
        """
        if not to_insert_list:
            return []

        comparison_data = []
        for record in to_insert_list:
            comparison_record = {
                '字段名': '整条记录',
                '爬虫数据': '新增记录',
                '目标数据': '',
                '变更类型': '新增',
                '目标表': target_table
            }

            # 添加记录的主要字段信息（用于识别）
            if hasattr(record, 'to_dict'):
                record_dict = record.to_dict()
            elif isinstance(record, dict):
                record_dict = record
            else:
                # 如果是pandas Series，转换为字典
                record_dict = record.to_dict() if hasattr(record, 'to_dict') else {}

            # 添加前几个字段作为标识信息
            field_count = 0
            for field, value in record_dict.items():
                if field_count < 3:  # 只添加前3个字段作为标识
                    comparison_record[f'字段_{field}'] = str(value) if value is not None else ''
                    field_count += 1
                else:
                    break

            comparison_data.append(comparison_record)

        return comparison_data


# 在模块加载时自动添加实现方法
try:
    from .batch_operations_impl import add_implementation_methods
    add_implementation_methods(BatchOperationManager)
except ImportError as e:
    logger.warning(f"无法导入批量操作实现方法: {e}")
    # 如果导入失败，添加简单的占位方法
    def _batch_delete_records(self, cursor, batch_data, target_table, unique_fields):
        logger.error("批量删除方法未实现")
        return {'deleted': 0, 'errors': len(batch_data)}

    def _batch_update_records_optimized(self, cursor, batch_data, target_table, unique_fields):
        logger.error("批量更新方法未实现")
        return {'updated': 0, 'errors': len(batch_data)}

    def _batch_insert_with_tosql(self, batch_data, target_table):
        logger.error("批量插入方法未实现")
        return {'inserted': 0, 'errors': len(batch_data)}

    # 添加占位方法到类
    BatchOperationManager._batch_delete_records = _batch_delete_records
    BatchOperationManager._batch_update_records_optimized = _batch_update_records_optimized
    BatchOperationManager._batch_insert_with_tosql = _batch_insert_with_tosql
