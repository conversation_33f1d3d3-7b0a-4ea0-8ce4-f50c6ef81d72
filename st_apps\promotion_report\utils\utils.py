import os
import re
import shutil
import sys
import json

import numpy as np
import requests
import pandas as pd


# 常用函数


def is_windows():
    return sys.platform == 'win32'


def replace_using_dict(text, replacement_pairs):
    """
    用于根据提供的替换字典替换文本中的内容
    :param text: 需要替换的文本
    :param replacement_pairs: 替换字典
    :return:
    """
    for old, new in replacement_pairs.items():
        text = text.replace(old, new)
    return text


def sum_or_combine(serie, name='合计'):
    """
    对数值列求和，对字符串列返回'合计'
    :param serie: series对象
    :param name: 非数值列的显示名称
    :return:
    """
    if pd.api.types.is_numeric_dtype(serie):
        return serie.sum()
    else:
        return name


def sum_or_combine_v1(df, name='合计', drop_columns=None, drop_statistic=None, default_statistic='sum'):
    """
    对DataFrame的数值列求和或计算均值，对字符串列返回'合计'。
    非数值列或指定剔除的列将返回None，除非在drop_statistic中指定了统计方法。

    :param df: DataFrame对象
    :param drop_columns: 需要从统计中剔除的列的列表
    :param drop_statistic: 一个字典，指定需要计算合计或均值的字段，键为列名，值为'sum'或'mean'
    :param default_statistic: 默认的统计方法，当字段没有在drop_statistic中指定时使用，'sum'或'mean'
    :return: 一个字典，包含统计结果
    """
    if drop_columns is None:
        drop_columns = []
    if drop_statistic is None:
        drop_statistic = {}

    result = {}
    for column in df.columns:
        if column in drop_columns:
            # 指定剔除的列返回None
            result[column] = None
        elif pd.api.types.is_numeric_dtype(df[column]):
            # 数值列根据drop_statistic字典或default_statistic参数求和或计算均值
            statistic_method = drop_statistic.get(column, default_statistic)
            if statistic_method == 'sum':
                result[column] = df[column].sum()
            elif statistic_method == 'mean':
                result[column] = df[column].mean()
            else:
                raise ValueError(f"Unsupported statistic method: {statistic_method}")
        else:
            # 非数值列返回'合计'
            result[column] = name

    return result


def join_list_to_string(lst, quote_char="'"):
    """
    将列表转换为字符串，并用指定字符包裹
    :param lst: 需要转化的list
    :param quote_char: 用什么字符包裹
    :return:
    """
    return f"{','.join([quote_char + item + quote_char for item in lst if item != ''])}"


def amount_group(df, amount_col, bins=None, labels=None):
    """
    按金额分组
    :param df:需要处理的dataframe
    :param amount_col:需要进行分组的列名
    :param bins:分组的金额列表
    :param labels:分组的标签列表
    :return:
    """
    if labels is None:
        labels = ['0-5000', '5000-10000', '10000-20000', '20000-50000', '50000-100000', '100000-200000', '200000以上']
    if bins is None:
        bins = [0, 5000, 10000, 20000, 50000, 100000, 200000, float('inf')]
    df['amount_group'] = pd.cut(df[amount_col], bins=bins, labels=labels, right=False)
    return df


def age_group(df, age_col, bins=None, labels=None):
    """
    按年龄分组
    :param df:需要处理的dataframe
    :param age_col:需要进行分组的列名
    :param bins:分组的年龄列表
    :param labels:分组的标签列表
    :return:
    """
    if labels is None:
        labels = ['0-17岁', '18-40岁', '41-65岁', '66岁及以上']
    if bins is None:
        bins = [0, 18, 41, 66, float('inf')]
    df['age_group'] = pd.cut(df[age_col], bins=bins, labels=labels, right=False)
    return df


def move_numbered_json_files(target_directory, source_directory=os.getcwd()):
    """
    将源目录下的纯数字命名的JSON文件移动到目标目录。
    :param target_directory: 目标目录的路径
    :param source_directory: 源目录的路径,默认为根目录
    """
    for filename in os.listdir(source_directory):
        if re.match(r'\d+\.json', filename):
            print(filename)
            shutil.move(os.path.join(source_directory, filename), target_directory)


def match_type(name, rules_df):
    """
    根据名称和规则数据框匹配类型(匹配的文件限制了名称为name，关键字为key_words的规则)
    参数:
    name: 待匹配的名称
    rules_df: 包含匹配规则的数据框
    返回:
    匹配到的类型名称，若未匹配到则返回原名称
    """
    for rule_index, rule_row in rules_df.iterrows():
        keywords = rule_row['key_words'].split('|')  # 将关键字按竖线分隔为列表
        for keyword in keywords:
            pattern = re.compile(keyword)  # 使用正则表达式进行匹配
            if pattern.search(name):  # 若匹配成功则返回规则中的名称
                return rule_row['name']
    return name  # 若未匹配到任何规则，则返回原名称

def round_values(record):
    """
    字典值保留两位小数
    :param record:
    :return:
    """
    return {k: round(v, 2) if isinstance(v, (int, float)) else v for k, v in record.items()}




def send_feishu_message(message, app_id='9133cc5a-d188-41b8-8ca4-8857e9d59a0b'):
    """
    发送消息到飞书机器人。
    参数:
    message -- 要发送的消息内容，格式为字典，包含消息类型和内容
    """
    # 消息内容编码
    msg = {
        "msg_type": "text",
        "content": {"text": message}
    }
    msg_encode = json.dumps(msg, ensure_ascii=True).encode("utf-8")

    # 请求头设置
    headers = {
        "Content-type": "application/json",
        "charset": "utf-8"
    }

    # 发送POST请求
    response = requests.post(
        url="https://open.feishu.cn/open-apis/bot/v2/hook/%s" % app_id,
        data=msg_encode,
        headers=headers
    )
    return response.status_code


def df_to_dict(df, key_column, value_columns):
    """
    将DataFrame转换为字典

    参数:
    - df: 输入的DataFrame
    - key_column: 用作字典键的列名
    - value_columns: 用作字典值的列名列表

    返回:
    - 一个字典，其中键是key_column指定的列的值，值是由value_columns指定的列组成的字典
    """
    # 创建空字典
    dict_data = {}

    # 迭代DataFrame的每一行
    for _, row in df.iterrows():
        # 获取键
        key = row[key_column]

        # 如果键不在字典中，则添加一个空列表
        if key not in dict_data:
            dict_data[key] = []

        # 添加值
        value = {col: row[col] for col in value_columns}
        dict_data[key].append(value)

    return dict_data

# 定义阿拉伯数字到中文数字的映射
num_to_chinese = {
    0: '零',
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六',
    7: '七',
    8: '八',
    9: '九',
    10: '十',
    100: '百',
}

def number_to_chinese(num):
    '''
    将阿拉伯数字转换为中文数字
    :param num: 阿拉伯数字
    :return:
    '''
    if num < 1 or num > 999:
        return "超出范围"

    chinese_num = ''
    if num >= 100:
        chinese_num += num_to_chinese[num // 100] + '百'
        num %= 100

    if num >= 10:
        chinese_num += num_to_chinese[num // 10] + '十'
        num %= 10

    chinese_num += num_to_chinese[num]
    if (len(chinese_num) == 3 or len(chinese_num) == 2) and chinese_num[0] == '一' and chinese_num[1] == '十':
        chinese_num = chinese_num[1:]
    return chinese_num.strip('零')



def simplify_replace(df, column_name, replacements, default_value='其他'):
    """
    简化替换DataFrame中指定列的值，将所有其他值替换为"其他"。

    参数:
    df : pandas.DataFrame
        要处理的DataFrame。
    column_name : str
        要替换值的列名。
    replacements : dict
        一个字典，包含要替换的旧值到新值的映射。
    default_value : str, optional
        未匹配到的默认值。默认值为'其他'。
    返回:
    pandas.DataFrame
        替换后的DataFrame。
    """
    # 将指定的替换应用到列
    df[column_name] = df[column_name].replace(replacements)

    # 将所有剩余的值替换为"其他"
    df[column_name] = np.where(df[column_name].isin(replacements.values()), df[column_name], default_value)