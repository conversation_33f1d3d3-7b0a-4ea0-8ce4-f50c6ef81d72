import json

import streamlit as st
from streamlit_echarts import st_echarts
import datetime
import logging
import warnings
from decimal import Decimal
import copy
from pprint import pprint
from plotly.subplots import make_subplots
import plotly.graph_objects as go
import streamlit_antd_components as sac
from pandasql import sqldf
from plotly import express as px

import idna
import numpy as np
import pandas as pd
import pymysql
from pandasql import sqldf
from utils.st import query_sql, empty_line, text_write
from utils.utils import sum_or_combine, df_to_dict

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
# pd.set_option('display.max_rows', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        concat(
		LEFT ( ps.NAME, 5 ),
		'-',
	RIGHT ( ps.NAME,( LENGTH( ps.NAME )- 15 )/ 3 )) version,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        and ps.code not in ('ninghuibaoV1','ninghuibaoV2','ninghuibaoV3')
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)

    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code


def adjust_data(df, sale_end_date_prev):
    """
    调整数据，将12-31（销售截止）之后的数据纳入之前的数据
    :param df: 需要处理的数据
    :return:
    """
    df_out_period = df[df['date'] > sale_end_date_prev]
    df_in_period = df[df['date'] <= sale_end_date_prev]
    if df_out_period.empty:
        df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
        df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
        return df_in_period
    else:
        # 计算销售期之前的销量每期的占比，再将销售期外的销量加到销售期内
        df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
        df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
        return df_in_period


def get_seller_group_report_data(product_set_code):
    """
    获取保司上传的团单数据
    """
    df_department_info = CONNECTOR_DW.query(query_sql('SQL_GROUP_REPORT').format(product_set_code=product_set_code),
                                            show_spinner='查询中...', ttl=0)

    max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
    max_publish_time_df = df_department_info.loc[max_publish_time_indices]

    max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
        lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
    max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
    max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
    max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
    max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
    max_publish_time_df['version'] = max_publish_time_df['name'].apply(
        lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
    max_publish_time_df = max_publish_time_df[max_publish_time_df['version'] == '合计'][['short_name', 'value']].rename(
        columns={'value': 'group_count', 'short_name': 'name'})
    number_sum = max_publish_time_df.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T

    max_publish_time_df = pd.concat([max_publish_time_df, number_sum], axis=0).reset_index(drop=True)
    return max_publish_time_df


def online_adjust_data(product_set_code_prev, sale_end_date_prev):
    """
    调整线上数据，将销售截止之后的数据纳入之前的数据，只计算个单。
    """
    # 获取上期的线上数据
    try:
        sql = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=product_set_code_prev,
            statistical_type='当期值', unit='单',
            freq='日', start_datetime='2000-01-01',
            end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        df = CONNECTOR_DW.query(sql, show_spinner='查询中...', ttl=0)
        df.rename(columns={'end_time': 'date'}, inplace=True)

        # 优化：只筛选一次，并使用映射来分配销售渠道
        channels = {
            '我的南京': '-销量-线上-我的南京-当期值',
            '公众号': '-销量-线上-公众号-当期值',
            '支付宝': '-销量-线上-支付宝-当期值',
            '合计': '-销量-线上-当期值'
        }

        df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
        for channel_name, filter_str in channels.items():
            channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
            channel_df['name'] = channel_name  # 添加销售渠道名称列
            adjust_channel_df = adjust_data(channel_df, sale_end_date_prev)  # 调整数据
            df_onlines = pd.concat([df_onlines, adjust_channel_df])  # 拼接数据
        df_onlines.sort_values(by=['name', 'date'], inplace=True)
        df_onlines.reset_index(drop=True, inplace=True)
        # if product_set_code_prev == 'ninghuibaoV4':
        #     # 日期对齐，往前面移动6天
        #     df_onlines['date'] = pd.to_datetime(df_onlines['date']) - datetime.timedelta(days=6)
        df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
        return df_onlines
    except Exception as e:
        print(f"Error occurred while processing online data: {e}")


def offline_adjust_data(product_set_code_prev, sale_end_date_prev):
    """
    调整线下数据，将12-31（销售截止）之后的数据纳入之前的数据，只计算个单
    :param df: 需要处理的数据
    :return:
    """
    # 提取SQL查询逻辑
    sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
        product_set_code=product_set_code_prev,
        statistical_type='当期值',
        unit='单',
        freq='日',
        start_datetime='2000-01-01',
        end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )

    try:
        # 使用上下文管理器确保连接正确关闭

        df = CONNECTOR_DW.query(sql_query, show_spinner='查询中...', ttl=0)
        df.rename(columns={'end_time': 'date'}, inplace=True)

        # 定义公司名称列表
        company_names = [
            ('-销量-线下-个单-中国人保-当期值', '中国人保'),
            ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
            ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
            ('-销量-线下-个单-中华联合-当期值', '中华联合'),
            ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
            ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
            ('-销量-线下-个单-太保产险-当期值', '太保产险'),
            ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
            ('-销量-线下-个单-中银保险-当期值', '中银保险'),
            ('-销量-线下-个单-当期值', '合计')
        ]

        # 循环处理每个公司数据
        dfs = []
        for pattern, name in company_names:
            filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
            adjusted_df = adjust_data(filtered_df, sale_end_date_prev)
            adjusted_df['name'] = name
            dfs.append(adjusted_df)

        # 合并所有公司数据
        df_offlines = pd.concat(dfs)
        df_offlines.sort_values(by=['name', 'date'], inplace=True)
        df_offlines.reset_index(drop=True, inplace=True)
        # if product_set_code_prev == 'ninghuibaoV4':
        #     # 日期对齐，往前面移动6天
        #     df_offlines['date'] = pd.to_datetime(df_offlines['date']) - datetime.timedelta(days=6)
        df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
        return df_offlines

    except Exception as e:
        # 异常处理
        print(f"Error occurred while processing data: {e}")


def online_daily_target(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev):
    """
    计算线上每日目标
    """
    sql = query_sql('SQL_DW_INDICATOR_DATA').format(
        product_set_code=product_set_code,
        statistical_type='当期值', unit='单',
        freq='日', start_datetime=sale_start_date,
        end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    df = CONNECTOR_DW.query(sql, show_spinner='查询中...', ttl=0)
    df.rename(columns={'end_time': 'date'}, inplace=True)
    # 优化：只筛选一次，并使用映射来分配销售渠道
    channels = {
        '我的南京': '-销量-线上-我的南京-当期值',
        '公众号': '-销量-线上-公众号-当期值',
        '支付宝': '-销量-线上-支付宝-当期值',
        '合计': '-销量-线上-当期值'
    }

    df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
    for channel_name, filter_str in channels.items():
        channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
        channel_df['name'] = channel_name  # 添加销售渠道名称列
        df_onlines = pd.concat([df_onlines, channel_df])  # 拼接数据
    df_onlines.sort_values(by=['name', 'date'], inplace=True)
    df_onlines.reset_index(drop=True, inplace=True)
    df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
    df_onlines['cumulative_count'] = df_onlines.groupby(['name'])['value'].cumsum()

    df_target_online = CONNECTOR_DW.query(
        "select name,short_name,target from public_target where type='online' and product_set_code='{product_set_code}'".format(
            product_set_code=product_set_code),
        show_spinner='查询中...', ttl=0)
    number_sum = df_target_online.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    # 保证合计在第一行
    df_target_online = pd.concat([number_sum, df_target_online], axis=0).reset_index(drop=True)
    # 获取去年的目标占比
    df_onlines_prev = online_adjust_data(product_set_code_prev, sale_end_date_prev)

    df_onlines_prev = pd.merge(df_onlines_prev, df_target_online, on='name', how='outer')
    df_onlines_prev['target_value_cumsum'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio_cumsum']
    df_onlines_prev['target_value'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio']
    df_onlines_prev = df_onlines_prev[['name', 'target', 'day', 'target_value', 'target_value_cumsum']]
    df_onlines_prev['target'] = df_onlines_prev['target'].astype(np.int64)
    df_onlines_prev.reset_index(drop=True, inplace=True)
    df_onlines.reset_index(drop=True, inplace=True)
    df = sqldf(
        "select a.*,b.target_value_cumsum ,ifnull(b.target_value,0) target_value,b.target  from df_onlines a left join df_onlines_prev b on a.name=b.name and a.day=b.day")
    df['target_value_cumsum'] = df.groupby('name')['target_value_cumsum'].transform(lambda x: x.ffill())

    # target 为空，根据name分组，取上一个值
    df['target'] = df['target'].fillna(method='ffill')
    df.fillna(0, inplace=True)
    df_last_record = df.groupby(['name'])['date'].transform('max')
    df_last_record = df[df['date'] == df_last_record]
    df_last_record = df_last_record[['name', 'cumulative_count', 'target']].rename(
        columns={'cumulative_count': '实际', 'target': '目标'})

    df.rename(columns={'cumulative_count': '实际(累计)', 'target_value_cumsum': '目标(累计)', 'value': '实际',
                       'target_value': '目标'}, inplace=True)
    return df, df_last_record


def offline_daily_target(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev):
    """
    计算线下每日目标
    """
    # 提取SQL查询逻辑
    sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
        product_set_code=product_set_code,
        statistical_type='当期值',
        unit='单',
        freq='日',
        start_datetime=sale_start_date,
        end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )

    # 使用上下文管理器确保连接正确关闭

    df = CONNECTOR_DW.query(sql_query, show_spinner='查询中...', ttl=0)
    df.rename(columns={'end_time': 'date'}, inplace=True)

    # 定义公司名称列表
    company_names = [
        ('-销量-线下-个单-中国人保-当期值', '中国人保'),
        ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
        ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
        ('-销量-线下-个单-中华联合-当期值', '中华联合'),
        ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
        ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
        ('-销量-线下-个单-太保产险-当期值', '太保产险'),
        ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
        ('-销量-线下-个单-中银保险-当期值', '中银保险'),
        ('-销量-线下-个单-当期值', '合计')
    ]

    # 循环处理每个公司数据
    dfs = []
    for pattern, name in company_names:
        filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
        filtered_df['name'] = name
        dfs.append(filtered_df)

    # 合并所有公司数据
    df_offlines = pd.concat(dfs)
    df_offlines.sort_values(by=['name', 'date'], inplace=True)
    df_offlines.reset_index(drop=True, inplace=True)
    df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
    df_offlines['cumulative_count'] = df_offlines.groupby(['name'])['value'].cumsum()

    # 获取今年的目标数据，计算出目标的每日分布与每日累计分布
    df_target_offline = CONNECTOR_DW.query(
        "select name,short_name,target from public_target where type='agent' and product_set_code='{product_set_code}'".format(
            product_set_code=product_set_code),
        show_spinner='查询中...', ttl=0)
    df_target_offline.rename(columns={'name': 'full_name', 'short_name': 'name'}, inplace=True)
    number_sum = df_target_offline.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    # 保证合计在第一行
    df_target_offline = pd.concat([number_sum, df_target_offline], axis=0).reset_index(drop=True)
    # # 团单数量是多少
    df_group = get_seller_group_report_data(product_set_code)
    df_target_offline = pd.merge(df_target_offline, df_group, on='name', how='left')
    df_target_offline['target'] = df_target_offline['target'] - df_target_offline['group_count']

    # 获取去年的目标占比
    df_offlines_prev = offline_adjust_data(product_set_code_prev, sale_end_date_prev)
    df_offlines_prev = pd.merge(df_offlines_prev, df_target_offline, on='name', how='left')
    df_offlines_prev['target_value_cumsum'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio_cumsum']
    df_offlines_prev['target_value'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio']
    df_offlines_prev = df_offlines_prev[
        ['full_name', 'name', 'day', 'target', 'target_value', 'target_value_cumsum']]
    df_offlines_prev['target'] = df_offlines_prev['target'].astype(np.int64)
    df_offlines_prev.reset_index(drop=True, inplace=True)
    df_offlines.reset_index(drop=True, inplace=True)
    df = sqldf(
        "select a.*,b.target_value_cumsum ,b.full_name,ifnull(b.target_value,0) target_value,b.target target from df_offlines a left join df_offlines_prev b on a.name=b.name and a.day=b.day")

    df['full_name'].fillna(df['name'], inplace=True)
    df['target_value_cumsum'] = df.groupby('name')['target_value_cumsum'].transform(lambda x: x.ffill())
    # target 为空，根据name分组，取上一个值
    df['target'] = df.groupby('name')['target'].transform(lambda x: x.bfill())
    df['target'] = df.groupby('name')['target'].transform(lambda x: x.ffill())
    df.fillna(0, inplace=True)

    # 根据name分组 取date最大的一条记录
    df_last_record = df.groupby(['name'])['date'].transform('max')
    df_last_record = df[df['date'] == df_last_record]
    df_last_record = df_last_record[['name', 'cumulative_count', 'target']].rename(
        columns={'cumulative_count': '实际', 'target': '目标'})

    df.rename(columns={'cumulative_count': '实际(累计)', 'target_value_cumsum': '目标(累计)', 'value': '实际',
                       'target_value': '目标'}, inplace=True)
    return df, df_last_record


def online(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev):
    text_write('线上完成情况')
    df, df_last_record = online_daily_target(product_set_code, product_set_code_prev, sale_start_date,
                                             sale_end_date_prev)
    keys = df.name.unique().tolist()
    # 如果keys中有合计，合计放到列表第一个字段
    if '合计' in keys:
        keys.remove('合计')
        keys.insert(0, '合计')

    for key in keys:
        # st.subheader(key)
        sub_data = df[df['name'] == key]
        sub_data_last = df_last_record[df_last_record['name'] == key]
        sub_data_last.fillna(0, inplace=True)
        sub_data.reset_index(drop=True, inplace=True)
        cols = st.columns([0.8, 0.2])
        with cols[0]:
            fig = make_subplots(specs=[[{"secondary_y": True}]])

            # 添加柱状图系列到主y轴
            fig.add_trace(
                go.Bar(x=sub_data['date'], y=sub_data['实际'], name='实际',
                       hovertemplate='日期: %{x}<br>实际: %{y:.0f}'),
                secondary_y=False
            )
            fig.add_trace(
                go.Bar(x=sub_data['date'], y=sub_data['目标'], name='目标',
                       hovertemplate='日期: %{x}<br>目标: %{y:.0f}'),
                secondary_y=False
            )

            # 添加线图系列到次y轴
            fig.add_trace(
                go.Scatter(x=sub_data['date'], y=sub_data['实际(累计)'], name='实际(累计)', mode='lines',
                           line=dict(color='#29B09D'), hovertemplate='日期: %{x}<br>实际(累计): %{y:.0f}'),
                secondary_y=True
            )
            fig.add_trace(
                go.Scatter(x=sub_data['date'], y=sub_data['目标(累计)'], name='目标(累计)', mode='lines',
                           hovertemplate='日期: %{x}<br>目标(累计): %{y:.0f}'),
                secondary_y=True
            )

            fig.update_xaxes(tickformat="%m-%d")
            # 移除副y轴的网格线
            fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
            # 设置y轴和y次轴的范围模式为从0开始
            fig.update_yaxes(rangemode="tozero", secondary_y=True)
            fig.update_yaxes(rangemode="tozero", secondary_y=False)
            fig.update_traces(
            )
            # 更新 y 轴格式为整数形式
            fig.update_yaxes(tickformat=".0f")
            fig.update_layout(yaxis_title="当期值",
                              yaxis2_title="累计值",
                              plot_bgcolor='rgba(0, 0, 0, 0)',
                              paper_bgcolor='rgba(0, 0, 0, 0)',
                              legend=dict(
                                  orientation="h",  # 水平方向
                                  yanchor="bottom",  # y轴锚点为底部
                                  y=1,  # y位置为1.02（顶部）
                                  xanchor="center",  # x轴锚点为中心
                                  x=0.5  # x位置为0.5（居中）
                              ),
                              title=dict(
                                  text=key,
                                  x=0.5,  # 居中对齐
                                  xanchor="center",
                                  y=0.95,
                                  font=dict(size=14)
                              ),
                              yaxis=dict(
                                  fixedrange=True,  # 禁止y轴的缩放和平移
                                  title_font=dict(size=12)  # 设置 yaxis_title 字体大小
                              ),
                              yaxis2=dict(
                                  overlaying='y',
                                  side='right',
                                  title_font=dict(size=12)  # 设置 yaxis2_title 字体大小
                              ),
                              dragmode=False,  # 禁用拖动模式
                              xaxis=dict(
                                  fixedrange=True  # 禁止x轴的缩放和平移
                              ))
            # 隐藏工具栏
            config = {'displayModeBar': False}
            st.plotly_chart(fig, config=config)

        with cols[1]:
            empty_line(6)
            # 计算完成率
            target = sub_data_last['目标'].values[0]
            actual = sub_data_last['实际'].values[0]

            if target > 0:
                complete_ratio = round(actual / target * 100, 1)
            elif actual > 0:
                complete_ratio = 100
            else:
                complete_ratio = 0
            st.metric('目标', int(sub_data_last['目标'].values[0]))
            st.metric('实际', int(sub_data_last['实际'].values[0]))
            st.metric('完成率', str(complete_ratio) + '%')


def offline(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev):
    text_write('线下完成情况')
    df, df_last_record = offline_daily_target(product_set_code, product_set_code_prev, sale_start_date,
                                              sale_end_date_prev)
    keys = df.name.unique().tolist()
    # 如果keys中有合计，合计放到列表第一个字段
    if '合计' in keys:
        keys.remove('合计')
        keys.insert(0, '合计')

    for key in keys:
        # st.subheader(key)
        sub_data = df[df['name'] == key]
        sub_data_last = df_last_record[df_last_record['name'] == key]
        sub_data_last.fillna(0, inplace=True)
        sub_data.reset_index(drop=True, inplace=True)
        cols = st.columns([0.8, 0.2])
        with cols[0]:
            fig = make_subplots(specs=[[{"secondary_y": True}]])

            # 添加柱状图系列到主y轴
            fig.add_trace(
                go.Bar(x=sub_data['date'], y=sub_data['实际'], name='实际',
                       hovertemplate='日期: %{x}<br>实际: %{y:.0f}'),
                secondary_y=False
            )
            fig.add_trace(
                go.Bar(x=sub_data['date'], y=sub_data['目标'], name='目标',
                       hovertemplate='日期: %{x}<br>目标: %{y:.0f}'),
                secondary_y=False
            )

            # 添加线图系列到次y轴
            fig.add_trace(
                go.Scatter(x=sub_data['date'], y=sub_data['实际(累计)'], name='实际(累计)', mode='lines',
                           line=dict(color='#29B09D'), hovertemplate='日期: %{x}<br>实际(累计): %{y:.0f}'),
                secondary_y=True
            )
            fig.add_trace(
                go.Scatter(x=sub_data['date'], y=sub_data['目标(累计)'], name='目标(累计)', mode='lines',
                           hovertemplate='日期: %{x}<br>目标(累计): %{y:.0f}'),
                secondary_y=True
            )

            fig.update_xaxes(tickformat="%m-%d")
            # 移除副y轴的网格线
            fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
            # 设置y轴和y次轴的范围模式为从0开始
            fig.update_yaxes(rangemode="tozero", secondary_y=True)
            fig.update_yaxes(rangemode="tozero", secondary_y=False)
            fig.update_traces(
            )
            # 更新 y 轴格式为整数形式
            fig.update_yaxes(tickformat=".0f")
            fig.update_layout(yaxis_title="当期值",
                              yaxis2_title="累计值",
                              plot_bgcolor='rgba(0, 0, 0, 0)',
                              paper_bgcolor='rgba(0, 0, 0, 0)',
                              legend=dict(
                                  orientation="h",  # 水平方向
                                  yanchor="bottom",  # y轴锚点为底部
                                  y=1,  # y位置为1.02（顶部）
                                  xanchor="center",  # x轴锚点为中心
                                  x=0.5  # x位置为0.5（居中）
                              ),
                              title=dict(
                                  text=key,
                                  x=0.5,  # 居中对齐
                                  xanchor="center",
                                  y=0.95,
                                  font=dict(size=14)
                              ),
                              yaxis=dict(
                                  fixedrange=True,  # 禁止y轴的缩放和平移
                                  title_font=dict(size=12)  # 设置 yaxis_title 字体大小
                              ),
                              yaxis2=dict(
                                  overlaying='y',
                                  side='right',
                                  title_font=dict(size=12)  # 设置 yaxis2_title 字体大小
                              ),
                              dragmode=False,  # 禁用拖动模式
                              xaxis=dict(
                                  fixedrange=True  # 禁止x轴的缩放和平移
                              ))
            # 隐藏工具栏
            config = {'displayModeBar': False}
            st.plotly_chart(fig, config=config)

        with cols[1]:
            empty_line(6)
            # 计算完成率
            target = sub_data_last['目标'].values[0]
            actual = sub_data_last['实际'].values[0]

            if target > 0:
                complete_ratio = round(actual / target * 100, 1)
            elif actual > 0:
                complete_ratio = 100
            else:
                complete_ratio = 0
            st.metric('目标', int(sub_data_last['目标'].values[0]))
            st.metric('实际', int(sub_data_last['实际'].values[0]))
            st.metric('完成率', str(complete_ratio) + '%')


def content():
    st.subheader('目标达成')
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=
    product_info[product_info['product_set_code'] != 'ninghuibaoV4']['product_set_name'],
                                                  placeholder="请选择产品")

    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[
        0]
    product_set_code_prev = \
    product_info[product_info['product_set_name'] == product_set_name]['prev_product_set_code'].values[
        0]
    sale_start_date = product_info[product_info['product_set_name'] == product_set_name]['sale_start_time'].values[
        0]
    sale_start_date = pd.to_datetime(sale_start_date).strftime('%Y-%m-%d')
    sale_end_date_prev = \
    product_info[product_info['product_set_code'] == product_set_code_prev]['sale_end_time'].values[
        0]
    sale_end_date_prev = pd.to_datetime(sale_end_date_prev).strftime('%Y-%m-%d')


    st.divider()
    online(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev)
    offline(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev)


def main():
    content()


if __name__ == '__main__':
    main()
