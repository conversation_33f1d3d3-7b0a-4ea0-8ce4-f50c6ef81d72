from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalSuppliesBase(BaseModel):
    code = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='耗材代码', **_db_comment_kwarg('耗材代码'))
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='耗材名称', **_db_comment_kwarg('耗材名称'))
    general_name = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='医保通用名称', **_db_comment_kwarg('医保通用名称'))
    first_type = models.CharField(max_length=64, blank=True, null=True, verbose_name='一级分类（学科、品类）', **_db_comment_kwarg('一级分类（学科、品类）'))
    second_type = models.CharField(max_length=64, blank=True, null=True, verbose_name='二级分类（用途、品目）', **_db_comment_kwarg('二级分类（用途、品目）'))
    third_type = models.CharField(max_length=64, blank=True, null=True, verbose_name='三级分类（部位、功能能、品种）', **_db_comment_kwarg('三级分类（部位、功能能、品种）'))
    type_path = models.CharField(max_length=255, blank=True, null=True, verbose_name='分类明细', **_db_comment_kwarg('分类明细'))
    material = models.CharField(max_length=64, blank=True, null=True, verbose_name='耗材材质', **_db_comment_kwarg('耗材材质'))
    specifications = models.CharField(max_length=255, blank=True, null=True, verbose_name='规格（特征、参数）', **_db_comment_kwarg('规格（特征、参数）'))
    production_company_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_supplies_base'
        verbose_name = '医保耗材分类基础表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

