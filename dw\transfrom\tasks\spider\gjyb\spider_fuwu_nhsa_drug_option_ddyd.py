import json
import pandas as pd
import subprocess
import os
import logging
import time
from datetime import datetime
from logging.handlers import RotatingFileHandler
import os
import glob

# 配置日志
logger = logging.getLogger('spider_fuwu_nhsa')
logger.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 创建文件处理器
current_dir = os.path.dirname(os.path.abspath(__file__))
log_dir = os.path.join(current_dir, 'download', 'log')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'log_fuwu_nhsa_wm_tcmpatinfo_z.log')

# 设置日志文件最大为10MB，保留3个备份文件
file_handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=3, encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

def get_hospital_data(max_pages=5, api_url='https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital', url='/nthl/api/CommQuery/queryFixedHospital', data={}, page_type=2, filename='drup_option_info_detail', max_retries=1):
    """
    获取医院数据
    :param max_pages: 获取的最大页数
    :param api_url: API接口地址
    :param url: 请求的URL
    :param data: 请求的数据
    :param page_type: 页码类型
    :param filename: 输出文件名(不带扩展名)
    :param max_retries: 最大重试次数
    :return: DataFrame格式的医院数据
    """
    # 检查断点续传文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    checkpoint_file = os.path.join(current_dir, 'download', 'checkpoint.json')
    start_page = 1
    
    # 从命令行参数获取起始页
    if 'start_page' in data:
        start_page = data['start_page']
        logging.info(f'使用命令行参数指定的起始页：{start_page}')
        # 如果指定了起始页，创建或更新断点文件
        os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump({'last_page': start_page - 1, 'total_records': (start_page - 1) * 100}, f)
        logging.info(f'已创建断点文件，记录上次爬取到第{start_page - 1}页')
    # 如果没有指定起始页，则尝试从断点文件获取
    elif os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint = json.load(f)
                start_page = checkpoint.get('last_page', 1) 
                logging.info(f'发现断点续传文件，从第{start_page}页继续爬取')
        except Exception as e:
            logging.warning(f'读取断点续传文件失败：{str(e)}')
    
    # 更新最大页数
    if start_page > max_pages:
        logging.warning(f'起始页{start_page}大于最大页数{max_pages}，重置为1')
        start_page = 1
    try:
        # 运行Node.js脚本获取数据
        script_path = os.path.join(os.path.dirname(__file__), 'yibao_drug.js')
        
        # 检查文件是否存在
        if not os.path.exists(script_path):
            raise FileNotFoundError(f'Node.js脚本文件不存在：{script_path}')
            
        logger.info(f'{api_url}开始获取医院数据，起始页：{start_page}，最大页数：{max_pages}')
        # 从data中移除start_page参数
        if 'start_page' in data:
            data.pop('start_page')
        # 构建Node.js命令，使用数组形式传递参数
        script_path = script_path.replace('\\', '\\\\')
        node_script = f"require('{script_path}').fetchHospitalData({max_pages}, '{api_url}', '{url}', {json.dumps(data)}, {page_type}, {start_page}, '{filename}').catch(console.error)"
        
        # 执行Node.js脚本并实时显示输出
        result = subprocess.run(['node', '-e', node_script], check=True, capture_output=False, text=True)
        logger.info(f"Node.js脚本执行输出: {result.stdout}")
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        download_dir = os.path.join(current_dir, 'download')
        json_dir = os.path.join(download_dir, 'json')
        os.makedirs(json_dir, exist_ok=True)
        
        # 查找所有批次数据文件
        batch_files = []
        for file in os.listdir(json_dir):
            if file.startswith(f'{filename}_') and file.endswith('.json'):
                batch_files.append(os.path.join(json_dir, file))
        
        # 读取并合并所有批次数据文件
        if not batch_files:
            raise FileNotFoundError(f'未找到任何数据文件')
        
        logging.info(f'找到{len(batch_files)}个批次数据文件')
        data = []
            
        # 直接读取所有批次文件数据
        for batch_file in sorted(batch_files):
            try:
                with open(batch_file, 'r', encoding='utf-8') as f:
                    batch_data = json.load(f)
                    if len(batch_data) == 0:
                        logging.info(f'批次文件 {batch_file} 数据为空，跳过')
                        continue
                    data.extend(batch_data)
                    logging.info(f'已处理批次文件: {batch_file}')
            except Exception as e:
                logging.error(f'处理批次文件{batch_file}时出错: {str(e)}')
                continue
        
        # 转换为DataFrame并清洗数据
        if not data:
            raise FileNotFoundError(f'未获取到任何有效数据')
            
        df = pd.DataFrame(data)
        df = clean_data(df)
        
        # 记录实际获取的数据量
        actual_total = len(df)
        logging.info(f'数据获取完成：实际获取 {actual_total} 条记录')
        
        # 更新断点文件
        os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            checkpoint_data = {'last_page': max_pages, 'total_records': actual_total}
            json.dump(checkpoint_data, f)
            
        return df
    
    except Exception as e:
        logging.error(f'获取医院数据时发生错误：{str(e)}')
        # 检查是否还有重试次数
        if 'retry_count' not in locals():
            retry_count = 0
        
        if retry_count < max_retries:
            retry_count += 1
            logging.info(f'准备第{retry_count}次重试...')
            
            # 保存错误信息到checkpoint文件
            os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                checkpoint_data = {
                    'last_page': start_page,
                    'total_records': (start_page - 1) * 100,
                    'error': str(e),
                    'retry_count': retry_count
                }
                json.dump(checkpoint_data, f)
            
            # 等待时间随重试次数递增
            wait_time = 5 * retry_count
            logging.info(f'等待{wait_time}秒后重试...')
            time.sleep(wait_time)
            return get_hospital_data(max_pages, api_url, url, data, page_type, filename, max_retries)
        else:
            logging.error(f'已达到最大重试次数{max_retries}，停止尝试')
            raise

def clean_data(df):
    """
    数据清洗处理
    :param df: 原始DataFrame
    :return: 清洗后的DataFrame
    """
    try:
        # 处理列表类型的数据
        for column in df.columns:
            if df[column].apply(lambda x: isinstance(x, list)).any():
                df[column] = df[column].apply(lambda x: ','.join(x) if isinstance(x, list) else x)
        
        # 删除重复数据
        df = df.drop_duplicates()
        
        # 填充空值
        df = df.fillna('')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        return df
    
    except Exception as e:
        logging.error(f'数据清洗时发生错误：{str(e)}')
        raise

def export_data(df, output_format='csv',file_name='drug_option_detail_data_'):
    """
    导出数据
    :param df: DataFrame数据
    :param output_format: 输出格式（csv/excel/json）
    :return: 导出文件的路径
    :raises ValueError: 当output_format不是支持的格式时
    """
    try:
        # 验证输出格式
        output_format = output_format.lower()
        if output_format not in ['csv', 'excel', 'json']:
            raise ValueError(f'不支持的输出格式：{output_format}，支持的格式有：csv, excel, json')

        # 获取程序执行目录并创建download文件夹
        current_dir = os.path.dirname(os.path.abspath(__file__))
        download_dir = os.path.join(current_dir, 'download')
        os.makedirs(download_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(download_dir, f'{file_name}_{timestamp}.{"xlsx" if output_format == "excel" else output_format}')
        
        # 根据格式导出数据
        if output_format == 'csv':
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
        elif output_format == 'excel':
            # 批量处理时间列
            time_columns = df.columns[df.columns.str.contains('Time|date', case=True)]
            if not time_columns.empty:
                # 修改处理逻辑，使用pd.to_numeric安全转换数值
                df[time_columns] = df[time_columns].apply(lambda x: pd.to_numeric(x, errors='coerce')).fillna('')
                # 对非空值进行整数转换
                df[time_columns] = df[time_columns].apply(lambda x: x.apply(lambda y: str(int(y)) if pd.notnull(y) and y != '' else ''))
            other_columns = df.columns[df.columns.str.contains('rid', case=True)]
            if not other_columns.empty:
                df[other_columns] = df[other_columns].apply(lambda x: x.apply(lambda y: str(y) if pd.notnull(y) and y != '' else ''))
            df.to_excel(output_file, index=False)
        elif output_format == 'json':
            df.to_json(output_file, orient='records', force_ascii=False, indent=2)
        
        logging.info(f'数据已导出到文件：{output_file}')
        return output_file
    
    except Exception as e:
        logging.error(f'导出数据时发生错误：{str(e)}')
        raise

def read_drug_names(excel_file):
    """
    从Excel文件中读取drugName
    :param excel_file: Excel文件路径
    :return: 符合条件的drugName列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 筛选existFlag为1的记录
        filtered_df = df[df['existFlag'] == 1]
        
        # 获取去重后的drugName列表
        drug_names = filtered_df['drugName'].unique().tolist()
        
        logging.info(f'从Excel中读取到{len(drug_names)}个符合条件的药品名称')
        return drug_names
    except Exception as e:
        logging.error(f'读取Excel文件出错：{str(e)}')
        raise

if __name__ == '__main__':
    try:
        # 设置基本参数
        max_pages = 10000  # 总页数
        api_url = 'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/drugOptins/queryDrugOptinsInfoDetail'
        url = api_url.replace('https://fuwu.nhsa.gov.cn/ebus/fuwu/api','')
        
        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        excel_file = os.path.join(current_dir, 'download', 'leave_data.xlsx')
        
        # 读取药品名称列表
        drug_names = read_drug_names(excel_file)

        
        # 遍历每个药品名称进行爬取
        for drug_name in drug_names:
            try:
                logging.info(f'开始爬取药品：{drug_name}')
                print(f'开始爬取药品：{drug_name}')
                
                data = {
                    'areaCode': "东城区",
                    "drugName": drug_name,
                    "medinsType": 2,
                    'pageSize': 100,
                    "province": '',
                    "region": '',
                    'start_page': 1
                }
                
                # 获取医院数据
                json_name = f'ddyd_{drug_name}'
                hospital_df = get_hospital_data(max_pages, api_url, url, data, 1,json_name)
                
                # 导出数据
                export_data(hospital_df, 'excel',file_name = json_name)
                
                logging.info(f'药品 {drug_name} 数据爬取完成')
                
            except Exception as e:
                logging.error(f'爬取药品 {drug_name} 时出错：{str(e)}')
                continue
        
    except Exception as e:
        logging.error(f'程序执行出错：{str(e)}')