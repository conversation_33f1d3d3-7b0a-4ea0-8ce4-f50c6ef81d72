#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏医保局(jsyb)字段处理配置
"""

import logging
from typing import Dict
from transfrom.utils.field_config import BaseFieldProcessingConfig

logger = logging.getLogger(__name__)


class JsybFieldProcessingConfig(BaseFieldProcessingConfig):
    """
    江苏医保局字段处理配置
    """

    def get_field_strategies(self) -> Dict:
        """
        获取江苏医保局的字段处理策略配置

        Returns:
            Dict: 字段策略配置，格式为 {源表: {目标表: {字段名: 处理策略}}}
        """
        return {
            # 江苏医疗服务项目数据 - 统一使用NULL策略
            'spider_jsyb_service_facilities': {
                'medical_service_entity': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 江苏医保药品数据 - 统一使用NULL策略
            'spider_jsyb_drug': {
                'medical_drug_entity': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            }
            # 江苏可能有特有的源表和目标表配置
            # 'jsyb_special_table': {
            #     'jsyb_target_table': {
            #         'special_field': 'null_to_empty_string'
            #     }
            # }
        }


# 创建江苏医保局配置实例
jsyb_field_config = JsybFieldProcessingConfig()


# 提供便捷的函数接口
def get_jsyb_data_cleaning_config(source_table: str):
    """获取江苏医保局数据清洗配置"""
    return jsyb_field_config.get_data_cleaning_config(source_table)


def get_jsyb_field_mapping_config(source_table: str, target_table: str):
    """获取江苏医保局字段映射配置"""
    return jsyb_field_config.get_field_mapping_config(source_table, target_table)


def get_jsyb_data_normalization_config(target_table: str):
    """获取江苏医保局数据标准化配置"""
    return jsyb_field_config.get_data_normalization_config(target_table)
