#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
访客分析数据模型
"""

import django
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from common.models import BaseModel
from utils.column_name import db_comment_kwarg


class InsureVisitorAnalytics(BaseModel):
    """
    统一的访客分析数据表
    支持五种数据类型：日期维度、小时维度、星期+小时维度、日期转换率数据、工作日访客数据
    """

    # 数据类型选择（层次化命名）
    DATA_TYPE_CHOICES = [
        # 转换率数据类型
        ('conversion_daily', '转换率-按日期'),
        ('conversion_hourly', '转换率-按小时'),
        ('conversion_weekly_hourly', '转换率-按星期+小时'),

        # 访客数据类型
        ('visitor_date_hourly', '访客-按日期+小时'),
        ('visitor_workday_hourly', '访客-按工作日+小时'),
        ('visitor_os', '访客-操作系统'),

    ]
    
    # 星期几选择（英文名称）
    WEEKDAY_CHOICES = [
        ('Monday', '星期一'),
        ('Tuesday', '星期二'),
        ('Wednesday', '星期三'),
        ('Thursday', '星期四'),
        ('Friday', '星期五'),
        ('Saturday', '星期六'),
        ('Sunday', '星期日'),
    ]

    # 英文星期名称到中文的映射
    WEEKDAY_DISPLAY_MAP = {
        'Monday': '星期一',
        'Tuesday': '星期二',
        'Wednesday': '星期三',
        'Thursday': '星期四',
        'Friday': '星期五',
        'Saturday': '星期六',
        'Sunday': '星期日',
    }
    
    # 基础字段
    data_type = models.CharField(
        max_length=128,
        choices=DATA_TYPE_CHOICES,
        verbose_name='数据类型',
        help_text='访客分析数据的类型',
        **db_comment_kwarg('访客分析数据的类型')
    )

    # 时间维度字段
    date = models.DateField(
        null=True,
        blank=True,
        verbose_name='日期',
        help_text='日期维度数据使用，格式：YYYY-MM-DD',
        **db_comment_kwarg('日期维度数据使用，格式：YYYY-MM-DD')
    )

    hour = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(23)],
        verbose_name='小时',
        help_text='小时维度数据使用，范围：0-23',
        **db_comment_kwarg('小时维度数据使用，范围：0-23')
    )

    hour_range = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='时间段',
        help_text='时间段，格式：6-9,9-12',
        **db_comment_kwarg('时间段')
    )

    weekday = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        choices=WEEKDAY_CHOICES,
        verbose_name='星期几',
        help_text='星期+小时维度数据使用，英文名称：Monday, Tuesday等',
        **db_comment_kwarg('星期+小时维度数据使用，英文名称：Monday, Tuesday等')
    )

    # 转换率字段
    conversion_rate = models.DecimalField(
        max_digits=20,
        decimal_places=4,
        null=True,
        blank=True,
        validators=[MinValueValidator(Decimal('0.0000'))],
        verbose_name='转换率',
        help_text='转换率数值，支持4位小数（部分数据类型使用）',
        **db_comment_kwarg('转换率数值，支持4位小数（部分数据类型使用）')
    )

    # 访客数字段
    visitor_count = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        verbose_name='访客数',
        help_text='访客数量（新增数据类型使用）',
        **db_comment_kwarg('访客数量（新增数据类型使用）')
    )

    # 操作系统
    os = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name='操作系统',
        help_text='操作系统名称',
        **db_comment_kwarg('操作系统名称')
    )

    # 工作日标识字段
    is_workday = models.BooleanField(
        null=True,
        blank=True,
        verbose_name='是否工作日',
        help_text='工作日访客数据使用：True=工作日，False=非工作日',
        **db_comment_kwarg('工作日访客数据使用：True=工作日，False=非工作日')
    )
    
    class Meta:
        db_table = 'insure_visitor_analytics'
        verbose_name = '健康险埋点访客分析'
        verbose_name_plural = verbose_name

        # Django 4.2+会自动识别这个属性，低版本会忽略
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

        # 唯一性约束
        unique_together = [
            # 转换率-按日期：数据类型 + 日期
            ('data_type', 'date'),
            # 转换率-按小时：数据类型 + 小时
            ('data_type', 'hour'),
            # 转换率-按星期+小时：数据类型 + 星期几 + 小时
            ('data_type', 'weekday', 'hour'),
            # 访客-按日期+小时：数据类型 + 日期 + 小时
            ('data_type', 'date', 'hour'),
            # 访客-按工作日+小时：数据类型 + 是否工作日 + 小时
            ('data_type', 'is_workday', 'hour'),
        ]

        # 索引优化
        indexes = [
            models.Index(fields=['data_type', 'date']),
            models.Index(fields=['data_type', 'hour']),
            models.Index(fields=['data_type', 'weekday', 'hour']),
            models.Index(fields=['data_type', 'date', 'hour']),
            models.Index(fields=['data_type', 'is_workday', 'hour']),
            models.Index(fields=['visitor_count']),
        ]

        # 排序
        ordering = ['-create_time']
    
    def __str__(self):
        # 转换率数据类型
        if self.data_type == 'conversion_daily':
            return f"转换率-按日期: {self.date} - {self.conversion_rate}"
        elif self.data_type == 'conversion_hourly':
            return f"转换率-按小时: {self.hour_range}时 - {self.conversion_rate}"
        elif self.data_type == 'conversion_weekly_hourly':
            weekday_display = self.WEEKDAY_DISPLAY_MAP.get(self.weekday, self.weekday)
            return f"转换率-按星期+小时: {weekday_display} {self.hour}时 - {self.conversion_rate}"

        # 访客数据类型
        elif self.data_type == 'visitor_date_hourly':
            weekday_display = self.WEEKDAY_DISPLAY_MAP.get(self.weekday, self.weekday) if self.weekday else ''
            return f"访客-按日期+小时: {self.date} {weekday_display} {self.hour}时 - 转换率:{self.conversion_rate} 访客:{self.visitor_count}"
        elif self.data_type == 'visitor_workday_hourly':
            workday_text = '工作日' if self.is_workday else '非工作日'
            return f"访客-按工作日+小时: {workday_text} {self.hour}时 - 访客:{self.visitor_count}"
        elif self.data_type == 'visitor_os':
            return f"访客-操作系统: {self.os} - 访客:{self.visitor_count}"

        return f"访客分析记录: {self.data_type}"
    
    def clean(self):
        """数据验证"""
        from django.core.exceptions import ValidationError

        # 转换率数据类型验证
        if self.data_type == 'conversion_daily':
            if not self.date:
                raise ValidationError('转换率-按日期数据必须提供日期')
            if self.hour is not None or self.weekday is not None:
                raise ValidationError('转换率-按日期数据不应包含小时或星期字段')
            if not self.conversion_rate:
                raise ValidationError('转换率-按日期数据必须提供转换率')
            if self.visitor_count is not None or self.is_workday is not None:
                raise ValidationError('转换率-按日期数据不应包含访客数或工作日字段')

        elif self.data_type == 'conversion_hourly':
            if self.hour_range is None:
                raise ValidationError('转换率-按小时数据必须提供小时时间段')
            if self.date is not None or self.weekday is not None:
                raise ValidationError('转换率-按小时数据不应包含日期或星期字段')
            if not self.conversion_rate:
                raise ValidationError('转换率-按小时数据必须提供转换率')
            if self.visitor_count is not None or self.is_workday is not None:
                raise ValidationError('转换率-按小时数据不应包含访客数或工作日字段')

        elif self.data_type == 'conversion_weekly_hourly':
            if self.weekday is None or self.hour is None:
                raise ValidationError('转换率-按星期+小时数据必须同时提供星期几和小时')
            if self.date is not None:
                raise ValidationError('转换率-按星期+小时数据不应包含日期字段')
            if not self.conversion_rate:
                raise ValidationError('转换率-按星期+小时数据必须提供转换率')
            if self.visitor_count is not None or self.is_workday is not None:
                raise ValidationError('转换率-按星期+小时数据不应包含访客数或工作日字段')

        # 访客数据类型验证
        elif self.data_type == 'visitor_date_hourly':
            # 访客-按日期+小时数据：包含日期、转换率、星期几、小时、访客数
            if not self.date:
                raise ValidationError('访客-按日期+小时数据必须提供日期')
            if not self.conversion_rate:
                raise ValidationError('访客-按日期+小时数据必须提供转换率')
            if self.weekday is None:
                raise ValidationError('访客-按日期+小时数据必须提供星期几')
            if self.hour is None:
                raise ValidationError('访客-按日期+小时数据必须提供小时')
            if self.visitor_count is None:
                raise ValidationError('访客-按日期+小时数据必须提供访客数')
            if self.is_workday is not None:
                raise ValidationError('访客-按日期+小时数据不应包含工作日字段')

        elif self.data_type == 'visitor_workday_hourly':
            # 访客-按工作日+小时数据：包含是否工作日、小时、访客数
            if self.is_workday is None:
                raise ValidationError('访客-按工作日+小时数据必须提供是否工作日')
            if self.hour is None:
                raise ValidationError('访客-按工作日+小时数据必须提供小时')
            if self.visitor_count is None:
                raise ValidationError('访客-按工作日+小时数据必须提供访客数')
            if self.date is not None or self.weekday is not None or self.conversion_rate is not None:
                raise ValidationError('访客-按工作日+小时数据不应包含日期、星期几或转换率字段')

        elif self.data_type == 'visitor_os':
            # 访客-操作系统数据：包含操作系统、访客数
            if not self.os:
                raise ValidationError('访客-操作系统数据必须提供操作系统名称')
            if self.visitor_count is None:
                raise ValidationError('访客-操作系统数据必须提供访客数')


