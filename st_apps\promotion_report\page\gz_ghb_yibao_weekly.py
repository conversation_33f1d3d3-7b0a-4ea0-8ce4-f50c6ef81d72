import datetime
import warnings
from pathlib import Path

import pandas as pd
import streamlit as st

from utils.st import query_sql, text_write
from utils.utils import simplify_replace

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)

CONNECTOR_JKX_GHB = st.connection('jkx_ghb', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
        SELECT 
        ps.NAME product_set_name,
            ps.CODE product_set_code ,p.sale_from
        FROM
            product_set ps join product p on p.product_set_id = ps.id
        WHERE
            ps.CODE like 'guihuibao%' 
        ORDER BY
        p.sale_from desc
        '''

    df_product_code = CONNECTOR_JKX_GHB.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['product_set_code_prev'] = df_product_code['product_set_code'].shift(-1)
    # 删除最后一行
    df_product_code = df_product_code[:-1]
    return df_product_code


def get_area_data(product_set_code, conn=CONNECTOR_DW):
    """
    获取区域基本医疗数据
    :param product_set_code:产品集编码
    :return:
    """
    sql = """
    select 
    code area_code,
    count base_insure,
    name area_name,
    employee_count,
    resident_count
    from public_area_base_insure 
    where product_set_code = '{product_set_code}'
    """.format(product_set_code=product_set_code)

    df_area = conn.query(sql, ttl=0)
    df_area = df_area[df_area['base_insure'] > 0]
    return df_area


def get_target_data(product_set_code, conn=CONNECTOR_DW):
    """
    获取目标数据
    :param product_set_code: 产品集编码
    :param conn:
    :return:
    """
    sql = """
    select name seller_name,
    short_name seller,
    target,
    type 
    from public_target 
    where product_set_code = '{product_set_code}'
    and type in ('agent','online')
    """.format(product_set_code=product_set_code)

    df_target = conn.query(sql, show_spinner='查询中...', ttl=0)
    return df_target


def fix_area_code(row):
    """
    处理地区编码
    """
    code_map = {
        '5299': '5200',
        '5221': '5203',
        '5222': '5206',
        '5224': '5205',
        '5225': '5204',
    }

    if row['area_code'] == '000000':
        if row['credential_type'] == 'IDENTITY_CARD':
            code_preix = row['credential_number'][:4]
            if code_preix[:2] == '52':
                area_code = code_preix + '00'
            else:
                area_code = '520100'
        else:
            area_code = '520100'
    else:
        area_code = row['area_code']
    code_prefix = area_code[:4]
    if code_prefix in code_map:
        area_code = code_map[code_prefix] + '00'
    return area_code


def get_channel_name(row):
    """
    获取渠道名称
    """
    if row['is_online'] == '线上':
        return '线上'
    else:
        return row['seller']


def get_pay_name(row):
    """
    获取支付方式名称
    """
    if row['is_personal'] == '个单':
        if row['pay_type'] == 'MEDICARE':
            return '医保'
        else:
            channel_code = row['channel_code']
            if channel_code == 'alipay':
                return '支付宝'
            else:
                return '微信'
    else:
        return '团单'


def get_kinship_name(kinship):
    """
    获取投保人与被保人关系名称
    """
    if kinship == 'SELF':
        return '本人'
    elif kinship == 'SPOUSE':
        return '配偶'
    elif kinship == 'CHILD':
        return '子女'
    elif kinship == 'PARENT':
        return '父母'
    else:
        return '其他'


@st.cache_data(ttl=60,show_spinner='查询中...')
def get_person_info(product_set_code, start_datetime, end_datetime, sql=query_sql('SQL_INSURE_PERSON_INFO'),
                    conn=CONNECTOR_JKX_GHB):
    """
    获取个人数据
    :param product_set_code: 产品集编码
    :param start_datetime: 开始时间
    :param end_datetime: 结束时间
    :return:
    """
    df = conn.query(
        sql.format(product_set_code=product_set_code, start_datetime=start_datetime, end_datetime=end_datetime),
        show_spinner='查询中...', ttl=0)
    df_target = get_target_data(product_set_code)
    if df.empty:
        return df
    df['area_code'].fillna('000000', inplace=True)
    df['area_code'] = df.apply(fix_area_code, axis=1)

    df['seller'].fillna(df['seller_name'], inplace=True)
    simplify_replace(df, 'is_online', {1: '线上', 0: '线下'})
    simplify_replace(df, 'is_personal', {1: '个单', 0: '团单'})
    simplify_replace(df, 'gender', {'FEMALE': '女', 'MALE': '男'}, default_value='性别未知')
    simplify_replace(df, 'medicare_type', {'EMPLOYEE': '职工医保', 'RESIDENT': '居民医保'}, default_value='其他医保')
    df['kinship'] = df['kinship'].apply(get_kinship_name)
    df['pay_name'] = df.apply(get_pay_name, axis=1)
    df['channel_name'] = df.apply(get_channel_name, axis=1)
    df_area = get_area_data(product_set_code)
    df = df.merge(df_area[['area_code', 'area_name', 'employee_count', 'resident_count']], on='area_code', how='left')
    df['area_name'].fillna('省本级', inplace=True)  # 匹配不到的填充省本级

    return df


def get_area_channel_data(df):
    """
    统计区域、渠道统计数据
    :param df:
    """
    if df.empty:
        return df
    # 统计区域、渠道统计数据
    pt_source = pd.pivot_table(df, index=['channel_name'], columns=['area_name'], values=['credential_number'],
                               aggfunc='count')
    pt_source.fillna(0, inplace=True)
    pt_source = pt_source.astype(int)
    # 修改index名称为渠道
    pt_source.index.name = '医保归属地'
    pt_source.columns = pt_source.columns.droplevel()

    # 列排序
    pt_source = pt_source[
        ['贵阳市', '遵义市', '安顺市', '铜仁市', '毕节市', '六盘水市', '黔南州', '黔东南州', '黔西南州', '省本级']]
    pt_source['合计'] = pt_source.sum(axis=1)
    pt_source.sort_values(by='贵阳市', ascending=False, inplace=True)
    pt_source.loc['合计'] = pt_source.sum(axis=0)
    return pt_source


def get_area_statistics(df, product_set_code):
    """
    统计区域数据
    :param df:
    :return:
    """
    if df.empty:
        return df
    df.rename(columns={'area_name': '医保归属地'}, inplace=True)
    # 根据医保归属地分组计数
    pt_参保量 = pd.pivot_table(df, index=['医保归属地'], values=['credential_number'], aggfunc='count')
    # 修改列名为参保量
    pt_参保量.columns = ['参保量']

    # 根据医保归属计算平均年龄
    pt_平均年龄 = pd.pivot_table(df, index=['医保归属地'], values=['age'], aggfunc='mean')
    # 修改列名为平均年龄
    pt_平均年龄.columns = ['平均年龄']
    pt_平均年龄 = pt_平均年龄.round(2)

    # 根据医保归属地和关系分组计数
    pt_性别 = pd.pivot_table(df, index=['医保归属地'], columns=['gender'], values=['credential_number'],
                             aggfunc='count')
    pt_性别.columns = pt_性别.columns.droplevel()

    # 支付方式
    pt_支付方式 = pd.pivot_table(df, index=['医保归属地'], columns=['pay_name'], values=['credential_number'],
                                 aggfunc='count')
    pt_支付方式.columns = pt_支付方式.columns.droplevel()

    # 关系
    pt_关系 = pd.pivot_table(df, index=['医保归属地'], columns=['kinship'], values=['credential_number'],
                             aggfunc='count')
    pt_关系.columns = pt_关系.columns.droplevel()

    # 医保类型
    pt_医保类型 = pd.pivot_table(df, index=['医保归属地'], columns=['medicare_type'], values=['credential_number'],
                                 aggfunc='count')
    pt_医保类型.columns = pt_医保类型.columns.droplevel()

    pt_area = pd.concat([pt_参保量, pt_平均年龄, pt_性别, pt_支付方式, pt_关系, pt_医保类型], axis=1)
    pt_area.fillna(0, inplace=True)
    pt_area = pt_area.astype(int)

    # #
    df_area_1 = get_area_data(product_set_code).rename(columns={'area_name': '医保归属地'})
    pt_area = pd.merge(df_area_1, pt_area, on='医保归属地', how='left')

    pt_area['参保率'] = pt_area['参保量'] / pt_area['base_insure']
    pt_area['参保率'] = pt_area['参保率'].round(4)

    pt_area['参保率-职工医保'] = pt_area['职工医保'] / pt_area['employee_count']
    pt_area['参保率-职工医保'] = pt_area['参保率-职工医保'].round(4)

    pt_area['参保率-居民医保'] = pt_area['居民医保'] / pt_area['resident_count']
    pt_area['参保率-居民医保'] = pt_area['参保率-居民医保'].round(4)

    pt_area.rename(columns= {'employee_count': '医保总人数-职工医保', 'resident_count': '医保总人数-居民医保'},inplace=True)
    pt_area.fillna(0, inplace=True)

    keys = [
        '医保归属地','参保量', '平均年龄','男', '女','职工医保', '居民医保','微信', '支付宝', '医保', '团单',
        '本人', '配偶', '父母', '子女', '其他','医保总人数-职工医保','医保总人数-居民医保',
        '参保率', '参保率-职工医保', '参保率-居民医保'
    ]
    if '支付宝' not in pt_area.columns:
        keys.remove('支付宝')
    # 保留index 和 keys中的列
    pt_area.reset_index(inplace=True)
    pt_area = pt_area[keys]
    rate_columns = [col for col in pt_area.columns if '率' in col]
    for col in rate_columns:
        pt_area[col] = pt_area[col].apply(lambda x: f"{x * 100:.2f}%")
    pt_area.set_index('医保归属地', inplace=True)
    return pt_area


def get_seller_sale_info(product_set_code, start_datetime, end_datetime,
                         sql=[query_sql('SQL_SELLER_PERSON'), query_sql('SQL_SELLER_SALE'),
                              query_sql('SQL_SELLER_ACTIVITY_USER')]):
    """
    获取渠道销售数据
    :param product_set_code: 产品集编码
    :param start_datetime: 开始时间
    :param end_datetime: 结束时间
    :return:
    """
    df_seller_person = CONNECTOR_JKX_GHB.query(sql[0].format(product_set_code=product_set_code,
                                                             start_datetime=start_datetime,
                                                             end_datetime=end_datetime),
                                               show_spinner='查询中...', ttl=0)
    df_seller_sale = CONNECTOR_JKX_GHB.query(sql[1].format(product_set_code=product_set_code,
                                                           start_datetime=start_datetime,
                                                           end_datetime=end_datetime),
                                             show_spinner='查询中...', ttl=0)
    df_seller_activity_user = CONNECTOR_JKX_GHB.query(sql[2].format(product_set_code=product_set_code,
                                                                    start_datetime=start_datetime,
                                                                    end_datetime=end_datetime),
                                                      show_spinner='查询中...', ttl=0)

    df_agent_all = pd.merge(df_seller_person, df_seller_sale, on=['seller', 'seller_name'], how='outer')
    df_agent_all = pd.merge(df_agent_all, df_seller_activity_user, on=['seller', 'seller_name'], how='outer')
    df_agent_all.fillna(0, inplace=True)
    if df_agent_all.empty:
        return df_agent_all
    df_agent_all['活跃率'] = df_agent_all['activity_user'] / df_agent_all['employee_num']
    df_agent_all['活跃率'] = df_agent_all['活跃率'].apply(lambda x: '%.2f%%' % (x * 100))
    df_agent_all['人均销量'] = df_agent_all['count'] / df_agent_all['activity_user']
    df_agent_all.sort_values(by='count', ascending=False, inplace=True)
    df_agent_all.reset_index(inplace=True, drop=True)
    df_agent_all.rename(
        columns={'seller': '保司', 'employee_num': '代理人数', 'count': '销量', 'activity_user': '活跃代理人数'},
        inplace=True)
    df_agent_all = df_agent_all[['保司', '代理人数', '销量', '活跃代理人数', '活跃率', '人均销量']]
    df_agent_all = df_agent_all[~df_agent_all['保司'].str.contains('线上渠道')]
    df_agent_all.set_index('保司', inplace=True)
    return df_agent_all


def get_daily_sale(product_set_code, start_datetime, end_datetime):
    """
    获取每日销售数据
    :param df_person_info:产品集编码
    :param start_datetime:开始时间
    :param end_datetime:结束时间
    :return:
    """
    df_daily_sale = CONNECTOR_JKX_GHB.query(query_sql('SQL_DAILY_SALE').format(product_set_code=product_set_code,
                                                                               start_datetime=start_datetime,
                                                                               end_datetime=end_datetime),
                                            show_spinner='查询中...', ttl=0)
    # 保留最后15条数据
    df_daily = df_daily_sale.rename(columns={'sale_date': '日期', 'count': '销量'})
    df_daily.set_index('日期', inplace=True)
    return df_daily


@st.cache_data(ttl=3600 * 24 * 90,show_spinner='查询中...')
def get_prev_insure_person(product_set_code):
    sql = """
    SELECT ps.code             as ps_code,
           CAST( o.is_online AS UNSIGNED ) AS is_online,
           seller.short_name         as seller,
           c.credential_number as credential_number
    FROM `order` o
             JOIN order_item oi ON oi.order_id = o.id
             JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
             JOIN product_set ps ON ps.id = o.product_set_id AND ps.code ='{product_set_code}'
             JOIN user c ON c.id = oic.client_id
             JOIN seller ON seller.id = o.seller_id
    WHERE o.order_status in ('PAID_SUCCESS', 'WAIT_DEDUCT')
    """.format(product_set_code=product_set_code)
    df = CONNECTOR_JKX_GHB.query(sql, show_spinner='查询中...', ttl=0)
    return df


def get_insure_person(product_set_code, end_datetime):
    sql = """
    SELECT ps.code             as ps_code,
           CAST( o.is_online AS UNSIGNED ) AS is_online,
           seller.short_name         as seller,
           c.credential_number as credential_number
    FROM `order` o
             JOIN order_item oi ON oi.order_id = o.id
             JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
             JOIN product_set ps ON ps.id = o.product_set_id AND ps.code ='{product_set_code}'
             JOIN user c ON c.id = oic.client_id
             JOIN seller ON seller.id = o.seller_id
    WHERE o.order_status in ('PAID_SUCCESS', 'WAIT_DEDUCT')
    and o.create_time <= '{end_datetime}'
    """.format(product_set_code=product_set_code, end_datetime=end_datetime)
    df = CONNECTOR_JKX_GHB.query(sql, show_spinner='查询中...', ttl=0)
    return df


def renew_ratio(product_set_code, grouped):
    # 截取V后面的数字
    num = product_set_code.split('V')[1]
    try:
        credential_numbers_v1 = grouped[grouped['ps_code'] == f'guihuibaoV{int(num) - 1}']['credential_number'].values[
            0]
    except IndexError:
        return '-', 0,'-'
    try:
        credential_numbers_v2 = grouped[grouped['ps_code'] == f'guihuibaoV{int(num)}']['credential_number'].values[0]
    except IndexError:
        return '-', 0,'-'
    intersection = list(set(credential_numbers_v1).intersection(set(credential_numbers_v2)))
    percent = '%.2f%%' % (len(intersection) * 100 / len(credential_numbers_v1))
    percent_v2 = '%.2f%%' % (len(intersection) * 100 / len(credential_numbers_v2))
    return percent, len(intersection),percent_v2


def get_renewal_data(product_set_code, product_set_code_prev, end_datetime):
    df_prev_insure_person = get_prev_insure_person(product_set_code_prev)
    df_insure_person = get_insure_person(product_set_code, end_datetime)
    df = pd.concat([df_prev_insure_person, df_insure_person])

    data = []

    df_grouped = df.groupby('ps_code')['credential_number'].apply(list).reset_index()
    total_renew_ratio, intersection_total,total_renew_ratiov2 = renew_ratio(product_set_code, df_grouped)
    data.append({'name': '整体', 'value': total_renew_ratio, 'renew_num': intersection_total, 'renew_ratio_v2': total_renew_ratiov2})

    # 线上
    df_online = df[df['is_online'] == 1]
    df_online_grouped = df_online.groupby('ps_code')['credential_number'].apply(list).reset_index()
    online_renew_ratio, intersection_online,online_renew_ratiov2 = renew_ratio(product_set_code, df_online_grouped)
    data.append({'name': '线上', 'value': online_renew_ratio, 'renew_num': intersection_online, 'renew_ratio_v2': online_renew_ratiov2})

    # 各保司
    df_offline = df[df['is_online'] == 0]
    sellers = df_offline['seller'].unique()
    for seller in sellers:
        if product_set_code == 'guihuibaoV2':
            if seller in ['泰康养老', '大地财险', '中国人寿财险', '太平洋财险', '大家财险']:
                continue
            if seller == '中国人寿':
                seller_v2 = ['中国人寿', '中国人寿财险']
                df_offline_seller = df_offline[df_offline['seller'].isin(seller_v2)]
            elif seller == '太平洋寿险':
                seller_v2 = ['太平洋寿险', '太平洋财险']
                df_offline_seller = df_offline[df_offline['seller'].isin(seller_v2)]
            else:
                df_offline_seller = df_offline[df_offline['seller'] == seller]
            df_seller_grouped = df_offline_seller.groupby('ps_code')['credential_number'].apply(list).reset_index()
            seller_renew_ratio, intersection_seller,seller_renew_ratiov2 = renew_ratio(product_set_code, df_seller_grouped)
            data.append({'name': seller, 'value': seller_renew_ratio, 'renew_num': intersection_seller, 'renew_ratio_v2': seller_renew_ratiov2})
        if product_set_code == 'guihuibaoV3':
            if seller in ['中华联合财险', '阳光财险']:
                data.append({'name': seller, 'value': '-', 'renew_num': 0, 'renew_ratio_v2': '-'})
            else:
                df_offline_seller = df_offline[df_offline['seller'] == seller]

                df_seller_grouped = df_offline_seller.groupby('ps_code')['credential_number'].apply(list).reset_index()
                seller_renew_ratio, intersection_seller,seller_renew_ratiov2 = renew_ratio(product_set_code, df_seller_grouped)
                data.append({'name': seller, 'value': seller_renew_ratio, 'renew_num': intersection_seller, 'renew_ratio_v2': seller_renew_ratiov2})

    df = pd.DataFrame(data)
    df = df[df['name'] != '贵惠保-线上渠道']
    df.rename(columns={'name': '渠道', 'value': '续保率', 'renew_num': '续保人数','renew_ratio_v2':'续保占比'}, inplace=True)
    df = df[['渠道', '续保人数', '续保率','续保占比']]
    df.set_index('渠道', inplace=True)

    return df


@st.fragment
def download_data(excel_name,name):
    st.download_button(
        label='下载数据',
        data=open(excel_name, 'rb').read(),
        file_name=name,
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def main():
    st.subheader('贵惠保周报')
    cols = st.columns(3)
    # 产品集编码
    product_info = get_product_code()
    # 选择日期
    date_from = datetime.date(2021, 1, 7)
    date_until = datetime.date.today()
    with cols[0]:
        product_set_name = st.selectbox('选择产品集', product_info['product_set_name'].tolist(), index=0)
    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[0]
    product_set_code_prev = \
        product_info[product_info['product_set_name'] == product_set_name]['product_set_code_prev'].values[0]
    product_sale_from = product_info[product_info['product_set_name'] == product_set_name]['sale_from'].values[0]
    product_sale_from = pd.to_datetime(product_sale_from)


    # 获取当前日期
    current_date = datetime.datetime.now().date()
    default_start = current_date - datetime.timedelta(days=7)
    with cols[1]:
        start_date = st.date_input('开始日期', min_value=date_from, max_value=date_until, value=None,
                                   key='start_date')
    with cols[2]:
        end_date = st.date_input('结束日期', min_value=date_from, max_value=date_until, value=current_date,
                                 key='end_date')

    if start_date is None:
        start_date = datetime.datetime.strptime(str(product_sale_from), '%Y-%m-%d %H:%M:%S')
    if end_date is None:
        end_date = current_date

    start_datetime = start_date.strftime('%Y-%m-%d') + ' 00:00:00'
    end_datetime = end_date.strftime('%Y-%m-%d') + ' 23:59:59'
    st.divider()
    df_person_info = get_person_info(product_set_code, start_datetime, end_datetime)
    df_area_channel_data = get_area_channel_data(df_person_info)
    text_write("分区域渠道统计数据")
    st.dataframe(df_area_channel_data, use_container_width=True)
    df_area_statistics = get_area_statistics(df_person_info, product_set_code)
    text_write("医保归属地统计数据")
    st.dataframe(df_area_statistics, use_container_width=True)
    df_agent_all = get_seller_sale_info(product_set_code, start_datetime, end_datetime)
    text_write("代理人统计数据")
    st.dataframe(df_agent_all, use_container_width=True)
    df_daily_sale = get_daily_sale(product_set_code, product_sale_from, end_datetime)
    text_write("销量数据")
    st.dataframe(df_daily_sale, use_container_width=True)
    df_renewal_data = get_renewal_data(product_set_code, product_set_code_prev, end_datetime)
    text_write("续保数据")
    st.dataframe(df_renewal_data, use_container_width=True)

    name = f'{product_set_name}参保情况_{start_datetime}_{end_datetime}.xlsx'

    std_name = f'{product_set_name}参保情况'
    excel_name = Path.cwd().joinpath('temp_files').joinpath(std_name)
    with pd.ExcelWriter(excel_name) as writer:
        df_area_channel_data.to_excel(writer, sheet_name='渠道')
        df_area_statistics.to_excel(writer, sheet_name='医保归属地')
        df_agent_all.to_excel(writer, sheet_name='代理人')
        df_daily_sale.to_excel(writer, sheet_name='日度销量')
        df_renewal_data.to_excel(writer, sheet_name='续保率')
    # 下载数据
    download_data(excel_name,name)



if __name__ == '__main__':
    main()
