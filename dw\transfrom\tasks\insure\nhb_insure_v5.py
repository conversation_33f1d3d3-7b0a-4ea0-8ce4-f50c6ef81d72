import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)

import datetime
import logging
import warnings
from decimal import Decimal
from dateutil.relativedelta import relativedelta

import idna
import numpy as np
import pandas as pd
import pymysql
from django.db import transaction
from pandasql import sqldf

from dw import settings
from insure.models import InsureOnline, InsureAgent, InsureArea, InsureAgeSex
from public.models import PublicIndicatorData, PublicIndicatorMain, PublicStatistics, PublicAreaBaseInsure, PublicTarget
from task.utils.cache_manager import CacheManager
from transfrom.utils.utils import simplify_replace, query_sql, sum_or_combine, age_group, custom_update_or_create, \
    send_feishu_message, query_indicator_code, get_day_of_week, get_week_start_end, query_indicator_code_v1, \
    get_reversed_week_number
from transfrom.tasks.insure.nhb_insure_v5_target import NhbInsureV5Target

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class NhbInsureV5(CacheManager):
    """
    南京宁惠保-五期数据处理
    指标名称数据需要手动添加，不自动生成
    关于所有的参保，只统计主险
    """

    def __init__(self):
        super().__init__()
        self.product_set_code = 'ninghuibaoV5'
        self.previous_product_set_code = 'ninghuibaoV4'
        self.DB = settings.DATABASES['jkx']
        self.DB_DW = settings.DATABASES['default']
        self.version = '南京宁惠保-五期'
        self.previous_version = '南京宁惠保-四期'
        self.INSURE_TARGET = 2000000  # 销售目标，数据不等于线上+线下，所以单独留着
        self.type = 'insure'  # 统计大类
        self.actual_sale_date = '2024-09-18'
        self.sale_start_time = '2024-09-12 16:00:00'  # 销售起始时间（含预售）
        self.sale_start_date = datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S').date()
        # self.end_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
        self.end_time = '2025-01-24 00:00:00'
        self.today = datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S')
        self.yesterday = datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
            days=1)
        self.publish_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.target_statistics_type = '2'  # 1:保司按照历史计算  2:保司按照提供数据来，不计算——人工手动维护
        self.concat_end_week = True  # 是否合并最后两周数据
        self.special_count = 109427 # 预计节后会扣除额单量，基础版23769单，升级版85658单

    def get_connection_dw(self):
        """
        获取dw数据库连接
        """
        self.conn = pymysql.connect(host=idna.encode(self.DB_DW["HOST"]).decode('utf-8'), port=int(self.DB_DW["PORT"]),
                                    user=self.DB_DW["USER"],
                                    password=self.DB_DW["PASSWORD"], database=self.DB_DW["NAME"])
        return self.conn

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection_dw() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def get_connection(self):
        """
        获取数据库连接
        """
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def get_full_range_date(self, df):
        """
        获取完整的日期范围
        """
        # 获取最小日期
        min_date = df['date'].min()
        # 如果 min_date 是空的，使用 sale_start_date 作为默认值
        min_date = min_date if pd.notnull(min_date) else self.sale_start_date
        # 如果 min_date 是 pandas.Timestamp 类型，转换为 datetime.date
        if isinstance(min_date, pd.Timestamp):
            min_date = min_date.to_pydatetime().date()
        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = pd.date_range(start=min(min_date, self.sale_start_date),
                                        end=self.today.date())
        return full_date_range

    def get_daily_sale(self):
        """
        获取健康险日度销售数据，包括日期、医保类型、是否线上、是否个人、支付方式、区域编码、代理人、渠道来源、销售金额、销售件数、产品名称
        """
        try:
            df_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_DAILY_SALE').format(product_set_code=self.product_set_code,
                                                       end_datetime=self.publish_time),
                self.get_connection())
            return df_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_daily_sale error:{e}')

    def cache_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        df_daily_sale = self.get_daily_sale()
        # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
        try:
            self.update_cache('get_daily_sale', df_daily_sale)
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_daily_sale error:{e}')
        return df_daily_sale

    def get_main_daily_sale(self):
        """
        获取健康险日度主要销售数据，主要是金额、数量数据
        """
        try:
            df_main_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_MAIN_DATA').format(product_set_code=self.product_set_code,
                                                      end_datetime=self.publish_time),
                self.get_connection())
            return df_main_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_main_daily_sale error:{e}')

    def cache_main_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        df_main_daily_sale = self.get_main_daily_sale()
        # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
        try:
            self.update_cache('get_main_daily_sale', df_main_daily_sale)
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_main_daily_sale error:{e}')
        return df_main_daily_sale

    def get_total_count(self):
        """
        获取总销量
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        total_count = data['count'].sum()
        indic_name = self.version + '-销量-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_count))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_count error:{e}')
        return total_count

    def get_total_personal_count(self):
        """
        获取个单总销量
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        total_count = data['count'].sum()
        indic_name = self.version + '-销量-个单-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_count))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_count error:{e}')
        return total_count

    def get_target_ratio(self):
        """
        获取完成率
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_offline_seller_group = self.get_seller_group_report_data()
        group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
        target_ratio = round((data['count'].sum() + group_seller['value'].sum()+self.special_count) / self.INSURE_TARGET * 100, 4)
        indic_name = self.version + '-完成率-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(target_ratio))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_target_ratio error:{e}')
        return target_ratio

    def get_total_amount(self):
        """
        获取总销售额
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        total_amount = data['amount'].sum()
        indic_name = self.version + '-销售额-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_amount))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_amount error:{e}')
        return total_amount

    def get_person_group_count(self):
        """
        获取团单、个单数量
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_person_group = data.groupby(['is_personal']).agg({'count': 'sum'}).reset_index()
        # 忽略其他类型，如果有空为底层数据问题，这边不做处理
        df_person_group['is_personal'] = df_person_group['is_personal'].map({0: '团单', 1: '个单'})

        # 手动上传的团单数据
        df_offline_seller_group = self.get_seller_group_report_data()
        group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
        group_seller.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
        # group_seller = pd.DataFrame(self.SELLERS)
        group_seller_sum = group_seller['group_count'].sum()
        df_group = pd.DataFrame({'is_personal': '团单', 'count': [group_seller_sum]})
        # 只取个单数据，剔除了团单数据
        df_person_group = df_person_group[df_person_group['is_personal'] == '个单']
        df_person_group = pd.concat([df_group, df_person_group], ignore_index=True)
        df_person_group.reset_index(drop=True, inplace=True)
        # 查询数据库中所有数据
        db_person_group = PublicStatistics.objects.filter(type=self.type, statistical_type='personal',
                                                          product_set_code=self.product_set_code)
        df_db_person_group = pd.DataFrame(list(db_person_group.values()))
        if df_db_person_group.empty:
            df_db_person_group = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_person_group = df_db_person_group[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_person_group a left join df_person_group b on a.key = b.is_personal where b.is_personal is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()
        try:
            with transaction.atomic():
                for index, row in df_person_group.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='personal',
                                            product_set_code=self.product_set_code, key=row['is_personal'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_person_group_count error:{e}')
        return df_person_group

    def get_insure_place_count(self):
        """
        获取投保归属地统计数据
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        # 有医保类型都是本地
        data.loc[data['medicare_type'].notnull(), 'medicare_type'] = '本地'
        # 团单都是本地
        data.loc[data['is_personal'] == 0, 'medicare_type'] = '本地'
        data['medicare_type'].fillna('异地', inplace=True)
        df_insure_place = data.groupby(['medicare_type']).agg({'count': 'sum'}).reset_index()
        # 查询数据库中所有数据
        db_insure_place = PublicStatistics.objects.filter(type=self.type, statistical_type='place',
                                                          product_set_code=self.product_set_code)
        df_db_insure_place = pd.DataFrame(list(db_insure_place.values()))
        if df_db_insure_place.empty:
            df_db_insure_place = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_insure_place = df_db_insure_place[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_insure_place a left join df_insure_place b on a.key = b.medicare_type where b.medicare_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()
        try:
            with transaction.atomic():
                for index, row in df_insure_place.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='place',
                                            product_set_code=self.product_set_code, key=row['medicare_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_insure_place_count error:{e}')
        # 以下是个单
        data_personal = data[data['is_personal'] == 1]

        df_insure_place_personal = data_personal.groupby(['medicare_type']).agg({'count': 'sum'}).reset_index()
        # 查询数据库中所有数据
        db_insure_place = PublicStatistics.objects.filter(type=self.type, statistical_type='place_personal',
                                                          product_set_code=self.product_set_code)
        df_db_insure_place = pd.DataFrame(list(db_insure_place.values()))
        if df_db_insure_place.empty:
            df_db_insure_place = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_insure_place = df_db_insure_place[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_insure_place a left join df_insure_place_personal b on a.key = b.medicare_type where b.medicare_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()
        try:
            with transaction.atomic():
                for index, row in df_insure_place_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='place_personal',
                                            product_set_code=self.product_set_code, key=row['medicare_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_insure_place_count error:{e}')

        return df_insure_place

    def get_product_count(self):
        """
        获取产品版本统计数据
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        # 查询product_name含有基础版和升级版的产品数量（如果有其他版本，需要修改）、剔除了其他版本的产品
        data = data[data['product_name'].str.contains('基础|升级', na=False)]
        df_product_count = data.groupby(['product_name']).agg({'count': 'sum', 'amount': 'sum'}).reset_index()
        # 金额是给医保局推送用，且只用取主险
        # 获取产品版本，如果不是用-分割，需要修改
        df_product_count['product_name'] = df_product_count['product_name'].apply(lambda x: x.split('-')[-1])

        # 查询数据库中所有数据
        db_product_count = PublicStatistics.objects.filter(type=self.type, statistical_type='product',
                                                           product_set_code=self.product_set_code)
        df_db_product_count = pd.DataFrame(list(db_product_count.values()))
        if df_db_product_count.empty:
            df_db_product_count = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_product_count = df_db_product_count[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_product_count a left join df_product_count b on a.key = b.product_name where b.product_name is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_product_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='product',
                                            product_set_code=self.product_set_code, key=row['product_name'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_product_count error:{e}')

        # 查询数据库中所有数据
        db_product_count = PublicStatistics.objects.filter(type=self.type, statistical_type='product_amount',
                                                           product_set_code=self.product_set_code)
        df_db_product_count = pd.DataFrame(list(db_product_count.values()))
        if df_db_product_count.empty:
            df_db_product_count = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_product_count = df_db_product_count[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_product_count a left join df_product_count b on a.key = b.product_name where b.product_name is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_product_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='product_amount',
                                            product_set_code=self.product_set_code, key=row['product_name'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_product_count error:{e}')

        # 这边是个单的数据，需要单独处理
        data_personal = data[data['is_personal'] == 1]
        df_product_count_personal = data_personal.groupby(['product_name']).agg({'count': 'sum', 'amount': 'sum'}).reset_index()
        # 金额是给医保局推送用，且只用取主险
        # 获取产品版本，如果不是用-分割，需要修改
        df_product_count_personal['product_name'] = df_product_count_personal['product_name'].apply(lambda x: x.split('-')[-1])

        # 查询数据库中所有数据
        db_product_count = PublicStatistics.objects.filter(type=self.type, statistical_type='product_personal',
                                                           product_set_code=self.product_set_code)
        df_db_product_count = pd.DataFrame(list(db_product_count.values()))
        if df_db_product_count.empty:
            df_db_product_count = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_product_count = df_db_product_count[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_product_count a left join df_product_count_personal b on a.key = b.product_name where b.product_name is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_product_count_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='product_personal',
                                            product_set_code=self.product_set_code, key=row['product_name'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_product_count  personal error:{e}')

        # 查询数据库中所有数据
        db_product_count = PublicStatistics.objects.filter(type=self.type, statistical_type='product_amount_personal',
                                                           product_set_code=self.product_set_code)
        df_db_product_count = pd.DataFrame(list(db_product_count.values()))
        if df_db_product_count.empty:
            df_db_product_count = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_product_count = df_db_product_count[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_product_count a left join df_product_count_personal b on a.key = b.product_name where b.product_name is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_product_count_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='product_amount_personal',
                                            product_set_code=self.product_set_code, key=row['product_name'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_product_count product_amount_personal error:{e}')
        return df_product_count

    def get_online_offline_count(self):
        """
        获取线上、线下销售统计数据
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_online_offline_count = data.groupby(['is_online']).agg({'count': 'sum'}).reset_index()
        df_online_offline_count['is_online'] = df_online_offline_count['is_online'].map({0: '线下', 1: '线上'})

        # 查询数据库中所有数据
        db_online_offline_count = PublicStatistics.objects.filter(type=self.type, statistical_type='isonline',
                                                                  product_set_code=self.product_set_code)
        df_db_online_offline_count = pd.DataFrame(list(db_online_offline_count.values()))
        if df_db_online_offline_count.empty:
            df_db_online_offline_count = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_online_offline_count = df_db_online_offline_count[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_online_offline_count a left join df_online_offline_count b on a.key = b.is_online where b.is_online is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        # 数据写入统计表
        try:
            with transaction.atomic():
                for index, row in df_online_offline_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='isonline',
                                            product_set_code=self.product_set_code, key=row['is_online'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count PublicStatistics error:{e}')
        # 保证下期取数方便，线上线下累计值写入指标表
        online_count = df_online_offline_count[df_online_offline_count['is_online'] == '线上'].get('count',
                                                                                                   0).values.tolist()
        online_count = online_count[0] if online_count else 0
        indic_name_online = self.version + '-销量-线上-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name_online).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(online_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count online error:{e}')
        offline_count = df_online_offline_count[df_online_offline_count['is_online'] == '线下'].get('count',
                                                                                                    0).values.tolist()
        offline_count = offline_count[0] if offline_count else 0
        indic_name_offline = self.version + '-销量-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name_offline).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(offline_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count offline error:{e}')

        # 这边是个单的数据，需要单独处理
        data_personal = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        df_online_offline_count_personal = data_personal.groupby(['is_online']).agg({'count': 'sum'}).reset_index()
        df_online_offline_count_personal['is_online'] = df_online_offline_count_personal['is_online'].map(
            {0: '线下', 1: '线上'})

        # 查询数据库中所有数据
        db_online_offline_count_personal = PublicStatistics.objects.filter(type=self.type, statistical_type='isonline_personal',
                                                                  product_set_code=self.product_set_code)
        df_db_online_offline_count_personal = pd.DataFrame(list(db_online_offline_count_personal.values()))
        if df_db_online_offline_count_personal.empty:
            df_db_online_offline_count_personal = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_online_offline_count_personal = df_db_online_offline_count_personal[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_online_offline_count_personal a left join df_online_offline_count_personal b on a.key = b.is_online where b.is_online is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        # 数据写入统计表
        try:
            with transaction.atomic():
                for index, row in df_online_offline_count_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='isonline_personal',
                                            product_set_code=self.product_set_code, key=row['is_online'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count PublicStatistics personal error:{e}')

        offline_count_personal = df_online_offline_count_personal[
            df_online_offline_count_personal['is_online'] == '线下'].get('count',
                                                                         0).values.tolist()
        offline_count_personal = offline_count_personal[0] if offline_count_personal else 0
        indic_name_offline_personal = self.version + '-销量-个单-线下-累计值'
        code_personal = PublicIndicatorMain.objects.get(name=indic_name_offline_personal).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code_personal, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(offline_count_personal))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count offline_count_personal error:{e}')
        return df_online_offline_count

    def get_pay_type_count(self):
        """
        获取支付方式统计数据
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(data, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '个账'})
        df_pay_type_count = data.groupby(['pay_type']).agg({'count': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='pay',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_pay_type_count b on a.key = b.pay_type where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_count error:{e}')

        # 下面个单数据
        data_personal = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        df_pay_type_count_personal = data_personal.groupby(['pay_type']).agg({'count': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='pay_personal',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_pay_type_count_personal b on a.key = b.pay_type where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_count_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay_personal',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_count pay_personal error:{e}')
        return df_pay_type_count

    def get_pay_type_amount(self):
        """
        获取支付方式统计金额数据，历史逻辑只取了主险，保持一致
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(data, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '个账'})
        df_pay_type_amount = data.groupby(['pay_type']).agg({'amount': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='pay_amount',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_pay_type_amount b on a.key = b.pay_type where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_amount.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay_amount',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_amount error:{e}')
        # 下面个单数据
        data_personal = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        df_pay_type_amount_personal = data_personal.groupby(['pay_type']).agg({'amount': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='pay_amount_personal',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_pay_type_amount_personal b on a.key = b.pay_type where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_amount_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay_amount_personal',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_amount pay_amount_personal error:{e}')
        return df_pay_type_amount

    def get_medicare_type_count(self):
        """
        获取医保类型统计数据
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        # 筛选medicare_type为空的数据
        simplify_replace(data, 'medicare_type', {'EMPLOYEE': '职保', 'RESIDENT': '居保'}, '其他')

        df_medicare_type_count = data.groupby(['medicare_type']).agg({'count': 'sum', }).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='medicare',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_medicare_type_count b on a.key = b.medicare_type where b.medicare_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_medicare_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='medicare',
                                            product_set_code=self.product_set_code, key=row['medicare_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_type_count error:{e}')

        # 下面个单数据，暂时不处理
        data_personal = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        df_medicare_type_count_personal = data_personal.groupby(['medicare_type']).agg({'count': 'sum', }).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='medicare_personal',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_medicare_type_count_personal b on a.key = b.medicare_type where b.medicare_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_medicare_type_count_personal.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='medicare_personal',
                                            product_set_code=self.product_set_code, key=row['medicare_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_type_count medicare_personal error:{e}')
        return df_medicare_type_count

    def get_medicare_person_count(self):
        """
        获取医保人数统计数据，低频处理，便于后续理赔指标计算
        """
        with self.get_connection() as conn:
            df_medicare = pd.read_sql(query_sql('SQL_MEDICAL_TYPE').format(product_set_code=self.product_set_code,
                                                                           end_datetime=self.publish_time), conn)
            df_total_person = pd.read_sql(
                query_sql('SQL_INSURE_PERSON_MAIN').format(product_set_code=self.product_set_code,
                                                           end_datetime=self.publish_time), conn)
        simplify_replace(df_medicare, 'medicare_type', {'EMPLOYEE': '职工医保', 'RESIDENT': '居民医保'}, '其他医保')
        df_medicare = df_medicare.groupby(['medicare_type']).agg({'person_count': 'sum', 'count': 'sum'}).reset_index()
        df_medicare['indic_name'] = df_medicare['medicare_type'].apply(
            lambda x: self.version + '-人数-' + x + '-当期值')

        df_total = pd.DataFrame({'indic_name': [self.version + '-参保人数-当期值'], 'medicare_type': ['合计'],
                                 'person_count': [df_total_person['person_num'].sum()],
                                 'count': [df_medicare['count'].sum()]})
        df_medicare = pd.concat([df_medicare, df_total], ignore_index=True)
        df_medicare.reset_index(drop=True, inplace=True)
        # 获取指标code
        query_indicator_code(df_medicare)
        # 删除code为空的行
        df_medicare.dropna(subset=['code'], inplace=True)
        df_medicare.fillna(0, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_medicare.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['person_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_person_count error:{e}')

        return df_medicare

    def get_last_24_hour_count(self):
        """
        获取过去24小时统计数据
        """
        # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
        start_datetime = max(datetime.datetime.strptime(self.publish_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
            hours=24), datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S'))
        with self.get_connection() as conn:
            df_last_24_hour_count = pd.read_sql(
                query_sql('SQL_LAST_24_HOUR_COUNT').format(product_set_code=self.product_set_code,
                                                           start_datetime=start_datetime,
                                                           end_datetime=self.publish_time), conn)
        df_last_24_hour_count['datetime'] = df_last_24_hour_count['datetime'].apply(
            lambda x: datetime.datetime.strptime(x, '%Y-%m-%d %H:%M:%S'))
        # 获取过去24小时的完整日期范围，日期格式为'%Y-%m-%d %H:00:00'
        full_date_range = pd.date_range(start=start_datetime.replace(minute=0, second=0),
                                        end=self.publish_time[:13] + ':00:00', freq='H')
        # 合并数据，并填充缺失值
        df_last_24_hour_count = (
            df_last_24_hour_count.set_index('datetime')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'datetime'})
        )
        indic_name = self.version + '-销量-当期值(小时)'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_last_24_hour_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['datetime'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_last_24_hour_count error:{e}')
        return df_last_24_hour_count

    def get_daily_count(self):
        """
        获取日度销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_count = data.groupby(['date']).agg({'count': 'sum'}).reset_index()

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_count)
        # 填充缺失值
        df_daily_count = (
            df_daily_count.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        indic_name = self.version + '-销量-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_count error:{e}')
        return df_daily_count

    def get_daily_cumsum(self):
        """
        获取日度累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_cumsum = data.groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum['cumulative_count'] = df_daily_cumsum['count'].cumsum()
        df_daily_cumsum.drop(columns=['count'], inplace=True)

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_cumsum)
        # 填充缺失值
        df_daily_cumsum = (
            df_daily_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_cumsum error:{e}')
        return df_daily_cumsum

    def get_daily_personal_cumsum(self):
        """
        获取日度个单累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        df_daily_cumsum = data.groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum['cumulative_count'] = df_daily_cumsum['count'].cumsum()
        df_daily_cumsum.drop(columns=['count'], inplace=True)

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_cumsum)
        # 填充缺失值
        df_daily_cumsum = (
            df_daily_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-个单-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_personal_cumsum error:{e}')
        return df_daily_cumsum

    def get_daily_online_count(self):
        """
        获取日度线上销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_online_count = data.query('is_online == 1').groupby(['date']).agg({'count': 'sum'}).reset_index()

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_online_count)
        # 填充缺失值
        df_daily_online_count = (
            df_daily_online_count.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )

        indic_name = self.version + '-销量-线上-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_online_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_online_count error:{e}')
        return df_daily_online_count

    def get_daily_online_cumsum(self):
        """
        获取日度线上累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_online_cumsum = data.query('is_online == 1').groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_online_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_online_cumsum.reset_index(drop=True, inplace=True)
        df_daily_online_cumsum['cumulative_count'] = df_daily_online_cumsum['count'].cumsum()
        df_daily_online_cumsum.drop(columns=['count'], inplace=True)

        full_date_range = self.get_full_range_date(df_daily_online_cumsum)
        df_daily_online_cumsum = (
            df_daily_online_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_online_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-线上-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_online_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_online_cumsum error:{e}')
        return df_daily_online_cumsum

    def get_daily_offline_count(self):
        """
        获取日度线下销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_offline_count = data.query('is_online == 0').groupby(['date']).agg({'count': 'sum'}).reset_index()

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_offline_count)
        # 填充缺失值
        df_daily_offline_count = (
            df_daily_offline_count.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )

        indic_name = self.version + '-销量-线下-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_count error:{e}')
        return df_daily_offline_count

    def get_daily_offline_cumsum(self):
        """
        获取日度线下累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_offline_cumsum = data.query('is_online == 0').groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_offline_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_offline_cumsum.reset_index(drop=True, inplace=True)
        df_daily_offline_cumsum['cumulative_count'] = df_daily_offline_cumsum['count'].cumsum()
        df_daily_offline_cumsum.drop(columns=['count'], inplace=True)

        full_date_range = self.get_full_range_date(df_daily_offline_cumsum)
        df_daily_offline_cumsum = (
            df_daily_offline_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_offline_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_cumsum error:{e}')
        return df_daily_offline_cumsum

    def get_daily_offline_personal_cumsum(self):
        """
        获取日度线下个单累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[(data['main'] == 1) & (data['is_personal'] == 1)]
        df_daily_offline_cumsum = data.query('is_online == 0').groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_offline_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_offline_cumsum.reset_index(drop=True, inplace=True)
        df_daily_offline_cumsum['cumulative_count'] = df_daily_offline_cumsum['count'].cumsum()
        df_daily_offline_cumsum.drop(columns=['count'], inplace=True)

        full_date_range = self.get_full_range_date(df_daily_offline_cumsum)
        df_daily_offline_cumsum = (
            df_daily_offline_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_offline_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-个单-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_personal_cumsum error:{e}')
        return df_daily_offline_cumsum

    def get_today_count(self):
        """
        获取当日销量
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        today_count = data[data['date'] == self.today.date()][
            'count'].sum()
        indic_name = self.version + '-销量-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(today_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_today_count error:{e}')
        return today_count

    def get_daily_amount(self):
        """
        获取日度销售额，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_amount = data.groupby(['date']).agg({'amount': 'sum'}).reset_index()

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_amount)
        df_daily_amount = (
            df_daily_amount.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        indic_name = self.version + '-销售额-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_amount.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount error:{e}')
        return df_daily_amount

    def get_daily_amount_cumsum(self):
        """
        获取日度销售额累计值，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_amount_cumsum = data.groupby(['date']).agg({'amount': 'sum'}).reset_index()

        df_daily_amount_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_amount_cumsum.reset_index(drop=True, inplace=True)
        df_daily_amount_cumsum['cumulative_amount'] = df_daily_amount_cumsum['amount'].cumsum()
        df_daily_amount_cumsum.drop(columns=['amount'], inplace=True)
        full_date_range = self.get_full_range_date(df_daily_amount_cumsum)
        df_daily_amount_cumsum = (
            df_daily_amount_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_amount_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销售额-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_amount_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount_cumsum error:{e}')
        return df_daily_amount_cumsum

    def get_today_amount(self):
        """
        获取当日销售额
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        today_amount = data[data['date'] == self.today.date()][
            'amount'].sum()
        indic_name = self.version + '-销售额-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(today_amount))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_today_amount error:{e}')
        return today_amount

    def get_yesterday_count(self):
        """
        获取昨日销量
        """
        indic_name = self.version + '-销量-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        data['date'] = pd.to_datetime(data['date'])
        yesterday_count = data[data['date'] == self.yesterday]['count'].sum()
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.yesterday,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(yesterday_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_yesterday_count error:{e}')
        return yesterday_count

    def get_yesterday_amount(self):
        """
        获取昨日销售额
        """
        indic_name = self.version + '-销售额-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        yesterday_amount = data[data['date'] == self.yesterday.date()]['amount'].sum()
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.yesterday,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(yesterday_amount))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_yesterday_amount error:{e}')
        return yesterday_amount

    def get_online_source_info(self):
        """
        获取线上销售渠道的数据，包括排名、渠道、占比、总单数、今日参保、昨日参保、目标、完成率
        线上个单数量
        """
        sql = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code,
            statistical_type='当期值', unit='单',
            freq='周', start_datetime='2000-01-01',
            end_datetime=self.end_time[:4] + '-12-31'
        )
        with self.get_connection_dw() as conn_dw:
            df_week_target = pd.read_sql(sql, conn_dw)
            df_week_target = df_week_target[
                df_week_target['name'].str.contains('目标') & df_week_target['name'].str.contains('线上') &
                df_week_target['name'].str.contains('动态')]
            df_week_target['source'] = df_week_target['name'].apply(lambda x: x.split('-')[-2])
            df_week_target['value'] = df_week_target['value'].apply(lambda x: int(x))
        # 获取今日是本周的第几天、本周的开始日期、本周的结束日期
        day_of_week = get_day_of_week(self.end_time[:10])
        start_of_week, end_of_week = get_week_start_end(self.end_time[:10])
        week_number = get_reversed_week_number(self.end_time[:10])
        # 如果合并了最后两周数据，特殊处理
        if self.concat_end_week:
            if int(week_number) == 1 or int(week_number) == 2:
                week_target = df_week_target[df_week_target['end_time'] == self.end_time[:4] + '-12-31']
            else:
                week_target = df_week_target[df_week_target['end_time'] == end_of_week.strftime('%Y-%m-%d')]
        else:
            week_target = df_week_target[df_week_target['end_time'] == end_of_week.strftime('%Y-%m-%d')]
        week_target = week_target[['source', 'value']].rename(columns={'value': 'week_target'})
        if self.concat_end_week:
            # 如果要合并，最后两周的数据要特殊处理，这边动态目标如果合并，最后一周不会计算
            if int(week_number) == 1:
                day = (pd.to_datetime(self.end_time[:4] + '-12-31') - pd.to_datetime(start_of_week)).days
                day += 7
            elif int(week_number) == 2:
                # 计算最后一周在本年的天数据
                day = (pd.to_datetime(self.end_time[:4]+'-12-31')- pd.to_datetime(end_of_week)+ pd.to_timedelta(1, unit='d')).days
                day += 7
            else:
                day = 7
        else:
            if int(week_number) == 1:
                day = (pd.to_datetime(self.end_time[:4] + '-12-31') - pd.to_datetime(start_of_week)).days
            else:
                day = 7
        week_target['until_today_target'] = week_target['week_target'] / day * day_of_week

        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_online_source_info = data.query("is_online==1 and is_personal==1").groupby(['source', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_online_source_info['date'] = df_online_source_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        df_online_source_info['source'] = df_online_source_info['source'].apply(
            lambda x: '我的南京' if '我的南京' in x
            else '支付宝' if '支付宝' in x
            else '公众号')
        # 写入指标表
        full_date_range = pd.date_range(start=self.sale_start_date, end=datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S').date(), freq='D')
        # 拼接完整的日期、地区、产品组合
        df_combinations = pd.MultiIndex.from_product(
            [full_date_range, ['我的南京', '支付宝', '公众号']],
            names=['date', 'source'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_group = df_online_source_info.groupby(['source', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_group['date'] = pd.to_datetime(df_group['date'])
        df_indicator = sqldf(
            "select a.*,ifnull(b.count,0) as `value` from df_full_combinations a left join df_group b on a.date=b.date and a.source=b.source")
        df_indicator['indic_name'] = self.version + '-销量-线上-' + df_indicator['source'] + '-当期值'
        query_indicator_code(df_indicator)

        # 删除code为空的行
        df_indicator.dropna(subset=['code'], inplace=True)
        df_indicator.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_indicator.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['value']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_source_info indicator error:{e}')

        df_online_source = df_online_source_info.groupby(['source']).agg({'count': 'sum'}).reset_index()
        # 公众号加上特殊单量
        df_online_source['count'] = np.where(df_online_source['source'] == '公众号',
                                             df_online_source['count'] + self.special_count,
                                             df_online_source['count'])

        # 获取线上渠道目标
        df_online_main_sources = PublicTarget.objects.filter(product_set_code=self.product_set_code, type='online')
        df_online_main_sources = pd.DataFrame(list(df_online_main_sources.values()))[['name', 'code', 'target']].rename(
            columns={'name': 'source'})
        df_online_source = pd.merge(df_online_source, df_online_main_sources, how='outer', on='source')
        df_online_source['count'].fillna(0, inplace=True)
        # 占比计算
        df_online_source['ratio'] = round(df_online_source['count'] / df_online_source['count'].sum(), 3)

        df_online_source.sort_values(by='count', ascending=False, inplace=True)
        df_online_source.reset_index(drop=True, inplace=True)
        df_online_source['position'] = df_online_source.index + 1
        # 本周已完成的目标数据
        df_online_source_info['strp_date'] = df_online_source_info['date'].apply(lambda x: pd.to_datetime(x))
        df_week_complete = df_online_source_info[df_online_source_info['strp_date'] >= pd.to_datetime(start_of_week)]
        df_week_complete = df_week_complete.groupby(['source']).agg({'count': 'sum'}).reset_index().rename(
            columns={'count': 'week_count'})

        df_online_source_today = df_online_source_info[
            df_online_source_info['date'] == self.today.date().strftime('%Y-%m-%d')]
        df_online_source_yesterday = df_online_source_info[
            df_online_source_info['date'] == self.yesterday.date().strftime('%Y-%m-%d')]
        df_online_source_today = df_online_source_today.groupby(['source']).agg({'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})
        df_online_source_yesterday = df_online_source_yesterday.groupby(['source']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        df_online_source = pd.merge(df_online_source, df_online_source_today, how='left', on='source')
        df_online_source = pd.merge(df_online_source, df_online_source_yesterday, how='left', on='source')
        df_online_source = pd.merge(df_online_source, df_week_complete, how='left', on='source')
        df_online_source = pd.merge(df_online_source, week_target, how='left', on='source')

        df_online_source.fillna(0, inplace=True)
        number_sum = df_online_source.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T

        number_sum['insure_ratio'] = number_sum.apply(
            lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
            axis=1
        )
        number_sum['position'] = len(df_online_source) + 1
        number_sum['ratio'] = 1
        df_online_source = pd.concat([df_online_source, number_sum], axis=0).reset_index(drop=True)
        df_online_source.drop(columns=['code'], inplace=True)
        df_online_source['product_set_code'] = self.product_set_code
        df_online_source['publish_time'] = self.publish_time
        # 完成率计算
        df_online_source['target_ratio'] = df_online_source.apply(
            lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
            axis=1
        )
        df_online_source['week_complete_ratio'] = df_online_source.apply(
            lambda row: round(row['week_count'] / row['until_today_target'], 3) if row[
                                                                                       'until_today_target'] != 0 else 0,
            axis=1
        )
        df_online_source['week_target_ratio'] = df_online_source.apply(
            lambda row: round(row['week_count'] / row['week_target'], 3) if row['week_target'] != 0 else 0,
            axis=1
        )

        # 查询数据库中所有数据
        db_insure_online = InsureOnline.objects.filter(product_set_code=self.product_set_code)
        if db_insure_online.exists():
            df_db_insure_online = pd.DataFrame(list(db_insure_online.values()))[['id', 'channel_name']]
            delete_df = sqldf(
                "select a.id from df_db_insure_online a left join df_online_source b on a.channel_name = b.source where b.source is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                InsureOnline.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_online_source.iterrows():
                    custom_update_or_create(InsureOnline,
                                            product_set_code=row['product_set_code'], channel_name=row['source'],
                                            defaults={'publish_time': self.publish_time, 'total_count': row['count'],
                                                      'insure_ratio': row['ratio'],
                                                      'position': row['position'], 'target': row['target'],
                                                      'target_ratio': row['target_ratio'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count'],
                                                      'week_complete_ratio': row['week_complete_ratio'],
                                                      'week_target_ratio': row['week_target_ratio'],
                                                      'week_count': row['week_count'],
                                                      'week_target': row['week_target']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_source_info error:{e}')
        return df_online_source

    def get_offline_seller(self):
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_group = data.query("is_online==0 and is_personal==1").groupby(['seller', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_total = data.query("is_online==0 and is_personal==1").groupby(['date']).agg({'count': 'sum'}).reset_index()
        full_date_range = pd.date_range(start=self.sale_start_date, end=self.today.date(), freq='D')
        # 拼接完整的日期、地区、产品组合
        df_combinations = pd.MultiIndex.from_product(
            [full_date_range,
             ['中国人保', '中国人寿', '国寿财险', '中华联合', '阳光财险', '紫金保险', '太保产险', '利安人寿',
              '中银保险']],
            names=['date', 'seller'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_group['date'] = pd.to_datetime(df_group['date'])
        df_indicator = sqldf(
            "select a.*,ifnull(b.count,0) as `value` from df_full_combinations a left join df_group b on a.date=b.date and a.seller=b.seller")
        df_indicator['indic_name'] = self.version + '-销量-线下-个单-' + df_indicator['seller'] + '-当期值'

        full_date_range = self.get_full_range_date(df_total)
        df_total = (
            df_total.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_total['indic_name'] = self.version + '-销量-线下-个单-当期值'
        df_total.rename(columns={'count': 'value'}, inplace=True)
        df_indicator = pd.concat([df_indicator, df_total], axis=0).reset_index(drop=True)
        query_indicator_code(df_indicator)
        # 删除code为空的行
        df_indicator.dropna(subset=['code'], inplace=True)
        df_indicator.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_indicator.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['value']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_offline_seller indicator error:{e}')
        return df_indicator

    def get_offline_seller_info(self):
        """
        获取线下保司的数据，包括排名、保司、占比、代理人数、人均出单、个单、团单、今日参保、昨日参保、目标、完成率
        """
        sql = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code,
            statistical_type='当期值', unit='单',
            freq='周', start_datetime='2000-01-01',
            end_datetime=self.end_time[:4] + '-12-31'
        )
        with self.get_connection_dw() as conn_dw:
            df_week_target = pd.read_sql(sql, conn_dw)
            df_week_target = df_week_target[
                df_week_target['name'].str.contains('目标') & df_week_target['name'].str.contains('线下') &
                df_week_target['name'].str.contains('动态')]
            df_week_target['seller'] = df_week_target['name'].apply(lambda x: x.split('-')[-2])
            df_week_target['value'] = df_week_target['value'].apply(lambda x: int(x))
        # 获取今日是本周的第几天、本周的开始日期、本周的结束日期
        day_of_week = get_day_of_week(self.end_time[:10])
        start_of_week, end_of_week = get_week_start_end(self.end_time[:10])
        week_number = get_reversed_week_number(self.end_time[:10])
        # 如果合并了最后两周数据，特殊处理
        if self.concat_end_week:
            if int(week_number) == 1 or int(week_number) == 2:
                week_target = df_week_target[df_week_target['end_time'] == self.end_time[:4] + '-12-31']
            else:
                week_target = df_week_target[df_week_target['end_time'] == end_of_week.strftime('%Y-%m-%d')]
        else:
            week_target = df_week_target[df_week_target['end_time'] == end_of_week.strftime('%Y-%m-%d')]
        week_target = week_target[['seller', 'value']].rename(columns={'value': 'week_target'})

        if self.concat_end_week:
            # 如果要合并，最后两周的数据要特殊处理，这边动态目标如果合并，最后一周不会计算
            if int(week_number) == 1:
                day = (pd.to_datetime(self.end_time[:4] + '-12-31') - pd.to_datetime(start_of_week)).days
                day += 7
            elif int(week_number) == 2:
                # 计算最后一周在本年的天数据
                day = (pd.to_datetime(self.end_time[:4]+'-12-31')- pd.to_datetime(end_of_week)+ pd.to_timedelta(1, unit='d')).days
                day += 7
            else:
                day = 7
        else:
            if int(week_number) == 1:
                day = (pd.to_datetime(self.end_time[:4] + '-12-31') - pd.to_datetime(start_of_week)).days
            else:
                day = 7
        week_target['until_today_target'] = week_target['week_target'] / day * day_of_week

        with self.get_connection() as conn:
            df_seller_person = pd.read_sql(
                query_sql('SQL_SELLER_PERSON').format(product_set_code=self.product_set_code), conn)
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        data = data[~data['seller_name'].str.contains('线上')]
        # data = data[]
        df_offline_seller_info = data.query("is_online==0").groupby(['seller', 'date', 'is_personal']).agg(
            {'count': 'sum'}).reset_index()
        df_offline_seller = df_offline_seller_info.groupby(['seller']).agg({'count': 'sum'}).reset_index()
        df_offline_seller = pd.merge(df_offline_seller, df_seller_person, how='left', on='seller')

        # 个单数量
        df_offline_seller_personal = df_offline_seller_info.query("is_personal==1").groupby(['seller']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'personal_count'})
        # 团单数量
        # 数据库取值逻辑
        # df_offline_seller_group = df_offline_seller_info.query("is_personal==0").groupby(['seller']).agg(
        #     {'count': 'sum'}).reset_index().rename(columns={'count': 'group_count'})
        # 手动取值逻辑
        df_offline_seller_group = self.get_seller_group_report_data()
        df_offline_seller_group = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
        df_offline_seller_group.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
        df_offline_seller_group = df_offline_seller_group[['seller', 'group_count']]
        # print(df_offline_seller_group)
        # df_offline_seller_group = pd.DataFrame(self.SELLERS)[['short_name','group_count']].rename(columns={'short_name':'seller'})
        # print(df_offline_seller_group)
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_personal, how='left', on='seller')
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_group, how='left', on='seller')
        # 手动计算
        df_offline_seller['count'] = df_offline_seller['personal_count'] + df_offline_seller['group_count']

        # 人均值只计算个单，20240921
        df_offline_seller['average_count'] = round(
            df_offline_seller['personal_count'] / df_offline_seller['employee_num'], 1)
        # print(df_offline_seller)

        df_offline_seller.fillna(0, inplace=True)
        df_offline_seller['insure_ratio'] = round(df_offline_seller['count'] / df_offline_seller['count'].sum(), 3)
        df_target = PublicTarget.objects.filter(product_set_code=self.product_set_code, type='agent')
        df_target = pd.DataFrame(list(df_target.values()))[['short_name', 'target']].rename(
            columns={'short_name': 'seller'})
        df_offline_seller = pd.merge(df_offline_seller, df_target, how='outer', on='seller')
        df_offline_seller['target_ratio'] = df_offline_seller.apply(
            lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
            axis=1
        )
        # 排序，将seller为中国人保放在一个，其他按照target_ratio排序
        df_offline_zgrb = df_offline_seller[df_offline_seller['seller'] == '中国人保']
        df_offline_other = df_offline_seller[df_offline_seller['seller'] != '中国人保']
        df_offline_other.sort_values(by=['target_ratio'], ascending=False, inplace=True)
        # 合并成完整数据
        df_offline_seller = pd.concat([df_offline_zgrb, df_offline_other], axis=0).reset_index(drop=True)

        # 本周已完成的目标数据
        df_offline_seller_info['strp_date'] = df_offline_seller_info['date'].apply(lambda x: pd.to_datetime(x))
        df_week_complete = df_offline_seller_info[df_offline_seller_info['strp_date'] >= pd.to_datetime(start_of_week)]
        df_week_complete = df_week_complete.groupby(['seller']).agg({'count': 'sum'}).reset_index().rename(
            columns={'count': 'week_count'})

        df_offline_seller_today = df_offline_seller_info[
            df_offline_seller_info['date'] == self.today.date()]
        df_offline_seller_yesterday = df_offline_seller_info[
            df_offline_seller_info['date'] == self.yesterday.date()]
        df_offline_seller_today = df_offline_seller_today.groupby(['seller']).agg(
            {'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})
        df_offline_seller_yesterday = df_offline_seller_yesterday.groupby(['seller']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_today, how='left', on='seller')
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_yesterday, how='left', on='seller')
        df_offline_seller = pd.merge(df_offline_seller, df_week_complete, how='left', on='seller')
        df_offline_seller = pd.merge(df_offline_seller, week_target, how='left', on='seller')
        df_offline_seller.fillna(0, inplace=True)

        df_offline_seller.reset_index(drop=True, inplace=True)
        df_offline_seller['position'] = df_offline_seller.index + 1
        number_sum = df_offline_seller.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        # 人均值计算
        number_sum['average_count'] = number_sum['personal_count'] / number_sum['employee_num']
        number_sum['insure_ratio'] = 1
        number_sum['position'] = len(df_offline_seller) + 1
        number_sum['insure_ratio'] = number_sum['insure_ratio'].apply(lambda x: 1 if x > 1 else x)
        df_offline_seller = pd.concat([df_offline_seller, number_sum], axis=0).reset_index(drop=True)
        df_offline_seller['product_set_code'] = self.product_set_code
        df_offline_seller['publish_time'] = self.publish_time
        # 如果目标还未确认，数据库赋值0，这边判定后直接赋值0
        df_offline_seller['target_ratio'] = df_offline_seller.apply(
            lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
            axis=1
        )
        df_offline_seller['week_complete_ratio'] = df_offline_seller.apply(
            lambda row: round(row['week_count'] / row['until_today_target'], 3) if row[
                                                                                       'until_today_target'] != 0 else 0,
            axis=1
        )
        df_offline_seller['week_target_ratio'] = df_offline_seller.apply(
            lambda row: round(row['week_count'] / row['week_target'], 3) if row['week_target'] != 0 else 0,
            axis=1
        )

        # 查询数据库中所有数据
        db_insure_agent = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        if db_insure_agent.exists():
            df_db_insure_agent = pd.DataFrame(list(db_insure_agent.values()))[['id', 'name']]
            delete_df = sqldf(
                "select a.id from df_db_insure_agent a left join df_offline_seller b on a.name = b.seller where b.seller is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                InsureAgent.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_offline_seller.iterrows():
                    custom_update_or_create(InsureAgent,
                                            product_set_code=row['product_set_code'], name=row['seller'],
                                            defaults={'publish_time': self.publish_time,
                                                      'employee_count': row['employee_num'],
                                                      'average_count': row['average_count'],
                                                      'total_count': row['count'],
                                                      'personal_count': row['personal_count'],
                                                      'group_count': row['group_count'],
                                                      'insure_ratio': row['insure_ratio'], 'position': row['position'],
                                                      'target': row['target'],
                                                      'target_ratio': row['target_ratio'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count'],
                                                      'week_complete_ratio': row['week_complete_ratio'],
                                                      'week_target_ratio': row['week_target_ratio'],
                                                      'week_count': row['week_count'],
                                                      'week_target': row['week_target']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_offline_seller_info error:{e}')
        return df_offline_seller

    def get_area_info(self):
        """
        地区参保数据，包括排名、参保地、占比、总单数、今日参保、昨日参保、参保率
        """
        df_area = PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code)
        df_area = pd.DataFrame(list(df_area.values()))[['count', 'code', 'name']].rename(
            columns={'code': 'area_code', 'count': 'base_insure'})
        # 名称标准化转化
        df_area['name'] = df_area['name'].apply(lambda x: x.replace('栖霞区', '栖霞区(含经济开发区)'))
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # 不统计团单，团单无法区分地区
        data = data.query("is_personal==1")
        # code部分超过6位，取前6位
        data['area_code'] = data['area_code'].apply(lambda x: x[:6] if x is not None else x)
        # area_code为空的置为999999，area_name为空的置为'其他'
        data.fillna({'area_code': '999999', 'area_name': '其他'}, inplace=True)
        # 地区合并，历史旧编码导致
        data.loc[data['area_code'] == '320108', 'area_name'] = '江北新区'
        data.loc[data['area_code'] == '320142', 'area_name'] = '溧水区'
        data.loc[data['area_code'] == '320125', 'area_name'] = '高淳区'
        data.loc[data['area_name'] == '溧水区', 'area_code'] = '320117'
        data.loc[data['area_name'] == '高淳区', 'area_code'] = '320118'
        data.loc[data['area_name'] == '江北新区', 'area_code'] = '320140'
        data.loc[data['area_code'] == '320131', 'area_name'] = '栖霞区(含经济开发区)'
        data.loc[data['area_name'] == '栖霞区(含经济开发区)', 'area_code'] = '320113'

        df_area_info = data.groupby(['area_code', 'area_name', 'date']).agg({'count': 'sum'}).reset_index()
        df_area_total = df_area_info.groupby(['area_code', 'area_name']).agg({'count': 'sum'}).reset_index()
        df_area_total = pd.merge(df_area_total, df_area, how='outer', on='area_code')
        # name如果为空，用area_name代替
        df_area_total['name'] = df_area_total['name'].fillna(df_area_total['area_name'])
        # 如果name为其他，area_code置为999999
        df_area_total.loc[df_area_total['name'] == '其他', 'area_code'] = '999999'
        df_area_total = df_area_total.groupby(['area_code', 'name']).agg(
            {'count': 'sum', 'base_insure': 'first'}).reset_index()
        df_area_total.fillna(0, inplace=True)

        df_area_total['count'] = df_area_total['count'].astype(int)
        # 计算占比
        if df_area_total['count'].sum() == 0:
            df_area_total['ratio'] = 0
        else:
            df_area_total['ratio'] = round(df_area_total['count'] / df_area_total['count'].sum(), 3)

        df_area_total['insure_ratio'] = df_area_total.apply(
            lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
            axis=1
        )
        df_area_total.sort_values(by=['insure_ratio', 'count'], ascending=False, inplace=True)
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total['position'] = df_area_total.index + 1
        # name如果为其他，放到最后，调整索引
        df_area_total.loc[df_area_total['name'] == '其他', 'position'] = df_area_total.shape[0] + 1
        df_area_total.sort_values(by='position', inplace=True)
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total['position'] = df_area_total.index + 1
        df_area_today = df_area_info[
            df_area_info['date'] == self.today.date()].groupby(['area_code']).agg(
            {'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})

        df_area_yesterday = df_area_info[
            df_area_info['date'] == self.yesterday.date()].groupby(['area_code']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        if df_area_today.empty:
            df_area_total['today_count'] = 0
        else:
            df_area_total = pd.merge(df_area_total, df_area_today, how='left', on='area_code')
            df_area_total['today_count'].fillna(0, inplace=True)
        if df_area_yesterday.empty:
            df_area_total['yesterday_count'] = 0
        else:
            df_area_total = pd.merge(df_area_total, df_area_yesterday, how='left', on='area_code')
            df_area_total['yesterday_count'].fillna(0, inplace=True)
        number_sum = df_area_total.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        number_sum['ratio'] = number_sum['ratio'].apply(lambda x: 1 if x > 1 else x)
        number_sum['position'] = len(df_area_total) + 1
        # 如果来不及订目标，则target_ratio为0，比率也都是0
        number_sum['insure_ratio'] = number_sum.apply(
            lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
            axis=1
        )
        df_area_total = pd.concat([df_area_total, number_sum], axis=0).reset_index(drop=True)
        df_area_total['product_set_code'] = self.product_set_code
        df_area_total['publish_time'] = self.publish_time
        # 查询数据库中所有数据
        db_insure_area = InsureArea.objects.filter(product_set_code=self.product_set_code)
        if db_insure_area.exists():
            df_db_insure_area = pd.DataFrame(list(db_insure_area.values()))[['id', 'name']]
            delete_df = sqldf(
                "select a.id from df_db_insure_area a left join df_area_total b on a.name = b.name where b.name is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                # print(delete_ids)
                InsureArea.objects.filter(id__in=delete_ids).delete()
        df_area_total.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_area_total.iterrows():
                    custom_update_or_create(InsureArea,
                                            product_set_code=row['product_set_code'], name=row['name'],
                                            defaults={'publish_time': self.publish_time, 'total_count': row['count'],
                                                      'ratio': row['ratio'], 'insure_ratio': row['insure_ratio'],
                                                      'position': row['position'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_area_info error:{e}')
        return df_area_total

    def get_age_gender_count(self):
        """
        获取年龄性别数据
        """
        with self.get_connection() as conn:
            df_age_gender_count = pd.read_sql(
                query_sql('SQL_AGE_GENDER').format(product_set_code=self.product_set_code,
                                                   end_datetime=self.publish_time),
                conn)
        return df_age_gender_count

    def cache_age_gender_count(self):
        """
        缓存年龄性别数据，主动推送，保证数据的实时性
        """
        try:
            df_age_gender_count = self.get_age_gender_count()
            self.update_cache('get_age_gender_count', df_age_gender_count)
            return df_age_gender_count
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_age_gender_count error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_age_gender_count error:{e}')

    def get_age_gender(self):
        """
        年龄性别数据，包括年龄段、性别、参保数
        """
        df_age_gender = self.get_from_cache('get_age_gender_count')
        age_bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, float('inf')]
        age_labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-59', '60-69', '70-79', '80-89', '90及以上']
        age_group(df_age_gender, 'age', age_bins, age_labels)
        df_age_gender['gender'].replace({'FEMALE': '女', 'MALE': '男'}, inplace=True)
        df_age_gender_group = df_age_gender.groupby(['age_group', 'gender']).agg({'count': 'sum'}).reset_index()

        # 如果组合在df_age_gender_group中不存在，则增加一条，值为0，保证数据完整
        for x in age_labels:
            for s in ['男', '女']:
                if len(df_age_gender_group[
                           (df_age_gender_group['age_group'] == x) & (df_age_gender_group['gender'] == s)]) == 0:
                    df_age_gender_group = df_age_gender_group._append({'age_group': x, 'gender': s, 'count': 0},
                                                                      ignore_index=True)
        df_age_gender_group.sort_values(by=['age_group', 'gender'], inplace=True)
        df_age_gender_group.reset_index(drop=True, inplace=True)
        df_age_gender_group['product_set_code'] = self.product_set_code
        df_age_gender_group['publish_time'] = self.publish_time
        try:
            with transaction.atomic():
                for index, row in df_age_gender_group.iterrows():
                    custom_update_or_create(InsureAgeSex,
                                            product_set_code=row['product_set_code'], sex=row['gender'],
                                            age_distribution=row['age_group'],
                                            defaults={'value': row['count'], 'publish_time': row['publish_time']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_gender error:{e}')
        return df_age_gender_group

    def get_age_range(self):
        """
        获取年龄范围统计数据、包括平均年龄、年龄中位数
        """
        df_age_gender = self.get_from_cache('get_age_gender_count')
        age_bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, float('inf')]
        age_labels = ['0-9', '10-19', '20-29', '30-39', '40-49', '50-59', '60-69', '70-79', '80-89', '90及以上']
        age_group(df_age_gender, 'age', age_bins, age_labels)
        df_age_group = df_age_gender.groupby(['age_group']).agg({'count': 'sum'}).reset_index()
        # 如果组合在df_age_group中不存在，则增加一条，值为0，保证数据完整
        for x in age_labels:
            if len(df_age_group[(df_age_group['age_group'] == x)]) == 0:
                df_age_group = df_age_group._append({'age_group': x, 'count': 0},
                                                    ignore_index=True)
        df_age_group['count'] = df_age_group['count'].astype(int)
        if df_age_group['count'].sum() == 0:
            df_age_group['ratio'] = 0
        else:
            df_age_group['ratio'] = round(df_age_group['count'] / df_age_group['count'].sum(), 4)
        
        # 查询数据库中所有数据
        db_age_group = PublicStatistics.objects.filter(type=self.type, statistical_type='age_ratio',
                                                       product_set_code=self.product_set_code)
        if db_age_group.exists():
            df_db_age_group = pd.DataFrame(list(db_age_group.values()))[['id', 'key']]
            delete_df = sqldf(
                "select a.id from df_db_age_group a left join df_age_group b on a.key = b.age_group where b.age_group is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_age_group.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='age_ratio',
                                            product_set_code=self.product_set_code, key=row['age_group'],
                                            defaults={'publish_time': self.publish_time, 'value': row['ratio']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range error:{e}')
        # 计算平均年龄
        df_age_gender['count'] = df_age_gender['count'].astype(int)
        total_people = df_age_gender['count'].sum()
        weighted_age_sum = (df_age_gender['age'] * df_age_gender['count']).sum()
        if total_people == 0:
            average_age = 0
        else:
            average_age = round(weighted_age_sum / total_people, 0)

        indic_name_average = self.version + '-年龄-平均值'
        code = PublicIndicatorMain.objects.get(name=indic_name_average).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(average_age))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range_average error:{e}')

        # 计算中位数
        all_ages = []
        for index, row in df_age_gender.iterrows():
            all_ages.extend([row['age']] * row['count'])
        median_age = pd.Series(all_ages).median()
        median_age = 0 if pd.isnull(median_age) else median_age
        indic_name_median = self.version + '-年龄-中位值'
        code = PublicIndicatorMain.objects.get(name=indic_name_median).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time, 'value': Decimal(str(median_age))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range_median error:{e}')
        return df_age_group, average_age, median_age

    def get_renewal_ratio(self):
        """
        获取续保占比、续保率数据（只计算个单）
        """
        with self.get_connection() as conn:
            df_renewal_ratio = pd.read_sql(
                query_sql('SQL_RENEWAL_RATIO_PERSONAL').format(product_set_code=self.product_set_code,
                                                      previous_product_set_code=self.previous_product_set_code,
                                                      end_datetime=self.publish_time), conn)
        insure_person_indic_name = self.previous_version + '-参保人数-个单-当期值'
        insure_person_indic_code = PublicIndicatorMain.objects.get(name=insure_person_indic_name).code
        # 直接从查询结果构建 DataFrame
        queryset = PublicIndicatorData.objects.filter(code=insure_person_indic_code).order_by('-end_time')
        df_insure_person = pd.DataFrame(list(queryset.values()))[['value']].rename(columns={'value': 'person_num'})

        # 如果需要确保只获取最后一个对象
        if not df_insure_person.empty:
            df_insure_person = df_insure_person.iloc[:1]
        previous_insure_person = df_insure_person['person_num'].values[0] if not df_insure_person.empty else 0
        # 如果除数为0特殊处理
        df_renewal_ratio['renewal_ratio'].fillna(0, inplace=True)
        if previous_insure_person == 0:
            renewal_percent = 0
        else:
            renewal_percent = round(df_renewal_ratio['renewal_person'].values[0] / previous_insure_person, 4) * 100
        indic_name_percent = self.previous_version + '-续保率-当期值'
        indic_name = self.version + '-续保占比-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        code_percent = PublicIndicatorMain.objects.get(name=indic_name_percent).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(
                                                      str(df_renewal_ratio['renewal_ratio'].values[0] * 100))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_renewal_ratio error:{e}')

        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code_percent, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(renewal_percent))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_renewal_ratio percent error:{e}')
        return df_renewal_ratio, renewal_percent

    def get_cumulative_yoy(self):
        """
        获取累计同比数据
        """
        indic_name = self.version + '-销量-累计值'
        previous_indic_name = self.previous_version + '-销量-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        previous_code = PublicIndicatorMain.objects.get(name=previous_indic_name).code
        cumsum_count = PublicIndicatorData.objects.filter(code=code)
        df_cumsum_count = pd.DataFrame(cumsum_count.values())[['end_time', 'value']]
        previous_cumsum_count = PublicIndicatorData.objects.filter(code=previous_code)
        df_previous_cumsum_count = pd.DataFrame(previous_cumsum_count.values())[['end_time', 'value']].rename(
            columns={'value': 'previous_value'})
        df_previous_cumsum_count['month_day'] = df_previous_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))
        df_cumsum_count['month_day'] = df_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))

        df_cumsum_total = pd.merge(df_previous_cumsum_count, df_cumsum_count, on='month_day', how='outer')
        df_cumsum_total.sort_values(by='end_time_y', ascending=True, inplace=True)
        df_cumsum_total.dropna(subset=['end_time_y'], inplace=True)  # 排序后，本期开始缺失数据会放到最后，导致数据计算有误，所以本期没有的数据删除
        df_cumsum_total = df_cumsum_total[['month_day', 'end_time_y', 'value', 'previous_value']].rename(
            columns={'end_time_y': 'end_time'})
        df_cumsum_total.fillna(method='ffill', inplace=True)
        df_cumsum_total.dropna(subset=['value'], inplace=True)
        df_cumsum_total.reset_index(inplace=True, drop=True)
        end_time_date = pd.to_datetime(self.end_time).date()
        df_cumsum_total = df_cumsum_total[df_cumsum_total['end_time'].dt.date <= end_time_date]
        df_cumsum_total.fillna(0, inplace=True)
        # 如果previous_value为0，则yoy赋值为100%
        df_cumsum_total['previous_value'] = df_cumsum_total['previous_value'].astype(float)
        df_cumsum_total['value'] = df_cumsum_total['value'].astype(float)
        df_cumsum_total['yoy'] = df_cumsum_total.apply(
            lambda row: (row['value'] - row['previous_value']) / row[
                'previous_value'] * 100 if row['previous_value'] != 0 else 100,
            axis=1
        )

        df_cumsum_total['yoy'] = df_cumsum_total['yoy'].apply(lambda x: round(x, 2))
        indic_name_yoy = self.version + '-销量-累计同比'
        code = PublicIndicatorMain.objects.get(name=indic_name_yoy).code
        try:
            with transaction.atomic():
                for index, row in df_cumsum_total.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['end_time'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['yoy']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_cumulative_yoy error:{e}')
        return df_cumsum_total

    def get_personal_cumulative_yoy(self):
        """
        获取个单累计同比数据
        """
        indic_name = self.version + '-销量-个单-累计值'
        previous_indic_name = self.previous_version + '-销量-个单-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        previous_code = PublicIndicatorMain.objects.get(name=previous_indic_name).code
        cumsum_count = PublicIndicatorData.objects.filter(code=code)
        df_cumsum_count = pd.DataFrame(cumsum_count.values())[['end_time', 'value']]
        previous_cumsum_count = PublicIndicatorData.objects.filter(code=previous_code)
        df_previous_cumsum_count = pd.DataFrame(previous_cumsum_count.values())[['end_time', 'value']].rename(
            columns={'value': 'previous_value'})
        df_previous_cumsum_count['month_day'] = df_previous_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))
        df_cumsum_count['month_day'] = df_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))

        df_cumsum_total = pd.merge(df_previous_cumsum_count, df_cumsum_count, on='month_day', how='outer')
        df_cumsum_total.sort_values(by='end_time_y', ascending=True, inplace=True)
        df_cumsum_total.dropna(subset=['end_time_y'], inplace=True)  # 排序后，本期开始缺失数据会放到最后，导致数据计算有误，所以本期没有的数据删除
        df_cumsum_total = df_cumsum_total[['month_day', 'end_time_y', 'value', 'previous_value']].rename(
            columns={'end_time_y': 'end_time'})
        df_cumsum_total.fillna(method='ffill', inplace=True)
        df_cumsum_total.dropna(subset=['value'], inplace=True)
        df_cumsum_total.reset_index(inplace=True, drop=True)
        end_time_date = pd.to_datetime(self.end_time).date()
        df_cumsum_total = df_cumsum_total[df_cumsum_total['end_time'].dt.date <= end_time_date]
        df_cumsum_total.fillna(0, inplace=True)
        # 如果previous_value为0，则yoy赋值为100%
        df_cumsum_total['previous_value'] = df_cumsum_total['previous_value'].astype(float)
        df_cumsum_total['value'] = df_cumsum_total['value'].astype(float)
        df_cumsum_total['yoy'] = df_cumsum_total.apply(
            lambda row: (row['value'] - row['previous_value']) / row[
                'previous_value'] * 100 if row['previous_value'] != 0 else 100,
            axis=1
        )

        df_cumsum_total['yoy'] = df_cumsum_total['yoy'].apply(lambda x: round(x, 2))
        indic_name_yoy = self.version + '-销量-个单-累计同比'
        code = PublicIndicatorMain.objects.get(name=indic_name_yoy).code
        try:
            with transaction.atomic():
                for index, row in df_cumsum_total.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['end_time'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['yoy']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_personal_cumulative_yoy error:{e}')
        return df_cumsum_total

    def get_online_cumulative_yoy(self):
        """
        获取线上累计同比数据
        """
        indic_name = self.version + '-销量-线上-累计值'
        previous_indic_name = self.previous_version + '-销量-线上-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        previous_code = PublicIndicatorMain.objects.get(name=previous_indic_name).code
        cumsum_count = PublicIndicatorData.objects.filter(code=code)
        df_cumsum_count = pd.DataFrame(cumsum_count.values())[['end_time', 'value']]
        previous_cumsum_count = PublicIndicatorData.objects.filter(code=previous_code)
        df_previous_cumsum_count = pd.DataFrame(previous_cumsum_count.values())[['end_time', 'value']].rename(
            columns={'value': 'previous_value'})
        df_previous_cumsum_count['month_day'] = df_previous_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))
        df_cumsum_count['month_day'] = df_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))

        df_cumsum_online_total = pd.merge(df_previous_cumsum_count, df_cumsum_count, on='month_day', how='outer')
        df_cumsum_online_total.sort_values(by='end_time_y', ascending=True, inplace=True)
        df_cumsum_online_total.dropna(subset=['end_time_y'], inplace=True)  # 排序后，本期开始缺失数据会放到最后，导致数据计算有误，所以本期没有的数据删除
        df_cumsum_online_total = df_cumsum_online_total[['month_day', 'end_time_y', 'value', 'previous_value']].rename(
            columns={'end_time_y': 'end_time'})
        df_cumsum_online_total.fillna(method='ffill', inplace=True)
        df_cumsum_online_total.dropna(subset=['value'], inplace=True)
        df_cumsum_online_total.reset_index(inplace=True, drop=True)
        end_time_date = pd.to_datetime(self.end_time).date()
        df_cumsum_online_total = df_cumsum_online_total[df_cumsum_online_total['end_time'].dt.date <= end_time_date]
        df_cumsum_online_total.fillna(0, inplace=True)
        # 如果previous_value为0，则yoy赋值为100%
        df_cumsum_online_total['previous_value'] = df_cumsum_online_total['previous_value'].astype(float)
        df_cumsum_online_total['value'] = df_cumsum_online_total['value'].astype(float)
        df_cumsum_online_total['yoy'] = df_cumsum_online_total.apply(
            lambda row: (row['value'] - row['previous_value']) / row[
                'previous_value'] * 100 if row['previous_value'] != 0 else 100,
            axis=1
        )

        df_cumsum_online_total['yoy'] = df_cumsum_online_total['yoy'].apply(lambda x: round(x, 2))
        indic_name_yoy = self.version + '-销量-线上-累计同比'
        code = PublicIndicatorMain.objects.get(name=indic_name_yoy).code
        try:
            with transaction.atomic():
                for index, row in df_cumsum_online_total.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['end_time'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['yoy']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_cumulative_yoy error:{e}')
        return df_cumsum_online_total

    def get_offline_cumulative_yoy(self):
        """
        获取线下累计同比数据
        """
        indic_name = self.version + '-销量-线下-累计值'
        previous_indic_name = self.previous_version + '-销量-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        previous_code = PublicIndicatorMain.objects.get(name=previous_indic_name).code
        cumsum_count = PublicIndicatorData.objects.filter(code=code)
        df_cumsum_count = pd.DataFrame(cumsum_count.values())[['end_time', 'value']]
        previous_cumsum_count = PublicIndicatorData.objects.filter(code=previous_code)
        df_previous_cumsum_count = pd.DataFrame(previous_cumsum_count.values())[['end_time', 'value']].rename(
            columns={'value': 'previous_value'})
        df_previous_cumsum_count['month_day'] = df_previous_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))
        df_cumsum_count['month_day'] = df_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))

        df_cumsum_offline_total = pd.merge(df_previous_cumsum_count, df_cumsum_count, on='month_day', how='outer')
        df_cumsum_offline_total.sort_values(by='end_time_y', ascending=True, inplace=True)
        df_cumsum_offline_total.dropna(subset=['end_time_y'], inplace=True)  # 排序后，本期开始缺失数据会放到最后，导致数据计算有误，所以本期没有的数据删除
        df_cumsum_offline_total = df_cumsum_offline_total[
            ['month_day', 'end_time_y', 'value', 'previous_value']].rename(
            columns={'end_time_y': 'end_time'})
        df_cumsum_offline_total.fillna(method='ffill', inplace=True)
        df_cumsum_offline_total.dropna(subset=['value'], inplace=True)
        df_cumsum_offline_total.reset_index(inplace=True, drop=True)
        end_time_date = pd.to_datetime(self.end_time).date()
        df_cumsum_offline_total = df_cumsum_offline_total[df_cumsum_offline_total['end_time'].dt.date <= end_time_date]
        df_cumsum_offline_total.fillna(0, inplace=True)
        df_cumsum_offline_total['previous_value'] = df_cumsum_offline_total['previous_value'].astype(float)
        df_cumsum_offline_total['value'] = df_cumsum_offline_total['value'].astype(float)
        # 如果previous_value为0，则yoy赋值为100%
        df_cumsum_offline_total['yoy'] = df_cumsum_offline_total.apply(
            lambda row: (row['value'] - row['previous_value']) / row[
                'previous_value'] * 100 if row['previous_value'] != 0 else 100,
            axis=1
        )

        df_cumsum_offline_total['yoy'] = df_cumsum_offline_total['yoy'].apply(lambda x: round(x, 2))
        indic_name_yoy = self.version + '-销量-线下-累计同比'
        code = PublicIndicatorMain.objects.get(name=indic_name_yoy).code
        try:
            with transaction.atomic():
                for index, row in df_cumsum_offline_total.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['end_time'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['yoy']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_offline_cumulative_yoy error:{e}')
        return df_cumsum_offline_total

    def get_offline_personal_cumulative_yoy(self):
        """
        获取线下个单累计同比数据
        """
        indic_name = self.version + '-销量-个单-线下-累计值'
        previous_indic_name = self.previous_version + '-销量-个单-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        previous_code = PublicIndicatorMain.objects.get(name=previous_indic_name).code
        cumsum_count = PublicIndicatorData.objects.filter(code=code)
        df_cumsum_count = pd.DataFrame(cumsum_count.values())[['end_time', 'value']]
        previous_cumsum_count = PublicIndicatorData.objects.filter(code=previous_code)
        df_previous_cumsum_count = pd.DataFrame(previous_cumsum_count.values())[['end_time', 'value']].rename(
            columns={'value': 'previous_value'})
        df_previous_cumsum_count['month_day'] = df_previous_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))
        df_cumsum_count['month_day'] = df_cumsum_count['end_time'].apply(
            lambda x: x.strftime('%m-%d'))

        df_cumsum_offline_total = pd.merge(df_previous_cumsum_count, df_cumsum_count, on='month_day', how='outer')
        df_cumsum_offline_total.sort_values(by='end_time_y', ascending=True, inplace=True)
        df_cumsum_offline_total.dropna(subset=['end_time_y'], inplace=True)  # 排序后，本期开始缺失数据会放到最后，导致数据计算有误，所以本期没有的数据删除
        df_cumsum_offline_total = df_cumsum_offline_total[
            ['month_day', 'end_time_y', 'value', 'previous_value']].rename(
            columns={'end_time_y': 'end_time'})
        df_cumsum_offline_total.fillna(method='ffill', inplace=True)
        df_cumsum_offline_total.dropna(subset=['value'], inplace=True)
        df_cumsum_offline_total.reset_index(inplace=True, drop=True)
        end_time_date = pd.to_datetime(self.end_time).date()
        df_cumsum_offline_total = df_cumsum_offline_total[df_cumsum_offline_total['end_time'].dt.date <= end_time_date]
        df_cumsum_offline_total.fillna(0, inplace=True)
        df_cumsum_offline_total['previous_value'] = df_cumsum_offline_total['previous_value'].astype(float)
        df_cumsum_offline_total['value'] = df_cumsum_offline_total['value'].astype(float)
        # 如果previous_value为0，则yoy赋值为100%
        df_cumsum_offline_total['yoy'] = df_cumsum_offline_total.apply(
            lambda row: (row['value'] - row['previous_value']) / row[
                'previous_value'] * 100 if row['previous_value'] != 0 else 100,
            axis=1
        )

        df_cumsum_offline_total['yoy'] = df_cumsum_offline_total['yoy'].apply(lambda x: round(x, 2))
        indic_name_yoy = self.version + '-销量-个单-线下-累计同比'
        code = PublicIndicatorMain.objects.get(name=indic_name_yoy).code
        try:
            with transaction.atomic():
                for index, row in df_cumsum_offline_total.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['end_time'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['yoy']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_offline_personal_cumulative_yoy error:{e}')
        return df_cumsum_offline_total

    def get_target_weekly_count(self):
        """
        获取目标周销量数据，居于历史预测，线下保司用保司提供的数据，线上用计算
        """
        source = NhbInsureV5Target()
        if self.target_statistics_type == "1":
            df_group = self.get_seller_group_report_data()
            df_group = df_group[df_group['version'] == '合计'][['short_name', 'value']].rename(
                columns={'short_name': 'name'})
            df_offline = source.offline_adjust_data()
            df_offline['date_new'] = df_offline['date'].apply(lambda x: x + relativedelta(years=1))
            # 变频处理
            df_offline.set_index('date_new', inplace=True)
            df_grouped = df_offline.groupby('name').apply(
                lambda x: x[['sale_ratio', 'sale_ratio_cumsum']].resample('W-SUN').agg(
                    {'sale_ratio': 'sum', 'sale_ratio_cumsum': 'last'}))

            df_offline_weekly = df_grouped.reset_index()
            # 剔除合计
            df_offline_weekly = df_offline_weekly[df_offline_weekly['name'] != '合计']
            # 获取线下渠道目标
            df_offline_target = PublicTarget.objects.filter(product_set_code=self.product_set_code, type='agent')
            df_offline_target = pd.DataFrame(list(df_offline_target.values()))[['short_name', 'code', 'target']].rename(
                columns={'short_name': 'name'})
            # 只计算个单的，所以需要目标中减去团单————团单是手动的，所以数据会不断修正历史
            df_offline_target = pd.merge(df_offline_target, df_group, on='name', how='left')
            df_offline_target['target'] = df_offline_target['target'] - df_offline_target['value']
            df_offline_target = df_offline_target[['name', 'target']]
            df_offline_total = pd.merge(df_offline_weekly, df_offline_target, on='name', how='left')
            # 当周目标
            df_offline_total['target_value'] = df_offline_total['target'] * df_offline_total['sale_ratio']
            df_offline_total['target_value'] = df_offline_total['target_value'].apply(lambda x: int(round(x, 0)))
            df_offline_total['indic_name'] = self.version + '-目标-线下-个单-' + df_offline_total['name'] + '-当期值'
            df_offline_total = query_indicator_code_v1(df_offline_total)
            # 根据name分组，如果target_value合计不等于target的值，则将每个分组的最后一条数据进行调整，多余的删除，不足的补足
            df_last = df_offline_total.groupby('name').agg({'target_value': 'sum'}).reset_index().rename(
                columns={'target_value': 'target_value_total'})
            df_offline_total = pd.merge(df_offline_total, df_last, on='name', how='left')
            df_offline_total['target_value'] = df_offline_total.apply(
                lambda x: x['target_value'] + x['target'] - x['target_value_total'] if int(
                    x['sale_ratio_cumsum']) == 1 else
                x[
                    'target_value'], axis=1)
            df_offline_total = df_offline_total[['name', 'target_value', 'indic_name', 'code', 'date_new']]
        else:
            df_offline_total = pd.DataFrame(columns=['name', 'target_value', 'indic_name', 'code', 'date_new'])
        df_online = source.online_adjust_data()
        df_online['date_new'] = df_online['date'].apply(lambda x: x + relativedelta(years=1))
        # 变频处理
        df_online.set_index('date_new', inplace=True)
        df_grouped = df_online.groupby('name').apply(
            lambda x: x[['sale_ratio', 'sale_ratio_cumsum']].resample('W-SUN').agg(
                {'sale_ratio': 'sum', 'sale_ratio_cumsum': 'last'}))
        df_online_weekly = df_grouped.reset_index()
        # 剔除合计
        df_online_weekly = df_online_weekly[df_online_weekly['name'] != '合计']
        # 获取线上渠道目标
        df_online_target = PublicTarget.objects.filter(product_set_code=self.product_set_code, type='online')
        df_online_target = pd.DataFrame(list(df_online_target.values()))[['name', 'code', 'target']]
        df_online_total = pd.merge(df_online_weekly, df_online_target, on='name', how='left')
        # 当周目标
        df_online_total['target_value'] = df_online_total['target'] * df_online_total['sale_ratio']
        df_online_total['target_value'] = df_online_total['target_value'].apply(lambda x: int(round(x, 0)))
        df_online_total['indic_name'] = self.version + '-目标-线上-个单-' + df_online_total['name'] + '-当期值'
        df_online_total = query_indicator_code_v1(df_online_total)
        # 根据name分组，如果target_value合计不等于target的值，则将每个分组的最后一条数据进行调整，多余的删除，不足的补足
        df_last = df_online_total.groupby('name').agg({'target_value': 'sum'}).reset_index().rename(
            columns={'target_value': 'target_value_total'})
        df_online_total = pd.merge(df_online_total, df_last, on='name', how='left')
        df_online_total['target_value'] = df_online_total.apply(
            lambda x: x['target_value'] + x['target'] - x['target_value_total'] if int(x['sale_ratio_cumsum']) == 1 else
            x[
                'target_value'], axis=1)
        df_online_total = df_online_total[['name', 'target_value', 'indic_name', 'code', 'date_new']]
        df_target_weekly_count = pd.concat([df_offline_total, df_online_total]).reset_index(drop=True)
        if self.concat_end_week:
            # 根据name分组，日期最后两条合并成一条，同时日期改成本年的最后一天
            df_target_weekly_concat = df_target_weekly_count.groupby('name').apply(lambda x: x.iloc[-2:]).reset_index(
                drop=True)
            df_target_weekly_group = df_target_weekly_concat.groupby(['name', 'indic_name', 'code']).agg(
                {'target_value': 'sum'}).reset_index()
            df_target_weekly_group['date_new'] = self.end_time[:4] + '-12-31'
            df_target_weekly_new = df_target_weekly_count.groupby('name').apply(lambda x: x.iloc[:-2]).reset_index(
                drop=True)
            df_target_weekly_count = pd.concat([df_target_weekly_new, df_target_weekly_group]).reset_index(drop=True)
            df_target_weekly_count.sort_values(by=['name', 'indic_name', 'code', 'date_new'], inplace=True)
        df_target_weekly_count.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_target_weekly_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date_new'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['target_value']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_target_weekly_count error:{e}')
        return df_target_weekly_count

    def get_dynamic_weekly_count(self):
        """
        获取动态周销量数据，居于周目标数据处理
        当周的目标 = 总目标-当周之后的剩余的目标-已经完成的单量（含团单）   但是，第一周不会计算，直接用的还是当周目标，只有第二周开始才会计算
        也就是说每次当新的一周后，都需要重新计算当周目标词，不重复修正计算，因为历史已变动
        如果有目标提前完成，那本周还是用开始的当周目标，不用重新计算
        """
        start_of_saleweek, end_of_saleweek = get_week_start_end(self.actual_sale_date)
        start_of_week, end_of_week = get_week_start_end(datetime.datetime.now().strftime('%Y-%m-%d'))
        start_of_last_week, end_of_last_week = get_week_start_end(datetime.datetime.now() - datetime.timedelta(days=7))
        # start_of_week, end_of_week = get_week_start_end('2024-12-22')
        # start_of_last_week, end_of_last_week = get_week_start_end(datetime.datetime(2024, 12, 22) - datetime.timedelta(days=7))
        sql = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=self.product_set_code,
            statistical_type='当期值', unit='单',
            freq='周', start_datetime='2000-01-01',
            end_datetime=self.end_time[:4] + '-12-31'
        )
        with self.get_connection_dw() as conn_dw:
            df_week_target = pd.read_sql(sql, conn_dw)
            df_week_target = df_week_target[
                df_week_target['name'].str.contains('线下|线上') & ~ df_week_target['name'].str.contains('动态')]
            df_week_target['seller'] = df_week_target['name'].apply(lambda x: x.split('-')[-2])
            df_week_target['value'] = df_week_target['value'].apply(lambda x: int(x))
            # 日期格式转换，保持与周开始、结束一致
            df_week_target['date_new'] = df_week_target['end_time'].apply(
                lambda x: datetime.datetime.strptime(x.strftime('%Y-%m-%d %H:%M:%S'), '%Y-%m-%d %H:%M:%S').date())
        if end_of_week == end_of_saleweek:
            # 第一周，直接用目标的数据，不要计算，直接入数据库
            df_dynamic_weekly_count = df_week_target.copy()
            df_dynamic_weekly_count = df_dynamic_weekly_count[['code','name','date_new','value']]
        else:
            # 动态计算本周的目标，用总目标 -当周之后的剩余的目标-已经完成的单量
            df_target = PublicTarget.objects.filter(product_set_code=self.product_set_code)
            df_target = pd.DataFrame(list(df_target.values()))[['short_name', 'target']].rename(
                columns={'short_name': 'seller'})
            df_dynamic_weekly_count = df_week_target.copy()
            df_dynamic_weekly_count = pd.merge(df_dynamic_weekly_count, df_target, on='seller', how='left')

            week_number = get_reversed_week_number(self.end_time[:10])

            # 如果合并了最后两周数据，特殊处理
            if self.concat_end_week:
                if int(week_number) == 1:
                    # 合并的最后一周不再计算目标，直接用倒数第二周的目标
                    return pd.DataFrame()
                elif int(week_number) == 2:
                    end_of_week = datetime.datetime(start_of_week.year, 12, 31).date()
                    df_dynamic_weekly_count = df_dynamic_weekly_count[
                        df_dynamic_weekly_count['date_new'] >= datetime.datetime(start_of_week.year,12,31).date()]
                else:
                    df_dynamic_weekly_count = df_dynamic_weekly_count[df_dynamic_weekly_count['date_new'] >= end_of_week]
            else:
                df_dynamic_weekly_count = df_dynamic_weekly_count[df_dynamic_weekly_count['date_new'] >= end_of_week]


            df_dynamic_weekly_count.reset_index(drop=True, inplace=True)
            df_group = self.get_seller_group_report_data()
            df_group = df_group[df_group['version'] == '合计'][['short_name', 'value']].rename(
                columns={'short_name': 'seller', 'value': 'group_value'})
            data = self.get_from_cache('get_daily_sale')

            df_sale_until_last_week = data[(data['date'] <= end_of_last_week) & (data['main'] == 1)]
            df_offline = df_sale_until_last_week[df_sale_until_last_week['is_online'] == 0]
            df_offline = df_offline.groupby('seller').agg({'count': 'sum'}).reset_index()
            df_online = df_sale_until_last_week[df_sale_until_last_week['is_online'] == 1]
            df_online['source'] = df_online['source'].apply(
                lambda x: '我的南京' if '我的南京' in x
                else '支付宝' if '支付宝' in x
                else '公众号')
            df_online = df_online.groupby(['source']).agg({'count': 'sum'}).reset_index().rename(
                columns={'source': 'seller'})
            df_sale = pd.concat([df_offline, df_online]).reset_index(drop=True)
            df_total_sale = pd.merge(df_group, df_sale, on='seller', how='outer')
            # 统计各个渠道的实际销量
            df_total_sale['total_count'] = df_total_sale['count'].fillna(0) + df_total_sale['group_value'].fillna(0)
            # 统计截止到本周应该完成的销量是多少
            df_dynamic_weekly_count['cumsum_value'] = df_dynamic_weekly_count.groupby('seller')['value'].cumsum()
            df_dynamic_weekly_count['sale_target'] = df_dynamic_weekly_count['target'] - df_dynamic_weekly_count[
                'cumsum_value']
            df_last_record = df_dynamic_weekly_count.groupby('seller').agg({'date_new': 'max'}).reset_index()
            df_last_record = pd.merge(df_last_record, df_dynamic_weekly_count, on=['seller', 'date_new'], how='left')[
                ['seller', 'sale_target']]
            df_total_sale = pd.merge(df_total_sale, df_last_record, on='seller', how='outer')
            df_total_sale['history_left_count'] = df_total_sale['sale_target'] - df_total_sale['total_count']
            df_total_sale['adjust_count'] = df_total_sale['history_left_count'].apply(lambda x: 0 if x < 0 else x)
            df_dynamic_weekly_count = pd.merge(df_dynamic_weekly_count, df_total_sale[['seller', 'adjust_count']],
                                               on='seller', how='left')
            # 如果 date_new 等于 end_of_week，则说明是新的一周，需要重新计算当周目标
            df_dynamic_weekly_count['adjust_value'] = df_dynamic_weekly_count.apply(
                lambda x: x['value'] + x['adjust_count'] if x['date_new'] == end_of_week else x['value'], axis=1)
            df_dynamic_weekly_count = df_dynamic_weekly_count[['code', 'name', 'date_new', 'adjust_value']].rename(columns={'adjust_value': 'value'})


        # 处理动态目标指标数据
        df_dynamic_weekly_count['indic_name'] = df_dynamic_weekly_count['name'].apply(
            lambda x: x.replace('目标', '目标(动态)'))
        df_dynamic_weekly_count = query_indicator_code_v1(df_dynamic_weekly_count)
        try:
            with transaction.atomic():
                for index, row in df_dynamic_weekly_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date_new'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': row['value']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_dynamic_weekly_count error:{e}')
        return df_dynamic_weekly_count


if __name__ == '__main__':
    source = NhbInsureV5()
    # cs = source.get_daily_sale()
    # print(cs)
    # source.cache_daily_sale()
    # source.cache_main_daily_sale()
    # df_daily_amount_cumsum = source.get_daily_amount_cumsum()
    # print(df_daily_amount_cumsum)
    # total_count = source.get_total_count()
    # print(total_count)
    # total_amount = source.get_total_amount()
    # print(total_amount)
    # person_group = source.get_person_group_count()
    # print(person_group)
    # product_count = source.get_product_count()
    # print(product_count)
    # online_offline_count = source.get_online_offline_count()
    # print(online_offline_count)
    # pay_type_count = source.get_pay_type_count()
    # print(pay_type_count)
    # pay_type_amount = source.get_pay_type_amount()
    # print(pay_type_amount)
    # df_last_24_hour_count = source.get_last_24_hour_count()
    # print(df_last_24_hour_count)
    # df_daily_count = source.get_daily_count()
    # print(df_daily_count)
    # today_count = source.get_today_count()
    # print(today_count)
    # df_daily_amount = source.get_daily_amount()
    # print(df_daily_amount)
    # today_amount = source.get_today_amount()
    # print(today_amount)
    # yesterday_count = source.get_yesterday_count()
    # print(yesterday_count)
    # yesterday_amount = source.get_yesterday_amount()
    # print(yesterday_amount)
    # df_online_source_info = source.get_online_source_info()
    # print(df_online_source_info)
    # df_offline_seller = source.get_offline_seller_info()
    # print(df_offline_seller)
    # df_offline = source.get_offline_seller()
    # print(df_offline)
    # df_area_total = source.get_area_info()
    # print(df_area_total)
    # df_age_gender_group = source.get_age_gender()
    # print(df_age_gender_group)
    # source.cache_age_gender_count()
    # df_age_group, average_age, median_age = source.get_age_range()
    # print(df_age_group)
    # df_daily_online_count = source.get_daily_online_count()
    # print(df_daily_online_count)
    # df_daily_offline_count = source.get_daily_offline_count()
    # print(df_daily_offline_count)
    # df_daily_cumsum = source.get_daily_cumsum()
    # print(df_daily_cumsum)
    # df_daily_online_cumsum = source.get_daily_online_cumsum()
    # print(df_daily_online_cumsum)
    # df_daily_offline_cumsum = source.get_daily_offline_cumsum()
    # print(df_daily_offline_cumsum)
    # df_renewal_ratio, renewal_percent = source.get_renewal_ratio()
    # print(df_renewal_ratio)
    # df_cumsum_total = source.get_cumulative_yoy()
    # print(df_cumsum_total)
    # df_cumsum_online_total = source.get_online_cumulative_yoy()
    # print(df_cumsum_online_total)
    # df_cumsum_offline_total = source.get_offline_cumulative_yoy()
    # print(df_cumsum_offline_total)
    # df_target_ratio =source.get_target_ratio()
    # print(df_target_ratio)
    # df_medicare_type_count = source.get_medicare_type_count()
    # print(df_medicare_type_count)
    # df_insure_place = source.get_insure_place_count()
    # print(df_insure_place)
    # df_medicare_person_count = source.get_medicare_person_count()
    # print(df_medicare_person_count)
    # df_seller_group_report_data = source.get_seller_group_report_data()
    # print(df_seller_group_report_data)
    # df_daily_personal_cumsum = source.get_daily_personal_cumsum()
    # print(df_daily_personal_cumsum)
    # df_total_personal_count = source.get_total_personal_count()
    # print(df_total_personal_count)
    # df_daily_offline_personal_cumsum = source.get_daily_offline_personal_cumsum()
    # print(df_daily_offline_personal_cumsum)
    # df_personal_cumulative_yoy = source.get_personal_cumulative_yoy()
    # print(df_personal_cumulative_yoy)
    # df_offline_personal_cumulative_yoy = source.get_offline_personal_cumulative_yoy()
    # print(df_offline_personal_cumulative_yoy)
    # df_target_weekly_count = source.get_target_weekly_count()
    # print(df_target_weekly_count)
    # df_dynamic_weekly_count = source.get_dynamic_weekly_count()
    # print(df_dynamic_weekly_count)
