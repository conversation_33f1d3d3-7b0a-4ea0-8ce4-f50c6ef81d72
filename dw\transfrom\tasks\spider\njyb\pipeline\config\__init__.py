#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南京医保局(njyb)pipeline配置模块初始化
自动注册配置到全局配置管理器
"""

from transfrom.utils.field_config import register_province_config
from transfrom.utils.data_cleaning_config import register_province_cleaning_config
from .field_config import njyb_field_config
from .data_cleaning_config import njyb_data_cleaning_config

# 自动注册南京医保局配置
register_province_config('njyb', njyb_field_config)
register_province_cleaning_config('njyb', njyb_data_cleaning_config)
