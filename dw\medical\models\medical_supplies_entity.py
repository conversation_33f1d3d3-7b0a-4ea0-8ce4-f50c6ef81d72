from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalSuppliesEntity(BaseModel):
    code = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='耗材代码', **_db_comment_kwarg('耗材代码'))
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='耗材名称', **_db_comment_kwarg('耗材名称'))
    central_code = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='中心编码', **_db_comment_kwarg('中心编码'))
    level_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='层级类型',**_db_comment_kwarg('层级类型'))
    province_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='省份编码',**_db_comment_kwarg('省份编码'))
    province_name = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='省份',**_db_comment_kwarg('省份'))
    city_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='城市编码',**_db_comment_kwarg('城市编码'))
    city_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='城市',**_db_comment_kwarg('城市'))
    registered_name = models.CharField(max_length=512, blank=True, null=True, verbose_name='注册名称', **_db_comment_kwarg('注册名称'))
    registered_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='注册证号', **_db_comment_kwarg('注册证号'))
    model = models.TextField(blank=True, null=True, verbose_name='型号', **_db_comment_kwarg('型号'))
    specifications = models.TextField(blank=True, null=True, verbose_name='规格', **_db_comment_kwarg('规格'))
    production_company_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    initial_payment_ratio = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='先行自付比例', **_db_comment_kwarg('先行自付比例'))
    payment_upper_limit = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='医保支付上限', **_db_comment_kwarg('医保支付上限'))
    tender_number = models.CharField(max_length=255, blank=True, null=True, verbose_name='招标号', **_db_comment_kwarg('招标号'))
    tender_price = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='招标价格', **_db_comment_kwarg('招标价格'))
    packaging_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='包装单位', **_db_comment_kwarg('包装单位'))
    begin_date = models.DateField(blank=True, null=True, verbose_name='开始日期', **_db_comment_kwarg('开始日期'))

    class Meta:
        db_table = 'medical_supplies_entity'
        verbose_name = '医保耗材省市实体表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

