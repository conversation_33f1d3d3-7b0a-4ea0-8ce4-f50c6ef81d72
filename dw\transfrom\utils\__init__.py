#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据转换工具包
提供统一的ETL处理工具和函数
"""

# 导入主要的类和函数
from .database_utils import (
    get_connection,
    DatabaseConnectionManager,
    create_sqlalchemy_engine_with_retry,
    check_mysql_configuration,
    optimize_mysql_session,
    DEFAULT_DB
)

from .data_cleaning import (
    DataCleaner,
    TextCleaner,
    default_cleaner,
    default_text_cleaner,
    clean_source_data,
    clean_address_basic
)

from .data_normalization import (
    DataTypeNormalizer,
    ValueComparator,
    default_normalizer,
    default_comparator,
    normalize_data_types,
    values_are_different
)

from .field_mapping import (
    FieldMappingManager,
    DictMappingManager,
    DataTransformer,
    default_transformer,
    transform_data
)

from .batch_operations import (
    BatchOperationManager
)

from .batch_operations_impl import (
    BatchOperationImplementation,
    add_implementation_methods
)

from .etl_base import (
    ETLProcessor,
    default_etl_processor,
    process_etl,
    save_transformed_data,
    print_processing_summary
)

# 版本信息
__version__ = '1.0.0'

# 导出的主要接口
__all__ = [
    # 数据库工具
    'get_connection',
    'DatabaseConnectionManager',
    'create_sqlalchemy_engine_with_retry',
    'check_mysql_configuration',
    'optimize_mysql_session',
    'DEFAULT_DB',

    # 数据清洗
    'DataCleaner',
    'TextCleaner',
    'default_cleaner',
    'default_text_cleaner',
    'clean_source_data',
    'clean_address_basic',

    # 数据标准化
    'DataTypeNormalizer',
    'ValueComparator',
    'default_normalizer',
    'default_comparator',
    'normalize_data_types',
    'values_are_different',

    # 字段映射
    'FieldMappingManager',
    'DictMappingManager',
    'DataTransformer',
    'default_transformer',
    'transform_data',

    # 批量操作
    'BatchOperationManager',
    'BatchOperationImplementation',
    'add_implementation_methods',

    # ETL基础
    'ETLProcessor',
    'default_etl_processor',
    'process_etl',
    'save_transformed_data',
    'print_processing_summary',
]