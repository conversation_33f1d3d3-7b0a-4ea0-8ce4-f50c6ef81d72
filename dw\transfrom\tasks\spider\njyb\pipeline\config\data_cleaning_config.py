#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南京医保局(njyb)数据清洗配置

遵循框架设计理念：
1. 优先使用框架默认字段映射（通过medical_field_mapping表）
2. 只有需要特殊处理的字段才配置自定义清洗
3. 通用处理逻辑抽取到common_field_processing模块复用
4. code字段的补0等逻辑通过description字段自动处理
"""

import logging
import pandas as pd
from typing import Dict, Callable
from transfrom.utils.data_cleaning_config import BaseDataCleaningConfig, CommonCleaningFunctions
from transfrom.utils.common_field_processing import CommonFieldProcessing

logger = logging.getLogger(__name__)


class NjybDataCleaningConfig(BaseDataCleaningConfig):
    """
    南京医保局数据清洗配置
    """

    def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
        """
        获取表级别的清洗规则配置

        遵循框架设计理念：
        1. 优先使用框架默认映射和字典映射
        2. 只有复杂业务逻辑才使用自定义函数
        3. 简单的字段验证通过type_validation配置

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {规则配置}}
        """
        return {
            # 南京医保局医疗服务项目数据 - 需要复杂业务逻辑处理
            'spider_njyb_service_facilities': {
                'custom_function': 'clean_njyb_service_facilities_data'
                # 这个表需要处理additional_info构建等复杂逻辑，适合使用自定义函数
            },
            'spider_njyb_medical_supplies': {
                # 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
            },
            'spider_njyb_drug': {
                # 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
            }
        }

    def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
        """
        获取字段级别的清洗规则配置

        遵循框架设计理念：
        1. 优先使用框架默认字段映射（通过medical_field_mapping表）
        2. 只有需要特殊处理的字段才在这里配置
        3. 通用处理逻辑（如code字段补0）通过description字段自动处理

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {字段名: {规则配置}}}
        """
        return {
            # spider_njyb_service_facilities 表不在这里配置字段级清洗
            # 因为它需要复杂的业务逻辑处理，使用表级自定义函数更合适
        }

    def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
        """
        获取自定义清洗函数

        只包含真正需要复杂业务逻辑的自定义函数
        简单的字段处理通过框架默认机制处理

        Returns:
            Dict[str, Callable]: 自定义清洗函数字典
        """
        return {
            'clean_njyb_service_facilities_data': self._clean_njyb_service_facilities_data
            # 注意：_clean_fuwu_hospital_data 和 _clean_fuwu_pharmacy_data 函数已移除
            # 这些表的清洗现在通过框架标准机制处理（字段级清洗规则 + 字典验证）
        }

    # 注意：原来的 _clean_fuwu_hospital_data 和 _clean_fuwu_pharmacy_data 函数已移除
    # 这些表的清洗现在通过框架标准机制处理：
    # 1. addr字段通过字段级清洗规则自动处理
    # 2. medinsType字段通过表级type_validation配置自动处理
    # 3. 其他字段通过框架默认映射和字典映射自动处理
    #
    # 这样做的好处：
    # 1. 减少代码重复
    # 2. 统一处理逻辑
    # 3. 更好地利用框架能力
    # 4. 便于维护和扩展

    def _clean_njyb_service_facilities_data(self, df):
        """
        清洗南京医保局医疗服务项目数据的自定义函数

        使用通用处理函数，遵循框架设计理念：
        1. additional_info处理：使用通用函数构建
        2. 数据过滤和去重：使用table_configs中的unique_fields配置
        3. 无效值清理：使用通用函数处理

        主要处理：
        1. additional_info处理：将来源表字段转换为字典存储，key为驼峰转下划线格式
        2. 地理位置标识：添加南京市的省份和城市信息
        3. 数据过滤：按照table_configs中配置的unique_fields进行过滤，确保数据唯一性
        4. table_config限制：province_name='江苏省' and level_type='city' and city_name='南京市'

        注意：字段映射由通用ETL框架处理，这里只处理additional_info和地理位置信息
        """
        logger.info(f"开始清洗南京医保局医疗服务项目数据，输入记录数: {len(df)}")
        try:
            # 1. 构建additional_info字段 - 使用通用函数
            df['additional_info'] = df.apply(
                lambda row: CommonFieldProcessing.build_additional_info_from_mapping(
                    row, 'spider_njyb_service_facilities', 'medical_service_entity'
                ), axis=1
            )
            # 2. 清理空值和无效值 - 使用通用函数
            invalid_values = ['-', '--', '—', '/', '\\', '', 'nan', 'NaN', 'null', 'NULL']
            for col in df.columns:
                if col == 'additional_info':
                    # JSON字段已经处理过了，跳过
                    continue
                df = CommonFieldProcessing.clean_invalid_values(df, col, invalid_values)

            # 3. 数据过滤和去重 - 使用table_configs中的unique_fields配置
            # 从table_configs获取unique_fields配置，确保配置一致性
            from .table_configs import get_table_config
            table_config = get_table_config("南京医疗服务项目")
            unique_fields = table_config.get("unique_fields", ['code', 'central_code'])

            # 将目标表字段名映射回源表字段名
            # 根据create_field_mapping.py中的实际映射关系：
            # code <- medListCodg (服务项目代码)
            # central_code <- centCodg (中心编码)
            source_unique_fields = []
            target_to_source_mapping = {
                'code': 'medListCodg',        # 服务项目代码
                'central_code': 'centCodg',   # 中心编码
                'charge_item_code': 'pricCodg',  # 收费项目代码
                'name': 'itemname'            # 服务项目名称
            }

            for field in unique_fields:
                if field in target_to_source_mapping:
                    source_field = target_to_source_mapping[field]
                    if source_field in df.columns:
                        source_unique_fields.append(source_field)
                    else:
                        logger.warning(f"源表中未找到字段: {source_field} (对应目标字段: {field})")
                else:
                    # 如果没有映射关系，检查源表是否直接有该字段
                    if field in df.columns:
                        source_unique_fields.append(field)
                    else:
                        logger.warning(f"未找到字段映射关系: {field}")

            logger.info(f"使用table_configs中的unique_fields: {unique_fields}")
            logger.info(f"映射到源表字段: {source_unique_fields}")

            if source_unique_fields:
                df = CommonFieldProcessing.filter_and_deduplicate_data(df, source_unique_fields)
            else:
                logger.warning("没有找到有效的唯一字段，跳过过滤和去重")

            logger.info(f"南京医保局医疗服务项目数据清洗完成，输出记录数: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"清洗南京医保局医疗服务项目数据时出错: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return df


# 创建南京医保局数据清洗配置实例
njyb_data_cleaning_config = NjybDataCleaningConfig()


# 提供便捷的函数接口
def get_njyb_table_cleaning_rules(table_name: str):
    """获取南京医保局表级别清洗规则"""
    return njyb_data_cleaning_config.get_table_cleaning_rules().get(table_name, {})


def get_njyb_field_cleaning_rules(table_name: str, field_name: str = None):
    """获取南京医保局字段级别清洗规则"""
    table_rules = njyb_data_cleaning_config.get_field_cleaning_rules().get(table_name, {})
    if field_name:
        return table_rules.get(field_name, {})
    return table_rules


def get_njyb_custom_cleaning_functions():
    """获取南京医保局自定义清洗函数"""
    return njyb_data_cleaning_config.get_custom_cleaning_functions()

