import datetime
import logging
import re
import warnings
from pprint import pprint

import idna
import numpy as np
import pandas as pd
import pymysql

from dw import settings
from insure.models import InsureArea, InsureAgeSex, InsureOnline, InsureAgent
from public.models import PublicStatistics, PublicAreaBaseInsure
from transfrom.utils.utils import query_sql
from task.utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class DataVNxbInsureV4(CacheManager):
    """
    特殊说明：日照默认数据从数据库取，不走手动数据，以防万一，还是留口子
    """

    def __init__(self):
        super().__init__()
        self.DB = settings.DATABASES['default']  # dw数据数据库
        self.product_set_code = 'rizhao_nxbV4'  # 产品集编码
        self.version = '日照暖心保-四期'  # 产品期，用于指标名称标准化
        self.type = 'insure'  # 统计大类
        self.group_count_statistic = 1  # 1：根据数据表计算   2：根据手动数据统计
        self.sale_start_date = '2024-11-04'
        self.sale_start_time = '2024-11-04 12:00:00'
        self.sale_start_time_zero = '2024-11-04 00:00:00'  # 取当日，需要从0点开始
        self.end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # 24小时前的时间
        self.start_datetime = (datetime.datetime.now() - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')

        self.today = datetime.datetime.today().strftime('%Y-%m-%d')
        # 昨天的时间
        self.yesterday = (datetime.datetime.today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        # 前天的时间
        self.two_days_ago = (datetime.datetime.today() - datetime.timedelta(days=2)).strftime('%Y-%m-%d')
        # 15日前的时间
        self.fifteen_days_ago = (datetime.datetime.today() - datetime.timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')
        self.end_time = self.today + ' 00:00:00'
        self.yesterday_time = self.yesterday + ' 00:00:00'
        self.two_days_ago_time = self.two_days_ago + ' 00:00:00'
        self.age_labels = ['90及以上', '80-90', '70-80', '60-70', '50-60', '40-50', '30-40', '20-30', '10-20', '0-10']

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def get_insure_data(self):
        """
        概览列表(累计参保、累计保费、今日参保、昨日参保（较上一日比较）、今日保费、昨日保费（较上一日比较）)
        """
        # 1、累计参保
        with self.get_connection() as conn:
            df_total_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='单'), conn)
            df_total_count = df_total_count[df_total_count['name'].str.contains('销量-累计值')][['value']]

            if self.group_count_statistic == 2:
                # 这边是保司上传的数据，保险公司的团单数据
                df_offline_seller_group = self.get_seller_group_report_data()
                group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
                group_seller.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
                group_count = group_seller['group_count'].sum()

                df_total_count['value'] = df_total_count['value'] + group_count

            if df_total_count.empty:
                df_total_count = pd.DataFrame({'value': [0]})
            if df_total_count['value'].values[0] is None or np.isnan(df_total_count['value'].values[0]):
                df_total_count['value'] = 0
            # 最终以json输出，转成dict
            dict_total_person_count = df_total_count.to_dict(orient='records')

            # 2、累计保费
            df_total_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='元'), conn)
            df_total_amount = df_total_amount[df_total_amount['name'].str.contains('销售额-累计值')][['value']]
            if df_total_amount.empty:
                df_total_amount = pd.DataFrame({'value': [0]})
            dict_total_amount = df_total_amount.to_dict(orient='records')

            # 3、累计参保(附加险)
            df_total_count_other = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='单'), conn)
            df_total_count_other = df_total_count_other[df_total_count_other['name'].str.contains('销量-附加险-累计值')][['value']]

            if df_total_count_other.empty:
                df_total_count_other = pd.DataFrame({'value': [0]})
            if df_total_count_other['value'].values[0] is None or np.isnan(df_total_count_other['value'].values[0]):
                df_total_count_other['value'] = 0
            # 最终以json输出，转成dict
            dict_total_person_count_other = df_total_count_other.to_dict(orient='records')

            # 4、累计保费(附加险)
            df_total_amount_other = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='元'), conn)
            df_total_amount_other = df_total_amount_other[df_total_amount_other['name'].str.contains('销售额-附加险-累计值')][['value']]
            if df_total_amount_other.empty:
                df_total_amount_other = pd.DataFrame({'value': [0]})
            dict_total_amount_other = df_total_amount_other.to_dict(orient='records')

            # 3、今日参保
            df_today_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.end_time), conn)
            df_today_count = df_today_count[
                df_today_count['name'].str.contains('-销量-当期值') & ~df_today_count[
                    'name'].str.contains('小时')][['value']]
            if df_today_count.empty:
                df_today_count = pd.DataFrame({'value': [0]})
            dict_today_count = df_today_count.to_dict(orient='records')

            # 4、今日保费
            df_today_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.end_time), conn)
            df_today_amount = df_today_amount[
                df_today_amount['name'].str.contains('-销售额-当期值') & ~df_today_amount[
                    'name'].str.contains('小时')][['value']]
            if df_today_amount.empty:
                df_today_amount = pd.DataFrame({'value': [0]})
            dict_today_amount = df_today_amount.to_dict(orient='records')

            # 5、昨日参保、前日参保、昨日较前日增长减少
            df_yesterday_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.yesterday_time), conn)
            df_day_before_yesterday_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.two_days_ago_time), conn)
            df_yesterday_count = df_yesterday_count[
                df_yesterday_count['name'].str.contains('-销量-当期值') & ~df_yesterday_count[
                    'name'].str.contains('小时')][['value']]
            df_day_before_yesterday_count = df_day_before_yesterday_count[
                df_day_before_yesterday_count['name'].str.contains('-销量-当期值') & ~df_day_before_yesterday_count[
                    'name'].str.contains('小时')][['value']]
            if df_yesterday_count.empty:
                df_yesterday_count = pd.DataFrame({'value': [0]})
                yesterday_count = 0
            else:
                yesterday_count = df_yesterday_count[['value']].values[0]
            day_before_yesterday_count = 0 if df_day_before_yesterday_count.empty else \
                df_day_before_yesterday_count.iloc[0]['value']
            df_count_compare = pd.DataFrame({'value': yesterday_count - day_before_yesterday_count}, index=[0])
            dict_count_compare = df_count_compare.to_dict(orient='records')
            dict_yesterday_count = df_yesterday_count.to_dict(orient='records')

            # 6、昨日保费、前日保费、昨日较前日增长减少
            df_yesterday_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.yesterday_time), conn)
            df_day_before_yesterday_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.two_days_ago_time), conn)
            df_yesterday_amount = df_yesterday_amount[
                df_yesterday_amount['name'].str.contains('-销售额-当期值') & ~df_yesterday_amount[
                    'name'].str.contains('小时')][['value']]
            df_day_before_yesterday_amount = df_day_before_yesterday_amount[
                df_day_before_yesterday_amount['name'].str.contains('-销售额-当期值') & ~df_day_before_yesterday_amount[
                    'name'].str.contains('小时')][['value']]
            if df_yesterday_amount.empty:
                df_yesterday_amount = pd.DataFrame({'value': [0]})
                yesterday_amount = 0
            else:
                yesterday_amount = df_yesterday_amount.iloc[0]['value']
            day_before_yesterday_amount = 0 if df_day_before_yesterday_amount.empty else \
                df_day_before_yesterday_amount.iloc[0]['value']

            df_amount_compare = pd.DataFrame({'value': yesterday_amount - day_before_yesterday_amount}, index=[0])
            dict_amount_compare = df_amount_compare.to_dict(orient='records')
            dict_yesterday_amount = df_yesterday_amount.to_dict(orient='records')

        return dict_total_person_count, dict_total_amount, dict_today_count, dict_today_amount, dict_yesterday_count, dict_count_compare, dict_yesterday_amount, dict_amount_compare,dict_total_person_count_other,dict_total_amount_other

    def get_sale_info(self):
        """
        产品销售情况
        """
        with self.get_connection() as conn:
            # 1、完成率
            df_complete_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_complete_rate = df_complete_rate[df_complete_rate['name'].str.contains('完成率-当期值')][
                ['value']].rename(columns={'value': 'percent'})
            if df_complete_rate.empty:
                df_complete_rate = pd.DataFrame({'percent': [0]})
            if df_complete_rate['percent'].values[0] is None:
                df_complete_rate['percent'] = 0
            df_complete_rate['value'] = df_complete_rate['percent']
            df_complete_rate['percent'] = df_complete_rate['percent'].apply(lambda x: round(x / 100, 4))

            dict_complete_rate = df_complete_rate.to_dict(orient='records')

        # 2、个单团单分布
        personal_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='personal')
        df_personal_data = pd.DataFrame(list(personal_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_personal_data.empty:
            df_personal_data = pd.DataFrame()
        else:
            df_personal_data['value'] = df_personal_data['value'].astype(int)
        dict_personal_data = df_personal_data.to_dict(orient='records')
        # 3、线上线下分布
        isonline_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='isonline')
        df_isonline_data = pd.DataFrame(list(isonline_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_isonline_data.empty:
            df_isonline_data = pd.DataFrame()
        else:
            df_isonline_data['value'] = df_isonline_data['value'].astype(int)
        dict_isonline_data = df_isonline_data.to_dict(orient='records')

        # 4、自费个账分布
        pay_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                   statistical_type='pay')
        df_pay_data = pd.DataFrame(list(pay_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_pay_data.empty:
            df_pay_data = pd.DataFrame()
        else:
            df_pay_data['value'] = df_pay_data['value'].astype(int)
        dict_pay_data = df_pay_data.to_dict(orient='records')

        # 5、险种分布
        medicare_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='medicare')
        df_medicare_data = pd.DataFrame(list(medicare_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_medicare_data.empty:
            df_medicare_data = pd.DataFrame()
        else:
            df_medicare_data['value'] = df_medicare_data['value'].astype(int)
        dict_medicare_data = df_medicare_data.to_dict(orient='records')

        return dict_complete_rate, dict_personal_data, dict_isonline_data, dict_pay_data, dict_medicare_data

    def get_last_24_hour_data(self):
        """
        最近24小时销量数据
        """
        with self.get_connection() as conn:
            # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
            start_datetime = max(
                datetime.datetime.strptime(self.start_datetime, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='小时', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_24_hour_data = df_last_24_hour_data[df_last_24_hour_data['name'].str.contains('销量-当期值')][
                ['end_time', 'value']]
            df_last_24_hour_data['end_time'] = df_last_24_hour_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data['value'] = df_last_24_hour_data['value'].astype(int)
            df_last_24_hour_data.rename(columns={'end_time': 'x', 'value': 'y'}, inplace=True)
            dict_last_24_hour_data = df_last_24_hour_data.to_dict(orient='records')
            return dict_last_24_hour_data

    def get_last_15_days_data(self):
        """
        最近15日销量数据
        """
        with self.get_connection() as conn:
            start_datetime = max(
                datetime.datetime.strptime(self.fifteen_days_ago, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S'))
            df_last_15_days_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_15_days_data = df_last_15_days_data[df_last_15_days_data['name'].str.contains('销量-当期值')][
                ['end_time', 'value']]
            df_last_15_days_data['end_time'] = df_last_15_days_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_last_15_days_data['value'] = df_last_15_days_data['value'].astype(int)
            df_last_15_days_data.rename(columns={'end_time': 'x', 'value': 'y'}, inplace=True)
            dict_last_15_days_data = df_last_15_days_data.to_dict(orient='records')
            return dict_last_15_days_data

    def get_area_data(self):
        """
        参保地统计报表（排名、参保地、占比、总单数、今日参保、昨日参保、参保率）
        """

        area_data = InsureArea.objects.filter(product_set_code=self.product_set_code)
        df_area_data = pd.DataFrame(list(
            area_data.values('position', 'name', 'ratio', 'total_count', 'today_count', 'yesterday_count',
                             'insure_ratio')))
        # 按照排序先确定顺序，并对合计的顺序置为-
        if df_area_data.empty:
            df_area_data = pd.DataFrame()
        else:
            df_area_data.sort_values(by='position', inplace=True)
            df_area_data.reset_index(inplace=True)
            df_area_data['position'] = df_area_data.index + 1
            df_area_data[['insure_ratio', 'ratio']] = df_area_data[['insure_ratio', 'ratio']].astype(float)
            df_area_data.loc[df_area_data['name'] == '合计', 'position'] = '-'
        dict_area_data = df_area_data.to_dict(orient='records')
        return dict_area_data

    def get_agent_data(self):
        """
        获取线下保司销售数据（含线上数据）（排名、保司、占比、代理人数、人均出单、个单、团单、目标、完成率）
        """
        agent_data = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        df_agent_data = pd.DataFrame(list(agent_data.values('position', 'name', 'insure_ratio', 'employee_count',
                                                            'average_count', 'personal_count', 'group_count', 'target',
                                                            'target_ratio', 'today_count', 'yesterday_count')))
        if df_agent_data.empty:
            df_agent_data = pd.DataFrame()
        else:
            # 按照排序先确定顺序，并对合计的顺序置为-
            df_agent_data.sort_values(by=['position'], inplace=True)
            df_agent_data[['insure_ratio', 'target_ratio', 'average_count']] = df_agent_data[
                ['insure_ratio', 'target_ratio', 'average_count']].astype(
                float)
            df_agent_data.loc[df_agent_data['name'] == '合计', 'position'] = '-'
            df_agent_data['average_count'] = df_agent_data['average_count'].round(1).astype(float)
        dict_agent_data = df_agent_data.to_dict(orient='records')
        return dict_agent_data

    # 人群特征
    def get_age_data(self):
        """
        平均年龄、年龄中位数
        """
        with self.get_connection() as conn:
            df_avg_age = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='平均值', unit='岁'), conn)
            df_avg_age = df_avg_age[df_avg_age['name'].str.contains('年龄-平均值')][['value']]
            if df_avg_age['value'].values[0] is None:
                df_avg_age['value'] = 0

            df_median_age = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='中位值', unit='岁'), conn)
            df_median_age = df_median_age[df_median_age['name'].str.contains('年龄-中位值')][['value']]
            if df_median_age['value'].values[0] is None:
                df_median_age['value'] = 0
            dict_avg_age = df_avg_age.to_dict(orient='records')
            dict_median_age = df_median_age.to_dict(orient='records')
            return dict_avg_age, dict_median_age

    def get_age_gender_data(self):
        """
        年龄性别分布
        """
        age_sex_data = InsureAgeSex.objects.filter(product_set_code=self.product_set_code).order_by('age_distribution',
                                                                                                    'sex')
        df_age_sex_data = pd.DataFrame(list(
            age_sex_data.values('sex', 'age_distribution', 'value')))
        df_age_sex_data.rename(columns={'age_distribution': 'x', 'value': 'y', 'sex': 's'}, inplace=True)
        dict_age_sex_data = df_age_sex_data.to_dict(orient='records')
        # new_df_age_sex_data = df_age_sex_data.pivot(index='age_distribution', columns='sex',
        #                                             values='value').reset_index()
        # new_df_age_sex_data.rename(columns={'age_distribution': 'y', '女': 'x2', '男': 'x1'}, inplace=True)
        # dict_age_sex_data = new_df_age_sex_data.to_dict(orient='records')
        return dict_age_sex_data

    def get_age_distribution_data(self):
        """
        年龄段分布
        """
        age_distribution_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                                statistical_type='age_ratio').order_by('key')

        df_age_distribution_data = pd.DataFrame(list(age_distribution_data.values('key', 'value')))
        if df_age_distribution_data.empty:
            df_age_distribution_data = pd.DataFrame()
        else:
            df_age_distribution_data['value'] = df_age_distribution_data['value'].astype(float)
            # 强制排序
            age_mapping = {age: i for i, age in enumerate(self.age_labels)}
            df_age_distribution_data['age_order'] = df_age_distribution_data['key'].map(age_mapping)
            df_age_distribution_data = df_age_distribution_data.sort_values(by=['age_order'])
            df_age_distribution_data = df_age_distribution_data.drop('age_order', axis=1).reset_index(drop=True)

            df_age_distribution_data.rename(columns={'key': 'y', 'value': 'x'}, inplace=True)
        dict_age_distribution_data = df_age_distribution_data.to_dict(orient='records')
        return dict_age_distribution_data

    def get_content(self):
        """
        获取全部数据
        """
        data = {}
        dict_total_person_count, dict_total_amount, dict_today_count, dict_today_amount, dict_yesterday_count, dict_count_compare, dict_yesterday_amount, dict_amount_compare,dict_total_person_count_other,dict_total_amount_other = self.get_insure_data()
        data['累计参保'] = dict_total_person_count
        data['累计保费'] = dict_total_amount
        data['累计参保附加险'] = dict_total_person_count_other
        data['累计保费附加险'] = dict_total_amount_other
        data['今日参保'] = dict_today_count
        data['今日保费'] = dict_today_amount
        data['昨日参保'] = dict_yesterday_count
        data['昨日参保较前一日'] = dict_count_compare
        data['昨日保费'] = dict_yesterday_amount
        data['昨日保费较前一日'] = dict_amount_compare
        dict_complete_rate, dict_personal_data, dict_isonline_data, dict_pay_data, dict_medicare_data = self.get_sale_info()
        data['完成率'] = dict_complete_rate
        data['个单团单'] = dict_personal_data
        data['线上线下'] = dict_isonline_data
        data['自费个账'] = dict_pay_data
        data['险种'] = dict_medicare_data
        data['近24小时投保量'] = self.get_last_24_hour_data()
        data['近15日投保量'] = self.get_last_15_days_data()
        data['参保地区'] = self.get_area_data()
        data['线下参保'] = self.get_agent_data()
        dict_avg_age, dict_median_age = self.get_age_data()
        data['平均年龄'] = dict_avg_age
        data['年龄中位数'] = dict_median_age
        data['年龄性别分布'] = self.get_age_gender_data()
        data['年龄段分布'] = self.get_age_distribution_data()
        return data

    def cache_content(self):
        """
        缓存全部数据
        """
        try:
            df_content = self.get_content()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache('get_content', df_content)
            return df_content
        except Exception as e:
            logger.error(f'{type(self).__name__} cache_content error:{e}')
            raise ValueError(f'{type(self).__name__} cache_content error:{e}')

    def get_datav_data(self):
        """
        获取数据
        """
        data = self.get_from_cache('get_content')
        return data


if __name__ == '__main__':
    data = DataVNxbInsureV4()
    # product_set_code = data.product_set_code
    # data.cache_area_code()
    # data.get_insure_data()
    # data.get_last_24_hour_data()
    # data.get_last_7_days_data()
    # data.get_school_data()
    # data.get_area_data()
    # data.get_person_data()
    # df = data.get_content()
    # pprint(df)
    # df_age_gender_data = data.get_age_gender_data()
    # print(df_age_gender_data)
    data.cache_content()
    datav = data.get_datav_data()
    pprint(datav)
