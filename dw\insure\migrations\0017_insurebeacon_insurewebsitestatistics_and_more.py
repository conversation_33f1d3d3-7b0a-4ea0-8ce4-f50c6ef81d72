# Generated by Django 4.2.1 on 2025-07-25 10:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0016_insuregroup_product_code"),
    ]

    operations = [
        migrations.CreateModel(
            name="InsureBeacon",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True,
                        db_comment="产品集编码",
                        max_length=32,
                        null=True,
                        verbose_name="产品集编码",
                    ),
                ),
                (
                    "statistics_date",
                    models.DateField(
                        blank=True,
                        db_comment="统计日期",
                        null=True,
                        verbose_name="统计日期",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        db_comment="数据来源",
                        default="umami",
                        max_length=32,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "step_name",
                    models.CharField(
                        blank=True,
                        db_comment="步骤名称",
                        max_length=50,
                        null=True,
                        verbose_name="步骤名称",
                    ),
                ),
                (
                    "step_order",
                    models.IntegerField(
                        blank=True,
                        db_comment="步骤顺序",
                        null=True,
                        verbose_name="步骤顺序",
                    ),
                ),
                (
                    "conversion_rate",
                    models.DecimalField(
                        blank=True,
                        db_comment="转化率",
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name="转化率",
                    ),
                ),
                (
                    "absolute_count",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="绝对数量",
                        null=True,
                        verbose_name="绝对数量",
                    ),
                ),
                (
                    "remarks",
                    models.TextField(
                        blank=True, db_comment="备注", null=True, verbose_name="备注"
                    ),
                ),
            ],
            options={
                "verbose_name": "销售埋点统计表",
                "verbose_name_plural": "销售埋点统计表",
                "db_table": "insure_beacon",
            },
        ),
        migrations.CreateModel(
            name="InsureWebsiteStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True,
                        db_comment="产品集编码",
                        max_length=32,
                        null=True,
                        verbose_name="产品集编码",
                    ),
                ),
                (
                    "statistics_date",
                    models.DateField(
                        blank=True,
                        db_comment="统计日期",
                        null=True,
                        verbose_name="统计日期",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        db_comment="数据来源",
                        default="umami",
                        max_length=32,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "total_page_views",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="总浏览量",
                        null=True,
                        verbose_name="总浏览量",
                    ),
                ),
                (
                    "total_visits",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="总访问次数",
                        null=True,
                        verbose_name="总访问次数",
                    ),
                ),
                (
                    "total_visitors",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="总访客数",
                        null=True,
                        verbose_name="总访客数",
                    ),
                ),
                (
                    "bounce_rate",
                    models.CharField(
                        blank=True,
                        db_comment="跳出率",
                        max_length=10,
                        null=True,
                        verbose_name="跳出率",
                    ),
                ),
                (
                    "avg_visit_duration",
                    models.CharField(
                        blank=True,
                        db_comment="平均访问时长",
                        max_length=20,
                        null=True,
                        verbose_name="平均访问时长",
                    ),
                ),
                (
                    "remarks",
                    models.TextField(
                        blank=True, db_comment="备注", null=True, verbose_name="备注"
                    ),
                ),
            ],
            options={
                "verbose_name": "销售网站埋点统计",
                "verbose_name_plural": "销售网站埋点统计",
                "db_table": "insure_website_statistics",
            },
        ),
        migrations.RemoveConstraint(
            model_name="insurepvuv",
            name="unique_insure_pvuv_combination",
        ),
        migrations.AddField(
            model_name="insurepvuv",
            name="source_group",
            field=models.CharField(
                blank=True,
                db_comment="渠道分组",
                max_length=32,
                null=True,
                verbose_name="渠道分组",
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="count",
            field=models.IntegerField(
                blank=True, db_comment="数量", null=True, verbose_name="数量"
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="end_date",
            field=models.DateField(
                blank=True, db_comment="截止日期", null=True, verbose_name="截止日期"
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="product_set_code",
            field=models.CharField(
                blank=True,
                db_comment="产品集编码",
                max_length=32,
                null=True,
                verbose_name="产品集编码",
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="source_code",
            field=models.CharField(
                blank=True,
                db_comment="渠道编码",
                max_length=32,
                null=True,
                verbose_name="渠道编码",
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="type",
            field=models.CharField(
                blank=True,
                db_comment="类型",
                max_length=32,
                null=True,
                verbose_name="类型",
            ),
        ),
        migrations.AddConstraint(
            model_name="insurepvuv",
            constraint=models.UniqueConstraint(
                fields=(
                    "product_set_code",
                    "end_date",
                    "source_group",
                    "source_code",
                    "type",
                ),
                name="unique_insure_pvuv_combination",
            ),
        ),
        migrations.AddIndex(
            model_name="insurewebsitestatistics",
            index=models.Index(
                fields=["product_set_code", "statistics_date"],
                name="insure_webs_product_f60151_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="insurewebsitestatistics",
            index=models.Index(
                fields=["statistics_date"], name="insure_webs_statist_e5b941_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="insurewebsitestatistics",
            index=models.Index(
                fields=["product_set_code"], name="insure_webs_product_6beb70_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="insurewebsitestatistics",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "statistics_date", "data_source"),
                name="unique_insure_website_statistics",
            ),
        ),
        migrations.AddIndex(
            model_name="insurebeacon",
            index=models.Index(
                fields=["product_set_code", "statistics_date"],
                name="insure_beac_product_89551f_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="insurebeacon",
            index=models.Index(
                fields=["step_order"], name="insure_beac_step_or_700035_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="insurebeacon",
            index=models.Index(
                fields=["product_set_code", "step_order"],
                name="insure_beac_product_2d0043_idx",
            ),
        ),
        migrations.AddConstraint(
            model_name="insurebeacon",
            constraint=models.UniqueConstraint(
                fields=(
                    "product_set_code",
                    "statistics_date",
                    "data_source",
                    "step_order",
                ),
                name="unique_insure_beacon",
            ),
        ),
    ]
