import datetime
import json
import logging
import warnings

import numpy as np
import pandas as pd

from dw import settings
from other.models import OtherYbStatisticNhb

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)


class YbgtNhbClaimV4Qa():
    def __init__(self):
        self.PRODUCT_CODE = "'ninghuibao-2024-standard', 'ninghuibao-2024-upgrade'"
        self.DB = settings.DATABASES['nhb']
        self.data_type = 'nhb_claim_v4'

    def qa_data(self, product_code):
        """
        数据质量校验
        """
        latest_record = OtherYbStatisticNhb.objects.filter(product_code=product_code, type=self.data_type).order_by(
            'update_time').last()

        df = json.loads(latest_record.content)

        # 创建一个DataFrame来记录错误
        errors_df = pd.DataFrame(columns=['error_message', 'error_value'])

        # 1 最高赔付金额不等于top10赔付金额最高值
        top10_max_pay = pd.DataFrame(df['最高赔付金额TOP10'])['pay_amount'].max()
        if df['理赔总体情况'][0]['max_amount'] != top10_max_pay:
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 最高赔付金额不等于top10赔付金额最高值"],
                'error_value': [f"{df['理赔总体情况'][0]['max_amount']}!= {top10_max_pay}"]})])

        # 2 最高赔付金额不等于往期赔付金额当期最大值
        past_analysis_max_pay = pd.DataFrame(df['往期对比分析']).iloc[-1]['max_pay_amount']
        if round(df['理赔总体情况'][0]['max_amount'] / 10000, 2) != past_analysis_max_pay:
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 最高赔付金额不等于往期赔付金额当期最大值"], 'error_value': [
                f"{round(df['理赔总体情况'][0]['max_amount'] / 10000, 2)}!= {past_analysis_max_pay}"]})])

        # 3 医保范围内费用=基本医疗+个人自付+大病保险+医疗救助
        df_top10 = pd.DataFrame(df['最高赔付金额TOP10'])
        df_top10.replace('', np.nan, regex=True, inplace=True)
        df_top10.fillna(0, inplace=True)
        for i in range(len(df_top10)):
            calculated_medicare_amount = round(df_top10.iloc[i]['base_amount'] + df_top10.iloc[i]['self_paid_amount'] +
                                               df_top10.iloc[i]['serious_illness_insurance_amount'] , 2)
            if round(df_top10.iloc[i]['medicare_amount'], 2) != calculated_medicare_amount:
                errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                    f"{product_code} 第{i + 1}条数据 医保范围内费用!=基本医疗+个人自付+大病保险"],
                    'error_value': [
                        f"{round(df_top10.iloc[i]['medicare_amount'], 2)}!= {calculated_medicare_amount}"]})])

        # 4总费用=医保范围内费用+个人自费
        for i in range(len(df_top10)):
            calculated_total_amount = round(df_top10.iloc[i]['medicare_amount'] + df_top10.iloc[i]['self_care_amount'],
                                            2)
            if round(df_top10.iloc[i]['total_amount'], 2) != calculated_total_amount:
                errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                    f"{product_code} 第{i + 1}条数据 总费用!=医保范围内费用+个人自费"],
                    'error_value': [f"{round(df_top10.iloc[i]['total_amount'], 2)}!= {calculated_total_amount}"]})])

        # 5 赔付人员年龄分布人数合计不等于总人数据
        total_people = pd.DataFrame(df['赔付人员年龄分布'])
        if round(total_people['person_num'].sum(), 2) != round(df['总人数'][0]['total_person_num'], 2):
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 赔付人员年龄分布人数合计不等于总人数据"],
                'error_value': [f"{total_people['person_num'].sum()} != {df['总人数'][0]['total_person_num']}"]})])

        # 6 版本分布情况赔付数量不等于参保类型分布情况赔付数量
        insurance_type_df = pd.DataFrame(df['参保类型分布情况'])
        version_df = pd.DataFrame(df['版本分布情况'])
        if round(insurance_type_df['pay_num'].sum(), 2) != round(version_df['pay_num'].sum(), 2):
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 版本分布情况赔付数量不等于参保类型分布情况赔付数量"],
                'error_value': [f"{version_df['pay_num'].sum()}!={insurance_type_df['pay_num'].sum()}"]})])

        # 7 版本分布情况赔付金额不等于参保类型分布情况赔付金额
        if round(insurance_type_df['pay_amount'].sum(), 2) != round(version_df['pay_amount'].sum(), 2) and abs(
                round(insurance_type_df['pay_amount'].sum() -
                      version_df['pay_amount'].sum(), 2)) != 0.01:
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 版本分布情况赔付金额不等于参保类型分布情况赔付金额"], 'error_value': [
                f"{round(version_df['pay_amount'].sum(), 2)}!={round(insurance_type_df['pay_amount'].sum(), 2)}"]})])

        # 8 往期对比分析本期赔付数量不等于版本分布情况赔付数量
        if pd.DataFrame(df['往期对比分析']).iloc[-1]['pay_num'] != round(version_df['pay_num'].sum(), 2):
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 往期对比分析本期赔付数量不等于版本分布情况赔付数量"], 'error_value': [
                f"{pd.DataFrame(df['往期对比分析']).iloc[-1]['pay_num']}!={version_df['pay_num'].sum()}"]})])

        # 9 人均赔付金额 ！= 总赔付金额/总人数
        total_pay_amount = df['理赔总体情况'][0]['amount']
        total_person_num = df['总人数'][0]['total_person_num']
        if round(total_pay_amount / total_person_num, 2) != df['理赔总体情况'][0]['avg_amount']:
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 人均赔付金额 ！= 总赔付金额/总人数"], 'error_value': [
                f"{round(total_pay_amount / total_person_num, 2)} != {df['理赔总体情况'][0]['avg_amount']}"]})])

        # 10 总申请量小于总结案数据
        if df['理赔总体情况'][0]['total_claim_num'] < df['理赔总体情况'][0]['finish_num']:
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 总申请量小于总结案数据"], 'error_value': [
                f"{df['理赔总体情况'][0]['total_claim_num']} < {df['理赔总体情况'][0]['finish_num']}"]})])

        # 11 赔付总额不等于往期赔付金额当期值
        if round(df['理赔总体情况'][0]['amount'] / 10000, 2) != pd.DataFrame(df['往期对比分析']).iloc[-1]['pay_amount']:
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 赔付总额不等于往期赔付金额当期值"], 'error_value': [
                f"{round(df['理赔总体情况'][0]['amount'] / 10000, 2)} != {pd.DataFrame(df['往期对比分析']).iloc[-1]['pay_amount']}"]})])

        # 12 当日数据未更新
        if latest_record.update_time.strftime('%Y-%m-%d') != datetime.datetime.now().strftime('%Y-%m-%d'):
            errors_df = pd.concat([errors_df, pd.DataFrame({'error_message': [
                f"{product_code} 当日数据未更新"], 'error_value': [f"无数据"]})])
        # 检查错误DataFrame是否为空
        if not errors_df.empty:
            logger.error("存在错误：\n{}".format(errors_df.to_string(index=False)))
            raise ValueError("存在错误：\n{}".format(errors_df.to_string(index=False)))
        else:
            logger.info(f"{product_code} 所有数据校验通过")


if __name__ == "__main__":
    yb_report = YbgtNhbClaimV4Qa()
    df = yb_report.qa_data(product_code=yb_report.PRODUCT_CODE)
