import logging

from apscheduler.schedulers.background import BackgroundScheduler
from django_apscheduler.jobstores import DjangoJobStore, register_job
from transfrom.utils.utils import send_feishu_message

logger = logging.getLogger(__name__)


# 实例化调度器
scheduler = BackgroundScheduler()
# 调度器使用默认的DjangoJobStore()
scheduler.add_jobstore(DjangoJobStore(), 'default')
# @register_job(scheduler, 'cron', id='宁惠保V4埋点任务' ,hour=3, minute=30, replace_existing=True)
# def job():
#     try:
#         from transfrom.tasks.insure.nhb_beacon_insure_v5 import content
#         content()
#         logger.info("埋点任务成功")
#     except Exception as e:
#         send_feishu_message(f"埋点任务失败：{e}")
#         logger.error(f"埋点任务失败：{e}")


# @register_job(scheduler, 'cron', id='宁惠保V5我的南京埋点任务' ,hour=1, minute=10, replace_existing=True)
# def wdnj_job():
#     try:
#         from task.tasks.insure.nhb_njapp.pvuv_njapp import main
#         main()
#         logger.info("我的南京埋点任务成功")
#     except Exception as e:
#         send_feishu_message(f"我的南京埋点任务失败：{e}")
#         logger.error(f"我的南京埋点任务失败：{e}")




@register_job(scheduler, 'interval', id='CELERY任务执行异常', minutes=11, replace_existing=True)
def timeout_job():
    try:
        from task.tasks.monitoring.insure_celery import get_timeout_task
        df =get_timeout_task()
        if df.shape[0] > 0:
            # 将task_name列提取出来,转成字符串
            task_names = '、'.join(df['task_name'].apply(str).tolist())
            # 发送飞书通知
            send_feishu_message(f"CELERY任务执行异常：{task_names}")
    except Exception as e:
        send_feishu_message(f"CELERY任务执行异常 ApScheduler任务失败：{e}")
        logger.error(f"CELERY任务执行异常 ApScheduler任务失败：{e}")

# 注册定时任务并开始
scheduler.start()


### 每天八点半定时执行
## @register_job(scheduler, 'cron', id='test1',hour=8, minute=30, args=['test'], replace_existing=True)

# # 单次定时执行
# @register_job(scheduler, 'date', id='test2', run_date='2024-06-10 06:06:06',args=['test'], replace_existing=True)