from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalMedicineDiagnosis(BaseModel):
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='疾病/手术名称', **_db_comment_kwarg('疾病/手术名称'))
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='疾病/手术代码', **_db_comment_kwarg('疾病/手术代码'))
    type = models.CharField(max_length=64, blank=True, null=True, verbose_name='分类', **_db_comment_kwarg('分类'))
    type_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='分类名称', **_db_comment_kwarg('分类名称'))
    chapter_code_scope = models.Char<PERSON>ield(max_length=64, blank=True, null=True, verbose_name='章代码范围', **_db_comment_kwarg('章代码范围'))
    chapter = models.CharField(max_length=255, blank=True, null=True, verbose_name='章', **_db_comment_kwarg('章'))
    section_code_scope = models.CharField(max_length=64, blank=True, null=True, verbose_name='节代码范围', **_db_comment_kwarg('节代码范围'))
    section = models.CharField(max_length=255, blank=True, null=True, verbose_name='节', **_db_comment_kwarg('节'))
    category_code = models.CharField(max_length=64, blank=True, null=True, verbose_name='类目代码', **_db_comment_kwarg('类目代码'))
    category = models.CharField(max_length=255, blank=True, null=True, verbose_name='类目', **_db_comment_kwarg('类目'))
    subcategory_code = models.CharField(max_length=64, blank=True, null=True, verbose_name='亚目代码', **_db_comment_kwarg('亚目代码'))
    subcategory = models.CharField(max_length=255, blank=True, null=True, verbose_name='亚目', **_db_comment_kwarg('亚目'))
    detail_code = models.CharField(max_length=64, blank=True, null=True, verbose_name='细目代码', **_db_comment_kwarg('细目代码'))
    detail = models.CharField(max_length=255, blank=True, null=True, verbose_name='细目', **_db_comment_kwarg('细目'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_medicine_diagnosis'
        verbose_name = '疾病诊断手术操作信息表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

