#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化配置模块
提供数据比对和ETL处理的性能调优配置
"""

import logging
import pandas as pd

logger = logging.getLogger(__name__)


class PerformanceConfig:
    """
    性能优化配置类
    """
    
    # 数据比对优化配置
    VECTORIZED_COMPARISON = True  # 启用向量化比较
    BATCH_FIELD_COMPARISON = True  # 启用分批字段比较
    FIELD_BATCH_SIZE = 10  # 字段比较批次大小
    
    # 内存优化配置
    CHUNK_SIZE = 50000  # 大数据集分块处理大小
    MAX_MEMORY_USAGE = 2048  # 最大内存使用量(MB)
    
    # 数据库优化配置
    DB_BATCH_SIZE = 10000  # 数据库批处理大小
    DB_CONNECTION_POOL_SIZE = 5  # 连接池大小
    DB_QUERY_TIMEOUT = 300  # 查询超时时间(秒)
    
    # 并行处理配置
    ENABLE_PARALLEL = False  # 是否启用并行处理
    MAX_WORKERS = 4  # 最大工作线程数
    
    # 缓存配置
    ENABLE_CACHE = True  # 启用缓存
    CACHE_SIZE = 1000  # 缓存大小
    
    # 日志配置
    ENABLE_PERFORMANCE_LOG = True  # 启用性能日志
    LOG_DETAIL_LEVEL = 'INFO'  # 日志详细级别
    
    @classmethod
    def get_optimal_config_for_data_size(cls, data_size):
        """
        根据数据量获取最优配置
        
        Args:
            data_size (int): 数据量
            
        Returns:
            dict: 优化配置
        """
        config = {}
        
        if data_size < 10000:
            # 小数据量：简单处理
            config.update({
                'batch_size': 5000,
                'chunk_size': data_size,
                'field_batch_size': 20,
                'enable_parallel': False,
                'vectorized_comparison': True
            })
        elif data_size < 100000:
            # 中等数据量：平衡处理
            # 针对30-50K数据量优化批处理大小
            if data_size > 30000:
                batch_size = min(20000, data_size)  # 对于30K+数据，使用更大的批处理
            else:
                batch_size = 10000

            config.update({
                'batch_size': batch_size,
                'chunk_size': 25000,
                'field_batch_size': 15,
                'enable_parallel': False,
                'vectorized_comparison': True
            })
        elif data_size < 500000:
            # 大数据量：优化处理
            config.update({
                'batch_size': 15000,
                'chunk_size': 50000,
                'field_batch_size': 10,
                'enable_parallel': True,
                'max_workers': 2,
                'vectorized_comparison': True
            })
        else:
            # 超大数据量：极致优化
            config.update({
                'batch_size': 20000,
                'chunk_size': 100000,
                'field_batch_size': 8,
                'enable_parallel': True,
                'max_workers': 4,
                'vectorized_comparison': True
            })
        
        logger.info(f"为 {data_size:,} 条数据选择配置: {config}")
        return config
    
    @classmethod
    def optimize_pandas_settings(cls):
        """
        优化pandas设置以提高性能
        """
        # 设置pandas选项以提高性能
        pd.set_option('mode.chained_assignment', None)  # 关闭链式赋值警告
        pd.set_option('compute.use_bottleneck', True)   # 启用bottleneck加速
        pd.set_option('compute.use_numexpr', True)      # 启用numexpr加速
        
        logger.info("已优化pandas性能设置")
    
    @classmethod
    def get_memory_efficient_dtypes(cls, df):
        """
        获取内存高效的数据类型
        
        Args:
            df (pd.DataFrame): 数据框
            
        Returns:
            dict: 优化的数据类型映射
        """
        dtype_map = {}
        
        for col in df.columns:
            col_type = df[col].dtype
            
            if col_type == 'object':
                # 尝试转换为category类型以节省内存
                unique_ratio = df[col].nunique() / len(df)
                if unique_ratio < 0.5:  # 如果唯一值比例小于50%
                    dtype_map[col] = 'category'
            elif col_type in ['int64', 'int32']:
                # 优化整数类型
                col_min = df[col].min()
                col_max = df[col].max()
                
                if col_min >= 0:
                    if col_max < 255:
                        dtype_map[col] = 'uint8'
                    elif col_max < 65535:
                        dtype_map[col] = 'uint16'
                    elif col_max < 4294967295:
                        dtype_map[col] = 'uint32'
                else:
                    if col_min > -128 and col_max < 127:
                        dtype_map[col] = 'int8'
                    elif col_min > -32768 and col_max < 32767:
                        dtype_map[col] = 'int16'
                    elif col_min > -2147483648 and col_max < 2147483647:
                        dtype_map[col] = 'int32'
        
        return dtype_map
    
    @classmethod
    def apply_memory_optimization(cls, df):
        """
        应用内存优化
        
        Args:
            df (pd.DataFrame): 数据框
            
        Returns:
            pd.DataFrame: 优化后的数据框
        """
        original_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        
        # 获取优化的数据类型
        dtype_map = cls.get_memory_efficient_dtypes(df)
        
        # 应用数据类型优化
        for col, dtype in dtype_map.items():
            try:
                df[col] = df[col].astype(dtype)
            except Exception as e:
                logger.warning(f"无法优化列 {col} 的数据类型: {e}")
        
        optimized_memory = df.memory_usage(deep=True).sum() / 1024 / 1024
        memory_reduction = (original_memory - optimized_memory) / original_memory * 100
        
        logger.info(f"内存优化完成: {original_memory:.2f}MB -> {optimized_memory:.2f}MB "
                   f"(减少 {memory_reduction:.1f}%)")
        
        return df


class PerformanceMonitor:
    """
    性能监控类
    """
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, operation_name):
        """
        开始计时
        
        Args:
            operation_name (str): 操作名称
        """
        import time
        self.start_times[operation_name] = time.time()
    
    def end_timer(self, operation_name, record_count=None):
        """
        结束计时并记录
        
        Args:
            operation_name (str): 操作名称
            record_count (int): 处理的记录数
        """
        import time
        if operation_name in self.start_times:
            elapsed_time = time.time() - self.start_times[operation_name]
            
            self.metrics[operation_name] = {
                'elapsed_time': elapsed_time,
                'record_count': record_count,
                'records_per_second': record_count / elapsed_time if record_count and elapsed_time > 0 else 0
            }
            
            if record_count:
                logger.info(f"{operation_name} 完成: {record_count:,} 条记录, "
                           f"耗时 {elapsed_time:.2f} 秒, "
                           f"速度 {self.metrics[operation_name]['records_per_second']:,.0f} 条/秒")
            else:
                logger.info(f"{operation_name} 完成: 耗时 {elapsed_time:.2f} 秒")
            
            del self.start_times[operation_name]
    
    def get_summary(self):
        """
        获取性能总结
        
        Returns:
            dict: 性能指标总结
        """
        return self.metrics.copy()
    
    def print_summary(self):
        """
        打印性能总结
        """
        print("\n" + "=" * 60)
        print("性能监控总结")
        print("=" * 60)
        
        if not self.metrics:
            print("没有性能数据")
            return
        
        print(f"{'操作名称':<20} {'记录数':<12} {'耗时(秒)':<10} {'速度(条/秒)':<15}")
        print("-" * 60)
        
        total_time = 0
        total_records = 0
        
        for operation, metrics in self.metrics.items():
            elapsed_time = metrics['elapsed_time']
            record_count = metrics.get('record_count', 0)
            speed = metrics.get('records_per_second', 0)
            
            total_time += elapsed_time
            if record_count:
                total_records += record_count
            
            record_str = f"{record_count:,}" if record_count else "N/A"
            speed_str = f"{speed:,.0f}" if speed > 0 else "N/A"
            
            print(f"{operation:<20} {record_str:<12} {elapsed_time:<10.2f} {speed_str:<15}")
        
        print("-" * 60)
        if total_records > 0:
            overall_speed = total_records / total_time if total_time > 0 else 0
            print(f"{'总计':<20} {total_records:<12,} {total_time:<10.2f} {overall_speed:<15,.0f}")


# 创建全局性能监控实例
performance_monitor = PerformanceMonitor()


def optimize_for_large_dataset():
    """
    为大数据集优化系统设置
    """
    # 优化pandas设置
    PerformanceConfig.optimize_pandas_settings()
    
    # 设置警告过滤
    import warnings
    warnings.filterwarnings('ignore', category=pd.errors.PerformanceWarning)
    
    logger.info("已应用大数据集优化设置")


def get_recommended_batch_size(data_size, available_memory_mb=2048):
    """
    根据数据量和可用内存推荐批处理大小
    
    Args:
        data_size (int): 数据量
        available_memory_mb (int): 可用内存(MB)
        
    Returns:
        int: 推荐的批处理大小
    """
    # 估算每条记录的内存使用量(假设平均50个字段，每个字段平均20字节)
    estimated_memory_per_record = 50 * 20  # 1KB per record
    
    # 计算可以在内存中处理的最大记录数
    max_records_in_memory = (available_memory_mb * 1024 * 1024) // estimated_memory_per_record
    
    # 推荐批处理大小为可用内存的50%，确保有足够的缓冲
    recommended_batch_size = min(max_records_in_memory // 2, data_size)
    
    # 设置合理的范围
    recommended_batch_size = max(1000, min(50000, recommended_batch_size))
    
    logger.info(f"数据量: {data_size:,}, 可用内存: {available_memory_mb}MB, "
               f"推荐批处理大小: {recommended_batch_size:,}")
    
    return recommended_batch_size
