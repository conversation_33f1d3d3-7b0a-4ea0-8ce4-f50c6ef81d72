from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class PublicIndicatorData(BaseModel):
    code = models.CharField(max_length=32, blank=True, null=True, db_index=True, verbose_name='指标编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布日期')
    end_time = models.DateTimeField(blank=True, null=True, verbose_name='截止日期')
    value = models.DecimalField(max_digits=28, decimal_places=10, blank=True, null=True, verbose_name='指标数据')
    description = models.TextField(blank=True, null=True, verbose_name='描述说明')

    class Meta:
        db_table = 'public_indicator_data'
        verbose_name = '数据指标结果表'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['code', 'publish_time', 'end_time'],
                name='unique_public_indicator_data_combination'
            ),
        ]
