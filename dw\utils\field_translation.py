"""
智能字段名称翻译服务
使用pydantic-ai框架和SiliconFlow API为数据库字段提供智能中文名称翻译
"""

import logging
import requests
import json
import time
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from pydantic import BaseModel, Field
from django.conf import settings

logger = logging.getLogger(__name__)


class FieldInfo(BaseModel):
    """字段信息模型"""
    field_name: str = Field(description="字段名称")
    data_type: str = Field(description="数据类型")
    table_name: str = Field(description="表名")
    database_name: str = Field(description="数据库名")
    is_primary_key: bool = Field(default=False, description="是否主键")
    is_unique: bool = Field(default=False, description="是否唯一")
    is_nullable: bool = Field(default=True, description="是否可空")
    default_value: Optional[str] = Field(default=None, description="默认值")


class TranslationResult(BaseModel):
    """翻译结果模型"""
    field_name: str = Field(description="字段名称")
    chinese_name: str = Field(description="中文名称")
    confidence: float = Field(default=0.8, description="置信度")
    success: bool = Field(default=True, description="是否成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class BatchTranslationResult(BaseModel):
    """批量翻译结果模型"""
    results: List[TranslationResult] = Field(description="翻译结果列表")
    total_count: int = Field(description="总数量")
    success_count: int = Field(description="成功数量")
    failed_count: int = Field(description="失败数量")


class FieldTranslationService:
    """智能字段翻译服务"""

    def __init__(self):
        """初始化翻译服务"""
        self.config = getattr(settings, 'AI_TRANSLATION_CONFIG', {})
        self.api_url = self.config.get('api_url')
        self.api_key = self.config.get('api_key')
        self.enabled = self.config.get('enabled', True)

        # 多模型配置支持
        self.models = self.config.get('models', [])
        if not self.models:
            # 向后兼容：如果没有models配置，使用旧的单模型配置
            self.models = [{
                'name': self.config.get('model', 'Qwen/Qwen3-8B'),
                'display_name': '默认模型',
                'max_tokens': self.config.get('max_tokens', 1024),
                'temperature': self.config.get('temperature', 0.3),
                'timeout': self.config.get('timeout', 30),
                'priority': 1,
                'description': '默认翻译模型'
            }]

        # 按优先级排序模型
        self.models.sort(key=lambda x: x.get('priority', 999))

        if not self.enabled:
            logger.warning("AI翻译功能已禁用")
        elif not self.api_key or not self.api_url:
            logger.error("AI翻译配置不完整，请检查API密钥和URL")
            self.enabled = False
        else:
            logger.info(f"AI翻译服务已启用，配置了 {len(self.models)} 个模型")
            for i, model in enumerate(self.models):
                logger.info(f"  模型{i+1}: {model['display_name']} ({model['name']})")

    def get_current_model(self):
        """获取当前使用的模型配置"""
        return self.models[0] if self.models else None

    def _get_translation_prompt(self, field_info: FieldInfo, model_config: dict = None) -> str:
        """生成翻译提示词，根据模型配置优化长度"""
        # 根据字段名提供更具体的上下文提示
        context_hints = {
            'id': '标识符',
            'user_id': '用户ID',
            'create_time': '创建时间',
            'update_time': '更新时间',
            'first_channel': '第一渠道',
            'second_channel': '第二渠道',
            'third_channel': '第三渠道',
            'channel': '渠道',
            'name': '名称',
            'title': '标题',
            'content': '内容',
            'status': '状态',
            'type': '类型',
            'code': '编码',
            'phone': '电话',
            'email': '邮箱',
            'address': '地址',
            'amount': '金额',
            'price': '价格',
            'count': '数量',
            'description': '描述',
            'remark': '备注',
        }

        field_name_lower = field_info.field_name.lower()
        context_hint = ""
        for key, hint in context_hints.items():
            if key in field_name_lower:
                context_hint = f" ({hint})"
                break

        # 根据模型配置决定提示词的详细程度
        if model_config and model_config.get('max_tokens', 1024) <= 512:
            # 简化版提示词，适用于有道等轻量模型
            prompt = f"""翻译数据库字段名为中文：
字段名：{field_info.field_name}
类型：{field_info.data_type}
表名：{field_info.table_name}{context_hint}

要求：2-8个汉字，简洁准确。直接返回中文名称。"""
        else:
            # 完整版提示词，适用于Qwen等大模型
            prompt = f"""你是数据库字段翻译专家。请为以下字段提供准确的中文名称：

字段信息：
- 字段名：{field_info.field_name}
- 数据类型：{field_info.data_type}
- 表名：{field_info.table_name}{context_hint}

翻译要求：
1. 简洁准确，2-8个汉字
2. 符合业务含义
3. 避免"文本"、"信息"等宽泛词汇
4. 直接返回中文名称，无需解释"""

        return prompt

    def _call_ai_api(self, prompt: str, field_info: FieldInfo = None, force_json: bool = False) -> Optional[str]:
        """调用AI API，支持多模型自动降级"""
        if not self.enabled:
            return None

        # 尝试每个模型，按优先级顺序
        for i, model_config in enumerate(self.models):
            try:
                # 根据模型配置生成优化的提示词
                if field_info:
                    optimized_prompt = self._get_translation_prompt(field_info, model_config)
                else:
                    optimized_prompt = prompt

                headers = {
                    'Authorization': f'Bearer {self.api_key}',
                    'Content-Type': 'application/json'
                }

                payload = {
                    'model': model_config['name'],
                    'messages': [
                        {'role': 'system', 'content': '你是专业的数据库字段翻译专家。'},
                        {'role': 'user', 'content': optimized_prompt}
                    ],
                    'max_tokens': model_config.get('max_tokens', 1024),
                    'temperature': model_config.get('temperature', 0.3),
                    'stream': False
                }

                # 如果需要强制返回JSON格式（用于批量翻译）
                if force_json:
                    payload['response_format'] = {'type': 'json_object'}

                model_timeout = model_config.get('timeout', 30)
                logger.info(f"尝试使用模型 {model_config['display_name']} ({model_config['name']}) 进行翻译")

                response = requests.post(
                    self.api_url,
                    headers=headers,
                    json=payload,
                    timeout=model_timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and len(result['choices']) > 0:
                        content = result['choices'][0]['message']['content'].strip()
                        logger.info(f"模型 {model_config['display_name']} 翻译成功")
                        return content
                    else:
                        logger.warning(f"模型 {model_config['display_name']} 返回格式异常: {result}")
                        continue
                else:
                    logger.warning(f"模型 {model_config['display_name']} 调用失败: {response.status_code} - {response.text}")
                    continue

            except requests.exceptions.Timeout:
                logger.warning(f"模型 {model_config['display_name']} 调用超时")
                continue
            except requests.exceptions.RequestException as e:
                logger.warning(f"模型 {model_config['display_name']} 调用异常: {str(e)}")
                continue
            except Exception as e:
                logger.warning(f"模型 {model_config['display_name']} 发生未知错误: {str(e)}")
                continue

        logger.error("所有模型都调用失败")
        return None

    def translate_field(self, field_info: FieldInfo) -> TranslationResult:
        """翻译单个字段"""
        if not self.enabled:
            return TranslationResult(
                field_name=field_info.field_name,
                chinese_name="",
                success=False,
                error_message="AI翻译功能未启用"
            )
        
        try:
            # 快速路径：对于常见字段名，直接翻译，跳过缓存查询
            quick_translations = {
                'id': '标识',
                'user_id': '用户ID',
                'create_time': '创建时间',
                'update_time': '更新时间',
                'created_at': '创建时间',
                'updated_at': '更新时间',
                'name': '名称',
                'title': '标题',
                'status': '状态',
                'type': '类型',
                'code': '编码',
                'amount': '金额',
                'price': '价格',
                'count': '数量',
                'phone': '电话',
                'email': '邮箱',
                'address': '地址'
            }

            field_name_lower = field_info.field_name.lower()
            if field_name_lower in quick_translations:
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name=quick_translations[field_name_lower],
                    confidence=0.99,  # 快速翻译给最高置信度
                    success=True
                )

            # 首先尝试从数据库缓存中查找相同的翻译
            cached_result = self._get_cached_translation(field_info.field_name)
            if cached_result:
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name=cached_result,
                    confidence=0.95,  # 缓存结果给高置信度
                    success=True
                )

            # 直接传递field_info，让API调用方法根据模型配置生成优化的提示词
            chinese_name = self._call_ai_api("", field_info)
            
            if chinese_name:
                # 简单的结果验证和清理
                chinese_name = chinese_name.strip().replace('"', '').replace("'", '')
                
                # 验证长度
                if len(chinese_name) > 20:
                    chinese_name = chinese_name[:20]
                
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name=chinese_name,
                    success=True
                )
            else:
                return TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name="",
                    success=False,
                    error_message="AI API调用失败"
                )
                
        except Exception as e:
            logger.error(f"翻译字段 {field_info.field_name} 时发生错误: {str(e)}")
            return TranslationResult(
                field_name=field_info.field_name,
                chinese_name="",
                success=False,
                error_message=str(e)
            )

    def translate_fields_batch(self, field_infos: List[FieldInfo]) -> BatchTranslationResult:
        """批量翻译字段（逐个翻译，带频率控制）"""
        results = []
        success_count = 0
        failed_count = 0

        # API调用频率控制：RPM 1000, TPM 50000
        # GLM-4模型速度更快，减少延迟时间
        request_delay = 0.02  # 20毫秒间隔，约每分钟3000次请求（仍在限制内）

        for i, field_info in enumerate(field_infos):
            # 在每次API调用前添加延迟（除了第一次）
            if i > 0:
                time.sleep(request_delay)

            result = self.translate_field(field_info)
            results.append(result)

            if result.success:
                success_count += 1
            else:
                failed_count += 1

            # 记录进度
            if (i + 1) % 10 == 0:
                logger.info(f"批量翻译进度: {i + 1}/{len(field_infos)}, 成功: {success_count}, 失败: {failed_count}")

        return BatchTranslationResult(
            results=results,
            total_count=len(field_infos),
            success_count=success_count,
            failed_count=failed_count
        )

    def translate_fields_batch_optimized(self, field_infos: List[FieldInfo]) -> BatchTranslationResult:
        """批量翻译字段（真正的批量优化版本）"""
        if not self.enabled:
            results = [
                TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name="",
                    success=False,
                    error_message="AI翻译功能未启用"
                ) for field_info in field_infos
            ]
            return BatchTranslationResult(
                results=results,
                total_count=len(field_infos),
                success_count=0,
                failed_count=len(field_infos)
            )

        try:
            logger.info(f"开始批量翻译 {len(field_infos)} 个字段")

            # 第一步：批量查询缓存翻译
            cached_translations = self._get_batch_cached_translations([f.field_name for f in field_infos])
            logger.info(f"从缓存中找到 {len(cached_translations)} 个翻译")

            # 第二步：分离缓存和需要AI翻译的字段
            results = []
            fields_need_ai = []

            for field_info in field_infos:
                if field_info.field_name in cached_translations:
                    # 使用缓存翻译
                    results.append(TranslationResult(
                        field_name=field_info.field_name,
                        chinese_name=cached_translations[field_info.field_name],
                        confidence=0.95,  # 缓存结果给高置信度
                        success=True
                    ))
                else:
                    # 需要AI翻译
                    fields_need_ai.append(field_info)

            # 第三步：对需要AI翻译的字段进行批量翻译（考虑TPM限制）
            if fields_need_ai:
                logger.info(f"需要AI翻译 {len(fields_need_ai)} 个字段")

                # 考虑TPM限制（50,000 tokens/min），将大批量分割成小批次
                # 估算每个字段约100-200 tokens，为安全起见，每批次最多100个字段
                batch_size = 100
                ai_results_all = []

                for i in range(0, len(fields_need_ai), batch_size):
                    batch_fields = fields_need_ai[i:i + batch_size]
                    logger.info(f"处理批次 {i//batch_size + 1}/{(len(fields_need_ai) + batch_size - 1)//batch_size}, 字段数: {len(batch_fields)}")

                    # 批次间添加延迟，避免触发RPM限制
                    if i > 0:
                        time.sleep(1.0)  # 批次间延迟1秒

                    # 获取当前模型配置
                    current_model = self.get_current_model()
                    batch_prompt = self._get_batch_translation_prompt(batch_fields, current_model)
                    # 批量翻译使用强制JSON格式
                    response = self._call_ai_api(batch_prompt, force_json=True)

                    if response:
                        # 解析AI翻译结果
                        ai_results = self._parse_batch_translation_response(response, batch_fields)
                        ai_results_all.extend(ai_results.results)
                    else:
                        # AI翻译失败，添加失败结果
                        for field_info in batch_fields:
                            ai_results_all.append(TranslationResult(
                                field_name=field_info.field_name,
                                chinese_name="",
                                success=False,
                                error_message="AI翻译服务调用失败"
                            ))

                results.extend(ai_results_all)

            # 统计结果
            success_count = sum(1 for r in results if r.success)
            failed_count = len(results) - success_count

            logger.info(f"批量翻译完成：成功 {success_count}，失败 {failed_count}")

            return BatchTranslationResult(
                results=results,
                total_count=len(field_infos),
                success_count=success_count,
                failed_count=failed_count
            )

        except Exception as e:
            logger.error(f"批量翻译过程中发生错误: {str(e)}")
            # 出错时回退到逐个翻译
            return self.translate_fields_batch(field_infos)

    def _get_batch_translation_prompt(self, field_infos: List[FieldInfo], model_config: dict = None) -> str:
        """构建批量翻译的prompt，根据模型配置优化"""
        fields_text = []
        for i, field_info in enumerate(field_infos, 1):
            # 只保留最核心的信息：字段名、类型、表名
            field_text = f"{i}. {field_info.field_name}"
            if field_info.data_type:
                field_text += f" ({field_info.data_type})"
            if field_info.table_name:
                field_text += f" @{field_info.table_name}"
            fields_text.append(field_text)

        fields_list = "\n".join(fields_text)

        # 根据模型配置决定提示词的详细程度
        # 注意：使用response_format强制JSON时，提示词要更简洁明确
        if model_config and model_config.get('max_tokens', 1024) <= 512:
            # 简化版批量翻译提示词
            return f"""翻译以下数据库字段名为中文：

{fields_list}

为每个字段提供中文翻译，要求2-8个汉字，简洁准确。返回JSON格式包含translations数组，每个元素有field_name、chinese_name、confidence字段。"""
        else:
            # 完整版批量翻译提示词
            return f"""请为以下数据库字段提供准确的中文名称翻译：

字段列表：
{fields_list}

翻译要求：
1. 中文名称2-8个汉字，简洁准确
2. 根据业务含义翻译，避免"文本"、"信息"等宽泛词汇
3. confidence值0.0-1.0，表示翻译置信度

返回JSON格式，包含translations数组，每个元素包含field_name、chinese_name、confidence三个字段。"""

    def _parse_batch_translation_response(self, response: str, field_infos: List[FieldInfo]) -> BatchTranslationResult:
        """解析批量翻译的响应"""
        results = []
        success_count = 0
        failed_count = 0

        try:
            # 记录原始响应用于调试
            logger.debug(f"批量翻译原始响应: {response[:500]}...")

            # 尝试解析JSON响应
            response_data = json.loads(response.strip())

            # 处理不同的响应格式
            if isinstance(response_data, list):
                # 直接返回数组格式：[{field_name, chinese_name, confidence}, ...]
                translations = response_data
                logger.debug(f"检测到数组格式响应，包含 {len(translations)} 个翻译")
            elif isinstance(response_data, dict):
                # 对象格式：{"translations": [...]} 或其他字段名
                translations = response_data.get('translations', [])

                if not translations:
                    logger.warning(f"批量翻译响应中没有找到translations字段: {list(response_data.keys())}")
                    # 尝试其他可能的字段名
                    if 'data' in response_data:
                        translations = response_data['data']
                    elif 'results' in response_data:
                        translations = response_data['results']
                    elif 'items' in response_data:
                        translations = response_data['items']
            else:
                logger.error(f"未知的响应格式: {type(response_data)}")
                translations = []

            logger.info(f"解析到 {len(translations)} 个翻译结果")

            # 创建字段名到翻译结果的映射
            translation_map = {t.get('field_name'): t for t in translations if t.get('field_name')}

            for field_info in field_infos:
                translation = translation_map.get(field_info.field_name)
                if translation and translation.get('chinese_name'):
                    chinese_name = translation['chinese_name'].strip().replace('"', '').replace("'", '')

                    # 处理confidence字段，可能是数字或文字
                    confidence_raw = translation.get('confidence', 0.8)
                    if isinstance(confidence_raw, str):
                        # 将文字转换为数字
                        confidence_map = {
                            '高': 0.9,
                            '中': 0.7,
                            '低': 0.5,
                            'high': 0.9,
                            'medium': 0.7,
                            'low': 0.5
                        }
                        confidence = confidence_map.get(confidence_raw.lower(), 0.8)
                    else:
                        try:
                            confidence = float(confidence_raw)
                        except (ValueError, TypeError):
                            confidence = 0.8

                    # 验证长度
                    if len(chinese_name) > 20:
                        chinese_name = chinese_name[:20]

                    results.append(TranslationResult(
                        field_name=field_info.field_name,
                        chinese_name=chinese_name,
                        confidence=confidence,
                        success=True
                    ))
                    success_count += 1
                    logger.debug(f"字段 {field_info.field_name} 翻译成功: {chinese_name} (置信度: {confidence})")
                else:
                    results.append(TranslationResult(
                        field_name=field_info.field_name,
                        chinese_name="",
                        success=False,
                        error_message="未找到翻译结果"
                    ))
                    failed_count += 1
                    logger.debug(f"字段 {field_info.field_name} 翻译失败: 未找到结果")

        except (json.JSONDecodeError, KeyError, ValueError) as e:
            logger.error(f"解析批量翻译响应失败: {str(e)}")
            logger.error(f"原始响应内容: {response}")
            # 解析失败时，为所有字段返回失败结果
            for field_info in field_infos:
                results.append(TranslationResult(
                    field_name=field_info.field_name,
                    chinese_name="",
                    success=False,
                    error_message="响应解析失败"
                ))
                failed_count += 1

        return BatchTranslationResult(
            results=results,
            total_count=len(field_infos),
            success_count=success_count,
            failed_count=failed_count
        )

    def _is_translation_quality_good(self, field_name: str, translation: str) -> bool:
        """检查翻译质量是否合理"""
        if not translation or not translation.strip():
            return False

        translation = translation.strip()

        # 检查明显不合理的翻译
        unreasonable_translations = {
            'first_channel': ['文本信息', '文本', '信息'],
            'second_channel': ['文本信息', '文本', '信息'],
            'third_channel': ['文本信息', '文本', '信息'],
            'channel': ['文本信息', '文本', '信息'],
            'id': ['文本信息', '文本', '信息'],
            'user_id': ['文本信息', '文本', '信息'],
            'create_time': ['文本信息', '文本', '信息'],
            'update_time': ['文本信息', '文本', '信息'],
            'data_type': ['文本信息', '文本', '信息'],
            'data_id': ['文本信息', '文本', '信息'],
            'old_info': ['文本信息', '文本', '信息'],
            'new_info': ['文本信息', '文本', '信息'],
        }

        # 如果字段名在不合理翻译列表中，检查翻译是否合理
        if field_name.lower() in unreasonable_translations:
            if translation in unreasonable_translations[field_name.lower()]:
                logger.warning(f"字段 {field_name} 的翻译 '{translation}' 被判定为不合理")
                return False

        # 检查翻译长度是否合理（太短或太长都可能有问题）
        if len(translation) < 2 or len(translation) > 15:
            logger.warning(f"字段 {field_name} 的翻译 '{translation}' 长度不合理")
            return False

        # 检查是否包含明显的错误标识
        error_indicators = ['错误', 'error', 'null', 'undefined', '未知']
        if any(indicator in translation.lower() for indicator in error_indicators):
            logger.warning(f"字段 {field_name} 的翻译 '{translation}' 包含错误标识")
            return False

        return True

    def _get_cached_translation(self, field_name: str) -> Optional[str]:
        """从数据库中查找相同字段名的已有翻译，取出现次数最多的高质量翻译"""
        try:
            from public.models import PublicColumnInfo
            from collections import Counter

            # 查找相同字段名且有中文注释的记录
            cached_columns = PublicColumnInfo.objects.filter(
                name=field_name,
                comment__isnull=False
            ).exclude(comment='').values_list('comment', flat=True)

            # 转换为列表并过滤空字符串
            comments = [comment.strip() for comment in cached_columns if comment.strip()]

            if not comments:
                logger.info(f"字段 {field_name} 没有找到缓存翻译")
                return None

            # 统计每个翻译的出现次数
            comment_counts = Counter(comments)

            # 按出现次数排序，取出现最多的翻译
            most_common_translations = comment_counts.most_common()

            # 尝试找到出现次数最多且质量好的翻译
            for translation, count in most_common_translations:
                if self._is_translation_quality_good(field_name, translation):
                    if len(most_common_translations) > 1:
                        logger.info(f"字段 {field_name} 存在多个翻译，选择出现最多的高质量翻译: {translation} (出现{count}次)")
                    else:
                        logger.info(f"字段 {field_name} 找到唯一高质量缓存翻译: {translation}")
                    return translation

            # 如果没有找到高质量的翻译，记录并返回None
            best_translation, best_count = most_common_translations[0]
            logger.info(f"字段 {field_name} 的最常见翻译 '{best_translation}' (出现{best_count}次) 质量不佳，使用AI重新翻译")
            return None

        except Exception as e:
            logger.error(f"查询缓存翻译时发生错误: {str(e)}")
            return None

    def _get_batch_cached_translations(self, field_names: List[str]) -> Dict[str, str]:
        """批量查询字段的缓存翻译，取出现次数最多的高质量翻译"""
        try:
            from public.models import PublicColumnInfo
            from collections import Counter

            # 批量查询所有字段名的翻译
            cached_data = PublicColumnInfo.objects.filter(
                name__in=field_names,
                comment__isnull=False
            ).exclude(comment='').values('name', 'comment')

            # 按字段名分组统计
            field_translations = {}
            for item in cached_data:
                field_name = item['name']
                comment = item['comment'].strip()
                if comment:  # 过滤空字符串
                    if field_name not in field_translations:
                        field_translations[field_name] = []
                    field_translations[field_name].append(comment)

            # 对每个字段，取出现次数最多且质量好的翻译
            best_translations = {}
            for field_name, comments in field_translations.items():
                if not comments:
                    continue

                # 统计每个翻译的出现次数
                comment_counts = Counter(comments)
                most_common_translations = comment_counts.most_common()

                # 尝试找到出现次数最多且质量好的翻译
                for translation, count in most_common_translations:
                    if self._is_translation_quality_good(field_name, translation):
                        best_translations[field_name] = translation
                        if len(most_common_translations) > 1:
                            logger.info(f"字段 {field_name} 选择出现最多的高质量翻译: {translation} (出现{count}次)")
                        else:
                            logger.info(f"字段 {field_name} 找到唯一高质量缓存翻译: {translation}")
                        break
                else:
                    # 如果没有找到高质量的翻译
                    best_translation, best_count = most_common_translations[0]
                    logger.info(f"字段 {field_name} 的最常见翻译 '{best_translation}' (出现{best_count}次) 质量不佳，跳过缓存")

            return best_translations

        except Exception as e:
            logger.error(f"批量查询缓存翻译时发生错误: {str(e)}")
            return {}

    def is_enabled(self) -> bool:
        """检查翻译服务是否可用"""
        return self.enabled

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型配置信息"""
        return {
            'enabled': self.enabled,
            'api_url': self.api_url,
            'models_count': len(self.models),
            'models': [
                {
                    'name': model['name'],
                    'display_name': model['display_name'],
                    'max_tokens': model.get('max_tokens', 1024),
                    'timeout': model.get('timeout', 30),
                    'priority': model.get('priority', 999),
                    'description': model.get('description', '')
                }
                for model in self.models
            ]
        }


# 全局翻译服务实例
translation_service = FieldTranslationService()


def get_translation_service() -> FieldTranslationService:
    """获取翻译服务实例"""
    return translation_service


def translate_field_name(field_name: str, data_type: str, table_name: str, 
                        database_name: str, **kwargs) -> Optional[str]:
    """便捷函数：翻译单个字段名称"""
    field_info = FieldInfo(
        field_name=field_name,
        data_type=data_type,
        table_name=table_name,
        database_name=database_name,
        **kwargs
    )
    
    result = translation_service.translate_field(field_info)
    return result.chinese_name if result.success else None
