import os
import pandas as pd

def merge_excel_files(folder_path):
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"文件夹 {folder_path} 不存在")
        return
    
    # 存储所有符合条件的Excel数据
    all_data = []
    
    # 遍历文件夹中的所有文件
    for filename in os.listdir(folder_path):
        print(filename)
        if filename.endswith('.xlsx') and filename.startswith('ddyd'):
            file_path = os.path.join(folder_path, filename)
            try:
                # 读取Excel文件
                df = pd.read_excel(file_path)
                # 添加来源文件信息
                df['来源文件'] = filename
                all_data.append(df)
            except Exception as e:
                print(f"读取文件 {filename} 时出错: {e}")
    
    if not all_data:
        print("没有找到符合条件的Excel文件")
        return
    
    # 合并所有数据
    merged_df = pd.concat(all_data, ignore_index=True)
    
    # 保存合并后的数据
    output_file = os.path.join(folder_path, '国谈药定点零售药店.xlsx')
    merged_df.to_excel(output_file, index=False)
    print(f"已将所有Excel文件合并到: {output_file}")

if __name__ == '__main__':
    # 合并Excel文件
    json_folder = os.path.join(os.path.dirname(__file__), 'download')
    merge_excel_files(json_folder)
