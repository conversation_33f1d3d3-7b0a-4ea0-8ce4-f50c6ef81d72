import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, sum_or_combine
from utils.st import query_sql, text_write, sub_text_write, empty_line

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称、最早销售时间
    :return:
    """
    SQL_PRODUCT_INFO = '''
    SELECT
    distinct
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        LEAST( COALESCE ( p.pre_sale_from, p.sale_from ), p.sale_from ) min_time 
    FROM
        product_set ps
        JOIN product p ON ps.id = p.product_set_id 
    WHERE
        p.delete_time IS NULL 
        and left(ps.code,10) = 'ninghuibao'
        AND p.main = 1 
    ORDER BY
        p.sale_from DESC
        '''

    df = CONNECTOR_JKX.query(SQL_PRODUCT_INFO, show_spinner='查询中...', ttl=600)
    return df


def get_special_employee_info(product_set_code, start_datetime,end_datetime, employee_id, conn=CONNECTOR_JKX,
                              sql=query_sql('SQL_DESIGNATED_AGENT_SALE')):
    """
    获取某一产品保司的管理人员信息
    :param product_set_code: 产品集代码
    :param seller_short_name: 保司简称
    :param conn:
    :return:
    """
    df = conn.query(
        sql.format(product_set_code=product_set_code, start_datetime=start_datetime, end_datetime=end_datetime,
                   employee_id=employee_id), show_spinner='查询中...', ttl=0)
    df['购买时间'] = pd.to_datetime(df['购买时间']).dt.strftime('%Y-%m-%d %H:%M:%S')
    return df


def get_employee_name(conn=CONNECTOR_JKX):
    """
    获取员工姓名
    :param conn:
    :return:
    """
    SQL_EMPLOYEE_NAME = '''
    select id,name from employee where id in ('2037690942042949636','1776880841796888580')
    '''
    return conn.query(SQL_EMPLOYEE_NAME, show_spinner='查询中...', ttl=0)



def main():
    # 权限检查
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("指定代理人单量统计")
    product_info = get_product_code()
    df_employee_name = get_employee_name()
    # 选择日期

    sale_until = datetime.date.today()
    # send_feishu_message(product_set_code_iframe)
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[0]
                print(product_set_code)
                sale_from = pd.to_datetime(product_info[product_info['product_set_name'] == column_name]['min_time'].values[0])
            else:
                product_set_code = None
                sale_from = datetime.datetime(2000, 1, 1)
        cols = st.columns([0.2, 0.2])

        with cols[0]:
            start_date = st.date_input('销售开始日期', min_value=sale_from, max_value=sale_until, value=None,
                                       key='start_date')

            if not start_date:
                start_date = sale_from.strftime('%Y-%m-%d')
            else:
                start_date = start_date.strftime('%Y-%m-%d')
            start_datetime = start_date+' 00:00:00'

        with cols[1]:
            end_date = st.date_input('销售结束日期', min_value=sale_from, max_value=sale_until, value=None,
                                     key='end_date')

            if not end_date:
                end_date = sale_until.strftime('%Y-%m-%d')
            else:
                end_date = end_date.strftime('%Y-%m-%d')
            end_datetime = end_date+' 23:59:59'
        print(start_datetime, end_datetime)
        employee_name = st.multiselect('请选择代理人员', options=df_employee_name['name'].values, default=df_employee_name['name'].values, key='employee_name')
        employee_id = "'" + "','".join(map(str, df_employee_name[df_employee_name['name'].isin(employee_name)]['id'].values.tolist())) + "'"
        if not employee_id:
            st.info('请选择代理人员')
        st.divider()
        if st.button('查询'):
            if start_date and end_date and pd.to_datetime(end_datetime) < pd.to_datetime(start_datetime):
                st.error('开始日期不能大于结束日期')
            else:
                with st.spinner('查询中...'):
                    df = get_special_employee_info(product_set_code, start_datetime,end_datetime, employee_id)
                    sub_text_write(f'销售单量：{len(df)}')
                    st.dataframe(df, hide_index=True, use_container_width=True)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
