# Generated by Django 4.2.1 on 2025-07-29 17:13

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0020_alter_insurebeacon_conversion_rate"),
    ]

    operations = [
        migrations.CreateModel(
            name="InsureConversionRate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "data_type",
                    models.CharField(
                        choices=[
                            ("daily", "按日期"),
                            ("hourly", "按小时"),
                            ("weekly_hourly", "按星期+小时"),
                        ],
                        help_text="转换率数据的类型",
                        max_length=20,
                        verbose_name="数据类型",
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        blank=True,
                        help_text="日期维度数据使用，格式：YYYY-MM-DD",
                        null=True,
                        verbose_name="日期",
                    ),
                ),
                (
                    "hour",
                    models.IntegerField(
                        blank=True,
                        help_text="小时维度数据使用，范围：0-23",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(23),
                        ],
                        verbose_name="小时",
                    ),
                ),
                (
                    "weekday",
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (0, "星期一"),
                            (1, "星期二"),
                            (2, "星期三"),
                            (3, "星期四"),
                            (4, "星期五"),
                            (5, "星期六"),
                            (6, "星期日"),
                        ],
                        help_text="星期+小时维度数据使用，0=星期一，6=星期日",
                        null=True,
                        verbose_name="星期几",
                    ),
                ),
                (
                    "conversion_rate",
                    models.DecimalField(
                        decimal_places=4,
                        help_text="转换率数值，支持4位小数",
                        max_digits=8,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.0000"))
                        ],
                        verbose_name="转换率",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "转换率记录",
                "verbose_name_plural": "转换率记录",
                "db_table": "insure_conversion_rate",
                "ordering": ["-create_time"],
                "indexes": [
                    models.Index(
                        fields=["data_type", "date"],
                        name="insure_conv_data_ty_5943a4_idx",
                    ),
                    models.Index(
                        fields=["data_type", "hour"],
                        name="insure_conv_data_ty_abd527_idx",
                    ),
                    models.Index(
                        fields=["data_type", "weekday", "hour"],
                        name="insure_conv_data_ty_bf0ee8_idx",
                    ),
                    models.Index(
                        fields=["create_time"], name="insure_conv_create__e2d5b4_idx"
                    ),
                ],
                "unique_together": {
                    ("data_type", "weekday", "hour"),
                    ("data_type", "hour"),
                    ("data_type", "date"),
                },
            },
        ),
    ]
