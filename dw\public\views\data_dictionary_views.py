"""
数据字典查询视图
提供Web界面查看数据字典信息
"""
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from django.core.management import call_command
from django.db import transaction
from django.utils import timezone
from public.models import PublicDatabaseInfo, PublicTableInfo, PublicColumnInfo, PublicIndexInfo
from public.cache_manager import cache_manager
import json
import pandas as pd
from io import BytesIO
import threading
import time


def get_related_sql_templates(table_name, database_name):
    """
    优化的SQL模板关联查询
    使用缓存和数据库查询优化来提升性能
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        from public.models.public_sql_template import PublicSqlTemplate
        from django.db.models import Q

        logger.debug(f"查询SQL模板: table={table_name}, database={database_name}")

        # 使用缓存
        cache_key = f"sql_templates_{table_name}_{database_name}"
        cached_result = cache_manager.get(cache_key, pattern="sql_templates")

        if cached_result is not None:
            logger.debug(f"从缓存获取SQL模板: {len(cached_result)} 个")
            return cached_result
    except Exception as e:
        logger.error(f"缓存获取失败: {e}")
        # 继续执行数据库查询

    try:
        # 构建搜索模式
        table_name_upper = table_name.upper()
        database_table_upper = f"{database_name}.{table_name}".upper()

        # 使用数据库查询而不是Python循环
        # 构建OR查询条件
        q_objects = Q()

        # 基本表名匹配
        q_objects |= Q(template__icontains=table_name)
        q_objects |= Q(template__icontains=f"{database_name}.{table_name}")

        # SQL关键字匹配
        sql_patterns = [
            f"FROM {table_name}",
            f"JOIN {table_name}",
            f"UPDATE {table_name}",
            f"INSERT INTO {table_name}",
            f"FROM `{table_name}`",
            f"JOIN `{table_name}`",
            f"UPDATE `{table_name}`",
            f"INSERT INTO `{table_name}`"
        ]

        for pattern in sql_patterns:
            q_objects |= Q(template__icontains=pattern)

        # 执行优化的查询，使用prefetch_related避免N+1问题
        templates = PublicSqlTemplate.objects.filter(
            q_objects
        ).prefetch_related('type', 'scope').only(
            'id', 'name', 'name_en', 'description', 'template',
            'is_param', 'param_names', 'param_values', 'param_description',
            'is_complete', 'create_time', 'update_time'
        )

        logger.debug(f"数据库查询到 {templates.count()} 个候选模板")

        related_templates = []
        for template in templates:
            try:
                # 二次验证（确保精确匹配）
                if template.template:
                    template_content = template.template.upper()

                    # 精确匹配验证
                    if (table_name_upper in template_content or
                        database_table_upper in template_content or
                        any(pattern.upper() in template_content for pattern in sql_patterns)):

                        related_templates.append({
                            'id': template.id,
                            'name': template.name or '未命名模板',
                            'name_en': template.name_en or '',
                            'description': template.description or '无描述',
                            'template': template.template,
                            'is_param': template.is_param,
                            'param_names': template.param_names or '',
                            'param_values': template.param_values or '',
                            'param_description': template.param_description or '',
                            'is_complete': template.is_complete,
                            'types': [t.value for t in template.type.all()],
                            'scopes': [s.value for s in template.scope.all()],
                            'create_time': template.create_time.strftime('%Y-%m-%d %H:%M:%S') if template.create_time else '未知',
                            'update_time': template.update_time.strftime('%Y-%m-%d %H:%M:%S') if template.update_time else '未知',
                        })
            except Exception as e:
                logger.warning(f"处理模板 {template.id} 时出错: {e}")
                continue

        logger.info(f"最终匹配到 {len(related_templates)} 个SQL模板")

        # 缓存结果（缓存10分钟）
        try:
            cache_manager.set(cache_key, related_templates, timeout=600, pattern="sql_templates")
        except Exception as e:
            logger.warning(f"缓存设置失败: {e}")

        return related_templates

    except Exception as e:
        logger.error(f"查询SQL模板时发生错误: {e}", exc_info=True)
        return []  # 返回空列表而不是抛出异常


def get_related_sql_templates_count(table_name, database_name):
    """
    快速获取关联SQL模板数量（不加载详细内容）
    """
    from public.models.public_sql_template import PublicSqlTemplate
    from django.db.models import Q

    # 使用缓存
    cache_key = f"sql_templates_count_{table_name}_{database_name}"
    cached_count = cache_manager.get(cache_key, pattern="sql_templates")

    if cached_count is not None:
        return cached_count

    # 构建搜索模式
    q_objects = Q()

    # 基本表名匹配
    q_objects |= Q(template__icontains=table_name)
    q_objects |= Q(template__icontains=f"{database_name}.{table_name}")

    # SQL关键字匹配
    sql_patterns = [
        f"FROM {table_name}",
        f"JOIN {table_name}",
        f"UPDATE {table_name}",
        f"INSERT INTO {table_name}",
        f"FROM `{table_name}`",
        f"JOIN `{table_name}`",
        f"UPDATE `{table_name}`",
        f"INSERT INTO `{table_name}`"
    ]

    for pattern in sql_patterns:
        q_objects |= Q(template__icontains=pattern)

    # 只计算数量，不加载数据
    count = PublicSqlTemplate.objects.filter(q_objects).count()

    # 缓存结果（缓存10分钟）
    cache_manager.set(cache_key, count, timeout=600, pattern="sql_templates")

    return count


def get_table_unique_key(table_id):
    """
    获取表的唯一键信息，优先级：主键 > 唯一索引
    """
    # 首先查找主键
    primary_keys = PublicColumnInfo.objects.filter(
        table_id=table_id,
        is_primary_key=True
    ).order_by('ordinal_position')

    if primary_keys.exists():
        pk_names = [pk.name for pk in primary_keys]
        return {
            'type': 'primary_key',
            'fields': pk_names,
            'display': ', '.join(pk_names)
        }

    # 如果没有主键，查找唯一索引
    unique_indexes = PublicIndexInfo.objects.filter(
        table_id=table_id,
        is_unique=True,
        is_primary=False
    ).order_by('name')

    if unique_indexes.exists():
        # 取第一个唯一索引
        first_unique = unique_indexes.first()
        unique_fields = [field.strip() for field in first_unique.column_names.split(',')]
        return {
            'type': 'unique_index',
            'fields': unique_fields,
            'display': ', '.join(unique_fields)
        }

    # 如果都没有，返回空
    return {
        'type': 'none',
        'fields': [],
        'display': '无'
    }


@login_required
def database_list(request):
    """数据库列表页面"""
    databases = PublicDatabaseInfo.objects.all().order_by('name')

    # 为每个数据库添加统计信息
    for database in databases:
        database.table_count = PublicTableInfo.objects.filter(database_id=database.id).count()
        database.active_table_count = PublicTableInfo.objects.filter(
            database_id=database.id,
            is_deprecated=False
        ).count()

    context = {
        'databases': databases,
        'title': '数据库列表'
    }
    return render(request, 'public/data_dictionary/database_list.html', context)


@login_required
def database_detail(request, database_id):
    """数据库详情页面"""
    database = get_object_or_404(PublicDatabaseInfo, id=database_id)
    
    # 获取查询参数
    search = request.GET.get('search', '')
    table_type = request.GET.get('type', '')
    show_deprecated = request.GET.get('deprecated', '') == 'true'
    
    # 构建查询条件
    tables_query = PublicTableInfo.objects.filter(database_id=database_id)
    
    if search:
        tables_query = tables_query.filter(
            Q(name__icontains=search) | Q(comment__icontains=search)
        )
    
    if table_type:
        tables_query = tables_query.filter(type=table_type)
    
    if not show_deprecated:
        tables_query = tables_query.filter(is_deprecated=False)
    
    # 先排序，然后分页处理
    tables_query = tables_query.order_by('name')

    # 分页 - 先分页再处理，减少需要处理的数据量
    paginator = Paginator(tables_query, 20)
    page_number = request.GET.get('page')
    tables_page = paginator.get_page(page_number)

    # 只为当前页的表获取统计信息，进一步优化性能
    table_ids = [table.id for table in tables_page]

    # 批量获取字段和索引统计信息
    if table_ids:
        # 批量获取字段数量
        column_counts = PublicColumnInfo.objects.filter(
            table_id__in=table_ids
        ).values('table_id').annotate(count=Count('id'))
        column_count_dict = {item['table_id']: item['count'] for item in column_counts}

        # 批量获取索引数量
        index_counts = PublicIndexInfo.objects.filter(
            table_id__in=table_ids
        ).values('table_id').annotate(count=Count('id'))
        index_count_dict = {item['table_id']: item['count'] for item in index_counts}

        # 批量获取唯一键信息
        unique_keys_cache = {}
        # 批量获取主键信息
        primary_keys = PublicColumnInfo.objects.filter(
            table_id__in=table_ids,
            is_primary_key=True
        ).values('table_id', 'name')

        # 按table_id分组
        for pk in primary_keys:
            table_id = pk['table_id']
            if table_id not in unique_keys_cache:
                unique_keys_cache[table_id] = []
            unique_keys_cache[table_id].append(pk['name'])

        # 对于没有主键的表，获取唯一索引
        tables_without_pk = [tid for tid in table_ids if tid not in unique_keys_cache]
        if tables_without_pk:
            unique_indexes = PublicIndexInfo.objects.filter(
                table_id__in=tables_without_pk,
                is_unique=True
            ).values('table_id', 'column_names')

            for idx in unique_indexes:
                table_id = idx['table_id']
                if table_id not in unique_keys_cache:
                    unique_keys_cache[table_id] = idx['column_names'].split(',') if idx['column_names'] else []

    # 为当前页的表添加统计信息和唯一键信息
    for table in tables_page:
        # 添加统计信息
        table.column_count = column_count_dict.get(table.id, 0)
        table.index_count = index_count_dict.get(table.id, 0)

        # 添加唯一键信息
        if table.id in unique_keys_cache:
            table.unique_key_display = {
                'fields': unique_keys_cache[table.id],
                'display': ', '.join(unique_keys_cache[table.id])
            }
        else:
            table.unique_key_display = {
                'fields': [],
                'display': '无'
            }
    
    # 获取表类型选项（使用智能缓存）
    table_types = cache_manager.get_table_types(database_id)
    
    context = {
        'database': database,
        'tables': tables_page,
        'table_types': table_types,
        'search': search,
        'table_type': table_type,
        'show_deprecated': show_deprecated,
        'title': f'{database.name} 数据库'
    }
    return render(request, 'public/data_dictionary/database_detail.html', context)


@login_required
def table_detail(request, table_id):
    """表详情页面"""
    table = get_object_or_404(PublicTableInfo, id=table_id)
    database = get_object_or_404(PublicDatabaseInfo, id=table.database_id)

    # 获取字段信息
    columns = PublicColumnInfo.objects.filter(table_id=table_id).order_by('ordinal_position')

    # 获取索引信息
    indexes = PublicIndexInfo.objects.filter(table_id=table_id).order_by('name')

    # 查找关联的SQL模板（优化版本）
    # 为了提升页面加载速度，先返回基本信息，SQL模板通过AJAX异步加载
    related_templates = []
    related_templates_count = 0

    # 检查是否需要立即加载SQL模板（通过参数控制）
    load_templates = request.GET.get('load_templates', 'false') == 'true'
    if load_templates:
        related_templates = get_related_sql_templates(table.name, database.name)
        related_templates_count = len(related_templates)
    else:
        # 异步模式：只获取数量，不加载详细内容
        related_templates_count = get_related_sql_templates_count(table.name, database.name)

    # 构建表基本信息
    table_info = {
        'name': table.name,
        'comment': table.comment or '无',
        'database_name': database.name,
        'table_type': table.type,
        'engine': table.engine or '未知',
        'charset': table.charset or '未知',
        'collation': table.collation or '未知',
        'description': table.description or '无详细描述',
        'unique_key_fields': get_table_unique_key(table.id)['display'],
        'data_source': table.data_source or '未知',
        'update_frequency': table.update_frequency or '未知',
        'is_deprecated': '是' if table.is_deprecated else '否',
        'create_time': table.create_time.strftime('%Y-%m-%d %H:%M:%S') if table.create_time else '未知',
        'update_time': table.update_time.strftime('%Y-%m-%d %H:%M:%S') if table.update_time else '未知',
        'column_count': columns.count(),
        'index_count': indexes.count(),
    }

    # 统计信息
    stats = {
        'total_columns': columns.count(),
        'primary_keys': columns.filter(is_primary_key=True).count(),
        'unique_columns': columns.filter(is_unique=True).count(),
        'nullable_columns': columns.filter(is_nullable=True).count(),
        'indexed_columns': columns.filter(is_indexed=True).count(),
        'total_indexes': indexes.count(),
        'unique_indexes': indexes.filter(is_unique=True).count(),
    }

    context = {
        'database': database,
        'table': table,
        'table_info': table_info,
        'columns': columns,
        'indexes': indexes,
        'stats': stats,
        'related_templates': related_templates,
        'related_templates_count': len(related_templates),
        'title': f'{table.name} - 表详情'
    }
    return render(request, 'public/data_dictionary/table_detail.html', context)


@login_required
@require_http_methods(["GET"])
def search_tables(request):
    """表搜索API"""
    query = request.GET.get('q', '').strip()
    database_id = request.GET.get('database_id')
    limit = int(request.GET.get('limit', 10))
    
    if not query:
        return JsonResponse({'results': []})
    
    # 构建查询条件
    tables_query = PublicTableInfo.objects.filter(
        Q(name__icontains=query) | Q(comment__icontains=query)
    ).filter(is_deprecated=False)
    
    if database_id:
        tables_query = tables_query.filter(database_id=database_id)
    
    tables = tables_query[:limit]

    results = []
    for table in tables:
        results.append({
            'id': table.id,
            'name': table.name,
            'comment': table.comment or '',
            'database_name': table.database_name,
            'full_name': f"{table.database_name}.{table.name}"
        })
    
    return JsonResponse({'results': results})


@login_required
@require_http_methods(["GET"])
def search_columns(request):
    """字段搜索API"""
    query = request.GET.get('q', '').strip()
    table_id = request.GET.get('table_id')
    limit = int(request.GET.get('limit', 10))
    
    if not query:
        return JsonResponse({'results': []})
    
    # 构建查询条件
    columns_query = PublicColumnInfo.objects.filter(
        Q(name__icontains=query) | Q(comment__icontains=query)
    )
    
    if table_id:
        columns_query = columns_query.filter(table_id=table_id)
    
    columns = columns_query[:limit]

    results = []
    for column in columns:
        # 获取表信息
        table = PublicTableInfo.objects.filter(id=column.table_id).first()
        database = PublicDatabaseInfo.objects.filter(id=table.database_id).first() if table else None

        results.append({
            'id': column.id,
            'name': column.name,
            'comment': column.comment or '',
            'data_type': column.data_type,
            'table_name': table.name if table else '未知',
            'database_name': database.name if database else '未知',
            'full_name': f"{database.name if database else '未知'}.{table.name if table else '未知'}.{column.name}"
        })
    
    return JsonResponse({'results': results})


@login_required
@require_http_methods(["GET"])
def table_structure_json(request, table_id):
    """获取表结构JSON数据"""
    table = get_object_or_404(PublicTableInfo, id=table_id)

    # 获取字段信息
    columns = PublicColumnInfo.objects.filter(table_id=table_id).order_by('ordinal_position')
    columns_data = []
    for column in columns:
        columns_data.append({
            'name': column.name,
            'comment': column.comment or '',
            'data_type': column.data_type,
            'type': column.type,
            'max_length': column.max_length,
            'numeric_precision': column.numeric_precision,
            'numeric_scale': column.numeric_scale,
            'is_nullable': column.is_nullable,
            'default': column.default,
            'is_auto_increment': column.is_auto_increment,
            'is_primary_key': column.is_primary_key,
            'is_unique': column.is_unique,
            'is_indexed': column.is_indexed,
            'ordinal_position': column.ordinal_position
        })
    
    # 获取索引信息
    indexes = PublicIndexInfo.objects.filter(table_id=table_id)
    indexes_data = []
    for index in indexes:
        indexes_data.append({
            'name': index.name,
            'type': index.type,
            'is_unique': index.is_unique,
            'is_primary': index.is_primary,
            'column_names': index.column_names,
            'column_orders': index.column_orders,
            'comment': index.comment or ''
        })
    
    data = {
        'table': {
            'name': table.name,
            'comment': table.comment or '',
            'type': table.type,
            'data_source': table.data_source or '',
            'update_frequency': table.update_frequency or '',
            'is_deprecated': table.is_deprecated,
            'engine': table.engine or '',
            'charset': table.charset or '',
            'unique_key_fields': table.unique_key_fields or ''
        },
        'database': {
            'name': table.database_name,
            'type': 'MySQL',  # 默认类型，可以从数据库信息中获取
            'description': ''
        },
        'columns': columns_data,
        'indexes': indexes_data
    }
    
    return JsonResponse(data, json_dumps_params={'ensure_ascii': False, 'indent': 2})


@login_required
def global_search(request):
    """全局搜索页面"""
    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'all')  # all, tables, columns
    
    results = {
        'query': query,
        'tables': [],
        'columns': [],
        'total_count': 0
    }
    
    if query:
        # 搜索表
        if search_type in ['all', 'tables']:
            tables = PublicTableInfo.objects.filter(
                Q(name__icontains=query) | Q(comment__icontains=query)
            ).filter(is_deprecated=False)[:50]

            for table in tables:
                results['tables'].append({
                    'id': table.id,
                    'name': table.name,
                    'comment': table.comment or '',
                    'database_name': table.database_name,
                    'type': table.type,
                    'data_source': table.data_source or ''
                })
        
        # 搜索字段
        if search_type in ['all', 'columns']:
            columns = PublicColumnInfo.objects.filter(
                Q(name__icontains=query) | Q(comment__icontains=query)
            )[:50]

            # 获取所有相关的表ID
            table_ids = [column.table_id for column in columns if column.table_id]

            # 批量获取表信息
            tables_dict = {}
            if table_ids:
                tables = PublicTableInfo.objects.filter(id__in=table_ids)
                tables_dict = {table.id: table for table in tables}

            for column in columns:
                # 获取表信息
                table = tables_dict.get(column.table_id) if column.table_id else None

                results['columns'].append({
                    'id': column.id,
                    'name': column.name,
                    'comment': column.comment or '',
                    'data_type': column.data_type,
                    'table_id': column.table_id,
                    'table_name': table.name if table else '未知',
                    'database_name': table.database_name if table else '未知',
                    'is_primary_key': column.is_primary_key,
                    'is_unique': column.is_unique
                })
        
        results['total_count'] = len(results['tables']) + len(results['columns'])
    
    context = {
        'results': results,
        'search_type': search_type,
        'title': '全局搜索'
    }
    return render(request, 'public/data_dictionary/global_search.html', context)


@login_required
@require_http_methods(["GET"])
def export_table(request, database_id, table_id):
    """导出表结构"""
    export_type = request.GET.get('type', 'excel')  # excel 或 sql

    database = get_object_or_404(PublicDatabaseInfo, id=database_id)
    table = get_object_or_404(PublicTableInfo, id=table_id, database_id=database_id)

    if export_type == 'excel':
        return _export_excel(database, table)
    elif export_type == 'sql':
        return _export_sql(database, table)
    else:
        return JsonResponse({'error': '不支持的导出类型'}, status=400)


def _export_excel(database, table):
    """导出Excel格式"""
    try:
        # 获取字段信息
        columns = PublicColumnInfo.objects.filter(
            table_id=table.id
        ).order_by('ordinal_position')

        # 构建数据
        data = []
        for i, col in enumerate(columns, 1):
            data.append({
                '序号': i,
                '字段名': col.name,
                '中文名': col.comment or '',
                '数据类型': col.type,
                '可空': '是' if col.is_nullable else '否',
                '默认值': col.default or '',
                '是否主键': '是' if col.is_primary_key else '否',
                '是否唯一': '是' if col.is_unique else '否',
                '是否索引': '是' if col.is_indexed else '否',
                '注释': col.description or ''
            })

        # 创建Excel文件
        df = pd.DataFrame(data)
        output = BytesIO()

        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=f'{table.name}字段信息', index=False)

        output.seek(0)

        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="{table.name}_字段信息.xlsx"'
        return response

    except Exception as e:
        return JsonResponse({'error': f'导出失败: {str(e)}'}, status=500)


def _export_sql(database, table):
    """导出SQL建表语句"""
    try:
        # 获取字段信息
        columns = PublicColumnInfo.objects.filter(
            table_id=table.id
        ).order_by('ordinal_position')

        # 构建SQL语句
        sql_lines = [f"-- {table.comment or table.name} 表结构"]
        sql_lines.append(f"CREATE TABLE `{table.name}` (")

        column_definitions = []
        for col in columns:
            col_def = f"  `{col.name}` {col.type}"

            if not col.is_nullable:
                col_def += " NOT NULL"

            if col.default:
                col_def += f" DEFAULT {col.default}"

            if col.is_auto_increment:
                col_def += " AUTO_INCREMENT"

            if col.comment:
                col_def += f" COMMENT '{col.comment}'"

            column_definitions.append(col_def)

        # 添加主键
        primary_keys = [col.name for col in columns if col.is_primary_key]
        if primary_keys:
            column_definitions.append(f"  PRIMARY KEY (`{'`, `'.join(primary_keys)}`)")

        sql_lines.append(",\n".join(column_definitions))
        sql_lines.append(f") ENGINE={table.engine or 'InnoDB'} DEFAULT CHARSET={table.charset or 'utf8mb4'}")

        if table.comment:
            sql_lines.append(f"COMMENT='{table.comment}';")
        else:
            sql_lines.append(";")

        sql_content = "\n".join(sql_lines)

        response = HttpResponse(sql_content, content_type='text/plain; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="{table.name}_create_table.sql"'
        return response

    except Exception as e:
        return JsonResponse({'error': f'导出失败: {str(e)}'}, status=500)


@login_required
def statistics(request):
    """数据字典统计页面"""
    from django.db.models import Count, Q
    from public.cache_manager import cache_manager
    import time

    # 检查缓存
    cache_key = 'statistics_data'
    cached_data = cache_manager.get(cache_key, pattern="statistics")

    if cached_data:
        return render(request, 'public/data_dictionary/statistics.html', cached_data)

    # 开始性能监控
    start_time = time.time()

    # 获取数据库统计（不使用复杂的关联查询）
    database_stats = PublicDatabaseInfo.objects.all().order_by('name')

    # 为每个数据库添加详细统计信息
    for database in database_stats:
        # 通过database_id获取该数据库的所有表
        tables = PublicTableInfo.objects.filter(database_id=database.id)
        database.table_count = tables.count()
        database.active_table_count = tables.filter(is_deprecated=False).count()

        # 获取该数据库的所有表ID
        table_ids = list(tables.values_list('id', flat=True))

        # 统计字段数和索引数
        database.column_count = PublicColumnInfo.objects.filter(table_id__in=table_ids).count()
        database.index_count = PublicIndexInfo.objects.filter(table_id__in=table_ids).count()

    # 使用聚合查询优化总体统计
    from django.db.models import Sum, Avg

    # 一次性获取所有统计数据
    databases_count = len(database_stats)
    tables_count = PublicTableInfo.objects.count()
    active_tables_count = PublicTableInfo.objects.filter(is_deprecated=False).count()
    columns_count = PublicColumnInfo.objects.count()
    indexes_count = PublicIndexInfo.objects.count()
    primary_keys_count = PublicColumnInfo.objects.filter(is_primary_key=True).count()
    unique_columns_count = PublicColumnInfo.objects.filter(is_unique=True).count()

    total_stats = {
        'databases': databases_count,
        'tables': tables_count,
        'active_tables': active_tables_count,
        'columns': columns_count,
        'indexes': indexes_count,
        'primary_keys': primary_keys_count,
        'unique_columns': unique_columns_count,
        # 计算平均值
        'avg_columns_per_table': round(columns_count / tables_count, 1) if tables_count > 0 else 0,
        'avg_indexes_per_table': round(indexes_count / tables_count, 1) if tables_count > 0 else 0,
        # 计算比例
        'primary_key_coverage': round(primary_keys_count / tables_count * 100, 1) if tables_count > 0 else 0,
        'active_table_ratio': round(active_tables_count / tables_count * 100, 1) if tables_count > 0 else 0,
        'deprecated_tables': tables_count - active_tables_count,
    }

    # 数据库分布（替代表类型分布，因为目前所有表都是数据表类型）
    database_distribution = list(PublicTableInfo.objects.values('database_name').annotate(
        count=Count('id')
    ).order_by('-count'))

    # 数据类型分布
    data_type_stats = list(PublicColumnInfo.objects.values('data_type').annotate(
        count=Count('id')
    ).order_by('-count')[:10])

    # 计算加载时间
    load_time = time.time() - start_time

    context = {
        'database_stats': database_stats,
        'total_stats': total_stats,
        'database_distribution': database_distribution,
        'data_type_stats': data_type_stats,
        'title': '数据字典统计',
        'load_time': round(load_time, 2)
    }

    # 缓存结果5分钟
    cache_manager.set(cache_key, context, timeout=300, pattern="statistics")

    return render(request, 'public/data_dictionary/statistics.html', context)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def sync_data_dictionary(request):
    """同步数据字典数据"""
    try:
        # 获取请求参数
        databases = request.POST.getlist('databases', ['jkx', 'nhb', 'dw', 'umami'])
        force_update = request.POST.get('force', 'false').lower() == 'true'

        # 检测是否在Gevent环境中
        try:
            import gevent
            is_gevent = True
        except ImportError:
            is_gevent = False

        # 如果在Gevent环境中，使用异步执行
        if is_gevent:
            def run_sync():
                try:
                    from io import StringIO
                    import sys
                    import logging

                    logger = logging.getLogger(__name__)
                    logger.info(f"Gevent异步同步开始: {databases}")

                    # 捕获命令输出
                    old_stdout = sys.stdout
                    old_stderr = sys.stderr
                    stdout_capture = StringIO()
                    stderr_capture = StringIO()

                    sys.stdout = stdout_capture
                    sys.stderr = stderr_capture

                    call_command(
                        'sync_multiple_databases',
                        '--databases', *databases,
                        '--force' if force_update else '--no-force',
                        verbosity=2
                    )

                    # 恢复标准输出
                    sys.stdout = old_stdout
                    sys.stderr = old_stderr

                    stdout_output = stdout_capture.getvalue()
                    stderr_output = stderr_capture.getvalue()

                    logger.info(f"Gevent异步同步完成: {', '.join(databases)}, 输出长度: {len(stdout_output)}")

                    if stderr_output:
                        logger.warning(f"Gevent同步警告: {stderr_output[:500]}")

                    # 清除同步状态和统计缓存
                    from django.core.cache import cache
                    from public.cache_manager import cache_manager
                    sync_status_key = f'data_sync_status_{request.user.id}'
                    start_time_key = f'data_sync_start_{request.user.id}'
                    cache.delete(sync_status_key)
                    cache.delete(start_time_key)
                    cache_manager.invalidate_cache("statistics")  # 清除统计缓存
                    logger.info("已清除同步状态和统计缓存")

                except Exception as e:
                    sys.stdout = old_stdout
                    sys.stderr = old_stderr
                    logger.error(f"Gevent异步同步错误: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # 即使出错也要清除同步状态
                    from django.core.cache import cache
                    sync_status_key = f'data_sync_status_{request.user.id}'
                    start_time_key = f'data_sync_start_{request.user.id}'
                    cache.delete(sync_status_key)
                    cache.delete(start_time_key)

            # 设置同步状态
            from django.core.cache import cache
            import time
            sync_status_key = f'data_sync_status_{request.user.id}'
            start_time_key = f'data_sync_start_{request.user.id}'
            cache.set(sync_status_key, 'running', 3600)  # 1小时过期
            cache.set(start_time_key, time.time(), 3600)

            # 使用Gevent spawn异步执行，确保任务能够执行
            greenlet = gevent.spawn(run_sync)
            # 不等待完成，让任务在后台执行

            return JsonResponse({
                'success': True,
                'message': f'数据同步已开始，正在后台同步数据库: {", ".join(databases)}',
                'databases': databases,
                'force_update': force_update,
                'async_mode': True
            })

        else:
            # 非Gevent环境，直接同步执行
            try:
                from io import StringIO
                import sys
                import logging

                # 设置日志
                logger = logging.getLogger(__name__)
                logger.info(f"开始同步数据库: {databases}, force_update: {force_update}")

                # 捕获命令输出和错误
                old_stdout = sys.stdout
                old_stderr = sys.stderr
                stdout_capture = StringIO()
                stderr_capture = StringIO()

                sys.stdout = stdout_capture
                sys.stderr = stderr_capture

                try:
                    call_command(
                        'sync_multiple_databases',
                        '--databases', *databases,
                        '--force' if force_update else '--no-force',
                        verbosity=2
                    )

                    # 恢复输出
                    sys.stdout = old_stdout
                    sys.stderr = old_stderr

                    stdout_output = stdout_capture.getvalue()
                    stderr_output = stderr_capture.getvalue()

                    logger.info(f"同步完成，输出长度: {len(stdout_output)}, 错误长度: {len(stderr_output)}")

                    if stderr_output:
                        logger.warning(f"同步过程中的警告/错误: {stderr_output[:500]}")

                    # 同步完成后清除统计缓存
                    from django.core.cache import cache
                    from public.cache_manager import cache_manager
                    cache_manager.invalidate_cache("statistics")
                    logger.info("已清除统计缓存")

                    return JsonResponse({
                        'success': True,
                        'message': f'数据同步完成，已同步数据库: {", ".join(databases)}',
                        'databases': databases,
                        'force_update': force_update,
                        'output': stdout_output,
                        'stderr': stderr_output if stderr_output else None,
                        'async_mode': False
                    })

                except Exception as cmd_error:
                    # 恢复输出
                    sys.stdout = old_stdout
                    sys.stderr = old_stderr

                    stderr_output = stderr_capture.getvalue()
                    logger.error(f"同步命令执行失败: {cmd_error}, stderr: {stderr_output}")

                    return JsonResponse({
                        'success': False,
                        'message': f'同步命令执行失败: {str(cmd_error)}',
                        'stderr': stderr_output,
                        'error_detail': str(cmd_error)
                    }, status=500)

            except Exception as e:
                logger.error(f"同步视图异常: {e}")
                return JsonResponse({
                    'success': False,
                    'message': f'同步过程中发生错误: {str(e)}',
                    'error_detail': str(e)
                }, status=500)

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'启动同步失败: {str(e)}'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def sync_status(request):
    """获取同步状态"""
    try:
        # 检查Redis中的同步状态
        from django.core.cache import cache
        sync_status_key = f'data_sync_status_{request.user.id}'
        status = cache.get(sync_status_key, 'completed')

        # 如果状态是running，检查是否超时（超过10分钟认为完成）
        if status == 'running':
            start_time_key = f'data_sync_start_{request.user.id}'
            start_time = cache.get(start_time_key)
            if start_time:
                import time
                if time.time() - start_time > 600:  # 10分钟超时
                    cache.delete(sync_status_key)
                    cache.delete(start_time_key)
                    status = 'completed'

        return JsonResponse({
            'success': True,
            'status': status,
            'message': '同步完成' if status == 'completed' else '同步进行中'
        })
    except Exception as e:
        logger.error(f"获取同步状态失败: {e}")
        return JsonResponse({
            'success': True,
            'status': 'completed',
            'message': '同步完成'
        })


@login_required
def data_supplement(request):
    """数据补充页面"""
    # 获取查询参数
    tab = request.GET.get('tab', 'tables')  # tables 或 columns
    database_filter = request.GET.get('database', '')
    search = request.GET.get('search', '')

    # 获取数据库列表用于筛选
    databases = PublicDatabaseInfo.objects.all().order_by('name')

    context = {
        'databases': databases,
        'tab': tab,
        'database_filter': database_filter,
        'search': search,
        'title': '数据补充'
    }

    if tab == 'tables':
        # 获取表中文名缺失的数据
        tables_query = PublicTableInfo.objects.filter(
            Q(comment__isnull=True) | Q(comment='') | Q(comment='NULL')
        ).filter(is_deprecated=False)

        if database_filter:
            tables_query = tables_query.filter(database_name=database_filter)

        if search:
            tables_query = tables_query.filter(
                Q(name__icontains=search) | Q(database_name__icontains=search)
            )

        # 分页
        paginator = Paginator(tables_query.order_by('database_name', 'name'), 50)
        page_number = request.GET.get('page')
        tables = paginator.get_page(page_number)

        context['tables'] = tables
        context['tables_count'] = tables_query.count()

    else:  # columns
        # 获取字段中文名缺失的数据
        columns_query = PublicColumnInfo.objects.filter(
            Q(comment__isnull=True) | Q(comment='') | Q(comment='NULL')
        )

        if database_filter:
            # 通过表的database_name筛选
            table_ids = PublicTableInfo.objects.filter(
                database_name=database_filter
            ).values_list('id', flat=True)
            columns_query = columns_query.filter(table_id__in=table_ids)

        if search:
            columns_query = columns_query.filter(
                Q(name__icontains=search) |
                Q(table_name__icontains=search)
            )

        # 分页
        paginator = Paginator(columns_query.order_by('table_name', 'ordinal_position'), 100)
        page_number = request.GET.get('page')
        columns = paginator.get_page(page_number)

        # 为每个字段添加数据库信息
        for column in columns:
            try:
                table = PublicTableInfo.objects.get(id=column.table_id)
                column.database_name = table.database_name
            except PublicTableInfo.DoesNotExist:
                column.database_name = '未知'

        context['columns'] = columns
        context['columns_count'] = columns_query.count()



    return render(request, 'public/data_dictionary/data_supplement.html', context)


@login_required
@require_http_methods(["POST"])
def clear_cache(request):
    """清空缓存接口"""
    try:
        from django.core.cache import cache
        from public.cache_manager import cache_manager
        cache_type = request.POST.get('type', 'all')

        if cache_type == 'all':
            cache_manager.clear_all_cache()
            # 清除统计缓存
            cache_manager.invalidate_cache("statistics")
            message = "所有缓存已清空"
        elif cache_type == 'statistics':
            # 清除统计缓存
            cache_manager.invalidate_cache("statistics")
            message = "统计缓存已清空"
        elif cache_type == 'table':
            table_id = request.POST.get('table_id')
            if table_id:
                cache_manager.clear_table_cache(int(table_id))
                message = f"表 {table_id} 的缓存已清空"
            else:
                cache_manager.clear_table_cache()
                message = "所有表缓存已清空"
        elif cache_type == 'database':
            database_id = request.POST.get('database_id')
            if database_id:
                cache_manager.clear_database_cache(int(database_id))
                message = f"数据库 {database_id} 的缓存已清空"
            else:
                cache_manager.clear_database_cache()
                message = "所有数据库缓存已清空"
        else:
            return JsonResponse({'success': False, 'message': '无效的缓存类型'})

        return JsonResponse({'success': True, 'message': message})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'清空缓存失败: {str(e)}'})


@login_required
@require_http_methods(["GET"])
def cache_status(request):
    """获取缓存状态"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        import time
        start_time = time.time()

        # 获取缓存状态信息
        cache_status_info = cache_manager.get_cache_status()

        # 添加详细的状态检查
        redis_available = cache_status_info.get('redis_available', False)
        backend = cache_status_info.get('backend', 'unknown')

        # 如果Redis不可用，尝试重新检查
        if not redis_available:
            try:
                from django.core.cache import cache
                test_key = f'cache_test_{int(time.time())}'
                cache.set(test_key, 'test', 1)
                test_result = cache.get(test_key)
                if test_result == 'test':
                    redis_available = True
                    backend = 'django_cache'
                    cache.delete(test_key)
            except Exception as e:
                logger.warning(f"Redis状态检查失败: {e}")

        # 统计缓存使用情况
        status = {
            'cache_version': cache_status_info.get('cache_version', '1.0'),
            'cache_enabled': True,
            'cache_backend': backend,
            'redis_available': redis_available,
            'memory_cache_size': cache_status_info.get('memory_cache_size', 0),
            'check_time': round(time.time() - start_time, 3),
            'features': {
                'auto_invalidation': True,
                'version_control': True,
                'pattern_based': True,
                'signal_driven': True,
                'graceful_degradation': True
            },
            'cache_patterns': [
                'table_info - 表信息相关缓存',
                'column_info - 字段信息相关缓存',
                'index_info - 索引信息相关缓存',
                'sql_templates - SQL模板相关缓存'
            ]
        }

        return JsonResponse({'success': True, 'status': status})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'获取缓存状态失败: {str(e)}'})


@login_required
def cache_management(request):
    """缓存管理页面"""
    context = {
        'title': '缓存管理'
    }
    return render(request, 'public/data_dictionary/cache_management.html', context)


@login_required
@require_http_methods(["GET"])
def load_sql_templates(request, table_id):
    """异步加载SQL模板接口"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"开始加载SQL模板，table_id: {table_id}")

        table = get_object_or_404(PublicTableInfo, id=table_id)
        database = get_object_or_404(PublicDatabaseInfo, id=table.database_id)

        logger.info(f"表信息: {table.name}, 数据库: {database.name}")

        # 获取关联的SQL模板
        related_templates = get_related_sql_templates(table.name, database.name)

        logger.info(f"找到 {len(related_templates)} 个关联的SQL模板")

        return JsonResponse({
            'success': True,
            'templates': related_templates,
            'count': len(related_templates)
        })

    except PublicTableInfo.DoesNotExist:
        logger.error(f"表不存在: table_id={table_id}")
        return JsonResponse({
            'success': False,
            'message': f'表不存在: table_id={table_id}'
        })
    except PublicDatabaseInfo.DoesNotExist:
        logger.error(f"数据库不存在: table_id={table_id}")
        return JsonResponse({
            'success': False,
            'message': f'数据库信息不存在'
        })
    except Exception as e:
        logger.error(f"加载SQL模板失败: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'加载SQL模板失败: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
def update_table_comments(request):
    """批量更新表注释"""
    try:
        data = json.loads(request.body)
        updates = data.get('updates', [])

        if not updates:
            return JsonResponse({'success': False, 'message': '没有要更新的数据'})

        updated_count = 0
        errors = []

        with transaction.atomic():
            for update in updates:
                table_id = update.get('id')
                comment = update.get('comment', '').strip()

                if not table_id:
                    errors.append('缺少表ID')
                    continue

                try:
                    table = PublicTableInfo.objects.get(id=table_id)
                    table.comment = comment
                    table.save(update_fields=['comment'])
                    updated_count += 1
                except PublicTableInfo.DoesNotExist:
                    errors.append(f'表ID {table_id} 不存在')
                except Exception as e:
                    errors.append(f'更新表ID {table_id} 失败: {str(e)}')

        if errors:
            return JsonResponse({
                'success': False,
                'message': f'部分更新失败，成功更新 {updated_count} 条记录',
                'errors': errors
            })

        return JsonResponse({
            'success': True,
            'message': f'成功更新 {updated_count} 条表注释'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': '请求数据格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def update_column_comments(request):
    """批量更新字段注释"""
    try:
        data = json.loads(request.body)
        updates = data.get('updates', [])

        if not updates:
            return JsonResponse({'success': False, 'message': '没有要更新的数据'})

        updated_count = 0
        errors = []

        with transaction.atomic():
            for update in updates:
                column_id = update.get('id')
                comment = update.get('comment', '').strip()

                if not column_id:
                    errors.append('缺少字段ID')
                    continue

                try:
                    column = PublicColumnInfo.objects.get(id=column_id)
                    column.comment = comment
                    column.save(update_fields=['comment'])
                    updated_count += 1
                except PublicColumnInfo.DoesNotExist:
                    errors.append(f'字段ID {column_id} 不存在')
                except Exception as e:
                    errors.append(f'更新字段ID {column_id} 失败: {str(e)}')

        if errors:
            return JsonResponse({
                'success': False,
                'message': f'部分更新失败，成功更新 {updated_count} 条记录',
                'errors': errors
            })

        return JsonResponse({
            'success': True,
            'message': f'成功更新 {updated_count} 条字段注释'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': '请求数据格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def batch_save_comments(request):
    """批量保存表和字段注释"""
    try:
        data = json.loads(request.body)
        updates = data.get('updates', [])

        if not updates:
            return JsonResponse({'success': False, 'message': '没有要更新的数据'})

        table_updates = []
        column_updates = []

        # 分离表和字段更新
        for update in updates:
            if update.get('type') == 'table':
                table_updates.append(update)
            elif update.get('type') == 'column':
                column_updates.append(update)

        updated_count = 0
        errors = []

        with transaction.atomic():
            # 处理表注释更新
            for update in table_updates:
                table_id = update.get('id')
                comment = update.get('comment', '').strip()

                if not table_id:
                    errors.append('缺少表ID')
                    continue

                try:
                    table = PublicTableInfo.objects.get(id=table_id)
                    table.comment = comment
                    table.save(update_fields=['comment'])
                    updated_count += 1
                except PublicTableInfo.DoesNotExist:
                    errors.append(f'表ID {table_id} 不存在')
                except Exception as e:
                    errors.append(f'更新表ID {table_id} 失败: {str(e)}')

            # 处理字段注释更新
            for update in column_updates:
                column_id = update.get('id')
                comment = update.get('comment', '').strip()

                if not column_id:
                    errors.append('缺少字段ID')
                    continue

                try:
                    column = PublicColumnInfo.objects.get(id=column_id)
                    column.comment = comment
                    column.save(update_fields=['comment'])
                    updated_count += 1
                except PublicColumnInfo.DoesNotExist:
                    errors.append(f'字段ID {column_id} 不存在')
                except Exception as e:
                    errors.append(f'更新字段ID {column_id} 失败: {str(e)}')

        if errors:
            return JsonResponse({
                'success': False,
                'message': f'部分更新失败，成功更新 {updated_count} 条记录',
                'errors': errors
            })

        return JsonResponse({
            'success': True,
            'message': f'成功批量保存 {updated_count} 条注释'
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': '请求数据格式错误'})
    except Exception as e:
        return JsonResponse({'success': False, 'message': f'批量保存失败: {str(e)}'})


@login_required
@require_http_methods(["POST"])
def toggle_table_status(request, table_id):
    """切换表的废弃状态"""
    try:
        # 获取表对象
        table = get_object_or_404(PublicTableInfo, id=table_id)

        # 获取新的状态
        is_deprecated = request.POST.get('is_deprecated', '').lower() == 'true'

        # 更新表状态
        table.is_deprecated = is_deprecated
        table.save(update_fields=['is_deprecated'])

        # 清除相关缓存
        cache_manager.clear_table_cache(table_id)
        cache_manager.clear_database_cache(table.database_id)

        # 记录操作日志
        action = "标记为废弃" if is_deprecated else "标记为活跃"
        print(f"用户 {request.user.username} 将表 {table.database_name}.{table.name} {action}")

        return JsonResponse({
            'success': True,
            'message': f'表状态已更新为{"废弃" if is_deprecated else "活跃"}',
            'is_deprecated': is_deprecated
        })

    except PublicTableInfo.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': '表不存在'
        })
    except Exception as e:
        print(f"切换表状态时发生错误: {str(e)}")
        return JsonResponse({
            'success': False,
            'message': f'操作失败: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ai_translate_field(request):
    """AI翻译单个字段"""
    try:
        data = json.loads(request.body)
        column_id = data.get('column_id')

        if not column_id:
            return JsonResponse({'success': False, 'message': '缺少字段ID'})

        column = get_object_or_404(PublicColumnInfo, id=column_id)

        # 使用AI翻译服务
        from utils.field_translation import FieldTranslationService, FieldInfo

        service = FieldTranslationService()
        field_info = FieldInfo(
            field_name=column.name,
            table_name=column.table_name,
            data_type=column.data_type or '',
            database_name=column.table_name.split('.')[0] if '.' in column.table_name else 'unknown'
        )

        result = service.translate_field(field_info)

        if result.success:
            # 更新字段的AI翻译信息（优化：减少字段更新）
            column.ai_suggested_name = result.chinese_name
            column.ai_confidence_score = result.confidence
            column.ai_translation_time = timezone.now()
            column.ai_translation_status = 'translated'
            # 使用select_for_update避免并发问题，只更新必要字段
            column.save(update_fields=['ai_suggested_name', 'ai_confidence_score', 'ai_translation_time', 'ai_translation_status'])

            return JsonResponse({
                'success': True,
                'chinese_name': result.chinese_name,
                'confidence': result.confidence,
                'message': 'AI翻译成功'
            })
        else:
            column.ai_translation_status = 'failed'
            column.save(update_fields=['ai_translation_status'])

            return JsonResponse({
                'success': False,
                'message': result.error_message or 'AI翻译失败'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'翻译失败: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def ai_translate_fields_batch(request):
    """AI批量翻译字段"""
    try:
        data = json.loads(request.body)
        column_ids = data.get('column_ids', [])

        if not column_ids:
            return JsonResponse({'success': False, 'message': '缺少字段ID列表'})

        # 获取字段信息
        columns = PublicColumnInfo.objects.filter(id__in=column_ids)
        if not columns.exists():
            return JsonResponse({'success': False, 'message': '未找到指定字段'})

        # 使用AI翻译服务
        from utils.field_translation import FieldTranslationService, FieldInfo

        service = FieldTranslationService()
        field_infos = []

        for column in columns:
            field_info = FieldInfo(
                field_name=column.name,
                table_name=column.table_name,
                data_type=column.data_type or '',
                database_name=column.table_name.split('.')[0] if '.' in column.table_name else 'unknown'
            )
            field_infos.append(field_info)

        # 使用优化的批量翻译
        batch_result = service.translate_fields_batch_optimized(field_infos)

        # 创建字段名到列对象的映射，确保正确匹配
        column_map = {col.name: col for col in columns}

        # 更新数据库
        updated_columns = []
        result_data = []

        for result in batch_result.results:
            # 通过字段名匹配列对象，而不是使用索引
            column = column_map.get(result.field_name)
            if not column:
                # 如果找不到匹配的列，跳过这个结果
                continue

            if result.success:
                column.ai_suggested_name = result.chinese_name
                column.ai_confidence_score = result.confidence
                column.ai_translation_time = timezone.now()
                column.ai_translation_status = 'translated'
                updated_columns.append(column)
            else:
                column.ai_translation_status = 'failed'
                updated_columns.append(column)

            # 构建返回结果，确保column_id正确匹配
            result_data.append({
                'column_id': column.id,
                'field_name': result.field_name,
                'chinese_name': result.chinese_name,
                'confidence': result.confidence,
                'success': result.success,
                'error_message': result.error_message
            })

        # 批量更新数据库
        if updated_columns:
            PublicColumnInfo.objects.bulk_update(
                updated_columns,
                ['ai_suggested_name', 'ai_confidence_score', 'ai_translation_time', 'ai_translation_status']
            )

        return JsonResponse({
            'success': True,
            'total_count': batch_result.total_count,
            'success_count': batch_result.success_count,
            'failed_count': batch_result.failed_count,
            'results': result_data,
            'message': f'批量翻译完成，成功 {batch_result.success_count} 个，失败 {batch_result.failed_count} 个'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'批量翻译失败: {str(e)}'
        })


@login_required
@require_http_methods(["GET"])
def get_all_column_ids(request):
    """获取所有符合筛选条件的字段中文名缺失的字段ID"""
    try:
        # 获取筛选参数
        database_filter = request.GET.get('database', '')
        search = request.GET.get('search', '')

        # 构建查询条件 - 只获取字段中文名缺失的数据
        queryset = PublicColumnInfo.objects.filter(
            Q(comment__isnull=True) | Q(comment='') | Q(comment='NULL')
        )

        if database_filter:
            # 通过表的database_name筛选
            table_ids = PublicTableInfo.objects.filter(
                database_name=database_filter
            ).values_list('id', flat=True)
            queryset = queryset.filter(table_id__in=table_ids)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(table_name__icontains=search)
            )

        # 获取所有符合条件的字段ID
        column_ids = list(queryset.values_list('id', flat=True))

        return JsonResponse({
            'success': True,
            'column_ids': column_ids,
            'total_count': len(column_ids)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取字段ID失败: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def confirm_ai_translation(request):
    """确认AI翻译建议"""
    try:
        data = json.loads(request.body)
        column_id = data.get('column_id')
        action = data.get('action')  # 'confirm' 或 'reject'
        chinese_name = data.get('chinese_name', '').strip()

        if not column_id:
            return JsonResponse({'success': False, 'message': '缺少字段ID'})

        column = get_object_or_404(PublicColumnInfo, id=column_id)

        if action == 'confirm':
            # 确认翻译：保存到comment字段，清除AI字段
            column.comment = chinese_name or column.ai_suggested_name
            column.ai_suggested_name = None
            column.ai_confidence_score = None
            column.ai_translation_time = None
            column.ai_translation_status = 'confirmed'
            column.save()

            return JsonResponse({
                'success': True,
                'message': '翻译已确认并保存'
            })

        elif action == 'reject':
            # 拒绝翻译：清除AI字段
            column.ai_suggested_name = None
            column.ai_confidence_score = None
            column.ai_translation_time = None
            column.ai_translation_status = 'rejected'
            column.save()

            return JsonResponse({
                'success': True,
                'message': '翻译已拒绝'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': '无效的操作'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'操作失败: {str(e)}'
        })


@login_required
def field_translation_management(request):
    """字段翻译管理页面"""
    # 获取查询参数
    database_name = request.GET.get('database', '')
    table_name = request.GET.get('table', '')
    status = request.GET.get('status', 'translated')  # 默认显示已翻译的字段
    page = request.GET.get('page', 1)

    # 构建查询条件
    queryset = PublicColumnInfo.objects.filter(
        ai_translation_status=status
    )

    if database_name:
        queryset = queryset.filter(table_name__startswith=database_name)

    if table_name:
        queryset = queryset.filter(table_name__icontains=table_name)

    # 排序：优先显示置信度高的
    queryset = queryset.order_by('-ai_confidence_score', 'table_name', 'name')

    # 分页
    paginator = Paginator(queryset, 50)
    columns = paginator.get_page(page)

    # 获取数据库列表用于筛选
    databases = PublicDatabaseInfo.objects.all().order_by('name')

    # 统计信息
    stats = {
        'pending': PublicColumnInfo.objects.filter(ai_translation_status='pending').count(),
        'translated': PublicColumnInfo.objects.filter(ai_translation_status='translated').count(),
        'confirmed': PublicColumnInfo.objects.filter(ai_translation_status='confirmed').count(),
        'rejected': PublicColumnInfo.objects.filter(ai_translation_status='rejected').count(),
        'failed': PublicColumnInfo.objects.filter(ai_translation_status='failed').count(),
    }

    context = {
        'columns': columns,
        'databases': databases,
        'current_database': database_name,
        'current_table': table_name,
        'current_status': status,
        'stats': stats,
        'status_choices': [
            ('pending', '待翻译'),
            ('translated', '已翻译'),
            ('confirmed', '已确认'),
            ('rejected', '已拒绝'),
            ('failed', '翻译失败'),
        ]
    }

    return render(request, 'public/field_translation_management.html', context)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def confirm_field_translation(request, column_id):
    """确认单个字段翻译"""
    try:
        column = get_object_or_404(PublicColumnInfo, id=column_id)
        data = json.loads(request.body)

        action = data.get('action')  # 'confirm' 或 'reject'
        chinese_name = data.get('chinese_name', '').strip()

        if action == 'confirm':
            if not chinese_name:
                return JsonResponse({
                    'success': False,
                    'message': '请提供中文名称'
                })

            # 确认翻译：保存到comment字段，清除AI字段
            column.comment = chinese_name
            column.ai_suggested_name = None
            column.ai_translation_status = 'confirmed'
            column.ai_translation_time = None
            column.ai_confidence_score = None
            column.save()

            return JsonResponse({
                'success': True,
                'message': f'已确认字段翻译: {column.table_name}.{column.name} -> {chinese_name}'
            })

        elif action == 'reject':
            # 拒绝翻译：清除AI字段
            column.ai_suggested_name = None
            column.ai_translation_status = 'rejected'
            column.ai_translation_time = None
            column.ai_confidence_score = None
            column.save()

            return JsonResponse({
                'success': True,
                'message': f'已拒绝字段翻译: {column.table_name}.{column.name}'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': '无效的操作'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'操作失败: {str(e)}'
        })


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def batch_confirm_translations(request):
    """批量确认字段翻译"""
    try:
        data = json.loads(request.body)
        column_ids = data.get('column_ids', [])
        action = data.get('action')  # 'confirm' 或 'reject'

        if not column_ids:
            return JsonResponse({
                'success': False,
                'message': '请选择要操作的字段'
            })

        columns = PublicColumnInfo.objects.filter(id__in=column_ids)

        if action == 'confirm':
            # 批量确认：使用AI建议的名称
            updated_count = 0
            for column in columns:
                if column.ai_suggested_name:
                    column.comment = column.ai_suggested_name
                    column.ai_suggested_name = None
                    column.ai_translation_status = 'confirmed'
                    column.ai_translation_time = None
                    column.ai_confidence_score = None
                    column.save()
                    updated_count += 1

            return JsonResponse({
                'success': True,
                'message': f'已批量确认 {updated_count} 个字段翻译'
            })

        elif action == 'reject':
            # 批量拒绝：清除AI字段
            updated_count = 0
            for column in columns:
                column.ai_suggested_name = None
                column.ai_translation_status = 'rejected'
                column.ai_translation_time = None
                column.ai_confidence_score = None
                column.save()
                updated_count += 1

            return JsonResponse({
                'success': True,
                'message': f'已批量拒绝 {updated_count} 个字段翻译'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': '无效的操作'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'操作失败: {str(e)}'
        })
