# Common 模块 - 跨数据库兼容的数据迁移工具

## 📖 概述

Common 模块提供了跨数据库兼容的数据迁移功能，支持 MySQL、PostgreSQL、SQLite、Oracle 等主流数据库。能够自动检测数据库类型并生成相应的 SQL 语句，确保迁移在不同数据库环境中都能正常工作。

## 🚀 快速开始

### 完整的项目部署流程

**⚠️ 重要：必须按以下顺序执行迁移命令**

```bash
# 完整的项目部署流程
# 1. 安装依赖
pip install -r requirements.txt

# 2. 执行 Django 标准迁移（创建表结构）
python manage.py migrate

# 3. 执行自定义时间戳默认值迁移（添加数据库级默认值）
python manage.py safe_migrate --dry-run  # 先预览
python manage.py safe_migrate            # 实际执行

# 4. 验证迁移结果
python manage.py test_timestamp_behavior

# 5. 启动服务
python manage.py runserver 0.0.0.0:8000
```

**为什么需要两步迁移？**
- `migrate`：创建表结构和字段，但 Django 模型的默认值不会在数据库层面生效
- `safe_migrate`：为已存在的表添加数据库级别的时间戳默认值和 `ON UPDATE` 行为

### 高级选项：智能自动发现

如果需要更精细的控制，可以使用 `migrate_with_defaults` 命令：

```bash
# 1. 预览所有符合条件的表
python manage.py migrate_with_defaults --action=auto_discover --dry-run

# 2. 为所有包含时间戳字段的表添加默认值
python manage.py migrate_with_defaults --action=auto_discover

# 3. 只处理特定app的表
python manage.py migrate_with_defaults --action=auto_discover --app-names insure claim

# 4. 按表名模式匹配
python manage.py migrate_with_defaults --action=auto_discover --table-patterns "insure_*" "claim_*"

# 5. 排除某些表
python manage.py migrate_with_defaults --action=auto_discover --exclude-tables "test_*" "temp_*"
```

#### 方法二：使用Django迁移（集成方式）

在Django迁移文件中使用自动发现：

```python
# your_app/migrations/xxxx_add_timestamp_defaults.py
from django.db import migrations
from common.migrations.utils import auto_add_timestamp_defaults

class Migration(migrations.Migration):
    dependencies = [
        ('your_app', 'previous_migration'),
    ]

    operations = [
        # 自动发现并处理所有符合条件的表
        auto_add_timestamp_defaults(),

        # 或者只处理特定app的表
        # auto_add_timestamp_defaults(
        #     app_names=['insure', 'claim'],
        #     exclude_tables=['django_migrations']
        # ),
    ]
```

这个方法会自动：
- 🔍 **智能发现** - 自动扫描数据库中所有包含 `create_time` 和 `update_time` 字段的表
- 🎯 **精确过滤** - 支持按app名称、表名模式、排除列表等多种过滤方式
- 🔧 **自动适配** - 检测数据库类型并生成相应的SQL语句
- 📝 **完整日志** - 记录所有操作，便于追踪和调试

## 🗄️ 主要功能

1. **数据库类型自动检测** - 自动识别当前使用的数据库引擎
2. **跨数据库兼容性** - 支持多种数据库的语法差异
3. **智能默认值处理** - 根据数据库类型生成相应的默认值SQL
4. **迁移日志记录** - 记录所有迁移操作，便于追踪和回滚
5. **安全的事务处理** - 使用数据库事务确保操作的原子性

## 🔧 支持的数据库

- MySQL 5.7+
- PostgreSQL 9.6+
- SQLite 3.8+
- Oracle 11g+

## 📊 数据库时间戳行为详细说明

### ✅ 正确的时间戳行为
- **create_time**:
  - ✅ 在 INSERT 时自动设置为当前时间
  - ✅ 在 UPDATE 时**保持不变**
- **update_time**:
  - ✅ 在 INSERT 时自动设置为当前时间
  - ✅ 在 UPDATE 时**自动更新**为当前时间

### 各数据库实现方式

#### 1. MySQL (推荐)
```sql
-- create_time: 只在INSERT时设置
ALTER TABLE table_name MODIFY COLUMN create_time DATETIME DEFAULT CURRENT_TIMESTAMP;

-- update_time: INSERT时设置，UPDATE时自动更新
ALTER TABLE table_name MODIFY COLUMN update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
```

**特点:**
- ✅ 原生支持 `ON UPDATE CURRENT_TIMESTAMP`
- ✅ 性能最佳，无需额外触发器
- ✅ 数据类型: `DATETIME`

#### 2. PostgreSQL
```sql
-- 设置默认值
ALTER TABLE table_name ALTER COLUMN create_time SET DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE table_name ALTER COLUMN update_time SET DEFAULT CURRENT_TIMESTAMP;

-- 创建更新触发器
CREATE OR REPLACE FUNCTION update_table_name_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_table_name_timestamp
    BEFORE UPDATE ON table_name
    FOR EACH ROW
    EXECUTE FUNCTION update_table_name_timestamp();
```

**特点:**
- ✅ 使用触发器实现自动更新
- ✅ 数据类型: `TIMESTAMP`
- ⚠️ 需要额外的函数和触发器

#### 3. SQLite
```sql
-- 创建更新触发器
CREATE TRIGGER IF NOT EXISTS trigger_update_table_name_timestamp
AFTER UPDATE ON table_name
FOR EACH ROW
WHEN NEW.update_time = OLD.update_time  -- 只有当update_time没有被手动修改时才自动更新
BEGIN
    UPDATE table_name SET update_time = CURRENT_TIMESTAMP WHERE rowid = NEW.rowid;
END;
```

**特点:**
- ⚠️ ALTER TABLE 功能有限
- ✅ 使用触发器实现自动更新
- ✅ 数据类型: `DATETIME`
- ⚠️ 建议在表创建时就设置默认值

#### 4. Oracle
```sql
-- 设置默认值
ALTER TABLE table_name MODIFY create_time DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE table_name MODIFY update_time DEFAULT CURRENT_TIMESTAMP;

-- 需要额外的触发器实现自动更新
```

**特点:**
- ✅ 数据类型: `TIMESTAMP`
- ⚠️ 需要额外触发器实现自动更新

## 🤔 为什么统一使用 CURRENT_TIMESTAMP 而不是 NOW()？

### MySQL时间函数对比

| 函数 | 返回值 | 标准兼容性 | 跨数据库支持 | 推荐度 |
|------|--------|------------|--------------|--------|
| `CURRENT_TIMESTAMP` | DATETIME | ✅ SQL标准 | ✅ 所有主流数据库 | ⭐⭐⭐⭐⭐ |
| `NOW()` | DATETIME | ❌ MySQL专用 | ❌ 仅MySQL | ⭐⭐⭐ |
| `SYSDATE()` | DATETIME | ❌ MySQL专用 | ❌ 仅MySQL | ⭐⭐ |
| `CURRENT_TIMESTAMP()` | DATETIME | ✅ SQL标准 | ✅ 所有主流数据库 | ⭐⭐⭐⭐⭐ |

### 我们的选择原则

#### 1. 跨数据库兼容性
```sql
-- ✅ 在所有数据库中都能工作
CURRENT_TIMESTAMP

-- ❌ 只在MySQL中工作
NOW()
```

#### 2. SQL标准合规性
```sql
-- ✅ 符合ANSI SQL标准
CURRENT_TIMESTAMP

-- ❌ 非标准函数
NOW()
```

#### 3. 功能完整性
```sql
-- ✅ 支持DEFAULT和ON UPDATE
DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

-- ⚠️ 在某些情况下可能不支持
DEFAULT NOW() ON UPDATE NOW()
```

## 🧪 测试验证

### 运行测试命令
```bash
# 测试默认数据库
python manage.py test_timestamp_behavior

# 测试指定数据库
python manage.py test_timestamp_behavior --database=your_db_alias
```

### 预期测试结果
```
开始测试数据库时间戳行为 (数据库类型: mysql)
==================================================
✓ 测试表创建成功 (mysql)
为测试表添加时间戳默认值...
✓ 时间戳默认值添加成功

=== 测试INSERT行为 ===
插入记录: ID=1, Name=测试记录1
create_time: 2024-01-01 10:00:00
update_time: 2024-01-01 10:00:00
✓ INSERT时create_time和update_time都已设置

=== 测试UPDATE行为 ===
更新后记录: Name=测试记录1_更新
原始create_time: 2024-01-01 10:00:00
新的create_time: 2024-01-01 10:00:00
原始update_time: 2024-01-01 10:00:00
新的update_time: 2024-01-01 10:00:01
✓ create_time在UPDATE时保持不变
✓ update_time在UPDATE时自动更新
✓ 测试表清理完成

==================================================
🎉 时间戳行为测试通过！
✓ create_time: 只在INSERT时设置
✓ update_time: INSERT时设置，UPDATE时自动更新
```

## 📊 数据类型对照表

| 数据库 | create_time 类型 | update_time 类型 | 默认值语法 |
|--------|------------------|------------------|------------|
| MySQL | DATETIME | DATETIME | CURRENT_TIMESTAMP |
| PostgreSQL | TIMESTAMP | TIMESTAMP | CURRENT_TIMESTAMP |
| SQLite | DATETIME | DATETIME | CURRENT_TIMESTAMP |
| Oracle | TIMESTAMP | TIMESTAMP | CURRENT_TIMESTAMP |

## 🛠️ 使用方法

### 推荐方式：集成在Django迁移中

**最佳实践是在Django迁移文件中直接使用我们的工具，这样可以一步完成表创建和默认值设置：**

```python
from django.db import migrations, models
from common.migrations.utils import add_timestamp_defaults, cross_db_sql

class Migration(migrations.Migration):
    dependencies = [
        ('your_app', 'previous_migration'),
    ]

    operations = [
        # 1. 创建表结构（标准Django操作）
        migrations.CreateModel(
            name='YourModel',
            fields=[
                ('id', models.BigAutoField(primary_key=True)),
                ('name', models.CharField(max_length=255)),
                ('create_time', models.DateTimeField(verbose_name='创建时间')),
                ('update_time', models.DateTimeField(verbose_name='更新时间')),
            ],
        ),

        # 2. 添加数据库级别的默认值（我们的工具）
        add_timestamp_defaults('your_table_name'),
    ]
```

### 备选方式：命令行工具

如果您需要为现有表添加默认值，也可以使用命令行工具：

#### 查看数据库信息
```bash
python manage.py migrate_with_defaults --action=info
```

#### 为表添加时间戳默认值
```bash
# 预览模式（不实际执行）
python manage.py migrate_with_defaults --action=add_timestamps --table=your_table_name --dry-run

# 实际执行
python manage.py migrate_with_defaults --action=add_timestamps --table=your_table_name
```

#### 修改列并添加默认值
```bash
# 修改列类型并添加默认值
python manage.py migrate_with_defaults \
    --action=modify_column \
    --table=your_table_name \
    --column=your_column_name \
    --column-type="VARCHAR(255)" \
    --default-value="'default_value'" \
    --comment="列注释"
```

#### 检查表和列的存在性
```bash
python manage.py migrate_with_defaults --action=check --table=your_table_name --column=your_column_name
```

### 在Python代码中直接使用

```python
from common.db_migration import DatabaseMigrationManager

# 初始化迁移管理器
migration_manager = DatabaseMigrationManager()

# 检查表是否存在
if migration_manager.check_table_exists('your_table'):
    # 为表添加时间戳默认值
    success = migration_manager.add_timestamp_defaults('your_table')

    # 修改列并添加默认值
    success = migration_manager.modify_column_with_default(
        table_name='your_table',
        column_name='your_column',
        column_type='VARCHAR(255)',
        default_value="'default_value'",
        comment='列注释'
    )
```

## 📝 迁移日志

所有迁移操作都会记录在 `database_migration_log` 表中，包括：

- 迁移名称
- 数据库类型
- 执行的SQL
- 执行状态（成功/失败/回滚）
- 错误信息（如果有）
- 执行时间

## 🚀 最佳实践

1. **始终先使用 `--dry-run` 预览SQL**
2. **在生产环境执行前先在测试环境验证**
3. **备份数据库后再执行迁移**
4. **使用有意义的迁移名称**
5. **为每个迁移编写回滚逻辑**

## 🔍 验证方法

### 检查MySQL数据库
```sql
-- 查看表结构，确认默认值已添加
DESCRIBE your_table_name;

-- 或者使用
SHOW CREATE TABLE your_table_name;
```

### 检查PostgreSQL数据库
```sql
-- 查看列信息
\d+ your_table_name

-- 或者查询系统表
SELECT column_name, column_default
FROM information_schema.columns
WHERE table_name = 'your_table_name';
```

### 检查迁移日志
```python
# 在Django shell中查看迁移日志
python manage.py shell

from common.models import DatabaseMigrationLog
logs = DatabaseMigrationLog.objects.all().order_by('-executed_at')
for log in logs:
    print(f"{log.migration_name}: {log.status}")
```

## ❓ 常见问题

### Q: 我的表没有 create_time 或 update_time 字段怎么办？
A: 迁移会自动跳过没有这些字段的表。您需要先使用Django迁移添加这些字段。

### Q: 如何为新表同时创建结构和默认值？
A: 使用集成方式：
```python
from django.db import migrations, models
from common.migrations.utils import add_timestamp_defaults

class Migration(migrations.Migration):
    operations = [
        # 1. 创建表
        migrations.CreateModel(name='NewModel', fields=[...]),
        # 2. 添加默认值
        add_timestamp_defaults('new_model'),
    ]
```

### Q: 支持哪些数据库？
A: 目前支持 MySQL、PostgreSQL、SQLite、Oracle。系统会自动检测并生成相应的SQL。

### Q: 如何回滚？
A: 使用Django的标准回滚命令：
```bash
python manage.py migrate common 0004  # 回滚到指定版本
```

### Q: 生产环境使用安全吗？
A: 是的，但建议：
1. 先在测试环境验证
2. 备份数据库
3. 使用 `--dry-run` 预览SQL
4. 在维护窗口执行

## 🔧 故障排除

1. **迁移失败** - 检查迁移日志表中的错误信息
2. **权限问题** - 确保数据库用户有ALTER TABLE权限
3. **语法错误** - 检查生成的SQL是否符合目标数据库的语法
4. **表不存在** - 使用check命令验证表的存在性

## ⚠️ 注意事项

1. 本工具主要针对结构化的迁移操作，复杂的数据转换建议使用Django的migration系统
2. 在生产环境使用前请充分测试
3. 某些数据库的特定功能可能需要额外的权限或配置
4. SQLite的ALTER TABLE功能有限，建议优先使用Django migration

## 📋 示例场景

### 场景1：为现有表添加时间戳默认值

假设您有一个表 `user_profile`，需要为其 `create_time` 和 `update_time` 字段添加默认值：

```bash
# 1. 检查表是否存在
python manage.py migrate_with_defaults --action=check --table=user_profile

# 2. 预览将要执行的SQL
python manage.py migrate_with_defaults --action=add_timestamps --table=user_profile --dry-run

# 3. 执行迁移
python manage.py migrate_with_defaults --action=add_timestamps --table=user_profile
```

### 场景2：修改列类型并添加默认值

```bash
# 修改status列为VARCHAR(20)并添加默认值
python manage.py migrate_with_defaults \
    --action=modify_column \
    --table=user_profile \
    --column=status \
    --column-type="VARCHAR(20)" \
    --default-value="'active'" \
    --comment="用户状态"
```
