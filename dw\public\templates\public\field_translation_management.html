{% extends 'public/data_dictionary/base.html' %}
{% load static %}

{% block title %}字段翻译管理{% endblock %}

{% block extra_css %}
<style>
    .translation-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: #fff;
    }
    .confidence-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
    }
    .confidence-high { background-color: #d4edda; color: #155724; }
    .confidence-medium { background-color: #fff3cd; color: #856404; }
    .confidence-low { background-color: #f8d7da; color: #721c24; }
    .field-info {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
        margin: 8px 0;
    }
    .translation-input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin: 8px 0;
    }
    .stats-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .stat-item {
        display: inline-block;
        margin-right: 20px;
        padding: 5px 10px;
        border-radius: 4px;
        background: #fff;
        border: 1px solid #ddd;
    }
    .filter-form {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .batch-actions {
        background: #e9ecef;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
        display: none;
    }

    /* 按钮样式优化 - 去除图标并调整宽度 */
    .action-buttons {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        min-width: 200px;
    }

    .action-buttons .btn {
        min-width: 60px;
        padding: 6px 12px;
        font-size: 13px;
        white-space: nowrap;
        flex-shrink: 0;
    }

    /* 移除所有按钮中的图标 */
    .action-buttons .btn i,
    .action-buttons .btn .fas,
    .action-buttons .btn .fa {
        display: none !important;
    }

    /* 批量操作按钮样式 */
    .batch-actions .btn {
        min-width: 80px;
        margin-right: 8px;
        padding: 6px 12px;
        font-size: 13px;
    }

    .batch-actions .btn i,
    .batch-actions .btn .fas,
    .batch-actions .btn .fa {
        display: none !important;
    }

    /* 确保按钮容器不会溢出 */
    .translation-card .col-md-6:last-child {
        overflow: visible;
        min-width: 220px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>字段翻译管理</h2>
            
            <!-- 统计信息 -->
            <div class="stats-card">
                <h5>翻译统计</h5>
                <div class="stat-item">
                    <strong>待翻译:</strong> {{ stats.pending }}
                </div>
                <div class="stat-item">
                    <strong>已翻译:</strong> {{ stats.translated }}
                </div>
                <div class="stat-item">
                    <strong>已确认:</strong> {{ stats.confirmed }}
                </div>
                <div class="stat-item">
                    <strong>已拒绝:</strong> {{ stats.rejected }}
                </div>
                <div class="stat-item">
                    <strong>翻译失败:</strong> {{ stats.failed }}
                </div>
            </div>
            
            <!-- 筛选表单 -->
            <div class="filter-form">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="database" class="form-label">数据库</label>
                        <select name="database" id="database" class="form-select">
                            <option value="">全部数据库</option>
                            {% for db in databases %}
                            <option value="{{ db.name }}" {% if db.name == current_database %}selected{% endif %}>
                                {{ db.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="table" class="form-label">表名</label>
                        <input type="text" name="table" id="table" class="form-control" 
                               value="{{ current_table }}" placeholder="输入表名筛选">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select name="status" id="status" class="form-select">
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if value == current_status %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="action-buttons">
                            <button type="submit" class="btn btn-primary">筛选</button>
                            <a href="{% url 'data_dictionary:field_translation_management' %}" class="btn btn-secondary">重置</a>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- 批量操作 -->
            <div class="batch-actions" id="batchActions">
                <button type="button" class="btn btn-success btn-sm" onclick="batchConfirm()">
                    批量确认
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="batchReject()">
                    批量拒绝
                </button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="clearSelection()">
                    取消选择
                </button>
                <span id="selectedCount" class="ms-3 text-muted">已选择 0 项</span>
            </div>
            
            <!-- 字段列表 -->
            {% if columns %}
            <div class="row">
                <div class="col-12">
                    {% for column in columns %}
                    <div class="translation-card">
                        <div class="row">
                            <div class="col-md-1">
                                <input type="checkbox" class="form-check-input column-checkbox" 
                                       value="{{ column.id }}" onchange="updateBatchActions()">
                            </div>
                            <div class="col-md-11">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>{{ column.table_name }}.{{ column.name }}</h6>
                                        <div class="field-info">
                                            <strong>数据类型:</strong> {{ column.type }}<br>
                                            {% if column.comment %}
                                            <strong>当前注释:</strong> {{ column.comment }}<br>
                                            {% endif %}
                                            {% if column.ai_suggested_name %}
                                            <strong>AI建议:</strong> {{ column.ai_suggested_name }}
                                            {% if column.ai_confidence_score %}
                                            <span class="confidence-badge 
                                                {% if column.ai_confidence_score >= 0.8 %}confidence-high
                                                {% elif column.ai_confidence_score >= 0.6 %}confidence-medium
                                                {% else %}confidence-low{% endif %}">
                                                置信度: {{ column.ai_confidence_score|floatformat:2 }}
                                            </span>
                                            {% endif %}
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        {% if current_status == 'translated' %}
                                        <div class="mb-2">
                                            <label class="form-label">确认中文名称:</label>
                                            <input type="text" class="translation-input" 
                                                   id="translation_{{ column.id }}" 
                                                   value="{{ column.ai_suggested_name|default:'' }}"
                                                   placeholder="输入或修改中文名称">
                                        </div>
                                        <div class="action-buttons">
                                            <button type="button" class="btn btn-success btn-sm"
                                                    onclick="confirmTranslation('{{ column.id }}')">
                                                确认
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    onclick="rejectTranslation('{{ column.id }}')">
                                                拒绝
                                            </button>
                                        </div>
                                        {% else %}
                                        <div class="text-muted">
                                            状态: {{ column.get_ai_translation_status_display }}
                                            {% if column.ai_translation_time %}
                                            <br>时间: {{ column.ai_translation_time|date:"Y-m-d H:i:s" }}
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- 分页 -->
            {% if columns.has_other_pages %}
            <nav aria-label="字段翻译分页">
                <ul class="pagination justify-content-center">
                    {% if columns.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ columns.previous_page_number }}&database={{ current_database }}&table={{ current_table }}&status={{ current_status }}">上一页</a>
                    </li>
                    {% endif %}
                    
                    {% for num in columns.paginator.page_range %}
                    {% if columns.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}&database={{ current_database }}&table={{ current_table }}&status={{ current_status }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if columns.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ columns.next_page_number }}&database={{ current_database }}&table={{ current_table }}&status={{ current_status }}">下一页</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <div class="alert alert-info">
                没有找到符合条件的字段翻译记录。
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// 确认单个翻译
function confirmTranslation(columnId) {
    const chineseName = document.getElementById(`translation_${columnId}`).value.trim();
    if (!chineseName) {
        alert('请输入中文名称');
        return;
    }
    
    fetch(`{% url 'data_dictionary:confirm_field_translation' 0 %}`.replace('0', columnId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'confirm',
            chinese_name: chineseName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}

// 拒绝单个翻译
function rejectTranslation(columnId) {
    if (!confirm('确定要拒绝这个翻译建议吗？')) {
        return;
    }
    
    fetch(`{% url 'data_dictionary:confirm_field_translation' 0 %}`.replace('0', columnId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'reject'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}

// 更新批量操作显示
function updateBatchActions() {
    const checkboxes = document.querySelectorAll('.column-checkbox:checked');
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');
    
    if (checkboxes.length > 0) {
        batchActions.style.display = 'block';
        selectedCount.textContent = `已选择 ${checkboxes.length} 项`;
    } else {
        batchActions.style.display = 'none';
    }
}

// 批量确认
function batchConfirm() {
    const checkboxes = document.querySelectorAll('.column-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('请选择要确认的字段');
        return;
    }
    
    if (!confirm(`确定要批量确认 ${checkboxes.length} 个字段翻译吗？`)) {
        return;
    }
    
    const columnIds = Array.from(checkboxes).map(cb => cb.value);
    
    fetch('{% url "data_dictionary:batch_confirm_translations" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'confirm',
            column_ids: columnIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}

// 批量拒绝
function batchReject() {
    const checkboxes = document.querySelectorAll('.column-checkbox:checked');
    if (checkboxes.length === 0) {
        alert('请选择要拒绝的字段');
        return;
    }
    
    if (!confirm(`确定要批量拒绝 ${checkboxes.length} 个字段翻译吗？`)) {
        return;
    }
    
    const columnIds = Array.from(checkboxes).map(cb => cb.value);
    
    fetch('{% url "data_dictionary:batch_confirm_translations" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'reject',
            column_ids: columnIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('操作失败');
    });
}

// 清除选择
function clearSelection() {
    document.querySelectorAll('.column-checkbox').forEach(cb => cb.checked = false);
    updateBatchActions();
}
</script>
{% endblock %}
