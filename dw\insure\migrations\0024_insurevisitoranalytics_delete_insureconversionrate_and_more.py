# Generated by Django 4.2.1 on 2025-07-30 09:31

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0023_alter_insureconversionrate_weekday"),
    ]

    operations = [
        migrations.CreateModel(
            name="InsureVisitorAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "data_type",
                    models.CharField(
                        choices=[
                            ("conversion_daily", "转换率-按日期"),
                            ("conversion_hourly", "转换率-按小时"),
                            ("conversion_weekly_hourly", "转换率-按星期+小时"),
                            ("visitor_date_hourly", "访客-按日期+小时"),
                            ("visitor_workday_hourly", "访客-按工作日+小时"),
                        ],
                        db_comment="访客分析数据的类型",
                        help_text="访客分析数据的类型",
                        max_length=128,
                        verbose_name="数据类型",
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        blank=True,
                        db_comment="日期维度数据使用，格式：YYYY-MM-DD",
                        help_text="日期维度数据使用，格式：YYYY-MM-DD",
                        null=True,
                        verbose_name="日期",
                    ),
                ),
                (
                    "hour",
                    models.IntegerField(
                        blank=True,
                        db_comment="小时维度数据使用，范围：0-23",
                        help_text="小时维度数据使用，范围：0-23",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(23),
                        ],
                        verbose_name="小时",
                    ),
                ),
                (
                    "weekday",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Monday", "星期一"),
                            ("Tuesday", "星期二"),
                            ("Wednesday", "星期三"),
                            ("Thursday", "星期四"),
                            ("Friday", "星期五"),
                            ("Saturday", "星期六"),
                            ("Sunday", "星期日"),
                        ],
                        db_comment="星期+小时维度数据使用，英文名称：Monday, Tuesday等",
                        help_text="星期+小时维度数据使用，英文名称：Monday, Tuesday等",
                        max_length=50,
                        null=True,
                        verbose_name="星期几",
                    ),
                ),
                (
                    "conversion_rate",
                    models.DecimalField(
                        blank=True,
                        db_comment="转换率数值，支持4位小数（部分数据类型使用）",
                        decimal_places=4,
                        help_text="转换率数值，支持4位小数（部分数据类型使用）",
                        max_digits=20,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.0000"))
                        ],
                        verbose_name="转换率",
                    ),
                ),
                (
                    "visitor_count",
                    models.IntegerField(
                        blank=True,
                        db_comment="访客数量（新增数据类型使用）",
                        help_text="访客数量（新增数据类型使用）",
                        null=True,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="访客数",
                    ),
                ),
                (
                    "is_workday",
                    models.BooleanField(
                        blank=True,
                        db_comment="工作日访客数据使用：True=工作日，False=非工作日",
                        help_text="工作日访客数据使用：True=工作日，False=非工作日",
                        null=True,
                        verbose_name="是否工作日",
                    ),
                ),
            ],
            options={
                "verbose_name": "健康险埋点访客分析",
                "verbose_name_plural": "健康险埋点访客分析",
                "db_table": "insure_visitor_analytics",
                "db_table_comment": "健康险埋点访客分析",
                "ordering": ["-create_time"],
            },
        ),
        migrations.DeleteModel(
            name="InsureConversionRate",
        ),
        migrations.AddIndex(
            model_name="insurevisitoranalytics",
            index=models.Index(
                fields=["data_type", "date"], name="insure_visi_data_ty_2b479d_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="insurevisitoranalytics",
            index=models.Index(
                fields=["data_type", "hour"], name="insure_visi_data_ty_29d08a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="insurevisitoranalytics",
            index=models.Index(
                fields=["data_type", "weekday", "hour"],
                name="insure_visi_data_ty_0a3dd8_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="insurevisitoranalytics",
            index=models.Index(
                fields=["data_type", "date", "hour"],
                name="insure_visi_data_ty_69acab_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="insurevisitoranalytics",
            index=models.Index(
                fields=["data_type", "is_workday", "hour"],
                name="insure_visi_data_ty_b9f978_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="insurevisitoranalytics",
            index=models.Index(
                fields=["visitor_count"], name="insure_visi_visitor_2aafe2_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="insurevisitoranalytics",
            unique_together={
                ("data_type", "is_workday", "hour"),
                ("data_type", "weekday", "hour"),
                ("data_type", "hour"),
                ("data_type", "date", "hour"),
                ("data_type", "date"),
            },
        ),
    ]
