# Generated by Django 3.2.12 on 2024-07-24 09:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='产品名称')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='理赔开始日期')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='销售金额')),
            ],
            options={
                'verbose_name': '理赔-产品信息表',
                'verbose_name_plural': '理赔-产品信息表',
                'db_table': 'other_product',
            },
        ),
        migrations.CreateModel(
            name='OtherProductAuditPerson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(blank=True, max_length=200, null=True, verbose_name='名称')),
                ('code', models.CharField(blank=True, max_length=512, null=True, verbose_name='产品编码集合')),
            ],
            options={
                'verbose_name': '理赔-审核人员数据范围表',
                'verbose_name_plural': '理赔-审核人员数据范围表',
                'db_table': 'other_product_audit_person',
            },
        ),
        migrations.CreateModel(
            name='OtherProductCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(blank=True, max_length=200, null=True, verbose_name='名称')),
                ('code', models.CharField(blank=True, max_length=512, null=True, verbose_name='产品编码集合')),
                ('conn', models.CharField(blank=True, max_length=64, null=True, verbose_name='数据库连接名称')),
                ('type', models.CharField(blank=True, max_length=64, null=True, verbose_name='理赔类型')),
            ],
            options={
                'verbose_name': '理赔-产品取数范围',
                'verbose_name_plural': '理赔-产品取数范围',
                'db_table': 'other_product_code',
            },
        ),
        migrations.CreateModel(
            name='OtherProductInsureType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='名称')),
                ('nanjing_basic_medicare', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='南京市基本医疗保险')),
                ('jiangsu_basic_medicare', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='江苏省内基本医疗保险')),
                ('nanjing_new_citizens', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='南京新市民')),
                ('free_meidical', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='公费医疗')),
                ('other', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='其他')),
            ],
            options={
                'verbose_name': '销售-宁惠保投保人员医保类型明细',
                'verbose_name_plural': '销售-宁惠保投保人员医保类型明细',
                'db_table': 'other_product_insure_type',
            },
        ),
        migrations.CreateModel(
            name='OtherProductProton',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='产品名称')),
                ('total_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='累计赔付')),
                ('payout_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='赔付率')),
                ('apply_num', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='理赔申请')),
                ('close_num', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='已完成结案')),
                ('pay_num', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='共理赔')),
                ('past_symptom_pay_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='既往症赔付')),
                ('avg_pay_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='人均赔付')),
                ('max_pay_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='最高获赔')),
                ('avg_burden_reduce_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='个人负担平均降低')),
                ('max_reduce_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='最高降低')),
                ('avg_reduce_ratio', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='平均降低')),
                ('total_claim_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='已申请理赔医疗总费用')),
                ('social_pay_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='社保统筹支付')),
                ('burden_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='个人负担')),
            ],
            options={
                'verbose_name': '理赔-质子重离子赔付数据',
                'verbose_name_plural': '理赔-质子重离子赔付数据',
                'db_table': 'other_product_proton',
            },
        ),
        migrations.CreateModel(
            name='OtherProductResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='产品名称')),
                ('type', models.DateField(blank=True, null=True, verbose_name='责任类型')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=28, null=True, verbose_name='赔付金额')),
            ],
            options={
                'verbose_name': '理赔-责任赔付金额',
                'verbose_name_plural': '理赔-责任赔付金额',
                'db_table': 'other_product_response',
            },
        ),
    ]
