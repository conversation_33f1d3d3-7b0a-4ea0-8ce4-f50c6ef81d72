import os
import pandas as pd


def merge_fixed_hospital_files(folder_path):
    """
    合并fixed_hospital开头的Excel文件
    Args:
        folder_path: Excel文件所在文件夹路径
    """
    # 检查文件夹是否存在
    if not os.path.exists(folder_path):
        print(f"文件夹 {folder_path} 不存在")
        return

    # 存储所有符合条件的Excel数据
    all_data = []

    # 遍历文件夹中的所有文件
    for filename in os.listdir(folder_path):
        if filename.endswith('.xlsx') and filename.startswith('pharmacies'):
            try:
                # 从文件名解析省份和城市信息
                parts = filename.split('_')
                if len(parts) >= 4:
                    province = parts[1]
                    city = parts[2]

                    # 读取Excel文件
                    file_path = os.path.join(folder_path, filename)
                    df = pd.read_excel(file_path)

                    # 添加省份和城市列
                    df['省份'] = province
                    df['城市'] = city

                    all_data.append(df)
                    print(f"已处理文件: {filename}")
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {e}")

    if not all_data:
        print("没有找到符合条件的Excel文件")
        return

    # 合并所有数据
    merged_df = pd.concat(all_data, ignore_index=True)

    # 调整列顺序，将省份和城市放在最前面
    columns = merged_df.columns.tolist()
    columns = ['省份', '城市'] + [col for col in columns if col not in ['省份', '城市']]
    merged_df = merged_df[columns]

    # 保存合并后的数据
    output_file = os.path.join(folder_path, 'merged_pharmacies.xlsx')
    merged_df.to_excel(output_file, index=False)
    print(f"已将所有Excel文件合并到: {output_file}")
    print(f"总数据条数: {len(merged_df)}")


if __name__ == '__main__':
    # 指定文件夹路径
    folder_path = os.path.join(os.path.dirname(__file__), 'download')
    merge_fixed_hospital_files(folder_path)