#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据类型标准化模块
提供数据类型转换、格式标准化等功能
"""

import logging
import pandas as pd
from .field_config import get_data_normalization_config

logger = logging.getLogger(__name__)


class DataTypeNormalizer:
    """
    数据类型标准化器
    支持整数字段、数值型varchar字段等的标准化
    """

    def __init__(self):
        """初始化标准化器"""
        # 定义需要转换为整数的字段（基于目标表的IntegerField字段）
        self.integer_fields_config = {
            'medical_designated_providers': [
                'cross_regional_medical_status', 'inpatient_status', 'outpatient_status',
                'outpatient_chronic_status', 'electronic_prescription_status',
                'electronic_certificate_status', 'mobile_payment_status',
                'insurance_wallet_status', 'electronic_billing_status', 'level'
            ]
        }

        # 定义需要转换为Decimal的字段（基于目标表的DecimalField字段）
        self.decimal_fields_config = {
            'medical_supplies_entity': [
                'initial_payment_ratio',  # 先行自付比例
                'payment_upper_limit',    # 医保支付上限
                'tender_price'            # 招标价格
            ],
            'medical_drug_entity': [
                'government_guided_price',  # 政府指导价
                'initial_payment_ratio',    # 先行自付比例
                'payment_upper_limit',      # 医保支付上限
                'procure_ceil_price'        # 采购上限价格
            ]
        }

        # 定义需要转换为Date的字段（基于目标表的DateField字段）
        self.date_fields_config = {
            'medical_supplies_entity': [
                'begin_date'  # 开始日期
            ],
            'medical_drug_entity': [
                'begin_date'  # 开始日期
            ]
        }

        # 定义需要标准化的数值型varchar字段（即使是varchar类型，但存储的是数值）
        self.numeric_varchar_fields_config = {
            'medical_designated_providers': [
                'level'  # level字段虽然是varchar，但存储数值，需要标准化格式
            ]
        }

        # 定义需要保留小数的数值型varchar字段（如经纬度字段）
        self.decimal_varchar_fields_config = {
            'medical_designated_providers': [
                'lat',   # 经度字段，需要保留小数部分
                'lnt'    # 纬度字段，需要保留小数部分
            ],
            'medical_national_negotiated_drug_providers': [
                'lat',   # 经度字段，需要保留小数部分
                'lnt'    # 纬度字段，需要保留小数部分
            ]
        }

        # 注意：preserve_empty_string_varchar_fields配置现在从统一配置模块获取
        # 不再在这里硬编码配置

    def add_integer_fields(self, target_table, fields):
        """
        添加整数字段配置

        Args:
            target_table (str): 目标表名
            fields (list): 整数字段列表
        """
        if target_table not in self.integer_fields_config:
            self.integer_fields_config[target_table] = []
        self.integer_fields_config[target_table].extend(fields)
        logger.info(f"为表 {target_table} 添加整数字段: {fields}")

    def add_numeric_varchar_fields(self, target_table, fields):
        """
        添加数值型varchar字段配置

        Args:
            target_table (str): 目标表名
            fields (list): 数值型varchar字段列表
        """
        if target_table not in self.numeric_varchar_fields_config:
            self.numeric_varchar_fields_config[target_table] = []
        self.numeric_varchar_fields_config[target_table].extend(fields)
        logger.info(f"为表 {target_table} 添加数值型varchar字段: {fields}")



    def normalize_data_types(self, df, target_table, province='gjyb'):
        """
        标准化数据类型，特别处理整数字段的浮点数值和varchar字段的数值格式

        Args:
            df (pd.DataFrame): 需要标准化的数据DataFrame
            target_table (str): 目标表名，用于确定字段类型
            province (str): 省份标识，默认为'gjyb'

        Returns:
            pd.DataFrame: 标准化后的数据DataFrame
        """
        if df.empty:
            return df

        # 获取当前目标表需要转换的字段
        integer_fields_to_convert = self.integer_fields_config.get(target_table, [])
        varchar_fields_to_convert = self.numeric_varchar_fields_config.get(target_table, [])
        decimal_varchar_fields_to_convert = self.decimal_varchar_fields_config.get(target_table, [])
        decimal_fields_to_convert = self.decimal_fields_config.get(target_table, [])
        date_fields_to_convert = self.date_fields_config.get(target_table, [])

        # 合并所有需要处理的字段
        all_fields_to_convert = list(set(
            integer_fields_to_convert + varchar_fields_to_convert +
            decimal_varchar_fields_to_convert + decimal_fields_to_convert +
            date_fields_to_convert
        ))

        if not all_fields_to_convert:
            logger.info(f"表 {target_table} 无需进行数据类型标准化")
            return df

        df_normalized = df.copy()
        converted_count = 0

        for field in all_fields_to_convert:
            if field in df_normalized.columns:
                original_values = df_normalized[field].copy()

                # 根据字段类型选择转换方式
                if field in decimal_varchar_fields_to_convert:
                    # 对于小数varchar字段，保留小数部分
                    convert_func = self._create_decimal_varchar_converter(target_table, field, province)
                elif field in varchar_fields_to_convert:
                    # 对于varchar字段，标准化数值格式（去除不必要的小数点）
                    convert_func = self._create_varchar_converter(target_table, field, province)
                elif field in decimal_fields_to_convert:
                    # 对于Decimal字段，转换为Decimal类型
                    convert_func = self._create_decimal_converter()
                elif field in date_fields_to_convert:
                    # 对于Date字段，转换为Date类型
                    convert_func = self._create_date_converter()
                else:
                    # 对于整数字段，转换为整数类型
                    convert_func = self._create_integer_converter()

                df_normalized[field] = original_values.apply(convert_func)

                # 统计转换情况
                changed_mask = (original_values != df_normalized[field]) & original_values.notna()
                changed_count = changed_mask.sum()
                if changed_count > 0:
                    converted_count += changed_count
                    if field in decimal_varchar_fields_to_convert:
                        field_type = "小数varchar格式"
                    elif field in varchar_fields_to_convert:
                        field_type = "varchar数值格式"
                    elif field in decimal_fields_to_convert:
                        field_type = "Decimal类型"
                    elif field in date_fields_to_convert:
                        field_type = "Date类型"
                    else:
                        field_type = "整数类型"
                    logger.info(f"字段 '{field}' 标准化为{field_type}: {changed_count} 个值已转换")

        if converted_count > 0:
            logger.info(f"数据类型标准化完成: 共转换 {converted_count} 个值")
        else:
            logger.info("数据类型标准化完成: 无需转换")

        return df_normalized

    def _create_varchar_converter(self, target_table=None, field_name=None, province='gjyb'):
        """
        创建varchar字段的数值格式转换器

        Args:
            target_table (str): 目标表名
            field_name (str): 字段名
            province (str): 省份标识，默认为'gjyb'

        Returns:
            function: 转换函数
        """
        # 从统一配置检查是否需要保留空字符串
        preserve_empty_string = False
        if target_table and field_name:
            config = get_data_normalization_config(province, target_table)
            preserve_fields = config.get('preserve_empty_string_varchar_fields', [])
            preserve_empty_string = field_name in preserve_fields

        def convert_value(value):
            if pd.isna(value) or value is None:
                return None
            try:
                # 如果是数值类型
                if isinstance(value, (int, float)):
                    float_val = float(value)
                    # 如果是整数值（如3.0），转换为整数字符串
                    if float_val.is_integer():
                        return str(int(float_val))
                    else:
                        # 保留小数，但转为字符串
                        return str(float_val)
                # 如果是字符串，尝试标准化
                elif isinstance(value, str):
                    if value.strip() == '':
                        # 根据配置决定是否保留空字符串
                        if preserve_empty_string:
                            return ''  # 保留空字符串
                        else:
                            return None  # 转换为None
                    try:
                        float_val = float(value.strip())
                        # 如果是整数值，转换为整数字符串
                        if float_val.is_integer():
                            return str(int(float_val))
                        else:
                            return str(float_val)
                    except ValueError:
                        # 不是数值，保持原字符串
                        return value.strip()
                else:
                    return str(value)
            except (ValueError, TypeError):
                # 转换失败，保持原值
                return value

        return convert_value

    def _create_decimal_varchar_converter(self, target_table=None, field_name=None, province='gjyb'):
        """
        创建小数varchar字段的转换器（如经纬度字段）
        保留小数部分，不进行整数化处理

        Args:
            target_table (str): 目标表名
            field_name (str): 字段名
            province (str): 省份标识，默认为'gjyb'

        Returns:
            function: 转换函数
        """
        # 从统一配置检查是否需要保留空字符串
        preserve_empty_string = False
        if target_table and field_name:
            config = get_data_normalization_config(province, target_table)
            preserve_fields = config.get('preserve_empty_string_varchar_fields', [])
            preserve_empty_string = field_name in preserve_fields

        def convert_value(value):
            if pd.isna(value) or value is None:
                return None
            try:
                # 如果是数值类型
                if isinstance(value, (int, float)):
                    # 直接转换为字符串，保留小数部分
                    return str(float(value))
                # 如果是字符串，尝试标准化
                elif isinstance(value, str):
                    if value.strip() == '':
                        # 根据配置决定是否保留空字符串
                        if preserve_empty_string:
                            return ''  # 保留空字符串
                        else:
                            return None  # 转换为None
                    try:
                        float_val = float(value.strip())
                        # 保留小数部分，转换为字符串
                        return str(float_val)
                    except ValueError:
                        # 不是数值，保持原字符串
                        return value.strip()
                else:
                    return str(value)
            except (ValueError, TypeError):
                # 转换失败，保持原值
                return value

        return convert_value

    def _create_integer_converter(self):
        """
        创建整数字段的转换器

        Returns:
            function: 转换函数
        """
        def convert_value(value):
            if pd.isna(value) or value is None:
                return None
            try:
                # 如果是数值类型
                if isinstance(value, (int, float)):
                    float_val = float(value)
                    # 如果是整数值（如3.0），转换为整数
                    if float_val.is_integer():
                        return int(float_val)
                    else:
                        # 如果有小数部分，四舍五入后转换
                        return int(round(float_val))
                # 如果是字符串，尝试转换
                elif isinstance(value, str):
                    if value.strip() == '':
                        return None
                    float_val = float(value.strip())
                    if float_val.is_integer():
                        return int(float_val)
                    else:
                        return int(round(float_val))
                else:
                    return value
            except (ValueError, TypeError):
                # 转换失败，保持原值
                return value

        return convert_value

    def _create_decimal_converter(self):
        """
        创建Decimal字段的转换器

        Returns:
            function: 转换函数
        """
        def convert_value(value):
            if pd.isna(value) or value is None:
                return None
            try:
                from decimal import Decimal
                # 如果是数值类型
                if isinstance(value, (int, float)):
                    return Decimal(str(value))
                # 如果是字符串，尝试转换
                elif isinstance(value, str):
                    if value.strip() == '':
                        return None
                    return Decimal(value.strip())
                else:
                    return Decimal(str(value))
            except (ValueError, TypeError, Exception):
                # 转换失败，保持原值
                return value

        return convert_value

    def _create_date_converter(self):
        """
        创建Date字段的转换器

        Returns:
            function: 转换函数
        """
        def convert_value(value):
            import pandas as pd
            from datetime import datetime, date

            if pd.isna(value) or value is None:
                return None
            try:

                # 如果已经是date类型
                if isinstance(value, date) and not isinstance(value, datetime):
                    return value
                # 如果是datetime类型
                elif isinstance(value, datetime):
                    return value.date()
                # 如果是pandas Timestamp类型
                elif hasattr(value, 'date'):
                    return value.date()
                # 如果是字符串，尝试转换
                elif isinstance(value, str):
                    if value.strip() == '':
                        return None
                    # 尝试解析常见的日期格式
                    value_str = value.strip()
                    # 处理 "2024-10-15 00:00:00" 格式
                    if ' ' in value_str:
                        value_str = value_str.split(' ')[0]
                    # 解析日期
                    parsed_date = datetime.strptime(value_str, '%Y-%m-%d').date()
                    return parsed_date
                else:
                    return value
            except (ValueError, TypeError, Exception):
                # 转换失败，保持原值
                return value

        return convert_value


class ValueComparator:
    """
    值比较器，提供智能的数据比较功能
    """

    def __init__(self):
        """初始化比较器"""
        # 定义需要严格字符串比较的字段（如经纬度字段）
        # 这些字段即使数值相等，但格式不同也应该被认为是不同的
        self.strict_string_compare_fields = {
            'lat',   # 经度字段，需要严格比较格式
            'lnt'    # 纬度字段，需要严格比较格式
        }

    def values_are_different(self, val1, val2, field_name=None):
        """
        比较两个值是否不同，优化数值类型的比较逻辑

        Args:
            val1: 第一个值（通常是爬虫数据）
            val2: 第二个值（通常是目标数据）
            field_name: 字段名，用于确定比较策略

        Returns:
            bool: 如果值不同返回True，相同返回False
        """
        # 标准化空值处理：将 None, '', '-', '--', '—', '/', '\' 等都视为空值
        def normalize_empty_value(val):
            """标准化空值"""
            if val is None:
                return None
            if isinstance(val, str):
                val = val.strip()
                if val in ['', '-', '--', '—', '/', '\\', 'None', 'null', 'NULL']:
                    return None
            return val

        # 标准化两个值
        normalized_val1 = normalize_empty_value(val1)
        normalized_val2 = normalize_empty_value(val2)

        # 处理标准化后的None值比较
        if normalized_val1 is None and normalized_val2 is None:
            return False
        if normalized_val1 is None or normalized_val2 is None:
            return True

        # 使用标准化后的值进行后续比较
        val1, val2 = normalized_val1, normalized_val2

        # 对于需要严格字符串比较的字段（如经纬度），直接进行字符串比较
        if field_name and field_name in self.strict_string_compare_fields:
            return str(val1) != str(val2)

        # 统一数值比较逻辑：尝试将两个值都转换为数值进行比较
        def try_numeric_compare(v1, v2):
            """尝试数值比较，如果失败则返回None"""
            try:
                # 尝试转换为浮点数
                if isinstance(v1, str):
                    v1 = v1.strip()
                    if v1 == '':
                        return None
                if isinstance(v2, str):
                    v2 = v2.strip()
                    if v2 == '':
                        return None

                float_val1 = float(v1)
                float_val2 = float(v2)

                # 检查是否都是整数值（即使类型是float）
                if float_val1.is_integer() and float_val2.is_integer():
                    # 如果都是整数值，按整数比较
                    return int(float_val1) != int(float_val2)
                else:
                    # 如果有小数部分，使用浮点数比较，考虑精度误差
                    return abs(float_val1 - float_val2) > 1e-10
            except (ValueError, TypeError):
                return None

        # 首先尝试数值比较
        numeric_result = try_numeric_compare(val1, val2)
        if numeric_result is not None:
            return numeric_result

        # 如果数值比较失败，进行字符串比较
        str1 = str(val1).strip() if val1 is not None else ''
        str2 = str(val2).strip() if val2 is not None else ''

        return str1 != str2


# 创建全局实例，方便直接使用
default_normalizer = DataTypeNormalizer()
default_comparator = ValueComparator()

# 提供便捷的函数接口
def normalize_data_types(df, target_table, province='gjyb'):
    """
    标准化数据类型的便捷函数

    Args:
        df (pd.DataFrame): 需要标准化的数据DataFrame
        target_table (str): 目标表名
        province (str): 省份标识，默认为'gjyb'

    Returns:
        pd.DataFrame: 标准化后的数据DataFrame
    """
    return default_normalizer.normalize_data_types(df, target_table, province)


def values_are_different(val1, val2, field_name=None):
    """
    比较两个值是否不同的便捷函数

    Args:
        val1: 第一个值
        val2: 第二个值
        field_name: 字段名，用于确定比较策略

    Returns:
        bool: 如果值不同返回True，相同返回False
    """
    return default_comparator.values_are_different(val1, val2, field_name)
