# Generated by Django 3.2.12 on 2025-03-04 10:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0003_claimmonthlypay'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimLiabilityPay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('liability_type', models.CharField(blank=True, max_length=32, null=True, verbose_name='责任类型')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_avg_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='人均赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
            ],
            options={
                'verbose_name': '理赔-责任赔付情况',
                'verbose_name_plural': '理赔-责任赔付情况',
                'db_table': 'claim_liability_pay',
            },
        ),
        migrations.CreateModel(
            name='ClaimPreexistingCondition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('past_symptom_type', models.CharField(blank=True, max_length=32, null=True, verbose_name='既往症类型')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_avg_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='人均赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
                ('premium_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='保费')),
                ('claim_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付率')),
            ],
            options={
                'verbose_name': '理赔-既往症赔付情况',
                'verbose_name_plural': '理赔-既往症赔付情况',
                'db_table': 'claim_preexisting_condition',
            },
        ),
    ]
