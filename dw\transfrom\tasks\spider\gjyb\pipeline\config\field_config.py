#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家医保局(gjyb)字段处理配置
"""

import logging
from typing import Dict
from transfrom.utils.field_config import BaseFieldProcessingConfig

logger = logging.getLogger(__name__)


class GjybFieldProcessingConfig(BaseFieldProcessingConfig):
    """
    国家医保局字段处理配置
    """

    def get_field_strategies(self) -> Dict:
        """
        获取国家医保局的字段处理策略配置

        Returns:
            Dict: 字段策略配置，格式为 {源表: {目标表: {字段名: 处理策略}}}
        """
        return {
            # 医保定点机构数据 - 统一使用NULL策略
            'spider_fuwu_fixed_hospital': {
                'medical_designated_providers': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 医保定点药店数据 - 统一使用NULL策略
            'spider_fuwu_retail_pharmacy': {
                'medical_designated_providers': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 医疗服务项目数据 - 统一使用NULL策略
            'spider_fuwu_service_facilities': {
                'medical_service_base': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 医用耗材数据 - 统一使用NULL策略
            'spider_fuwu_medical_supplies': {
                'medical_supplies_base': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                },
                'medical_supplies_register_message': {
                    # 医用耗材注册信息表 - 统一使用NULL策略
                    # 这是一对多映射的目标表，需要特殊的清洗处理
                }
            },
            # 西医疾病诊断数据 - 统一使用NULL策略
            'spider_fuwu_western_disease': {
                'medical_medicine_diagnosis': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 国谈药定点零售药店数据 - 统一使用NULL策略
            'spider_fuwu_national_drug_retail_pharmacy': {
                'medical_national_negotiated_drug_providers': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                    # 特别注意经纬度字段的处理
                }
            }
            # 可以继续添加其他源表和目标表的配置
        }


# 创建国家医保局配置实例
gjyb_field_config = GjybFieldProcessingConfig()


# 提供便捷的函数接口
def get_gjyb_data_cleaning_config(source_table: str):
    """获取国家医保局数据清洗配置"""
    return gjyb_field_config.get_data_cleaning_config(source_table)


def get_gjyb_field_mapping_config(source_table: str, target_table: str):
    """获取国家医保局字段映射配置"""
    return gjyb_field_config.get_field_mapping_config(source_table, target_table)


def get_gjyb_data_normalization_config(target_table: str):
    """获取国家医保局数据标准化配置"""
    return gjyb_field_config.get_data_normalization_config(target_table)
