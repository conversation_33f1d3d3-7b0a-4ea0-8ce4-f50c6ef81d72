import datetime
import logging
import warnings
from decimal import Decimal
import os
import idna
import numpy as np
import pandas as pd
import pymysql
from django.db import transaction
from django.db.models import Q
from pandasql import sqldf
from django.core.mail import EmailMessage

from dw import settings
from insure.models import InsureOnline, InsureAgent, InsureArea, InsureAgeSex
from public.models import PublicIndicatorData, PublicIndicatorMain, PublicStatistics, PublicAreaBaseInsure, PublicTarget
from task.utils.cache_manager import CacheManager
from transfrom.utils.utils import simplify_replace, query_sql, sum_or_combine, age_group, custom_update_or_create, \
    send_feishu_message, query_indicator_code

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)

DB = settings.DATABASES['jkx']
end_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
today = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
yesterday = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
    days=1)

TO = ['<EMAIL>','<EMAIL>','<EMAIL>']
# TO = ['<EMAIL>']
CC= ['<EMAIL>']

def get_connection(DB):
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn


def get_area_info(product_set_code, publish_time):
    """
    地区参保数据，包括排名、参保地、占比、总单数、今日参保、昨日参保、参保率
    """
    df_area = PublicAreaBaseInsure.objects.filter(product_set_code=product_set_code)
    df_area = pd.DataFrame(list(df_area.values()))[['count', 'code', 'name']].rename(
        columns={'code': 'area_code', 'count': 'base_insure'})
    data = pd.read_sql(
        query_sql('SQL_JKX_DAILY_SALE').format(product_set_code=product_set_code,
                                               end_datetime=publish_time),
        get_connection(DB))
    data = data[data['main'] == 1]

    # code部分超过6位，取前6位
    data['area_code'] = data['area_code'].apply(lambda x: x[:6] if x is not None else x)
    # area_code为空的置为999999，area_name为空的置为'其他'
    data.fillna({'area_code': '000000', 'area_name': '其他'}, inplace=True)
    # # 地区合并，地区、医保编码不一致，强制调平
    data.loc[data['area_name'] == '天衢新区', 'area_code'] = '378403'
    data.loc[data['area_name'] == '德城区', 'area_code'] = '378402'
    data.loc[data['area_name'] == '陵城区', 'area_code'] = '378421'
    data.loc[data['area_name'] == '宁津县', 'area_code'] = '378422'
    data.loc[data['area_name'] == '庆云县', 'area_code'] = '378423'
    data.loc[data['area_name'] == '临邑县', 'area_code'] = '378424'
    data.loc[data['area_name'] == '齐河县', 'area_code'] = '378425'
    data.loc[data['area_name'] == '平原县', 'area_code'] = '378426'
    data.loc[data['area_name'] == '夏津县', 'area_code'] = '378427'
    data.loc[data['area_name'] == '武城县', 'area_code'] = '378428'
    data.loc[data['area_name'] == '乐陵市', 'area_code'] = '378481'
    data.loc[data['area_name'] == '禹城市', 'area_code'] = '378482'
        # 区分线上线下单子
    data_offline = data.query("is_online==0")
    data_online = data.query("is_online==1")
    data_online_info = data_online.groupby(['area_code', 'area_name', 'date']).agg({'count': 'sum','amount':'sum'}).reset_index()
    data_online_info['seller'] = '线上'

    df_area_info = data_offline.groupby(['seller', 'area_code', 'area_name', 'date']).agg({'count': 'sum','amount':'sum'}).reset_index()
    df_area_info = pd.concat([df_area_info, data_online_info])
    df_area_info = pd.merge(df_area_info, df_area, how='outer', on='area_code')
    # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
    df_area_info['base_insure'] = df_area_info['base_insure'].fillna(0)
    mask = (df_area_info['base_insure'] == 0) & (df_area_info['area_name'] != '其他')
    df_area_info.loc[mask, 'area_name'] = '其他'
    # name如果为空，用area_name代替
    df_area_info['name'] = df_area_info['name'].fillna(df_area_info['area_name'])
    # 如果name为其他，area_code置为999999
    df_area_info.loc[df_area_info['name'] == '其他', 'area_code'] = '000000'

    df_area_total = df_area_info.groupby(['seller', 'area_code', 'area_name']).agg({'count': 'sum','amount':'sum'}).reset_index()
    df_area_total = pd.merge(df_area_total, df_area, how='outer', on='area_code')
    # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
    df_area_total['base_insure'] = df_area_total['base_insure'].fillna(0)
    mask = (df_area_total['base_insure'] == 0) & (df_area_total['area_name'] != '其他')
    df_area_total.loc[mask, 'area_name'] = '其他'

    # name如果为空，用area_name代替
    df_area_total['name'] = df_area_total['name'].fillna(df_area_total['area_name'])
    # 如果name为其他，area_code置为999999
    df_area_total.loc[df_area_total['name'] == '其他', 'area_code'] = '000000'

    df_area_total = df_area_total.groupby(['seller', 'area_code', 'name']).agg(
        {'count': 'sum','amount':'sum', 'base_insure': 'first'}).reset_index()
    df_area_total.fillna(0, inplace=True)

    df_area_total['count'] = df_area_total['count'].astype(int)
    df_area_total['amount'] = df_area_total['amount'].astype(int)
    # 计算占比
    if df_area_total['count'].sum() == 0:
        df_area_total['ratio'] = 0
    else:
        df_area_total['ratio'] = round(df_area_total['count'] / df_area_total['count'].sum(), 3)

    df_area_total['insure_ratio'] = df_area_total.apply(
        lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
        axis=1
    )
    df_area_total.sort_values(by=['seller', 'insure_ratio', 'count'], ascending=False, inplace=True)
    df_area_total.reset_index(drop=True, inplace=True)
    df_area_total['position'] = df_area_total.index + 1
    # name如果为其他，放到最后，调整索引
    df_area_total.reset_index(drop=True, inplace=True)
    df_area_total['position'] = df_area_total.index + 1
    # df_area_info['date'] = df_area_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_area_today = df_area_info[
        df_area_info['date'] == today.date()].groupby(['seller', 'area_code']).agg(
        {'count': 'sum','amount':'sum'}).reset_index().rename(
        columns={'count': 'today_count', 'amount': 'today_amount'})

    df_area_yesterday = df_area_info[
        df_area_info['date'] == yesterday.date()].groupby(['seller', 'area_code']).agg(
        {'count': 'sum','amount':'sum'}).reset_index().rename(columns={'count': 'yesterday_count', 'amount': 'yesterday_amount'})
    if df_area_today.empty:
        df_area_total['today_count'] = 0
        df_area_total['today_amount'] = 0
    else:
        df_area_total = pd.merge(df_area_total, df_area_today, how='outer', on=['seller', 'area_code'])
        df_area_total['today_count'].fillna(0, inplace=True)
        df_area_total['today_amount'].fillna(0, inplace=True)
    if df_area_yesterday.empty:
        df_area_total['yesterday_count'] = 0
        df_area_total['yesterday_amount'] = 0
    else:
        df_area_total = pd.merge(df_area_total, df_area_yesterday, how='outer', on=['seller', 'area_code'])
        df_area_total['yesterday_count'].fillna(0, inplace=True)
        df_area_total['yesterday_amount'].fillna(0, inplace=True)

    df_area_total['product_set_code'] = product_set_code
    df_area_total['publish_time'] = publish_time
    df_area_total.fillna(0, inplace=True)
    df_area_total = df_area_total[
        ['seller', 'name', 'count','amount', 'ratio', 'insure_ratio', 'today_count','today_amount', 'yesterday_count','yesterday_amount', 'publish_time']]
    df_area_total.rename(
        columns={'seller': '渠道', 'name': '地区', 'count': '参保人数', 'ratio': '占比', 'insure_ratio': '参保率',
                 'today_count': '今日参保人数', 'yesterday_count': '昨日参保人数', 'publish_time': '截止时间','amount':'参保金额',
                 'today_amount': '今日参保金额', 'yesterday_amount': '昨日参保金额'},inplace=True)
    return df_area_total

def email_dz_hmb():
    date = datetime.datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'德州惠民保三期-保司分地区销量统计_{date}.xlsx')
    df = get_area_info('dezhou_hmbV3', datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
    df.to_excel(path, index=False)

    mail = EmailMessage(
        subject=f'德州惠民保三期-保司分地区销量统计_{date}',
        body='见附件',
        to=TO,
        cc=CC,
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)



