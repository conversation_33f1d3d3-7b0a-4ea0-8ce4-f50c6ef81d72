# Generated by Django 4.2.1 on 2025-07-04 11:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="PublicAreaBaseInsure",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发布时间"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="地区名称"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="地区编码"
                    ),
                ),
                (
                    "count",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="基本医保人数"
                    ),
                ),
                (
                    "employee_count",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="职工医保人数"
                    ),
                ),
                (
                    "resident_count",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="居民医保人数"
                    ),
                ),
                (
                    "target",
                    models.IntegerField(blank=True, null=True, verbose_name="销售目标"),
                ),
            ],
            options={
                "verbose_name": "地区基础参保信息",
                "verbose_name_plural": "地区基础参保信息",
                "db_table": "public_area_base_insure",
            },
        ),
        migrations.CreateModel(
            name="PublicColumnInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "table_id",
                    models.IntegerField(
                        blank=True,
                        db_comment="所属表ID，用于关联public_table_info.id",
                        help_text="所属表ID，用于关联public_table_info.id",
                        null=True,
                        verbose_name="所属表ID",
                    ),
                ),
                (
                    "table_name",
                    models.CharField(
                        db_comment="所属表名称",
                        default="",
                        help_text="所属表名称",
                        max_length=128,
                        verbose_name="所属表名称",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_comment="字段名",
                        help_text="字段名",
                        max_length=128,
                        verbose_name="字段名",
                    ),
                ),
                (
                    "comment",
                    models.CharField(
                        blank=True,
                        db_comment="字段注释",
                        help_text="字段注释",
                        max_length=512,
                        null=True,
                        verbose_name="字段注释",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        db_comment="字段详细描述",
                        help_text="字段详细描述",
                        null=True,
                        verbose_name="字段描述",
                    ),
                ),
                (
                    "data_type",
                    models.CharField(
                        db_comment="基础数据类型，如varchar、int、decimal等",
                        help_text="基础数据类型，如varchar、int、decimal等",
                        max_length=64,
                        verbose_name="基础数据类型",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        db_comment="完整字段类型，如varchar(255)、decimal(10,2)等",
                        help_text="完整字段类型，如varchar(255)、decimal(10,2)等",
                        max_length=128,
                        verbose_name="完整字段类型",
                    ),
                ),
                (
                    "max_length",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="字段最大长度",
                        help_text="字段最大长度",
                        null=True,
                        verbose_name="最大长度",
                    ),
                ),
                (
                    "numeric_precision",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="数值精度（总位数）",
                        help_text="数值精度（总位数）",
                        null=True,
                        verbose_name="数值精度",
                    ),
                ),
                (
                    "numeric_scale",
                    models.BigIntegerField(
                        blank=True,
                        db_comment="数值小数位数",
                        help_text="数值小数位数",
                        null=True,
                        verbose_name="数值小数位数",
                    ),
                ),
                (
                    "is_nullable",
                    models.BooleanField(
                        db_comment="是否允许为空，TRUE表示可为空",
                        default=True,
                        help_text="是否允许为空，TRUE表示可为空",
                        verbose_name="是否允许为空",
                    ),
                ),
                (
                    "default",
                    models.TextField(
                        blank=True,
                        db_comment="默认值",
                        help_text="默认值",
                        null=True,
                        verbose_name="默认值",
                    ),
                ),
                (
                    "is_auto_increment",
                    models.BooleanField(
                        db_comment="是否自增，TRUE表示自增字段",
                        default=False,
                        help_text="是否自增，TRUE表示自增字段",
                        verbose_name="是否自增",
                    ),
                ),
                (
                    "is_primary_key",
                    models.BooleanField(
                        db_comment="是否主键，TRUE表示主键字段",
                        default=False,
                        help_text="是否主键，TRUE表示主键字段",
                        verbose_name="是否主键",
                    ),
                ),
                (
                    "is_unique",
                    models.BooleanField(
                        db_comment="是否唯一约束，TRUE表示有唯一约束",
                        default=False,
                        help_text="是否唯一约束，TRUE表示有唯一约束",
                        verbose_name="是否唯一约束",
                    ),
                ),
                (
                    "is_indexed",
                    models.BooleanField(
                        db_comment="是否有索引，TRUE表示有索引",
                        default=False,
                        help_text="是否有索引，TRUE表示有索引",
                        verbose_name="是否有索引",
                    ),
                ),
                (
                    "ordinal_position",
                    models.IntegerField(
                        db_comment="字段在表中的位置序号",
                        help_text="字段在表中的位置序号",
                        verbose_name="字段位置",
                    ),
                ),
            ],
            options={
                "verbose_name": "字段信息表",
                "verbose_name_plural": "字段信息表",
                "db_table": "public_column_info",
            },
        ),
        migrations.CreateModel(
            name="PublicDatabaseInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_comment="数据库名称，唯一标识",
                        help_text="数据库名称，唯一标识",
                        max_length=64,
                        unique=True,
                        verbose_name="数据库名称",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        db_comment="数据库类型，如MySQL、PostgreSQL等",
                        default="MySQL",
                        help_text="数据库类型，如MySQL、PostgreSQL等",
                        max_length=32,
                        verbose_name="数据库类型",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        db_comment="数据库描述信息",
                        help_text="数据库描述信息",
                        null=True,
                        verbose_name="数据库描述",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        blank=True,
                        db_comment="数据来源说明",
                        help_text="数据来源说明",
                        max_length=128,
                        null=True,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "update_frequency",
                    models.CharField(
                        blank=True,
                        db_comment="更新频率，如实时更新、每日更新等",
                        help_text="更新频率，如实时更新、每日更新等",
                        max_length=64,
                        null=True,
                        verbose_name="更新频率",
                    ),
                ),
            ],
            options={
                "verbose_name": "数据库信息表",
                "verbose_name_plural": "数据库信息表",
                "db_table": "public_database_info",
            },
        ),
        migrations.CreateModel(
            name="PublicIndexInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "table_id",
                    models.IntegerField(
                        blank=True,
                        db_comment="所属表ID，用于关联public_table_info.id",
                        help_text="所属表ID，用于关联public_table_info.id",
                        null=True,
                        verbose_name="所属表ID",
                    ),
                ),
                (
                    "table_name",
                    models.CharField(
                        db_comment="所属表名称",
                        default="",
                        help_text="所属表名称",
                        max_length=128,
                        verbose_name="所属表名称",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_comment="索引名称",
                        help_text="索引名称",
                        max_length=128,
                        verbose_name="索引名称",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        db_comment="索引类型，如BTREE、HASH、FULLTEXT、SPATIAL",
                        default="BTREE",
                        help_text="索引类型，如BTREE、HASH、FULLTEXT、SPATIAL",
                        max_length=32,
                        verbose_name="索引类型",
                    ),
                ),
                (
                    "is_unique",
                    models.BooleanField(
                        db_comment="是否唯一索引，TRUE表示唯一索引",
                        default=False,
                        help_text="是否唯一索引，TRUE表示唯一索引",
                        verbose_name="是否唯一索引",
                    ),
                ),
                (
                    "is_primary",
                    models.BooleanField(
                        db_comment="是否主键索引，TRUE表示主键索引",
                        default=False,
                        help_text="是否主键索引，TRUE表示主键索引",
                        verbose_name="是否主键索引",
                    ),
                ),
                (
                    "column_names",
                    models.TextField(
                        db_comment="索引包含的字段名，多个字段用逗号分隔",
                        help_text="索引包含的字段名，多个字段用逗号分隔",
                        verbose_name="索引字段",
                    ),
                ),
                (
                    "column_orders",
                    models.TextField(
                        blank=True,
                        db_comment="字段排序方式，用逗号分隔，如ASC,DESC,ASC",
                        help_text="字段排序方式，用逗号分隔，如ASC,DESC,ASC",
                        null=True,
                        verbose_name="字段排序",
                    ),
                ),
                (
                    "comment",
                    models.CharField(
                        blank=True,
                        db_comment="索引注释说明",
                        help_text="索引注释说明",
                        max_length=512,
                        null=True,
                        verbose_name="索引注释",
                    ),
                ),
            ],
            options={
                "verbose_name": "索引信息表",
                "verbose_name_plural": "索引信息表",
                "db_table": "public_index_info",
            },
        ),
        migrations.CreateModel(
            name="PublicIndicatorData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=32,
                        null=True,
                        verbose_name="指标编码",
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发布日期"
                    ),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="截止日期"
                    ),
                ),
                (
                    "value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=10,
                        max_digits=28,
                        null=True,
                        verbose_name="指标数据",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述说明"),
                ),
            ],
            options={
                "verbose_name": "数据指标结果表",
                "verbose_name_plural": "数据指标结果表",
                "db_table": "public_indicator_data",
            },
        ),
        migrations.CreateModel(
            name="PublicIndicatorMain",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True,
                        max_length=32,
                        null=True,
                        unique=True,
                        verbose_name="指标编码",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="指标名称"
                    ),
                ),
                (
                    "area",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="地区"
                    ),
                ),
                (
                    "currency",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="货币"
                    ),
                ),
                (
                    "freq",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="频度"
                    ),
                ),
                (
                    "unit",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="单位"
                    ),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=32,
                        null=True,
                        verbose_name="产品集编码",
                    ),
                ),
                (
                    "statistical_type",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="统计类型"
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述说明"),
                ),
            ],
            options={
                "verbose_name": "数据指标主表",
                "verbose_name_plural": "数据指标主表",
                "db_table": "public_indicator_main",
            },
        ),
        migrations.CreateModel(
            name="PublicMapping",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="分类"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="标准名称"
                    ),
                ),
                (
                    "keywords",
                    models.CharField(
                        blank=True,
                        max_length=1024,
                        null=True,
                        verbose_name="容错关键字",
                    ),
                ),
                (
                    "medical_keywords",
                    models.CharField(
                        blank=True,
                        max_length=1024,
                        null=True,
                        verbose_name="医疗关键字",
                    ),
                ),
                (
                    "drug_keywords",
                    models.CharField(
                        blank=True,
                        max_length=1024,
                        null=True,
                        verbose_name="特药关键字",
                    ),
                ),
            ],
            options={
                "verbose_name": "容错表",
                "verbose_name_plural": "容错表",
                "db_table": "public_mapping",
            },
        ),
        migrations.CreateModel(
            name="PublicSqlScope",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "scope",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="sql适用产品范围"
                    ),
                ),
                ("value", models.CharField(max_length=100, verbose_name="结果值")),
            ],
            options={
                "verbose_name": "sql适用范围表",
                "verbose_name_plural": "sql适用范围表",
                "db_table": "public_sql_scope",
            },
        ),
        migrations.CreateModel(
            name="PublicSqlTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True,
                        max_length=128,
                        null=True,
                        verbose_name="sql模板名称",
                    ),
                ),
                (
                    "name_en",
                    models.CharField(
                        blank=True,
                        max_length=128,
                        null=True,
                        unique=True,
                        verbose_name="sql模板英文名称",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="sql描述"),
                ),
                (
                    "template",
                    models.TextField(blank=True, null=True, verbose_name="sql模板"),
                ),
                (
                    "is_param",
                    models.BooleanField(
                        blank=True, default=True, null=True, verbose_name="是否含参数"
                    ),
                ),
                (
                    "param_names",
                    models.CharField(
                        blank=True,
                        max_length=256,
                        null=True,
                        verbose_name="参数名称(分号分隔)",
                    ),
                ),
                (
                    "param_values",
                    models.CharField(
                        blank=True,
                        max_length=256,
                        null=True,
                        verbose_name="参数值(分号分隔)",
                    ),
                ),
                (
                    "param_description",
                    models.TextField(blank=True, null=True, verbose_name="参数描述"),
                ),
                (
                    "is_complete",
                    models.BooleanField(
                        blank=True,
                        default=True,
                        null=True,
                        verbose_name="sql语句是否完整",
                    ),
                ),
            ],
            options={
                "verbose_name": "sql模板表",
                "verbose_name_plural": "sql模板表",
                "db_table": "public_sql_template",
            },
        ),
        migrations.CreateModel(
            name="PublicSqlType",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="sql分类"
                    ),
                ),
                ("value", models.CharField(max_length=100, verbose_name="结果值")),
            ],
            options={
                "verbose_name": "sql类型参数表",
                "verbose_name_plural": "sql类型参数表",
                "db_table": "public_sql_type",
            },
        ),
        migrations.CreateModel(
            name="PublicStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发布时间"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="分类"
                    ),
                ),
                (
                    "statistical_type",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="统计分类"
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="数据标签"
                    ),
                ),
                (
                    "value",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="结果值",
                    ),
                ),
                (
                    "additional_info",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="补充信息"
                    ),
                ),
            ],
            options={
                "verbose_name": "数据统计表",
                "verbose_name_plural": "数据统计表",
                "db_table": "public_statistics",
            },
        ),
        migrations.CreateModel(
            name="PublicTableInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "database_id",
                    models.IntegerField(
                        blank=True,
                        db_comment="所属数据库ID，用于关联public_database_info.id",
                        help_text="所属数据库ID，用于关联public_database_info.id",
                        null=True,
                        verbose_name="所属数据库ID",
                    ),
                ),
                (
                    "database_name",
                    models.CharField(
                        db_comment="所属数据库名称（冗余字段）",
                        default="default",
                        help_text="所属数据库名称（冗余字段）",
                        max_length=64,
                        verbose_name="所属数据库名称",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_comment="表名",
                        help_text="表名",
                        max_length=128,
                        verbose_name="表名",
                    ),
                ),
                (
                    "comment",
                    models.CharField(
                        blank=True,
                        db_comment="表中文名或注释",
                        help_text="表中文名或注释",
                        max_length=512,
                        null=True,
                        verbose_name="表注释",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        db_comment="表详细描述",
                        help_text="表详细描述",
                        null=True,
                        verbose_name="表描述",
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        blank=True,
                        db_comment="数据来源，表示数据的实际来源",
                        help_text="数据来源，表示数据的实际来源",
                        max_length=128,
                        null=True,
                        verbose_name="数据来源",
                    ),
                ),
                (
                    "update_frequency",
                    models.CharField(
                        blank=True,
                        db_comment="更新频率",
                        help_text="更新频率",
                        max_length=64,
                        null=True,
                        verbose_name="更新频率",
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        db_comment="表类型，如BASE_TABLE、VIEW等",
                        default="BASE_TABLE",
                        help_text="表类型，如BASE_TABLE、VIEW等",
                        max_length=32,
                        verbose_name="表类型",
                    ),
                ),
                (
                    "is_deprecated",
                    models.BooleanField(
                        db_comment="是否不再使用，TRUE表示已废弃",
                        default=False,
                        help_text="是否不再使用，TRUE表示已废弃",
                        verbose_name="是否废弃",
                    ),
                ),
                (
                    "unique_key_fields",
                    models.TextField(
                        blank=True,
                        db_comment="唯一索引字段组合，多个组合用分号分隔",
                        help_text="唯一索引字段组合，多个组合用分号分隔",
                        null=True,
                        verbose_name="唯一索引字段",
                    ),
                ),
                (
                    "engine",
                    models.CharField(
                        blank=True,
                        db_comment="存储引擎，如InnoDB、MyISAM等",
                        help_text="存储引擎，如InnoDB、MyISAM等",
                        max_length=32,
                        null=True,
                        verbose_name="存储引擎",
                    ),
                ),
                (
                    "charset",
                    models.CharField(
                        blank=True,
                        db_comment="字符集",
                        help_text="字符集",
                        max_length=32,
                        null=True,
                        verbose_name="字符集",
                    ),
                ),
                (
                    "collation",
                    models.CharField(
                        blank=True,
                        db_comment="排序规则",
                        help_text="排序规则",
                        max_length=64,
                        null=True,
                        verbose_name="排序规则",
                    ),
                ),
            ],
            options={
                "verbose_name": "表信息表",
                "verbose_name_plural": "表信息表",
                "db_table": "public_table_info",
            },
        ),
        migrations.CreateModel(
            name="PublicTarget",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="发布时间"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="类型"
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="名称"
                    ),
                ),
                (
                    "short_name",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="简称"
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="编码"
                    ),
                ),
                (
                    "target",
                    models.IntegerField(blank=True, null=True, verbose_name="销售目标"),
                ),
            ],
            options={
                "verbose_name": "销售目标表",
                "verbose_name_plural": "销售目标表",
                "db_table": "public_target",
            },
        ),
        migrations.CreateModel(
            name="SystemDict",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="字典名称"
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="字典类型"
                    ),
                ),
                (
                    "status",
                    models.IntegerField(
                        blank=True,
                        default=1,
                        null=True,
                        verbose_name="状态（0正常 1停用）",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述说明"),
                ),
            ],
            options={
                "verbose_name": "数据字典表",
                "verbose_name_plural": "数据字典表",
                "db_table": "system_dict",
            },
        ),
        migrations.CreateModel(
            name="SystemDictValue",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "dict_id",
                    models.IntegerField(blank=True, null=True, verbose_name="字典ID"),
                ),
                (
                    "position",
                    models.IntegerField(blank=True, null=True, verbose_name="字典排序"),
                ),
                (
                    "label",
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=128,
                        null=True,
                        verbose_name="字典标签",
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="字典键值"
                    ),
                ),
                (
                    "status",
                    models.IntegerField(
                        blank=True,
                        default=1,
                        null=True,
                        verbose_name="状态（0正常 1停用）",
                    ),
                ),
                (
                    "description",
                    models.TextField(blank=True, null=True, verbose_name="描述说明"),
                ),
            ],
            options={
                "verbose_name": "数据字典值表",
                "verbose_name_plural": "数据字典值表",
                "db_table": "system_dict_value",
            },
        ),
        migrations.AddConstraint(
            model_name="publictarget",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "type", "name", "short_name"),
                name="unique_public_target_combination",
            ),
        ),
        migrations.AddIndex(
            model_name="publictableinfo",
            index=models.Index(fields=["database_id"], name="idx_pub_tbl_database_id"),
        ),
        migrations.AddIndex(
            model_name="publictableinfo",
            index=models.Index(
                fields=["database_name"], name="idx_pub_tbl_database_name"
            ),
        ),
        migrations.AddIndex(
            model_name="publictableinfo",
            index=models.Index(fields=["data_source"], name="idx_pub_tbl_data_source"),
        ),
        migrations.AddIndex(
            model_name="publictableinfo",
            index=models.Index(fields=["is_deprecated"], name="idx_pub_tbl_deprecated"),
        ),
        migrations.AddIndex(
            model_name="publictableinfo",
            index=models.Index(fields=["name"], name="idx_pub_tbl_name"),
        ),
        migrations.AddIndex(
            model_name="publictableinfo",
            index=models.Index(fields=["type"], name="idx_pub_tbl_type"),
        ),
        migrations.AlterUniqueTogether(
            name="publictableinfo",
            unique_together={("database_id", "name"), ("database_name", "name")},
        ),
        migrations.AddConstraint(
            model_name="publicstatistics",
            constraint=models.UniqueConstraint(
                fields=(
                    "product_set_code",
                    "publish_time",
                    "type",
                    "statistical_type",
                    "key",
                    "additional_info",
                ),
                name="unique_public_statistics_combination",
            ),
        ),
        migrations.AddField(
            model_name="publicsqltemplate",
            name="scope",
            field=models.ManyToManyField(
                blank=True, to="public.publicsqlscope", verbose_name="sql适用产品范围"
            ),
        ),
        migrations.AddField(
            model_name="publicsqltemplate",
            name="type",
            field=models.ManyToManyField(
                blank=True, to="public.publicsqltype", verbose_name="sql分类"
            ),
        ),
        migrations.AddConstraint(
            model_name="publicmapping",
            constraint=models.UniqueConstraint(
                fields=("type", "name"), name="unique_public_mapping"
            ),
        ),
        migrations.AddConstraint(
            model_name="publicindicatormain",
            constraint=models.UniqueConstraint(
                fields=("name",), name="unique_public_indicator_main_combination"
            ),
        ),
        migrations.AddConstraint(
            model_name="publicindicatordata",
            constraint=models.UniqueConstraint(
                fields=("code", "publish_time", "end_time"),
                name="unique_public_indicator_data_combination",
            ),
        ),
        migrations.AddIndex(
            model_name="publicindexinfo",
            index=models.Index(fields=["table_id"], name="idx_pub_idx_table_id"),
        ),
        migrations.AddIndex(
            model_name="publicindexinfo",
            index=models.Index(fields=["table_name"], name="idx_pub_idx_table_name"),
        ),
        migrations.AddIndex(
            model_name="publicindexinfo",
            index=models.Index(fields=["is_unique"], name="idx_pub_idx_unique"),
        ),
        migrations.AddIndex(
            model_name="publicindexinfo",
            index=models.Index(fields=["is_primary"], name="idx_pub_idx_primary"),
        ),
        migrations.AddIndex(
            model_name="publicindexinfo",
            index=models.Index(fields=["type"], name="idx_pub_idx_type"),
        ),
        migrations.AlterUniqueTogether(
            name="publicindexinfo",
            unique_together={("table_id", "name")},
        ),
        migrations.AddIndex(
            model_name="publicdatabaseinfo",
            index=models.Index(fields=["name"], name="idx_pub_db_name"),
        ),
        migrations.AddIndex(
            model_name="publicdatabaseinfo",
            index=models.Index(fields=["type"], name="idx_pub_db_type"),
        ),
        migrations.AddIndex(
            model_name="publicdatabaseinfo",
            index=models.Index(fields=["data_source"], name="idx_pub_db_data_source"),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(fields=["table_id"], name="idx_pub_col_table_id"),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(fields=["table_name"], name="idx_pub_col_table_name"),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(
                fields=["is_primary_key"], name="idx_pub_col_primary_key"
            ),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(fields=["is_unique"], name="idx_pub_col_unique"),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(fields=["is_indexed"], name="idx_pub_col_indexed"),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(fields=["data_type"], name="idx_pub_col_data_type"),
        ),
        migrations.AddIndex(
            model_name="publiccolumninfo",
            index=models.Index(
                fields=["ordinal_position"], name="idx_pub_col_position"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="publiccolumninfo",
            unique_together={("table_id", "name")},
        ),
        migrations.AddConstraint(
            model_name="publicareabaseinsure",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "name"),
                name="unique_public_area_base_insure_combination",
            ),
        ),
    ]
