from django.db import models
from common.models import BaseModel


class ClaimAgeOverview(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市') # 当is_provincial为1时，city为空
    avg_age = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='平均年龄')
    max_age = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='最大年龄')
    max_age_gender = models.CharField(max_length=32, blank=True, null=True, verbose_name='最大年龄性别')
    min_age = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='最小月龄')

    class Meta:
        db_table = 'claim_age_overview'
        verbose_name = '理赔-赔付年龄概览'
        verbose_name_plural = verbose_name

