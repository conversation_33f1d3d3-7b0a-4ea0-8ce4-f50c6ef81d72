import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, sum_or_combine, simplify_replace
from utils.st import query_sql,text_write,empty_line

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        ps.name AS product_set_name,
        ps.code AS product_set_code,
        DATE_FORMAT(MIN(p.sale_from), '%Y-%m-%d') AS sale_from,
        DATE_FORMAT(MIN(p.pre_sale_from), '%Y-%m-%d') AS pre_sale_from
    FROM
        product_set ps
    JOIN
        product p ON p.product_set_id = ps.id AND p.main = 1
    WHERE
        LEFT(ps.code, 10) in ('rizhao_nxb','dezhou_hmb','binzhou_yh')
    GROUP BY
        ps.name
    ORDER BY
        ps.name DESC;

        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_group_info(product_set_code, end_datetime,product_main,sql = query_sql('SQL_GROUP_INFO')):
    """
    获取保司物料的传播情况，包括阅读和转发
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售开始时间
    :return:
    """
    df = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code, end_datetime=end_datetime,product_main = product_main),
                               ttl=0)
    # 2024-11-29T17:08:28.000格式转换为2024-11-29 17:08:28格式
    df['导入时间'] = df['导入时间'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
    return df

def main():
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("团单信息报表")
    product_info = get_product_code()
    # 选择日期
    sale_from = datetime.date(2021, 1, 7)
    sale_until = datetime.date.today()
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[0]
                sale_date = min(product_info[product_info['product_set_name'] == column_name]['sale_from'].values[0],
                                product_info[product_info['product_set_name'] == column_name]['pre_sale_from'].values[
                                    0])
            else:
                product_set_code = None
                sale_date = '2000-01-01'
        with cols[1]:
            end_date = st.date_input('截止日期', min_value=sale_from, max_value=sale_until, value=sale_until,
                                     key='end_date')
        with cols[2]:
            main_name = st.selectbox("请选择产品类型", options=['全部','主险','附加险'],placeholder="请选择产品类型")
            if main_name == '全部':
                main_str = "'0','1'"
            elif main_name == '主险':
                main_str = '1'
            else:
                main_str = '0'


        st.divider()
        if st.button('查询'):
            with st.spinner('查询中...'):
                df = get_group_info(product_set_code, end_date.strftime('%Y-%m-%d') + ' 23:59:59',main_str)
                st.dataframe(df, hide_index=True, use_container_width=True)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
