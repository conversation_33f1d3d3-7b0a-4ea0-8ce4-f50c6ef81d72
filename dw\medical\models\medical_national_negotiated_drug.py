from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalNationalNegotiatedDrug(BaseModel):
    drug_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='药品名称', **_db_comment_kwarg('药品名称'))
    production_company_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    sale_flag = models.IntegerField(blank=True, null=True, verbose_name='出售标识', **_db_comment_kwarg('出售标识'))
    mobile = models.CharField(max_length=255, blank=True, null=True, verbose_name='联系电话', **_db_comment_kwarg('联系电话'))

    class Meta:
        db_table = 'medical_national_negotiated_drug'
        verbose_name = '国家谈判药品配备机构目录'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

