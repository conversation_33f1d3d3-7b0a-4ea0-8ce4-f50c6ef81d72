from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class PublicOnlineTarget(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='渠道名称')
    code = models.CharField(max_length=64, blank=True, null=True, verbose_name='渠道编码')
    target = models.IntegerField(blank=True, null=True, verbose_name='销售目标')

    class Meta:
        db_table = 'public_online_target'
        verbose_name = '线上销售目标表'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'name'],
                name='unique_public_online_target_combination'
            ),
        ]
