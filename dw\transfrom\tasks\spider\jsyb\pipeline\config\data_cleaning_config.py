#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏医保局(jsyb)数据清洗配置
"""

import logging
import pandas as pd
import json
from typing import Dict, Callable
from transfrom.utils.data_cleaning_config import BaseDataCleaningConfig, CommonCleaningFunctions
from transfrom.utils.common_field_processing import CommonFieldProcessing

logger = logging.getLogger(__name__)


class JsybDataCleaningConfig(BaseDataCleaningConfig):
    """
    江苏医保局数据清洗配置
    """

    def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
        """
        获取表级别的清洗规则配置

        表级别清洗适用于跨字段的复杂清洗逻辑
        注意：避免与字段级别清洗重复配置

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {规则配置}}
        """
        return {
            'spider_jsyb_service_facilities': {
                'custom_function': 'clean_jsyb_service_facilities_data'
            },
            'spider_jsyb_drug': {
                'custom_function': 'clean_jsyb_drug_data'
            }
        }

    def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
        """
        获取字段级别的清洗规则配置

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {字段名: {规则配置}}}
        """
        return {
            # 'spider_fuwu_fixed_hospital': {
            #     'addr': {
            #         'address_cleaning': {
            #             'enabled': True,
            #             'min_length': 200
            #         }
            #     }
            # },
        }

    def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
        """
        获取自定义清洗函数

        Returns:
            Dict[str, Callable]: 自定义清洗函数字典
        """
        return {
            'clean_fuwu_hospital_data': self._clean_fuwu_hospital_data,
            'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data,
            'clean_jsyb_service_facilities_data': self._clean_jsyb_service_facilities_data,
            'clean_jsyb_drug_data': self._clean_jsyb_drug_data,
            'validate_medins_type': self._validate_medins_type
        }

    def _clean_fuwu_hospital_data(self, df):
        """
        清洗医保定点机构数据的自定义函数

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 对addr字段进行清洗（如果存在且长度大于200）
        if 'addr' in df.columns:
            before_clean = df['addr'].apply(lambda x: len(str(x)) if pd.notna(x) else 0).sum()
            df['addr'] = df['addr'].apply(
                lambda x: CommonCleaningFunctions.clean_address_basic(x)
                if isinstance(x, str) and len(x) > 200 else x
            )
            after_clean = df['addr'].apply(lambda x: len(str(x)) if pd.notna(x) else 0).sum()
            logger.info(f"addr字段清洗完成: 总字符数从 {before_clean} 减少到 {after_clean}")

        # 对type字段进行验证清洗
        df = self._validate_medins_type(df)

        return df

    def _clean_fuwu_pharmacy_data(self, df):
        """
        清洗医保定点药店数据的自定义函数

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 对addr字段进行清洗（如果存在且长度大于200）
        if 'addr' in df.columns:
            before_clean = df['addr'].apply(lambda x: len(str(x)) if pd.notna(x) else 0).sum()
            df['addr'] = df['addr'].apply(
                lambda x: CommonCleaningFunctions.clean_address_basic(x)
                if isinstance(x, str) and len(x) > 200 else x
            )
            after_clean = df['addr'].apply(lambda x: len(str(x)) if pd.notna(x) else 0).sum()
            logger.info(f"addr字段清洗完成: 总字符数从 {before_clean} 减少到 {after_clean}")

        # 对type字段进行验证清洗
        df = self._validate_medins_type(df)

        return df

    def _validate_medins_type(self, df):
        """
        验证和清洗机构类型字段

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        if 'medinsType' not in df.columns:
            return df

        try:
            # 延迟导入避免循环导入
            from transfrom.utils.field_mapping import DictMappingManager
            dict_mapping_manager = DictMappingManager()
            institution_type = dict_mapping_manager.get_dict_mapping(dict_id=24)

            if not institution_type.empty:
                # 获取有效的type代码列表
                dict_code_list = institution_type['dict_code'].tolist()
                logger.info(f"获取到的机构类型字典: {dict_code_list}")

                # 记录清洗前的统计
                before_clean_count = df['medinsType'].notna().sum()

                # 清洗type字段：只保留在字典范围内的值
                invalid_types = df[~df['medinsType'].isin(dict_code_list) & df['medinsType'].notna()]['medinsType'].unique()
                if len(invalid_types) > 0:
                    logger.info(f"发现无效的medinsType值: {invalid_types.tolist()}")

                df['medinsType'] = df['medinsType'].apply(lambda x: x if x in dict_code_list else None)

                # 清洗type_name字段：如果对应的type为None，则type_name也设为None
                if 'medinsTypeName' in df.columns:
                    type_is_null = df['medinsType'].isna()
                    df.loc[type_is_null, 'medinsTypeName'] = None

                # 记录清洗后的统计
                after_clean_count = df['medinsType'].notna().sum()
                logger.info(f"type字段清洗完成: 类型范围为 {dict_code_list}")
                logger.info(f"type字段清洗统计: 清洗前 {before_clean_count} 条有效记录，清洗后 {after_clean_count} 条有效记录")
            else:
                logger.warning("未获取到dict_id=24的字典数据，跳过type字段清洗")
        except Exception as e:
            logger.error(f"type字段清洗失败: {e}")

        return df

    def _clean_jsyb_service_facilities_data(self, df):
        """
        清洗江苏医疗服务项目数据的自定义函数
        主要处理：
        1. 不能以任何形式修改medical_field_mapping表中的记录
        2. 直接创建所有目标字段，避免依赖medical_field_mapping表
        3. 源表唯一键：['收费项目编码', '收费项目名称', '国家医疗服务项目代码', '三类医院价格苏南']
        4. 目标表唯一键：['charge_item_code', 'charge_item_name', 'code', 'remark']
        5. remark字段映射'三类医院价格苏南'字段，用于唯一性约束
        6. 价格字段映射写入additional_info字段，格式为JSON
        7. 添加省份和城市信息
        8. 按照唯一键进行过滤
        """
        logger.info(f"开始清洗江苏医疗服务项目数据，输入记录数: {len(df)}")
        try:
            # 先过滤 国家医疗服务项目代码 不为空且不等于'-' 的数据
            if '国家医疗服务项目代码' in df.columns:
                before = len(df)
                df = df[df['国家医疗服务项目代码'].notna() & (df['国家医疗服务项目代码'] != '-')]
                logger.info(f"过滤国家医疗服务项目代码为空或为'-'的数据: {before} → {len(df)}")

            # 字段映射 - 直接创建目标字段
            field_mappings = {
                '收费项目编码': 'charge_item_code',
                '收费项目名称': 'charge_item_name',
                '国家医疗服务项目代码': 'code',
                '国家医疗服务项目名称': 'name',
                '项目内涵': 'treatment_item_content',
                '除外内容': 'treatment_excluded_content',
                '医保支付类别': 'payment_type',
                '计价单位': 'pricing_unit',
                '供应价格': 'price',
                '说明': 'treatment_item_description',
                '服务产出': 'service_output',
                '价格构成': 'price_composition',
                '限定支付': 'limited_payment',
                '先行自付': 'initial_payment_ratio',
                '三类医院价格苏南': 'remark'
            }
            for source_field, target_field in field_mappings.items():
                if source_field in df.columns:
                    df[target_field] = df[source_field]

            # 地理信息 - 固定值
            df['province_code'] = '32'
            df['province_name'] = '江苏省'
            df['city_code'] = ''
            df['city_name'] = ''
            df['level_type'] = '省级'
            df['central_code'] = ''
            df['payment_upper_limit'] = ''
            df['begin_date'] = None

            # 组装additional_info为JSON格式
            def build_additional_info(row):
                """
                将价格字段组装成JSON格式写入additional_info
                格式：{"level1_hospital_price_sunan": 100, "level1_hospital_price_suzhong": 95, ...}
                """
                price_mapping = {
                    '一类医院价格苏南': 'level1_hospital_price_sunan',
                    '一类医院价格苏中': 'level1_hospital_price_suzhong',
                    '一类医院价格苏北': 'level1_hospital_price_subei',
                    '二类医院价格苏南': 'level2_hospital_price_sunan',
                    '二类医院价格苏中': 'level2_hospital_price_suzhong',
                    '二类医院价格苏北': 'level2_hospital_price_subei',
                    '三类医院价格苏南': 'level3_hospital_price_sunan',
                    '三类医院价格苏中': 'level3_hospital_price_suzhong',
                    '三类医院价格苏北': 'level3_hospital_price_subei',
                }
                additional_info = {}
                for source_key, target_key in price_mapping.items():
                    val = row.get(source_key, None)
                    if pd.notna(val) and str(val).strip() not in ['-', '--', '—', '/', '\\', '']:
                        try:
                            # 尝试转换为数值
                            additional_info[target_key] = float(val)
                        except (ValueError, TypeError):
                            # 如果转换失败，保留原值
                            additional_info[target_key] = str(val).strip()

                return json.dumps(additional_info, ensure_ascii=False) if additional_info else '{}'

            # 确保所有价格字段存在
            price_fields = [
                '一类医院价格苏南', '一类医院价格苏中', '一类医院价格苏北',
                '二类医院价格苏南', '二类医院价格苏中', '二类医院价格苏北',
                '三类医院价格苏南', '三类医院价格苏中', '三类医院价格苏北'
            ]
            for field in price_fields:
                if field not in df.columns:
                    df[field] = None

            # 生成additional_info字段
            df['additional_info'] = df.apply(build_additional_info, axis=1)

            # 价格主字段 - 来自供应价格字段
            if '供应价格' in df.columns:
                df['price'] = pd.to_numeric(df['供应价格'], errors='coerce')
            else:
                df['price'] = None

            # remark字段已经通过字段映射创建，无需额外处理

            # 清理空值和无效值
            invalid_values = ['-', '--', '—', '/', '\\', '']
            for col in df.columns:
                if col != 'additional_info':  # additional_info已经处理过了
                    df[col] = df[col].replace(invalid_values, None)
                    df[col] = df[col].apply(lambda x: None if pd.isna(x) or str(x).strip() == '' else x)

            # 数值字段特殊处理
            if 'initial_payment_ratio' in df.columns:
                df['initial_payment_ratio'] = pd.to_numeric(df['initial_payment_ratio'], errors='coerce')

            # 数据过滤和去重 - 使用table_configs中的unique_fields配置
            # 从table_configs获取unique_fields配置，确保配置一致性
            from .table_configs import get_jsyb_table_config
            table_config = get_jsyb_table_config("江苏医疗服务项目")
            unique_fields = table_config.get("unique_fields", ['charge_item_code', 'charge_item_name', 'code', 'remark'])

            logger.info(f"使用table_configs中的unique_fields: {unique_fields}")

            # 使用通用的过滤和去重函数
            df = CommonFieldProcessing.filter_and_deduplicate_data(df, unique_fields)

            logger.info(f"江苏医疗服务项目数据清洗完成，输出记录数: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"清洗江苏医疗服务项目数据时出错: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return df

    def _clean_jsyb_drug_data(self, df):
        """
        清洗江苏医保药品数据的自定义函数
        主要处理：
        1. 字段映射：将源表中文字段映射到目标表英文字段
        2. 唯一键：国家药品代码 -> code
        3. 地理位置信息：添加江苏省省级标识
        4. 数据类型转换：价格、比例等数值字段
        5. 数据过滤：按唯一键过滤无效记录
        6. 去重处理：按唯一键去重
        """
        logger.info(f"开始清洗江苏医保药品数据，输入记录数: {len(df)}")
        try:
            # 先过滤国家药品代码不为空的数据
            if '国家药品代码' in df.columns:
                before = len(df)
                df = df[df['国家药品代码'].notna() & (df['国家药品代码'] != '') & (df['国家药品代码'] != '-')]
                logger.info(f"过滤国家药品代码为空或无效的数据: {before} → {len(df)}")

            # 字段映射 - 直接创建目标字段
            field_mappings = {
                '国家药品代码': 'code',
                '医保药品名称': 'product_name',
                '医保支付类别': 'categories_national',
                '医保剂型': 'dosage_form_national',
                '注册名称': 'generic_name_national',
                '实际规格': 'specifications',
                '最小包装数量': 'minimum_prescription_unit',
                '省集中采购上限价': 'procure_ceil_price',
                '批准文号': 'approval_number',
                '药品企业': 'production_company_name',
                '医保限定支付范围': 'payment_restricted_scope',
                '个人先行自付比例': 'initial_payment_ratio'
            }

            for source_field, target_field in field_mappings.items():
                if source_field in df.columns:
                    df[target_field] = df[source_field]

            # 地理信息 - 固定值（江苏省省级数据）
            df['province_code'] = '32'
            df['province_name'] = '江苏省'
            df['city_code'] = ''
            df['city_name'] = ''
            df['level_type'] = 'province'  # 使用英文，与target_where_clause保持一致
            df['central_code'] = ''

            # 药品分类相关字段
            df['category'] = '医保药品'
            df['category_name'] = '医保药品'

            # 数值字段处理
            # 价格字段
            if 'procure_ceil_price' in df.columns:
                df['procure_ceil_price'] = pd.to_numeric(df['procure_ceil_price'], errors='coerce')
            # 国家医保类别转换
            if 'categories_national' in df.columns:
                df['categories_national'] = df['categories_national'].str.rstrip('类')

            # 个人先行自付比例
            if 'initial_payment_ratio' in df.columns:
                # 处理百分比格式，如"10%"转换为0.1
                def convert_percentage(val):
                    if pd.isna(val):
                        return None
                    val_str = str(val).strip()
                    if val_str.endswith('%'):
                        try:
                            return float(val_str[:-1]) / 100
                        except ValueError:
                            return None
                    else:
                        try:
                            return float(val_str)
                        except ValueError:
                            return None

                df['initial_payment_ratio'] = df['initial_payment_ratio'].apply(convert_percentage)

            # 清理空值和无效值
            invalid_values = ['-', '--', '—', '/', '\\', '', 'nan', 'NaN', 'null', 'NULL']
            for col in df.columns:
                if col in ['procure_ceil_price', 'initial_payment_ratio']:
                    # 数值字段已经处理过了，跳过
                    continue
                df[col] = df[col].replace(invalid_values, None)
                df[col] = df[col].apply(lambda x: None if pd.isna(x) or str(x).strip() == '' else str(x).strip())

            # 数据过滤和去重 - 使用table_configs中的unique_fields配置
            # 从table_configs获取unique_fields配置，确保配置一致性
            from .table_configs import get_jsyb_table_config
            table_config = get_jsyb_table_config("江苏医保药品")
            unique_fields = table_config.get("unique_fields", ['code'])

            logger.info(f"使用table_configs中的unique_fields: {unique_fields}")

            # 使用通用的过滤和去重函数
            df = CommonFieldProcessing.filter_and_deduplicate_data(df, unique_fields)

            logger.info(f"江苏医保药品数据清洗完成，输出记录数: {len(df)}")
            return df

        except Exception as e:
            logger.error(f"清洗江苏医保药品数据时出错: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return df


# 创建江苏医保局数据清洗配置实例
jsyb_data_cleaning_config = JsybDataCleaningConfig()


# 提供便捷的函数接口
def get_jsyb_table_cleaning_rules(table_name: str):
    """获取江苏医保局表级别清洗规则"""
    return jsyb_data_cleaning_config.get_table_cleaning_rules().get(table_name, {})


def get_jsyb_field_cleaning_rules(table_name: str, field_name: str = None):
    """获取江苏医保局字段级别清洗规则"""
    table_rules = jsyb_data_cleaning_config.get_field_cleaning_rules().get(table_name, {})
    if field_name:
        return table_rules.get(field_name, {})
    return table_rules


def get_jsyb_custom_cleaning_functions():
    """获取江苏医保局自定义清洗函数"""
    return jsyb_data_cleaning_config.get_custom_cleaning_functions()


# 向后兼容的清洗函数
def clean_fuwu_hospital_data(df, province='jsyb'):
    """清洗医保定点机构数据（向后兼容）"""
    return jsyb_data_cleaning_config._clean_fuwu_hospital_data(df)


def clean_fuwu_pharmacy_data(df, province='jsyb'):
    """清洗医保定点药店数据（向后兼容）"""
    return jsyb_data_cleaning_config._clean_fuwu_pharmacy_data(df)
