# Generated by Django 3.2.12 on 2025-03-05 09:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0010_alter_claimmonthlypay_pay_month'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimAgeOverview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('avg_age', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='平均年龄')),
                ('max_age', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='最大年龄')),
                ('min_age', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='最小月龄')),
            ],
            options={
                'verbose_name': '理赔-赔付年龄概览',
                'verbose_name_plural': '理赔-赔付年龄概览',
                'db_table': 'claim_age_overview',
            },
        ),
        migrations.CreateModel(
            name='ClaimPayOverview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_avg_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='人均赔付金额')),
                ('pay_max_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='单笔最高赔付金额')),
                ('pay_max_person_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='单人最高赔付金额')),
            ],
            options={
                'verbose_name': '理赔-赔付概览',
                'verbose_name_plural': '理赔-赔付概览',
                'db_table': 'claim_pay_overview',
            },
        ),
        migrations.AddField(
            model_name='claimgenderpay',
            name='premium_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='保费'),
        ),
        migrations.AddField(
            model_name='claimtopcaseinfo',
            name='fund_paid_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='基金支付金额'),
        ),
        migrations.AddField(
            model_name='claimtopcaseinfo',
            name='reduction_rate',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='减负率'),
        ),
        migrations.AddField(
            model_name='claimtopcaseinfo',
            name='self_burden_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='个人负担金额'),
        ),
        migrations.AddField(
            model_name='claimtopcaseinfo',
            name='total_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='总费用'),
        ),
    ]
