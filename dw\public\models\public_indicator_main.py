from django.db import models
from common.models import BaseModel
from django.db.models import constraints

class PublicIndicatorMain(BaseModel):
    code = models.CharField(max_length=32,unique=True, blank=True, null=True, verbose_name='指标编码')
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='指标名称')
    area = models.CharField(max_length=64, blank=True, null=True, verbose_name='地区')
    currency = models.CharField(max_length=64, blank=True, null=True, verbose_name='货币')
    freq = models.CharField(max_length=32, blank=True, null=True, verbose_name='频度')
    unit = models.CharField(max_length=32, blank=True, null=True, verbose_name='单位')
    product_set_code = models.CharField(max_length=32, blank=True, null=True,db_index=True, verbose_name='产品集编码')
    statistical_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='统计类型')
    description = models.TextField(blank=True, null=True, verbose_name='描述说明')

    class Meta:
        db_table = 'public_indicator_main'
        verbose_name = '数据指标主表'
        verbose_name_plural = verbose_name
        constraints = [
            constraints.UniqueConstraint(
                fields=['name'],
                name='unique_public_indicator_main_combination'
            ),
        ]
