from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalChineseHerbalDrugBase(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='中草药品代码', **_db_comment_kwarg('中草药品代码'))
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='中草药品名称', **_db_comment_kwarg('中草药品名称'))
    type = models.CharField(max_length=64, blank=True, null=True, verbose_name='药品类别', **_db_comment_kwarg('药品类别'))
    category_source = models.CharField(max_length=512, blank=True, null=True, verbose_name='药品类别来源', **_db_comment_kwarg('药品类别来源'))
    category = models.CharField(max_length=64, blank=True, null=True, verbose_name='药品类型', **_db_comment_kwarg('药品类型'))
    dosage = models.CharField(max_length=255, blank=True, null=True, verbose_name='用量', **_db_comment_kwarg('用量'))
    medicinal_material_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='药材名称', **_db_comment_kwarg('药材名称'))
    source_standard_document_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='来源标准文档名称', **_db_comment_kwarg('来源标准文档名称'))
    province_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='省份编码', **_db_comment_kwarg('省份编码'))
    province_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='省份', **_db_comment_kwarg('省份'))
    efficacy_information = models.CharField(max_length=1024, blank=True, null=True, verbose_name='疗效说明', **_db_comment_kwarg('疗效说明'))
    national_healthcare_policy = models.CharField(max_length=255, blank=True, null=True, verbose_name='国家医保政策', **_db_comment_kwarg('国家医保政策'))
    provincial_healthcare_policy = models.CharField(max_length=255, blank=True, null=True, verbose_name='省级医保政策', **_db_comment_kwarg('省级医保政策'))
    medicinal_part = models.CharField(max_length=128, blank=True, null=True, verbose_name='入药部位', **_db_comment_kwarg('入药部位'))
    properties_meridian_tropism = models.CharField(max_length=255, blank=True, null=True, verbose_name='性味/归经', **_db_comment_kwarg('性味/归经'))
    preparation_method = models.CharField(max_length=255, blank=True, null=True, verbose_name='炮制方法', **_db_comment_kwarg('炮制方法'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_chinese_herbal_drug_base'
        verbose_name = '医保中草药信息基础表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name


