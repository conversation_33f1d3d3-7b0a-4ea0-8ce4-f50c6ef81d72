from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class InsureAgent(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='代理人名称')
    employee_count = models.IntegerField(blank=True, null=True, verbose_name='代理人数量')
    average_count = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,
                                         verbose_name='代理人人均单量')
    total_count = models.IntegerField(blank=True, null=True, verbose_name='销售总量')
    personal_count = models.IntegerField(blank=True, null=True, verbose_name='个单数量')
    group_count = models.IntegerField(blank=True, null=True, verbose_name='团单数量')
    insure_ratio = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='销售占比')
    position = models.IntegerField(blank=True, null=True, verbose_name='销售排名')
    target = models.IntegerField(blank=True, null=True, verbose_name='销售目标数量')
    target_ratio = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,
                                       verbose_name='销售目标完成率')
    today_count = models.IntegerField(blank=True, null=True, verbose_name='当日销售量')
    yesterday_count = models.IntegerField(blank=True, null=True, verbose_name='昨日销售量')
    week_target = models.IntegerField(blank=True, null=True, verbose_name='周销售目标')
    week_count = models.IntegerField(blank=True, null=True, verbose_name='本周销售量')
    week_complete_ratio = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,
                                              verbose_name='序时完成率')
    week_target_ratio = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,
                                            verbose_name='周目标完成率')
    # 由于统计可能细化，例如分地区、产品、保司等，会导致表格过大，因此增加一个字段作为补充信息
    additional_info = models.CharField(max_length=128, blank=True, null=True, verbose_name='补充信息')

    class Meta:
        db_table = 'insure_agent'
        verbose_name = '健康险代理人参保信息'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'publish_time', 'name','additional_info'],
                name='unique_insure_agent_combination'
            ),
        ]
