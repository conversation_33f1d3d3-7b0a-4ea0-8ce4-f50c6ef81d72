# 数据字典模型使用说明

## 概述

本数据字典系统基于您提供的MySQL表结构设计，使用Django模型实现，提供了完整的数据库元数据管理功能。

## 模型结构

### 1. PublicDatabaseInfo (数据库信息表)
- **表名**: `public_database_info`
- **功能**: 存储各个数据库的基本信息
- **主要字段**:
  - `id`: 主键ID
  - `name`: 数据库名称（唯一）
  - `type`: 数据库类型（MySQL、PostgreSQL等）
  - `description`: 数据库描述信息
  - `data_source`: 数据来源说明
  - `update_frequency`: 更新频率

### 2. PublicTableInfo (表信息表)
- **表名**: `public_table_info`
- **功能**: 存储各个表的基本信息和元数据
- **主要字段**:
  - `id`: 主键ID
  - `database_id`: 所属数据库ID（用于关联）
  - `database_name`: 所属数据库名称（冗余字段）
  - `name`: 表名
  - `comment`: 表注释
  - `data_source`: 数据来源
  - `type`: 表类型（BASE_TABLE、VIEW等）
  - `is_deprecated`: 是否废弃
  - `unique_key_fields`: 唯一索引字段组合
  - `engine`: 存储引擎
  - `charset`: 字符集
  - `collation`: 排序规则

### 3. PublicColumnInfo (字段信息表)
- **表名**: `public_column_info`
- **功能**: 存储各个表的字段详细信息，包含约束信息
- **主要字段**:
  - `id`: 主键ID
  - `table_id`: 所属表ID（用于关联）
  - `table_name`: 所属表名称（备用关联字段）
  - `name`: 字段名
  - `comment`: 字段注释
  - `data_type`: 基础数据类型
  - `type`: 完整字段类型
  - `max_length`: 最大长度
  - `numeric_precision`: 数值精度
  - `numeric_scale`: 数值小数位数
  - `is_nullable`: 是否允许为空
  - `default`: 默认值
  - `is_auto_increment`: 是否自增
  - `is_primary_key`: 是否主键
  - `is_unique`: 是否唯一约束
  - `is_indexed`: 是否有索引
  - `ordinal_position`: 字段位置序号

### 4. PublicIndexInfo (索引信息表)
- **表名**: `public_index_info`
- **功能**: 存储各个表的索引信息，每行记录一个完整索引
- **主要字段**:
  - `id`: 主键ID
  - `table_id`: 所属表ID（用于关联）
  - `table_name`: 所属表名称（备用关联字段）
  - `name`: 索引名称
  - `type`: 索引类型（BTREE、HASH等）
  - `is_unique`: 是否唯一索引
  - `is_primary`: 是否主键索引
  - `column_names`: 索引包含的字段名（逗号分隔）
  - `column_orders`: 字段排序方式（逗号分隔）
  - `comment`: 索引注释

## 关联关系设计

本数据字典系统采用了**无外键约束**的设计，但保留了关联字段，提供了两种关联方式：

### 1. 通过ID字段关联（推荐）
- `PublicTableInfo.database_id` → `PublicDatabaseInfo.id`
- `PublicColumnInfo.table_id` → `PublicTableInfo.id`
- `PublicIndexInfo.table_id` → `PublicTableInfo.id`

### 2. 通过名称字段关联（备用）
- `PublicTableInfo.database_name` → `PublicDatabaseInfo.name`
- `PublicColumnInfo.table_name` → `PublicTableInfo.name`
- `PublicIndexInfo.table_name` → `PublicTableInfo.name`

### 优势
- **灵活性**: 无外键约束，不会因为关联数据不存在而导致插入失败
- **性能**: 可以根据需要选择最优的查询方式
- **兼容性**: 支持跨数据库的数据字典管理
- **冗余保护**: 即使关联表被删除，仍可通过名称字段查询

## 使用方法

### 1. 数据库迁移

首先需要创建数据库表：

```bash
# 生成迁移文件
python manage.py makemigrations public

# 执行迁移
python manage.py migrate
```

### 2. 同步元数据

使用管理命令从数据库中同步元数据信息：

```bash
# 同步默认数据库的所有表
python manage.py sync_data_dictionary

# 同步指定数据库
python manage.py sync_data_dictionary --database=your_database

# 同步指定表
python manage.py sync_data_dictionary --tables table1 table2

# 预览模式（不实际修改数据）
python manage.py sync_data_dictionary --dry-run

# 强制更新已存在的记录
python manage.py sync_data_dictionary --force
```

### 3. 导出数据字典

使用管理命令导出数据字典为各种格式：

```bash
# 导出为Excel格式（默认）
python manage.py export_data_dictionary

# 导出为Markdown格式
python manage.py export_data_dictionary --format=markdown

# 导出为CSV格式
python manage.py export_data_dictionary --format=csv

# 导出为JSON格式
python manage.py export_data_dictionary --format=json

# 导出指定数据库
python manage.py export_data_dictionary --database=your_database

# 导出指定表
python manage.py export_data_dictionary --tables table1 table2

# 包含已废弃的表
python manage.py export_data_dictionary --include-deprecated

# 指定输出文件
python manage.py export_data_dictionary --output=/path/to/output.xlsx
```

### 4. Django Admin管理

在Django Admin中可以直接管理数据字典信息：

- 访问 `/admin/public/publicdatabaseinfo/` 管理数据库信息
- 访问 `/admin/public/publictableinfo/` 管理表信息
- 访问 `/admin/public/publiccolumninfo/` 管理字段信息
- 访问 `/admin/public/publicindexinfo/` 管理索引信息

### 5. 编程接口

在Python代码中使用模型：

```python
from public.models import PublicDatabaseInfo, PublicTableInfo, PublicColumnInfo, PublicIndexInfo

# 方式1: 通过ID关联查询（推荐，性能更好）
database = PublicDatabaseInfo.objects.get(name='your_database')
tables = PublicTableInfo.objects.filter(database_id=database.id, is_deprecated=False)

for table in tables:
    # 通过table_id查询字段
    columns = PublicColumnInfo.objects.filter(table_id=table.id).order_by('ordinal_position')
    for column in columns:
        print(f"{table.name}.{column.name}: {column.type}")

    # 通过table_id查询索引
    indexes = PublicIndexInfo.objects.filter(table_id=table.id)
    for index in indexes:
        print(f"{table.name}.{index.name}: {index.column_names}")

# 方式2: 通过名称关联查询（备用方式）
database_name = 'your_database'
tables = PublicTableInfo.objects.filter(database_name=database_name, is_deprecated=False)

for table in tables:
    # 通过名称查询字段
    columns = PublicColumnInfo.objects.filter(
        table_name=table.name
    ).order_by('ordinal_position')

    # 通过名称查询索引
    indexes = PublicIndexInfo.objects.filter(
        table_name=table.name
    )

# 方式3: 混合查询（灵活性最高）
# 优先使用ID查询，如果ID为空则使用名称查询
table = PublicTableInfo.objects.first()
if table.database_id:
    # 使用ID查询
    columns = PublicColumnInfo.objects.filter(table_id=table.id)
else:
    # 使用名称查询
    columns = PublicColumnInfo.objects.filter(
        table_name=table.name
    )

# 高效的统计查询
from django.db.models import Count

# 统计每个数据库的表数量
database_stats = PublicDatabaseInfo.objects.annotate(
    table_count_by_id=Count('publictableinfo', filter=Q(publictableinfo__database_id=F('id'))),
    table_count_by_name=Count('publictableinfo', filter=Q(publictableinfo__database_name=F('name')))
)

# 查找特定表的完整信息
def get_table_full_info(database_name, table_name):
    """获取表的完整信息，包括字段和索引"""
    try:
        # 获取表信息
        table = PublicTableInfo.objects.get(
            database_name=database_name,
            name=table_name
        )

        # 获取字段信息（优先使用table_id）
        if table.id:
            columns = PublicColumnInfo.objects.filter(table_id=table.id)
            indexes = PublicIndexInfo.objects.filter(table_id=table.id)
        else:
            columns = PublicColumnInfo.objects.filter(
                table_name=table_name
            )
            indexes = PublicIndexInfo.objects.filter(
                table_name=table_name
            )

        return {
            'table': table,
            'columns': columns.order_by('ordinal_position'),
            'indexes': indexes.order_by('name'),
            'primary_keys': columns.filter(is_primary_key=True),
            'unique_columns': columns.filter(is_unique=True),
        }
    except PublicTableInfo.DoesNotExist:
        return None
```

## 特性说明

### 1. 数据库兼容性
- 支持Django 4.2+的数据库注释功能
- 自动适配不同Django版本
- 兼容MySQL、PostgreSQL等主流数据库

### 2. 索引优化
- 为常用查询字段添加了数据库索引
- 支持复合索引和唯一约束
- 优化了外键关联查询性能

### 3. 数据完整性
- 使用外键约束保证数据一致性
- 支持级联删除操作
- 提供数据验证和约束检查

### 4. 扩展性
- 模型设计支持未来功能扩展
- 预留了描述和注释字段
- 支持自定义字段和属性

## 注意事项

1. **权限管理**: 确保数据库用户有足够权限访问 `information_schema` 表
2. **性能考虑**: 大型数据库同步时可能需要较长时间，建议分批处理
3. **数据一致性**: 定期同步以保持数据字典与实际数据库结构一致
4. **备份策略**: 重要的数据字典信息建议定期备份

## 依赖库

导出功能需要安装额外的Python库：

```bash
# Excel导出功能
pip install pandas openpyxl

# CSV导出功能
pip install pandas
```

## 故障排除

### 常见问题

1. **同步失败**: 检查数据库连接和权限设置
2. **导出错误**: 确保已安装必要的依赖库
3. **性能问题**: 考虑添加数据库索引或分批处理

### 日志查看

系统会记录详细的操作日志，可以通过Django的日志系统查看：

```python
import logging
logger = logging.getLogger(__name__)
```

## 更新历史

- **v1.0**: 初始版本，基于MySQL表结构设计
- 支持基本的CRUD操作和数据同步
- 提供多种格式的数据字典导出功能
