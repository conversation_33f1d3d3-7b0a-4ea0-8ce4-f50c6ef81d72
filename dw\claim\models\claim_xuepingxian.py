from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}

class ClaimXuePingXian(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    township = models.CharField(max_length=32, blank=True, null=True, verbose_name='乡镇', **_db_comment_kwarg('乡镇'))
    school_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='学校名称', **_db_comment_kwarg('学校名称'))
    school_type = models.CharField(max_length=128, blank=True, null=True, verbose_name='学校类型',**_db_comment_kwarg('学校类型'))
    school_nature = models.Char<PERSON><PERSON>(max_length=128, blank=True, null=True, verbose_name='学校性质',**_db_comment_kwarg('学校性质'))
    insured_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='被保险人姓名', **_db_comment_kwarg('被保险人姓名'))
    insured_credential_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='被保险人身份证号', **_db_comment_kwarg('被保险人身份证号'))
    age = models.IntegerField(blank=True, null=True, verbose_name='年龄', **_db_comment_kwarg('年龄'))
    parent_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='家长姓名', **_db_comment_kwarg('家长姓名'))
    parent_mobile = models.CharField(max_length=128, blank=True, null=True, verbose_name='家长手机号', **_db_comment_kwarg('家长手机号'))
    policy_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='保单号', **_db_comment_kwarg('保单号'))
    claim_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='报案号', **_db_comment_kwarg('报案号'))
    case_stage = models.CharField(max_length=128, blank=True, null=True, verbose_name='案件环节', **_db_comment_kwarg('案件环节'))
    overall_reimbursement_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='统筹报销金额', **_db_comment_kwarg('统筹报销金额'))
    outside_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='非医保', **_db_comment_kwarg('非医保'))
    amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='发票总金额', **_db_comment_kwarg('发票总金额'))
    actual_paid_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='结案金额', **_db_comment_kwarg('结案金额'))
    accident_reason = models.CharField(max_length=128, blank=True, null=True, verbose_name='出险原因', **_db_comment_kwarg('出险原因'))
    accident_type = models.CharField(max_length=128, blank=True, null=True, verbose_name='出现类型', **_db_comment_kwarg('出现类型'))
    accident_category = models.CharField(max_length=128, blank=True, null=True, verbose_name='出险类别', **_db_comment_kwarg('出险类别'))
    issue_date = models.DateField(blank=True, null=True, verbose_name='报案日期', **_db_comment_kwarg('报案日期'))
    accident_date = models.DateField(blank=True, null=True, verbose_name='出险日期', **_db_comment_kwarg('出险日期'))
    remark = models.CharField(max_length=512, blank=True, null=True, verbose_name='备注', **_db_comment_kwarg('备注'))

    class Meta:
        db_table = 'claim_xuepingxian'
        verbose_name = '理赔-学平险理赔明细'
        verbose_name_plural = verbose_name

# 动态为 Meta 添加 db_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    ClaimXuePingXian.Meta.db_comment = '理赔-学平险理赔明细'

