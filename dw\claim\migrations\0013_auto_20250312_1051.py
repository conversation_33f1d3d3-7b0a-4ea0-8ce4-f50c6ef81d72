# Generated by Django 3.2.12 on 2025-03-12 10:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0012_claimageoverview_max_age_gender'),
    ]

    operations = [
        migrations.AddField(
            model_name='claimageoverview',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimageoverview',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimagerangepay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimagerangepay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimamountrangepay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimamountrangepay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimareapay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimareapay',
            name='district',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='区县'),
        ),
        migrations.AddField(
            model_name='claimareapay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimgenderpay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimgenderpay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimgrouppay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimgrouppay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimliabilitypay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimliabilitypay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimmonthlypay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimmonthlypay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimpayoverview',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimpayoverview',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimpaytype',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimpaytype',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimpaytype',
            name='pay_person_number',
            field=models.IntegerField(blank=True, null=True, verbose_name='赔付人数'),
        ),
        migrations.AddField(
            model_name='claimpreexistingcondition',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimpreexistingcondition',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimproductpay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimproductpay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
        migrations.AddField(
            model_name='claimsellerpay',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimsellerpay',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
    ]
