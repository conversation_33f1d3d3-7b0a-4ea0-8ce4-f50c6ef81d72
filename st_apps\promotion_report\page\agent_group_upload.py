import datetime
from pprint import pprint

import numpy as np
import pandas as pd
from sqlalchemy import text
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from plotly.subplots import make_subplots
from utils.st import query_sql, text_write,submit_and_update_table
from streamlit_autorefresh import st_autorefresh
from utils.utils import number_to_chinese
from pyecharts import options as opts
from pyecharts.charts import Sankey
from streamlit_echarts import st_pyecharts
import warnings
import plotly.figure_factory as ff
import matplotlib.pyplot as plt

import sys
import venn

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')



def get_agent_group_upload_data(product_set_code, conn=CONNECTOR_DW,sql=query_sql('SQL_GROUP_REPORT')):
    """
    获取团单上报数据
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_department_info = conn.query(sql.format(product_set_code=product_set_code), show_spinner='查询中...', ttl=0)
    df_department_info['publish_time'] = df_department_info['publish_time'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
    df_department_info['end_time'] = df_department_info['end_time'].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
    df_department_info['id'] = df_department_info['id'].astype(str)
    df_department_info['value'] = pd.to_numeric(df_department_info['value'], errors='coerce')
    return df_department_info


def submit_and_insert_table(edited_df, table_name, data_columns, db_columns,key='submit_and_insert_table'):
    """
    新增数据到数据库。
    :param edited_df: 编辑后的 DataFrame。
    :param table_name: 数据库中的表名。
    :param data_columns: 编辑后的 DataFrame 中用于插入的列名列表。
    :param db_columns: 数据库中对应的列名列表。
    :param key: 按钮的key值，用于标识不同的按钮。
    """
    CONNECTOR_DW = st.connection('dw', type='sql')

    if st.button('复制最新数据', key=key):
        if edited_df is not None and not edited_df.empty:  # 检查是否有数据被提交
            # 准备数据
            data_to_insert = edited_df[data_columns].to_dict(orient='records')
            print(data_to_insert)
            # 构建插入语句模板
            columns_clause = ', '.join(db_columns)
            values_clause = ', '.join(["'%s'"] * len(db_columns))
            insert_query_template = f"INSERT INTO {table_name} ({columns_clause}) VALUES ({values_clause});"
            print(insert_query_template)
            # 执行插入
            with CONNECTOR_DW.session as s:
                for params in data_to_insert:
                    compiled_stmt = insert_query_template % tuple(params.values())
                    # 替换参数列表中的 None 值为 'NULL'，以符合 SQL 语法
                    compiled_stmt_str = str(compiled_stmt).replace('None', 'NULL')
                    print(compiled_stmt_str)
                    s.execute(text(compiled_stmt_str))
                    s.commit()

            st.success('数据已成功插入。')
            st_autorefresh()
        else:
            st.warning('没有检测到新增的数据。')


def main():
    st.subheader('团单数据上报')
    product_set_code_list = ['disease_hospital_allowance','ninghuibaoV5','guihuibaoV3','rizhao_nxbV4','binzhou_yhbV4','dezhou_hmbV3']
    product_set_code =st.selectbox('选择产品', product_set_code_list, key='product_name')

    df_department_info = get_agent_group_upload_data(product_set_code)
    edited_data = st.data_editor(df_department_info,use_container_width=True,hide_index=True,disabled=["id", "name", "code"])
    cols = st.columns(6)
    with cols[0]:
        submit_and_update_table(edited_data, "public_indicator_data", ['publish_time', 'end_time', 'value','id'],
                                ['publish_time', 'end_time', 'value'], ['id'])
    # 获取df_department_info根据code分组，publish_time倒序最大的记录
    df_department_info['publish_time'] = pd.to_datetime(df_department_info['publish_time'])
    max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
    max_publish_time_df = df_department_info.loc[max_publish_time_indices]
    max_publish_time_df['publish_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    max_publish_time_df['end_time'] = datetime.datetime.now().strftime('%Y-%m-%d')
    with cols[1]:
        submit_and_insert_table(max_publish_time_df, "public_indicator_data", ['code','publish_time', 'end_time', 'value'],
                                ['code','publish_time', 'end_time', 'value'])

    # 选项为product_name+medicare_type+person_num
    options_list = []
    for i in range(len(df_department_info)):
        options_list.append(
            str(df_department_info['id'][i]) + '--' + str(df_department_info['publish_time'][i]) + '--' + str(df_department_info['name'][i])+ '--' + str(df_department_info['value'][i]))
    # 删除记录
    selected_records = st.multiselect('选择要删除的记录', options=options_list,
                                      key='delete_record_2')
    if st.button('删除记录'):
        pass
        with CONNECTOR_DW.session as s:
            for i in selected_records:
                slected_id = i.split('--')[0]
                delete_query = "delete from public_indicator_data where id = '%s';" % (slected_id)
                # 将更新后的数据写入
                s.execute(text(delete_query))
            s.commit()
        st_autorefresh()  # 刷新页面，显示最新数据
        st.success('数据删除成功')





if __name__ == '__main__':
    main()