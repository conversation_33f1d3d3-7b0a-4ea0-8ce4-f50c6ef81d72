from django import forms
from public.models import PublicStatistics, SystemDictValue
from django.forms.models import ModelChoiceField


class StatisticsChoiceField(ModelChoiceField):
    # 重写label_from_instance方法，使得下拉框显示中文
    def label_from_instance(self, obj):
        return obj.key


class StatisticsForm(forms.ModelForm):
    class Meta:
        model = PublicStatistics
        fields = '__all__'

    # 字段与需要替换的字段一致
    statistical_type = StatisticsChoiceField(
        queryset=SystemDictValue.objects.filter(dict_id=1),
        to_field_name='label',
        label='统计分类',
    )

    type = StatisticsChoiceField(
        queryset=SystemDictValue.objects.filter(dict_id=2),
        to_field_name='label',
        label='分类',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
