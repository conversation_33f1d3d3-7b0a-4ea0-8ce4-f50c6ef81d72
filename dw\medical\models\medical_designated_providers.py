from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalDesignatedProviders(BaseModel):
    province_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构省份编码', **_db_comment_kwarg('机构省份编码'))
    province_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='机构省份', **_db_comment_kwarg('机构省份'))
    city_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构城市编码', **_db_comment_kwarg('机构城市编码'))
    city_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='机构城市', **_db_comment_kwarg('机构城市'))
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='机构编码', **_db_comment_kwarg('机构编码'))
    name = models.CharField(max_length=255, blank=True, null=True, verbose_name='机构名称', **_db_comment_kwarg('机构名称'))
    type = models.CharField(max_length=255, blank=True, null=True, verbose_name='机构分类', **_db_comment_kwarg('机构分类'))
    type_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='机构分类名称', **_db_comment_kwarg('机构分类名称'))
    address = models.CharField(max_length=512, blank=True, null=True, verbose_name='地址', **_db_comment_kwarg('地址'))
    lat = models.CharField(max_length=512, blank=True, null=True, verbose_name='经度', **_db_comment_kwarg('经度'))
    lnt = models.CharField(max_length=512, blank=True, null=True, verbose_name='纬度', **_db_comment_kwarg('纬度'))
    category = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构类型', **_db_comment_kwarg('机构类型'))
    category_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='机构类型名称', **_db_comment_kwarg('机构类型名称'))
    level = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构等级', **_db_comment_kwarg('机构等级'))
    level_name = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构等级名称', **_db_comment_kwarg('机构等级名称'))
    uscc = models.CharField(max_length=255, blank=True, null=True, verbose_name='统一社会信用代码', **_db_comment_kwarg('统一社会信用代码'))
    mobile = models.CharField(max_length=255, blank=True, null=True, verbose_name='电话号码', **_db_comment_kwarg('电话号码'))
    business_scope = models.TextField(blank=True, null=True, verbose_name='经营范围', **_db_comment_kwarg('经营范围'))
    cross_regional_medical_status = models.IntegerField(blank=True, null=True, verbose_name='异地就医服务状态', **_db_comment_kwarg('异地就医服务状态'))
    inpatient_status = models.IntegerField(blank=True, null=True, verbose_name='住院开通情况', **_db_comment_kwarg('住院开通情况'))
    outpatient_status = models.IntegerField(blank=True, null=True, verbose_name='门诊开通情况', **_db_comment_kwarg('门诊开通情况'))
    outpatient_chronic_status = models.IntegerField(blank=True, null=True, verbose_name='门诊慢特病开通情况', **_db_comment_kwarg('门诊慢特病开通情况'))
    outpatient_chronic_disease = models.CharField(max_length=1024, blank=True, null=True, verbose_name='门诊慢特病支持病种编码', **_db_comment_kwarg('门诊慢特病支持病种编码'))
    outpatient_chronic_disease_name = models.CharField(max_length=1024, blank=True, null=True, verbose_name='门诊慢特病支持病种', **_db_comment_kwarg('门诊慢特病支持病种'))
    electronic_prescription_status = models.IntegerField(blank=True, null=True, verbose_name='电子处方开通状态', **_db_comment_kwarg('电子处方开通状态'))
    electronic_certificate_status = models.IntegerField(blank=True, null=True, verbose_name='电子凭证开通状态', **_db_comment_kwarg('电子凭证开通状态'))
    mobile_payment_status = models.IntegerField(blank=True, null=True, verbose_name='移动支付开通状态', **_db_comment_kwarg('移动支付开通状态'))
    insurance_wallet_status = models.IntegerField(blank=True, null=True, verbose_name='医保钱包开通状态', **_db_comment_kwarg('医保钱包开通状态'))
    electronic_billing_status = models.IntegerField(blank=True, null=True, verbose_name='电子票据开通状态', **_db_comment_kwarg('电子票据开通状态'))

    class Meta:
        db_table = 'medical_designated_providers'
        verbose_name = '医保定点医疗服务机构信息表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

