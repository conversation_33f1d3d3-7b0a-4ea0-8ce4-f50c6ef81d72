# Generated by Django 3.2.12 on 2025-03-04 11:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0005_claimtopcaseinfo'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimPayAmountRange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('amount_range', models.CharField(blank=True, max_length=32, null=True, verbose_name='赔付金额范围')),
                ('type', models.CharField(blank=True, max_length=32, null=True, verbose_name='赔付类型')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付数量')),
                ('pay_number_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付数量占比')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
            ],
            options={
                'verbose_name': '理赔-赔付金额分布',
                'verbose_name_plural': '理赔-赔付金额分布',
                'db_table': 'claim_pay_amount_range',
            },
        ),
    ]
