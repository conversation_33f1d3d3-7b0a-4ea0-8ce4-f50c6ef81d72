import logging.config
import os
import traceback
from datetime import datetime
import streamlit as st

from utils.auth import Auth, maxkey_get_person_info, iframe_get_person_info,auth_from_session,get_agent_auth

st.set_page_config(
    page_title="销售数据报表",
    layout="wide",
    initial_sidebar_state="expanded",
    page_icon="📊"
)

LOG_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'log')
if not os.path.exists(LOG_PATH):
    os.makedirs(LOG_PATH)
LOG_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(threadName)s] [%(levelname)s] (%(pathname)s:%(lineno)d %(funcName)s) %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
        },
        'app_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'filename': os.path.join(LOG_PATH, 'app.log'),
            'maxBytes': 1024 * 1024 * 100,  # 100MB
            'backupCount': 5,
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'standard',
            'filename': os.path.join(LOG_PATH, 'error.log'),
            'maxBytes': 1024 * 1024 * 100,  # 100MB
            'backupCount': 5,
        },
    },
    'loggers': {
        '': {  # Root logger
            'handlers': ['console', 'app_file', 'error_file'],
            'level': 'INFO',
            'propagate': True,
        },
    }
}

logging.config.dictConfig(LOG_CONFIG)
logger = logging.getLogger(__name__)


def check_auth():
    auth = Auth()
    # maxkey 认证
    code = st.query_params.get('code')
    if code is not None:
        person_info = maxkey_get_person_info(code)
        if person_info:
            auth.set_auth_to_cookies(person_info, is_iframe=False)
            return person_info
    # 从iframe中获取鉴权信息
    iframe_code = st.query_params.get('iframecode')
    if iframe_code is not None:
        # 需要前端回传ProductSetCode,displayName、UserId、IframeCode,is_iframe鉴权码等信息
        person_info = iframe_get_person_info(iframe_code)
        auth.set_auth_to_cookies(person_info, is_iframe=True)
        return person_info
    # 尝试从cookie中获取鉴权信息
    person_info = auth.auth_from_cookies()
    if person_info:
        return person_info

    return None


def run(person_info=None):
    # 清除url中的参数
    st.query_params.clear()
    def home_page():
        st.subheader(f"健康险销售数据报表系统，欢迎使用!")
        st.write('当前用户:', person_info.get('displayName') if person_info else '未知用户')
        st.write('现在时间:', datetime.now())
    from page.agent_report import main as agent_report
    from page.marketing_plan import main as marketing_activities
    from page.personal_sale import main as personal_sale
    from page.renewal_info import main as renewal_info
    from page.beacon_analysis import main as beacon_analysis
    from page.insure_info import main as insure_info
    from page.material_forward import main as material_forward
    from page.previous_compare import main as previous_compare
    from page.pvuv_njapp import main as pvuv_njapp
    from page.agent_group_upload import main as agent_group_upload
    from page.gz_ghb_yibao_weekly import main as gz_ghb_yibao_weekly
    from page.picc_agent_report import main as picc_agent_report
    from page.seller_renewal_summary import main as seller_renewal_summary
    from page.agent_renewal_summary import main as agent_renewal_summary
    from page.channel_sale_info import main as channel_sale_info
    from page.phone_number_matching import main as phone_number_matching
    from page.agent_area_analysis import main as agent_area_analysis
    from page.agent_group_analysis import main as agent_group_analysis
    from page.special_employee_report import main as special_employee_report
    from page.order_query import main as order_query
    # 查看信息中是否有iframe标识，如果有则使用iframe方式，否则使用默认方式
    if person_info.get('is_iframe') ==1:
        df_user_record =get_agent_auth(person_info.get('UserId'),person_info.get('ProductSetCode'))
        if df_user_record['show_manage_data'].values[0] == 1 and person_info.get('ProductSetCode')[:10] == 'ninghuibao':
            if person_info.get('UserId') in [174,322]:
                # 康鲁江特有报表
                pg = st.navigation([st.Page(agent_report, title='代理人统计', icon='🗒', url_path='/agent-report'),
                                    st.Page(picc_agent_report, title='人保单量统计', icon='🗒', url_path='/picc-agent-report'),
                                    st.Page(seller_renewal_summary, title='机构续保汇总', icon='🗒',url_path='/seller-renewal-summary'),
                                    st.Page(agent_renewal_summary, title='代理人续保情况', icon='🗒',url_path='/agent-renewal-summary'),
                                    st.Page(special_employee_report, title='指定代理人销量情况', icon='🗒',url_path='/special-employee-report')])
            else:
                pg = st.navigation([st.Page(agent_report, title='代理人统计', icon='🗒', url_path='/agent-report'),
                                    st.Page(picc_agent_report, title='人保单量统计', icon='🗒',url_path='/picc-agent-report'),
                                    st.Page(seller_renewal_summary, title='机构续保汇总', icon='🗒',url_path='/seller-renewal-summary'),
                                    st.Page(agent_renewal_summary, title='代理人续保情况', icon='🗒',url_path='/agent-renewal-summary')])
        else:
            if person_info.get('ProductSetCode')[:10] == 'ninghuibao':
                pg = st.navigation([st.Page(agent_report, title='代理人统计', icon='🗒',url_path ='/agent-report'),
                                    st.Page(seller_renewal_summary, title='机构续保汇总', icon='🗒',url_path='/seller-renewal-summary'),
                                    st.Page(agent_renewal_summary, title='代理人续保情况', icon='🗒',url_path='/agent-renewal-summary')])
            else:
                if person_info.get('ProductSetCode')[:10] in ['rizhao_nxb','binzhou_yh','dezhou_hmb']:
                    # 如果是主承保或者有全部权限的，可以看到所有的页面
                    if df_user_record['show_manage_data'].values[0] == 1:
                        if  person_info.get('UserId') in [385,322,455]: # 梁晓庆单独的报表
                            pg = st.navigation(
                                [st.Page(agent_report, title='代理人统计', icon='🗒', url_path='/agent-report'),
                                 st.Page(channel_sale_info, title='分渠道销量统计', icon='🗒',url_path='/channel-sale-info'),
                                 st.Page(agent_area_analysis, title='分保司分地区统计报表', icon='🗒',url_path='/agent-area-analysis'),
                                 st.Page(agent_group_analysis, title='团单信息报表', icon='🗒',url_path='/agent-group-analysis'),
                                 st.Page(phone_number_matching, title='手机号码匹配', icon='🗒',url_path='/phone-number-matching'),
                                 st.Page(order_query, title='保单查询', icon='🗒',url_path='/order-query')]
                                )
                        else:
                            pg = st.navigation([st.Page(agent_report, title='代理人统计', icon='🗒', url_path='/agent-report'),
                                                st.Page(channel_sale_info, title='分渠道销量统计', icon='🗒', url_path='/channel-sale-info'),
                                                st.Page(agent_area_analysis, title='分保司分地区统计报表', icon='🗒',url_path='/agent-area-analysis'),
                                                st.Page(agent_group_analysis, title='团单信息报表', icon='🗒',url_path='/agent-group-analysis'),
                                                st.Page(phone_number_matching, title='手机号码匹配', icon='🗒', url_path='/phone-number-matching')]
                                               )
                    else:
                        pg = st.navigation(
                            [st.Page(agent_report, title='代理人统计', icon='🗒', url_path='/agent-report'),
                             st.Page(channel_sale_info, title='分渠道销量统计', icon='🗒',url_path='/channel-sale-info')]
                            )
                else:
                    pg = st.navigation([st.Page(agent_report, title='代理人统计', icon='🗒',url_path ='/agent-report')])
    else:
        if person_info.get('userId') == '985530186017538048':
            #限制团单数据上传权限
            pg = st.navigation(
                [st.Page(home_page, title='首页', icon='🏠'),
                 st.Page(agent_report, title='代理人统计', icon='🗒',url_path ='/agent-report'),
                 st.Page(picc_agent_report, title='人保单量统计', icon='🗒',url_path='/picc-agent-report'),
                 st.Page(seller_renewal_summary, title='机构续保汇总', icon='🗒', url_path='/seller-renewal-summary'),
                 st.Page(agent_renewal_summary, title='代理人续保情况', icon='🗒', url_path='/agent-renewal-summary'),
                 st.Page(insure_info, title='总览', icon='🗒', url_path='/insure-info'),
                 st.Page(material_forward, title='保司素材分享', icon='🗒', url_path='/material-forward'),
                 st.Page(marketing_activities, title='营销', icon='🗒', url_path='/marketing-activities'),
                 st.Page(personal_sale, title='目标达成', icon='🗒', url_path='/personal-sale'),
                 st.Page(previous_compare, title='往期对比', icon='🗒', url_path='/previous-compare'),
                 st.Page(renewal_info, title='续保分析', icon='🗒', url_path='/renewal-info'),
                 st.Page(beacon_analysis, title='埋点分析', icon='🗒',url_path ='/beacon-analysis'),
                 st.Page(pvuv_njapp, title='我的南京APP分析', icon='🗒', url_path='/pvuv-njapp'),
                 st.Page(agent_group_upload, title='代理人团单数据上传', icon='🗒', url_path='/agent-group-upload'),
                 st.Page(gz_ghb_yibao_weekly, title='贵惠保周报', icon='🗒', url_path='/ghb-yibao-weekly'),
                 st.Page(channel_sale_info, title='分渠道销量统计', icon='🗒', url_path='/channel-sale-info'),
                 st.Page(phone_number_matching, title='手机号码匹配', icon='🗒', url_path='/phone-number-matching'),
                 st.Page(agent_group_analysis, title='团单信息报表', icon='🗒', url_path='/agent-group-analysis'),
                 st.Page(agent_area_analysis, title='分保司分地区统计报表', icon='🗒', url_path='/agent-area-analysis'),
                 st.Page(special_employee_report, title='指定代理人销量情况', icon='🗒',url_path='/special-employee-report'),
                 st.Page(order_query, title='保单查询', icon='🗒', url_path='/order-query')
                 ])
        else:
            pg = st.navigation(
                [st.Page(home_page, title='首页', icon='🏠'),
                 st.Page(agent_report, title='代理人统计', icon='🗒', url_path='/agent-report'),
                 st.Page(picc_agent_report, title='人保单量统计', icon='🗒', url_path='/picc-agent-report'),
                 st.Page(seller_renewal_summary, title='机构续保汇总', icon='🗒', url_path='/seller-renewal-summary'),
                 st.Page(agent_renewal_summary, title='代理人续保情况', icon='🗒', url_path='/agent-renewal-summary'),
                 st.Page(insure_info, title='总览', icon='🗒', url_path='/insure-info'),
                 st.Page(material_forward, title='保司素材分享', icon='🗒', url_path='/material-forward'),
                 st.Page(marketing_activities, title='营销', icon='🗒', url_path='/marketing-activities'),
                 st.Page(personal_sale, title='目标达成', icon='🗒', url_path='/personal-sale'),
                 st.Page(previous_compare, title='往期对比', icon='🗒', url_path='/previous-compare'),
                 st.Page(renewal_info, title='续保分析', icon='🗒', url_path='/renewal-info'),
                 st.Page(beacon_analysis, title='埋点分析', icon='🗒', url_path='/beacon-analysis'),
                 st.Page(pvuv_njapp, title='我的南京APP分析', icon='🗒', url_path='/pvuv-njapp'),
                 st.Page(gz_ghb_yibao_weekly, title='贵惠保周报', icon='🗒', url_path='/ghb-yibao-weekly'),
                 st.Page(channel_sale_info, title='分渠道销量统计', icon='🗒', url_path='/channel-sale-info'),
                 st.Page(phone_number_matching, title='手机号码匹配', icon='🗒', url_path='/phone-number-matching'),
                 st.Page(agent_group_analysis, title='团单信息报表', icon='🗒', url_path='/agent-group-analysis'),
                 st.Page(agent_area_analysis, title='分保司分地区统计报表', icon='🗒', url_path='/agent-area-analysis'),
                 st.Page(special_employee_report, title='指定代理人销量情况', icon='🗒',url_path='/special-employee-report'),
                 st.Page(order_query, title='保单查询', icon='🗒', url_path='/order-query')
                 ])
    pg.run()


def auth_failed():
    st.error(f'请重新登陆')


def main():
    # 判断环境变量 DISABLE_AUTH 是否存在，如果存在则不进行认证
    if 'DISABLE_AUTH' in os.environ:
        logger.info('DISABLE_AUTH is set, skip auth')
        run()

    else:
        try:
            person_info = check_auth()
            if person_info:
                run(person_info)
            else:
                auth_failed()
        except Exception as e:
            traceback.print_exc()
            st.error(f'认证失败: {e}')


if __name__ == "__main__":
    main()
