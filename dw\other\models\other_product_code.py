from django.db import models
from common.models import BaseModel


class OtherProductCode(BaseModel):
    name = models.CharField(max_length=200, blank=True, null=True, verbose_name='名称',unique=True)
    code = models.CharField(max_length=512, blank=True, null=True, verbose_name='产品编码集合')
    conn = models.CharField(max_length=64, blank=True, null=True, verbose_name='数据库连接名称')
    type = models.CharField(max_length=64, blank=True, null=True, verbose_name='理赔类型')

    class Meta:
        db_table = 'other_product_code'
        verbose_name = '理赔-产品取数范围'
        verbose_name_plural = verbose_name
