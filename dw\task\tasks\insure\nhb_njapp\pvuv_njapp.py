import datetime
import warnings

import idna
import pandas as pd
import pymysql
from django.db import transaction
from sqlalchemy import create_engine

from dw import settings
from insure.models import InsurePvUv
from transfrom.utils.utils import query_sql, custom_update_or_create, \
    send_feishu_message

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

DB = settings.DATABASES['jkx']
DB_DW = settings.DATABASES['default']
DB_UMAMI = settings.DATABASES['umami']

username = DB_DW['USER']
password = DB_DW['PASSWORD']
host = DB_DW['HOST']
port = DB_DW['PORT']
database = DB_DW['NAME']

connection_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}?charset=utf8"
conn_dw = create_engine(connection_string)


def get_connection_jkx(DB):
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn


CONNECTOR_JKX = get_connection_jkx(DB)
CONNECTOR_DW = get_connection_jkx(DB_DW)
CONNECTOR_UMAMI = get_connection_jkx(DB_UMAMI)


def get_njapp_pv(website_id, start_date, end_date, conn=CONNECTOR_UMAMI, sql=query_sql('SQL_NJAPP_PV')):
    """
    获取南京app浏览量
    :param website_id: 监控的网站id
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_njapp_pv = pd.read_sql(sql.format(website_id=website_id, start_date=start_date, end_date=end_date), conn)
    df_njapp_pv.rename(columns={'num': 'count'}, inplace=True)
    df_njapp_pv['date'] = df_njapp_pv['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_njapp_pv['source_code'] = df_njapp_pv['source_code'].apply(lambda x: x.split('&')[0])
    df_njapp_pv.groupby(['date', 'source_code'])['count'].sum().reset_index()
    df_njapp_pv['type'] = 'pv'
    df_njapp_pv.rename(columns={'date': 'end_date'}, inplace=True)
    df_njapp_pv.reset_index(drop=True, inplace=True)
    return df_njapp_pv


def get_njapp_uv(website_id, start_date, end_date, conn=CONNECTOR_UMAMI, sql=query_sql('SQL_NJAPP_UV')):
    """
    获取南京app访客量
    :param website_id: 监控的网站id
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_njapp_uv = pd.read_sql(sql.format(website_id=website_id, start_date=start_date, end_date=end_date), conn)
    df_njapp_uv.rename(columns={'person_count': 'count'}, inplace=True)
    df_njapp_uv['date'] = df_njapp_uv['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_njapp_uv['source_code'] = df_njapp_uv['source_code'].apply(lambda x: x.split('&')[0])
    df_njapp_uv.groupby(['date', 'source_code'])['count'].sum().reset_index()
    df_njapp_uv['type'] = 'uv'
    df_njapp_uv.rename(columns={'date': 'end_date'}, inplace=True)
    df_njapp_uv.reset_index(drop=True, inplace=True)
    return df_njapp_uv


def main():
    # 获取前一日的日期
    product_set_code = 'ninghuibaoV6'
    source_group = 'njapp'
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    yesterday = yesterday.strftime('%Y-%m-%d')
    start_datetime = yesterday + ' 00:00:00'
    end_datetime = yesterday + ' 23:59:59'
    website_id = 'c73e45a2-073b-402c-8e83-4caa1f8a60ca'
    # 获取南京app名称
    df_pv = get_njapp_pv(website_id, start_datetime, end_datetime)
    df_uv = get_njapp_uv(website_id, start_datetime, end_datetime)
    df_pv['product_set_code'] = product_set_code
    df_uv['product_set_code'] = product_set_code
    df_pv['source_group'] = source_group
    df_uv['source_group'] = source_group

    # 先删除库里面yesterday的数据
    sql_del = "delete from insure_pvuv where product_set_code='{}' and source_group='{}' and end_date='{}'".format(
        product_set_code, source_group, yesterday)
    cursor = CONNECTOR_DW.cursor()
    cursor.execute(sql_del)
    CONNECTOR_DW.commit()

    df = pd.concat([df_pv, df_uv], axis=0)
    df = df.reset_index(drop=True)
    print(df)
    # 插入数据，使用主键，避免重复插入
    try:
        for index, row in df.iterrows():
            with transaction.atomic():
                custom_update_or_create(InsurePvUv,
                                        product_set_code=product_set_code, end_date=yesterday,
                                        source_group=source_group,
                                        source_code=row['source_code'],
                                        type=row['type'],
                                        defaults={'count': row['count']})
    except Exception as e:
        send_feishu_message(f'南京APP PV UV 数据入库失败：{str(e)}')


if __name__ == '__main__':
    main()
