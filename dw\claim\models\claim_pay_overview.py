from django.db import models
from common.models import BaseModel


class ClaimPayOverview(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市') # 当is_provincial为1时，city为空
    pay_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人次')
    pay_person_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人数')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    pay_avg_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='人均赔付金额')
    pay_max_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='单笔最高赔付金额')
    pay_max_person_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='单人最高赔付金额')

    class Meta:
        db_table = 'claim_pay_overview'
        verbose_name = '理赔-赔付概览'
        verbose_name_plural = verbose_name

