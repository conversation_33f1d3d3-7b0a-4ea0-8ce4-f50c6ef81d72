from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalSuppliesRegisterMessage(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='耗材代码', **_db_comment_kwarg('耗材代码'))
    registration_number = models.CharField(max_length=255, blank=True, null=True, verbose_name='注册编码', **_db_comment_kwarg('注册编码'))
    single_product_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='单件产品名称', **_db_comment_kwarg('单件产品名称'))

    class Meta:
        db_table = 'medical_supplies_register_message'
        verbose_name = '医保耗材注册信息表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

