import json

import streamlit as st
from streamlit_echarts import st_echarts
import datetime
import logging
import warnings
from decimal import Decimal
import copy
from pprint import pprint
from plotly.subplots import make_subplots
import plotly.graph_objects as go
import streamlit_antd_components as sac
from pandasql import sqldf
from plotly import express as px
from pathlib import Path

import idna
import numpy as np
import pandas as pd
import pymysql
from pandasql import sqldf
from utils.st import query_sql, empty_line, text_write
from utils.utils import sum_or_combine, df_to_dict

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
# pd.set_option('display.max_rows', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        concat(
		LEFT ( ps.NAME, 5 ),
		'-',
	RIGHT ( ps.NAME,( LENGTH( ps.NAME )- 15 )/ 3 )) version,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        and ps.code not in ('ninghuibaoV1','ninghuibaoV2','ninghuibaoV3')
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)

    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code

def get_online_daily_data(product_set_code, sale_start_date):
    """
    获取线上的每日销售情况
    """
    sql = query_sql('SQL_DW_INDICATOR_DATA').format(
        product_set_code=product_set_code,
        statistical_type='当期值', unit='单',
        freq='日', start_datetime=sale_start_date,
        end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )
    df = CONNECTOR_DW.query(sql, show_spinner='查询中...', ttl=0)
    df.rename(columns={'end_time': 'date'}, inplace=True)
    # 优化：只筛选一次，并使用映射来分配销售渠道
    channels = {
        '我的南京': '-销量-线上-我的南京-当期值',
        '公众号': '-销量-线上-公众号-当期值',
        '支付宝': '-销量-线上-支付宝-当期值',
        '合计': '-销量-线上-当期值'
    }

    df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
    for channel_name, filter_str in channels.items():
        channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
        channel_df['name'] = channel_name  # 添加销售渠道名称列
        df_onlines = pd.concat([df_onlines, channel_df])  # 拼接数据
    df_onlines.sort_values(by=['name', 'date'], inplace=True)
    df_onlines.reset_index(drop=True, inplace=True)
    df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
    df_onlines['cumulative_count'] = df_onlines.groupby(['name'])['value'].cumsum()
    df_onlines['date_order'] = (df_onlines['date'] - df_onlines['date'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
    return df_onlines


def get_online_compare(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev):
    """
    获取线上对比数据，本期与上期
    :param product_set_code:
    :param product_set_code_prev:
    :return:
    """
    df = get_online_daily_data(product_set_code, sale_start_date)
    df_prev = get_online_daily_data(product_set_code_prev, sale_end_date_prev)
    # 合并数据
    df_merge = pd.merge(df, df_prev,how='left', on=['day', 'name'], suffixes=('_本期', '_上期'))
    df_merge.rename(columns={'value_本期': '本期', 'value_上期': '上期', 'name': '渠道名称', 'day_本期': '日期',
                             'date_本期': '时间', 'cumulative_count_本期': '本期累计',
                             'cumulative_count_上期': '上期累计','day': '日期'}, inplace=True)
    df_merge.fillna(0, inplace=True)
    df_merge = df_merge[['时间', '日期', '渠道名称', '本期', '上期', '本期累计', '上期累计']]
    return df_merge


def get_offline_daily_data(product_set_code, sale_start_date):
    """
    计算线下每日目标
    """
    # 提取SQL查询逻辑
    sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
        product_set_code=product_set_code,
        statistical_type='当期值',
        unit='单',
        freq='日',
        start_datetime=sale_start_date,
        end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    )

    # 使用上下文管理器确保连接正确关闭

    df = CONNECTOR_DW.query(sql_query, show_spinner='查询中...', ttl=0)
    df.rename(columns={'end_time': 'date'}, inplace=True)

    # 定义公司名称列表
    company_names = [
        ('-销量-线下-个单-中国人保-当期值', '中国人保'),
        ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
        ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
        ('-销量-线下-个单-中华联合-当期值', '中华联合'),
        ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
        ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
        ('-销量-线下-个单-太保产险-当期值', '太保产险'),
        ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
        ('-销量-线下-个单-中银保险-当期值', '中银保险'),
        ('-销量-线下-个单-当期值', '合计')
    ]

    # 循环处理每个公司数据
    dfs = []
    for pattern, name in company_names:
        filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
        filtered_df['name'] = name
        dfs.append(filtered_df)

    # 合并所有公司数据
    df_offlines = pd.concat(dfs)
    df_offlines.sort_values(by=['name', 'date'], inplace=True)
    df_offlines.reset_index(drop=True, inplace=True)
    df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
    df_offlines['cumulative_count'] = df_offlines.groupby(['name'])['value'].cumsum()
    df_offlines['date_order'] = (df_offlines['date'] - df_offlines['date'].min()).dt.days
    return df_offlines




def get_offline_compare(product_set_code, product_set_code_prev, sale_start_date, sale_end_date_prev):
    """
    获取线上对比数据，本期与上期
    :param product_set_code:
    :param product_set_code_prev:
    :return:
    """
    df = get_offline_daily_data(product_set_code, sale_start_date)
    df_prev = get_offline_daily_data(product_set_code_prev, sale_end_date_prev)
    # 合并数据
    df_merge = pd.merge(df, df_prev,how='left', on=['day', 'name'], suffixes=('_本期', '_上期'))
    df_merge.rename(columns={'value_本期': '本期', 'value_上期': '上期', 'name': '保司', 'day_本期': '日期',
                             'date_本期': '时间', 'cumulative_count_本期': '本期累计',
                             'cumulative_count_上期': '上期累计','day':'日期'}, inplace=True)
    df_merge.fillna(0, inplace=True)
    df_merge = df_merge[['时间', '日期', '保司', '本期', '上期', '本期累计', '上期累计']]
    return df_merge


@st.fragment
def download_excel(excel_name):
    """
    将DataFrame保存为Excel文件
    """
    # 将DataFrame转换为Excel文件
    st.download_button(
        label='下载数据',
        data=open(excel_name, 'rb').read(),
        file_name='往期对比数据.xlsx',
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def main():
    st.subheader('往期对比')
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=
    product_info[product_info['product_set_code'] != 'ninghuibaoV4']['product_set_name'],
                                                  placeholder="请选择产品")

    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[
        0]
    product_set_code_prev = \
        product_info[product_info['product_set_name'] == product_set_name]['prev_product_set_code'].values[
            0]
    sale_start_date = product_info[product_info['product_set_name'] == product_set_name]['sale_start_time'].values[
        0]
    sale_start_date = pd.to_datetime(sale_start_date).strftime('%Y-%m-%d')
    sale_end_date_prev = \
        product_info[product_info['product_set_code'] == product_set_code_prev]['sale_end_time'].values[
            0]
    sale_end_date_prev = pd.to_datetime(sale_end_date_prev).strftime('%Y-%m-%d')
    sale_start_date_prev = \
        product_info[product_info['product_set_code'] == product_set_code_prev]['sale_start_time'].values[
            0]
    sale_start_date_prev = pd.to_datetime(sale_start_date_prev).strftime('%Y-%m-%d')
    text_write('线上往期对比')
    df = get_online_compare(product_set_code, product_set_code_prev, sale_start_date, sale_start_date_prev)
    # print(df)
    keys = df.渠道名称.unique().tolist()
    # 如果keys中有合计，合计放到列表第一个字段
    if '合计' in keys:
        keys.remove('合计')
        keys.insert(0, '合计')

    for key in keys:
        # st.subheader(key)
        sub_data = df[df['渠道名称'] == key]
        sub_data.reset_index(drop=True, inplace=True)
        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # 添加柱状图系列到主y轴
        fig.add_trace(
            go.Bar(x=sub_data['时间'], y=sub_data['本期'], name='本期', hovertemplate='日期: %{x}<br>本期: %{y:.0f}'),
            secondary_y=False
        )
        fig.add_trace(
            go.Bar(x=sub_data['时间'], y=sub_data['上期'], name='上期', hovertemplate='日期: %{x}<br>上期: %{y:.0f}'),
            secondary_y=False
        )

        # 添加线图系列到次y轴
        fig.add_trace(
            go.Scatter(x=sub_data['时间'], y=sub_data['本期累计'], name='本期累计', mode='lines',
                       line=dict(color='#29B09D'), hovertemplate='日期: %{x}<br>本期累计: %{y:.0f}'),
            secondary_y=True
        )
        fig.add_trace(
            go.Scatter(x=sub_data['时间'], y=sub_data['上期累计'], name='上期累计', mode='lines',
                       hovertemplate='日期: %{x}<br>上期累计: %{y:.0f}'),
            secondary_y=True
        )

        fig.update_xaxes(tickformat="%m-%d")
        # 更新 y 轴格式为整数形式
        fig.update_yaxes(tickformat=".0f")
        # 移除副y轴的网格线
        fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
        # 设置y轴和y次轴的范围模式为从0开始
        fig.update_yaxes(rangemode="tozero", secondary_y=True)
        fig.update_yaxes(rangemode="tozero", secondary_y=False)
        fig.update_traces(
        )
        fig.update_layout(yaxis_title="当期值",
                          yaxis2_title="累计值",
                          plot_bgcolor='rgba(0, 0, 0, 0)',
                          paper_bgcolor='rgba(0, 0, 0, 0)',
                          legend=dict(
                              orientation="h",  # 水平方向
                              yanchor="bottom",  # y轴锚点为底部
                              y=1,  # y位置为1.02（顶部）
                              xanchor="center",  # x轴锚点为中心
                              x=0.5  # x位置为0.5（居中）
                          ),
                          title=dict(
                              text=key,
                              x=0.5,  # 居中对齐
                              xanchor="center",
                              y=0.95,
                              font=dict(size=14)
                          ),
                          yaxis=dict(
                              fixedrange=True,  # 禁止y轴的缩放和平移
                              title_font=dict(size=12)  # 设置 yaxis_title 字体大小
                          ),
                          yaxis2=dict(
                              overlaying='y',
                              side='right',
                              title_font=dict(size=12)  # 设置 yaxis2_title 字体大小
                          ),
                          dragmode=False,  # 禁用拖动模式
                          xaxis=dict(
                              fixedrange=True  # 禁止x轴的缩放和平移
                          ))
        # 隐藏工具栏
        config = {'displayModeBar': False}
        st.plotly_chart(fig, config=config)

    text_write('线下往期对比')
    df_offline = get_offline_compare(product_set_code, product_set_code_prev, sale_start_date, sale_start_date_prev)
    # print(df_offline)
    keys_offline = df_offline.保司.unique().tolist()
    # 如果keys中有合计，合计放到列表第一个字段
    if '合计' in keys_offline:
        keys_offline.remove('合计')
        keys_offline.insert(0, '合计')

    for key in keys_offline:
        # st.subheader(key)
        sub_data = df_offline[df_offline['保司'] == key]
        sub_data.reset_index(drop=True, inplace=True)
        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # 添加柱状图系列到主y轴
        fig.add_trace(
            go.Bar(x=sub_data['时间'], y=sub_data['本期'], name='本期', hovertemplate='日期: %{x}<br>本期: %{y:.0f}'),
            secondary_y=False
        )
        fig.add_trace(
            go.Bar(x=sub_data['时间'], y=sub_data['上期'], name='上期', hovertemplate='日期: %{x}<br>上期: %{y:.0f}'),
            secondary_y=False
        )

        # 添加线图系列到次y轴
        fig.add_trace(
            go.Scatter(x=sub_data['时间'], y=sub_data['本期累计'], name='本期累计', mode='lines',
                       line=dict(color='#29B09D'), hovertemplate='日期: %{x}<br>本期累计: %{y:.0f}'),
            secondary_y=True
        )
        fig.add_trace(
            go.Scatter(x=sub_data['时间'], y=sub_data['上期累计'], name='上期累计', mode='lines', hovertemplate='日期: %{x}<br>上期累计: %{y:.0f}'),
            secondary_y=True
        )

        fig.update_xaxes(tickformat="%m-%d")
        # 更新 y 轴格式为整数形式
        fig.update_yaxes(tickformat=".0f")
        # 移除副y轴的网格线
        fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
        # 设置y轴和y次轴的范围模式为从0开始
        fig.update_yaxes(rangemode="tozero", secondary_y=True)
        fig.update_yaxes(rangemode="tozero", secondary_y=False)
        fig.update_traces(
        )
        fig.update_layout(yaxis_title="当期值",
                          yaxis2_title="累计值",
                          plot_bgcolor='rgba(0, 0, 0, 0)',
                          paper_bgcolor='rgba(0, 0, 0, 0)',
                          legend=dict(
                              orientation="h",  # 水平方向
                              yanchor="bottom",  # y轴锚点为底部
                              y=1,  # y位置为1.02（顶部）
                              xanchor="center",  # x轴锚点为中心
                              x=0.5  # x位置为0.5（居中）
                          ),
                          title=dict(
                              text=key,
                              x=0.5,  # 居中对齐
                              xanchor="center",
                              y=0.95,
                              font=dict(size=14)
                          ),
                          yaxis=dict(
                              fixedrange=True,  # 禁止y轴的缩放和平移
                              title_font=dict(size=12)  # 设置 yaxis_title 字体大小
                          ),
                          yaxis2=dict(
                              overlaying='y',
                              side='right',
                              title_font=dict(size=12)  # 设置 yaxis2_title 字体大小
                          ),
                          dragmode=False,  # 禁用拖动模式
                          xaxis=dict(
                              fixedrange=True  # 禁止x轴的缩放和平移
                          ))
        # 隐藏工具栏
        config = {'displayModeBar': False}
        st.plotly_chart(fig, config=config)
        # 使用 ExcelWriter 将多个 DataFrame 写入同一个 Excel 文件的不同工作表
    excel_name = Path.cwd().joinpath('temp_files').joinpath("往期对比数据.xlsx")
    with pd.ExcelWriter(excel_name) as writer:
        df.to_excel(writer, sheet_name='线上', index=False)
        df_offline.to_excel(writer, sheet_name='线下', index=False)
        # 下载数据
    empty_line(1)
    download_excel(excel_name)



if __name__ == '__main__':
    main()
