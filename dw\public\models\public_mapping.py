from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class PublicMapping(BaseModel):
    type = models.CharField(max_length=64, blank=True, null=True, verbose_name='分类') #区分mapping类型
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='标准名称')
    keywords = models.CharField(max_length=1024, blank=True, null=True, verbose_name='容错关键字')
    medical_keywords = models.CharField(max_length=1024, blank=True, null=True, verbose_name='医疗关键字')
    drug_keywords = models.CharField(max_length=1024, blank=True, null=True, verbose_name='特药关键字')


    class Meta:
        db_table = 'public_mapping'
        verbose_name = '容错表'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['type', 'name'],
                name='unique_public_mapping'
            ),
        ]
