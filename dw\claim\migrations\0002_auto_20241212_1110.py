# Generated by Django 3.2.12 on 2024-12-12 11:10

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='claimprotonheavyion',
            name='coordinated_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='统筹金额'),
        ),
        migrations.AddField(
            model_name='claimprotonheavyion',
            name='past_symptom',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='是否既往症'),
        ),
        migrations.AddField(
            model_name='claimprotonheavyion',
            name='self_burden_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='个人负担金额'),
        ),
        migrations.AddField(
            model_name='claimprotonheavyion',
            name='total_amount',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='总费用'),
        ),
    ]
