from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class OtherProductInsureType(BaseModel):
    product_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='名称')
    type = models.CharField(max_length=200, blank=True, null=True, verbose_name='人员医保类型')
    person_num = models.IntegerField(blank=True, null=True, verbose_name='参保人数')

    class Meta:
        db_table = 'other_product_insure_type'
        verbose_name = '销售-宁惠保投保人员医保类型明细'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_name', 'type'],
                name='unique_other_product_insure_type_combination'
            ),
        ]
