# Generated by Django 4.2.1 on 2025-05-26 16:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("other", "0018_alter_otherauditpersontarget_create_time_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="otherauditpersontarget",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherauditpersontarget",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="othermanagementstaff",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="othermanagementstaff",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproduct",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproduct",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductauditperson",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductauditperson",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductcode",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductcode",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductinsuretype",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductinsuretype",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductmedicaltype",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductmedicaltype",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductproton",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductproton",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductresponse",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherproductresponse",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="othersentryreplayfiles",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="othersentryreplayfiles",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherstreamlitkeymapping",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherstreamlitkeymapping",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherybstatisticnhb",
            name="create_time",
            field=models.DateTimeField(
                db_comment="创建时间",
                help_text="由数据库自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="otherybstatisticnhb",
            name="update_time",
            field=models.DateTimeField(
                db_comment="更新时间",
                help_text="由数据库自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
    ]
