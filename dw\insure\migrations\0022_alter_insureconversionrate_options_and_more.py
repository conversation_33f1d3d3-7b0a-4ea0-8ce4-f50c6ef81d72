# Generated by Django 4.2.1 on 2025-07-29 17:21

from decimal import Decimal
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0021_insureconversionrate"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="insureconversionrate",
            options={
                "ordering": ["-create_time"],
                "verbose_name": "健康险埋点转换率",
                "verbose_name_plural": "健康险埋点转换率",
            },
        ),
        migrations.AlterModelTableComment(
            name="insureconversionrate",
            table_comment="健康险埋点转换率",
        ),
        migrations.RemoveIndex(
            model_name="insureconversionrate",
            name="insure_conv_create__e2d5b4_idx",
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="conversion_rate",
            field=models.DecimalField(
                db_comment="转换率数值，支持4位小数",
                decimal_places=4,
                help_text="转换率数值，支持4位小数",
                max_digits=8,
                validators=[
                    django.core.validators.MinValueValidator(Decimal("0.0000"))
                ],
                verbose_name="转换率",
            ),
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="data_type",
            field=models.CharField(
                choices=[
                    ("daily", "按日期"),
                    ("hourly", "按小时"),
                    ("weekly_hourly", "按星期+小时"),
                ],
                db_comment="转换率数据的类型",
                help_text="转换率数据的类型",
                max_length=20,
                verbose_name="数据类型",
            ),
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="date",
            field=models.DateField(
                blank=True,
                db_comment="日期维度数据使用，格式：YYYY-MM-DD",
                help_text="日期维度数据使用，格式：YYYY-MM-DD",
                null=True,
                verbose_name="日期",
            ),
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="hour",
            field=models.IntegerField(
                blank=True,
                db_comment="小时维度数据使用，范围：0-23",
                help_text="小时维度数据使用，范围：0-23",
                null=True,
                validators=[
                    django.core.validators.MinValueValidator(0),
                    django.core.validators.MaxValueValidator(23),
                ],
                verbose_name="小时",
            ),
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureconversionrate",
            name="weekday",
            field=models.CharField(
                blank=True,
                db_comment="星期+小时维度数据使用",
                help_text="星期+小时维度数据使用",
                max_length=50,
                null=True,
                verbose_name="星期几",
            ),
        ),
    ]
