from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalFieldMapping(BaseModel):
    source_table = models.Char<PERSON>ield(max_length=64, blank=True, null=True, verbose_name='来源表', **_db_comment_kwarg('来源表'))
    source_field = models.CharField(max_length=128, blank=True, null=True, verbose_name='来源字段', **_db_comment_kwarg('来源字段'))
    target_table = models.Char<PERSON>ield(max_length=64, blank=True, null=True, verbose_name='目标表', **_db_comment_kwarg('目标表'))
    target_field = models.CharField(max_length=128, blank=True, null=True, verbose_name='目标字段', **_db_comment_kwarg('目标字段'))
    default = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='默认值', **_db_comment_kwarg('默认值'))
    description = models.Char<PERSON>ield(max_length=512, blank=True, null=True, verbose_name='描述', **_db_comment_kwarg('描述'))
    dict_table = models.CharField(max_length=64, blank=True, null=True, verbose_name='字典表', **_db_comment_kwarg('字典表'))
    dict_id = models.CharField(max_length=32, blank=True, null=True, verbose_name='字典分类id', **_db_comment_kwarg('字典分类id'))
    dict_code_field = models.CharField(max_length=64, blank=True, null=True, verbose_name='字典表编码字段', **_db_comment_kwarg('字典表编码字段'))
    dict_name_field = models.CharField(max_length=128, blank=True, null=True, verbose_name='字典表名称字段', **_db_comment_kwarg('字典表名称字段'))

    class Meta:
        db_table = 'medical_field_mapping'
        verbose_name = '医保字段映射表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name
