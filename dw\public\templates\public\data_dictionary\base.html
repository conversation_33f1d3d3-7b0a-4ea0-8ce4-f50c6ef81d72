<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}数据字典{% endblock %}</title>

    <!-- DNS预解析和资源预加载 -->
    <link rel="dns-prefetch" href="//cdn.bootcdn.net">
    <link rel="preconnect" href="https://cdn.bootcdn.net" crossorigin>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64,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">

    <!-- 自定义CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'public/css/data_dictionary.css' %}">

    <!-- 导航栏现代化样式 -->
    <style>
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 16px;
            color: #495057 !important;
        }

        .navbar-nav .nav-link {
            position: relative;
            padding: 8px 16px !important;
            margin: 0 4px;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 14px;
            color: #495057 !important;
        }

        .navbar-nav .nav-link:hover {
            background-color: #e9ecef;
            color: #212529 !important;
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            background-color: #cce7ff;
            color: #212529 !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .navbar-nav .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background-color: #007bff;
            border-radius: 1px;
        }

        .navbar-nav .nav-link i {
            margin-right: 6px;
            font-size: 13px;
        }

        .navbar-toggler {
            border-color: rgba(0,0,0,0.1);
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        /* 确保导航栏在所有页面的位置一致 */
        .navbar .container-fluid {
            max-width: 1400px !important;
            margin: 0 auto !important;
        }
    </style>

    {% block extrahead %}{% endblock %}
</head>
<body class="data-dictionary-page">
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light" style="padding: 0; min-height: 35px; height: 35px; background-color: #f8f9fa;">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'data_dictionary:database_list' %}" style="margin-bottom: 0;">
                <i class="fas fa-book"></i> 数据字典
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'database_list' or request.resolver_match.url_name == 'database_detail' or request.resolver_match.url_name == 'table_detail' %}active{% endif %}"
                           href="{% url 'data_dictionary:database_list' %}">
                            <i class="fas fa-database"></i> 数据库列表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'global_search' %}active{% endif %}"
                           href="{% url 'data_dictionary:global_search' %}">
                            <i class="fas fa-search"></i> 全局搜索
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'statistics' %}active{% endif %}"
                           href="{% url 'data_dictionary:statistics' %}">
                            <i class="fas fa-chart-bar"></i> 统计信息
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'data_supplement' %}active{% endif %}"
                           href="{% url 'data_dictionary:data_supplement' %}">
                            <i class="fas fa-edit"></i> 数据补充
                        </a>
                    </li>

                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="/dw/admin/" target="_blank">
                            <i class="fas fa-cog"></i> 管理后台
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="fas fa-database"></i> 数据字典系统 - 
                <small>提供完整的数据库元数据管理</small>
            </p>
        </div>
    </footer>

    <!-- jQuery 多重备用加载方案 -->
    <script>
    // jQuery加载检测和多重备用方案
    (function() {
        const jqueryVersions = [
            'https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js',
            'https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js',
            'https://code.jquery.com/jquery-3.6.0.min.js'
        ];

        let currentIndex = 0;

        function loadJQuery() {
            if (currentIndex >= jqueryVersions.length) {
                console.error('所有jQuery CDN都加载失败，页面功能可能受限');
                // 显示用户友好的错误提示
                document.addEventListener('DOMContentLoaded', function() {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-warning alert-dismissible fade show';
                    errorDiv.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 9999; max-width: 400px;';
                    errorDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        网络资源加载异常，部分功能可能无法使用。请检查网络连接或刷新页面重试。
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.body.appendChild(errorDiv);
                });
                return;
            }

            const script = document.createElement('script');
            script.src = jqueryVersions[currentIndex];
            script.async = false; // 确保同步加载

            script.onload = function() {
                console.log(`jQuery加载成功: ${jqueryVersions[currentIndex]}`);
                // 验证jQuery是否真正可用
                if (typeof window.$ !== 'undefined' && typeof window.jQuery !== 'undefined') {
                    console.log('jQuery验证通过，版本:', $.fn.jquery);
                    // 触发自定义事件通知jQuery已就绪
                    document.dispatchEvent(new CustomEvent('jqueryReady'));
                } else {
                    console.warn('jQuery加载但验证失败，尝试下一个CDN');
                    currentIndex++;
                    loadJQuery();
                }
            };

            script.onerror = function() {
                console.warn(`jQuery加载失败: ${jqueryVersions[currentIndex]}`);
                currentIndex++;
                loadJQuery();
            };

            document.head.appendChild(script);
        }

        // 开始加载jQuery
        loadJQuery();
    })();
    </script>

    <!-- Bootstrap JS (等待jQuery加载完成后再加载) -->
    <script>
    document.addEventListener('jqueryReady', function() {
        const bootstrapScript = document.createElement('script');
        bootstrapScript.src = 'https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js';
        bootstrapScript.onload = function() {
            console.log('Bootstrap JS加载成功');
        };
        bootstrapScript.onerror = function() {
            console.warn('Bootstrap JS加载失败，尝试备用CDN');
            const fallbackScript = document.createElement('script');
            fallbackScript.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js';
            document.head.appendChild(fallbackScript);
        };
        document.head.appendChild(bootstrapScript);
    });
    </script>

    <!-- 现代化性能监控脚本 -->
    <script>
    // 现代化页面性能监控
    (function() {
        'use strict';

        // 使用现代Performance API
        function measurePerformance() {
            try {
                // 优先使用现代Performance Observer API
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        entries.forEach(entry => {
                            if (entry.entryType === 'navigation') {
                                const loadTime = entry.loadEventEnd - entry.fetchStart;
                                const domReady = entry.domContentLoadedEventEnd - entry.fetchStart;

                                // 只在开发环境显示性能信息
                                if (isDevelopmentEnvironment()) {
                                    console.group('📊 页面性能监控');
                                    console.log(`⏱️ 页面加载时间: ${Math.round(loadTime)}ms`);
                                    console.log(`🏗️ DOM就绪时间: ${Math.round(domReady)}ms`);
                                    console.log(`🌐 DNS查询: ${Math.round(entry.domainLookupEnd - entry.domainLookupStart)}ms`);
                                    console.log(`🔗 TCP连接: ${Math.round(entry.connectEnd - entry.connectStart)}ms`);
                                    console.groupEnd();
                                }
                            }
                        });
                    });
                    observer.observe({entryTypes: ['navigation']});
                } else if (window.performance && window.performance.timing) {
                    // 降级到传统Performance Timing API
                    const timing = window.performance.timing;
                    const loadTime = timing.loadEventEnd - timing.navigationStart;
                    const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;

                    if (isDevelopmentEnvironment() && loadTime > 0) {
                        console.log(`📊 页面加载时间: ${loadTime}ms, DOM就绪时间: ${domReady}ms`);
                    }
                }
            } catch (error) {
                console.warn('性能监控初始化失败:', error);
            }
        }

        function isDevelopmentEnvironment() {
            return window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.hostname.includes('dev') ||
                   window.location.port !== '';
        }

        // 页面加载完成后执行性能监控
        if (document.readyState === 'complete') {
            measurePerformance();
        } else {
            window.addEventListener('load', measurePerformance);
        }
    })();
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
