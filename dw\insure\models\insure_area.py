from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class InsureArea(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    name = models.CharField(max_length=64, blank=True, null=True, verbose_name='地区名称')
    total_count = models.IntegerField(blank=True, null=True, verbose_name='参保总数')
    ratio = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,
                                       verbose_name='参保占比')
    insure_ratio = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,
                                       verbose_name='参保率;健康险与居民医保的比率')
    position = models.IntegerField(blank=True, null=True, verbose_name='排名')

    today_count = models.IntegerField(blank=True, null=True, verbose_name='今日参保数量')
    yesterday_count = models.IntegerField(blank=True, null=True, verbose_name='昨日参保数量')
    # 由于统计可能细化，例如分地区、产品、保司等，会导致表格过大，因此增加一个字段作为补充信息
    additional_info = models.CharField(max_length=128, blank=True, null=True, verbose_name='补充信息')

    class Meta:
        db_table = 'insure_area'
        verbose_name = '健康险地区参保信息'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'publish_time', 'name','additional_info'],
                name='unique_insure_area_combination'
            ),
        ]
