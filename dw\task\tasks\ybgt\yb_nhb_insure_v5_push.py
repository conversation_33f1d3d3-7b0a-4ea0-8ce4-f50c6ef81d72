import json
import logging
import numpy as np
import pandas as pd
import requests

from transfrom.tasks.ybgt.yb_nhb_insure_v5 import YbNhbInsureV5
from other.models import OtherYbStatisticNhb
logger = logging.getLogger(__name__)


def default_converter(o):
    if isinstance(o, int) or isinstance(o, np.int64):
        return int(o)
    raise TypeError(f'Object of type {o.__class__.__name__} is not JSON serializable')

def yb_nhb_insure_v5_push():
    try:
        source = YbNhbInsureV5()
        data_type = 'nhb_insure_v5'  # 产品类型，销售数据用insure、理赔数据用claim
        data = source.get_content()
        body = {'type': data_type,
                'data': data}
        last_record = OtherYbStatisticNhb.objects.filter(type=data_type,product_code=source.product_set_code).order_by('update_time').last()
        post_flg = last_record.is_post #是否推送标识
        if not post_flg:
            response = requests.post(
                url='https://gf.njybjyybz.org.cn:11020/nhb-medicare/prod/nhb_statistic',   #测试环境stage，正式环境prod
                headers={'Content-Type': 'application/json; charset=utf-8'},
                data=json.dumps(body, ensure_ascii=False, default=default_converter).encode('utf8'),
                timeout=10
            )
            if response.status_code != 200:
                logger.error(f'ybgt_nhb_insure_v5_push error with code {response.status_code}')
            else:
                # 如果当天推送过，会更新表中is_post字段为1，避免重复推送
                last_record.is_post = 1
                last_record.save()
            logger.info(f'ybgt_nhb_insure_v5_push success with code {response.status_code}')
            return response.status_code
        else:
            logger.info(f'ybgt_nhb_insure_v5_push 已经推送过')
            return f'ybgt_nhb_insure_v5_push 已经推送过'
    except Exception as e:
        logger.error(f'ybgt_nhb_insure_v5_push error with {e}')
        raise e
