from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalServiceEntity(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='服务项目代码', **_db_comment_kwarg('服务项目代码'))
    name = models.CharField(max_length=512, blank=True, null=True, verbose_name='服务项目名称', **_db_comment_kwarg('服务项目名称'))
    charge_item_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='收费项目代码', **_db_comment_kwarg('收费项目代码'))
    charge_item_name = models.CharField(max_length=512, blank=True, null=True, verbose_name='收费项目名称', **_db_comment_kwarg('收费项目名称'))
    central_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='中心编码', **_db_comment_kwarg('中心编码'))
    level_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='层级类型', **_db_comment_kwarg('层级类型'))
    province_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='省份编码', **_db_comment_kwarg('省份编码'))
    province_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='省份', **_db_comment_kwarg('省份'))
    city_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='城市编码', **_db_comment_kwarg('城市编码'))
    city_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='城市', **_db_comment_kwarg('城市'))
    treatment_item_content = models.TextField(blank=True, null=True, verbose_name='诊疗项目内涵', **_db_comment_kwarg('诊疗项目内涵'))
    treatment_item_description = models.CharField(max_length=1024, blank=True, null=True, verbose_name='诊疗项目说明', **_db_comment_kwarg('诊疗项目说明'))
    treatment_excluded_content = models.CharField(max_length=1024, blank=True, null=True, verbose_name='诊疗除外内容', **_db_comment_kwarg('诊疗除外内容'))
    pricing_unit = models.CharField(max_length=32, blank=True, null=True, verbose_name='计价单位', **_db_comment_kwarg('计价单位'))
    payment_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='医保支付类别', **_db_comment_kwarg('医保支付类别'))
    payment_upper_limit = models.CharField(max_length=255, blank=True, null=True, verbose_name='医保支付上限', **_db_comment_kwarg('医保支付上限'))
    price = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='供应价格', **_db_comment_kwarg('供应价格'))
    price_composition = models.CharField(max_length=1024, blank=True, null=True, verbose_name='价格构成', **_db_comment_kwarg('价格构成'))
    service_output = models.CharField(max_length=1024, blank=True, null=True, verbose_name='服务产出', **_db_comment_kwarg('服务产出'))
    initial_payment_ratio = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='先行自付比例', **_db_comment_kwarg('先行自付比例'))
    limited_payment = models.CharField(max_length=255, blank=True, null=True, verbose_name='限定支付', **_db_comment_kwarg('限定支付'))
    begin_date = models.DateField(blank=True, null=True, verbose_name='开始日期', **_db_comment_kwarg('开始日期'))
    additional_info = models.TextField(blank=True, null=True, verbose_name='附加信息', **_db_comment_kwarg('附加信息'))
    remark = models.CharField(max_length=1024, blank=True, null=True, verbose_name='备注', **_db_comment_kwarg('备注'))

    class Meta:
        db_table = 'medical_service_entity'
        verbose_name = '医疗服务项目省市实体表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name
