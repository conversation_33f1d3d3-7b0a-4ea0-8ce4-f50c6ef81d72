# Generated by Django 4.2.1 on 2025-05-23 09:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("claim", "0014_auto_20250314_1417"),
    ]

    operations = [
        migrations.CreateModel(
            name="ClaimXuePingXian",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "township",
                    models.CharField(
                        blank=True,
                        db_comment="乡镇",
                        max_length=32,
                        null=True,
                        verbose_name="乡镇",
                    ),
                ),
                (
                    "school_name",
                    models.CharField(
                        blank=True,
                        db_comment="学校名称",
                        max_length=128,
                        null=True,
                        verbose_name="学校名称",
                    ),
                ),
                (
                    "insured_name",
                    models.CharField(
                        blank=True,
                        db_comment="被保险人姓名",
                        max_length=128,
                        null=True,
                        verbose_name="被保险人姓名",
                    ),
                ),
                (
                    "insured_credential_number",
                    models.CharField(
                        blank=True,
                        db_comment="被保险人身份证号",
                        max_length=128,
                        null=True,
                        verbose_name="被保险人身份证号",
                    ),
                ),
                (
                    "age",
                    models.IntegerField(
                        blank=True, db_comment="年龄", null=True, verbose_name="年龄"
                    ),
                ),
                (
                    "parent_name",
                    models.CharField(
                        blank=True,
                        db_comment="家长姓名",
                        max_length=128,
                        null=True,
                        verbose_name="家长姓名",
                    ),
                ),
                (
                    "parent_mobile",
                    models.CharField(
                        blank=True,
                        db_comment="家长手机号",
                        max_length=128,
                        null=True,
                        verbose_name="家长手机号",
                    ),
                ),
                (
                    "policy_number",
                    models.CharField(
                        blank=True,
                        db_comment="保单号",
                        max_length=128,
                        null=True,
                        verbose_name="保单号",
                    ),
                ),
                (
                    "claim_number",
                    models.CharField(
                        blank=True,
                        db_comment="报案号",
                        max_length=128,
                        null=True,
                        verbose_name="报案号",
                    ),
                ),
                (
                    "case_stage",
                    models.CharField(
                        blank=True,
                        db_comment="案件环节",
                        max_length=128,
                        null=True,
                        verbose_name="案件环节",
                    ),
                ),
                (
                    "overall_reimbursement_amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="统筹报销金额",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="统筹报销金额",
                    ),
                ),
                (
                    "outside_amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="非医保",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="非医保",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="发票总金额",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="发票总金额",
                    ),
                ),
                (
                    "actual_paid_amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="结案金额",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="结案金额",
                    ),
                ),
                (
                    "accident_reason",
                    models.CharField(
                        blank=True,
                        db_comment="出险原因",
                        max_length=128,
                        null=True,
                        verbose_name="出险原因",
                    ),
                ),
                (
                    "accident_type",
                    models.CharField(
                        blank=True,
                        db_comment="出现类型",
                        max_length=128,
                        null=True,
                        verbose_name="出现类型",
                    ),
                ),
                (
                    "accident_category",
                    models.CharField(
                        blank=True,
                        db_comment="出险类别",
                        max_length=128,
                        null=True,
                        verbose_name="出险类别",
                    ),
                ),
                (
                    "issue_date",
                    models.DateField(
                        blank=True,
                        db_comment="报案日期",
                        null=True,
                        verbose_name="报案日期",
                    ),
                ),
                (
                    "accident_date",
                    models.DateField(
                        blank=True,
                        db_comment="出险日期",
                        null=True,
                        verbose_name="出险日期",
                    ),
                ),
                (
                    "remark",
                    models.CharField(
                        blank=True,
                        db_comment="备注",
                        max_length=512,
                        null=True,
                        verbose_name="备注",
                    ),
                ),
            ],
            options={
                "verbose_name": "理赔-学平险理赔明细",
                "verbose_name_plural": "理赔-学平险理赔明细",
                "db_table": "claim_xuepingxian",
            },
        ),
    ]
