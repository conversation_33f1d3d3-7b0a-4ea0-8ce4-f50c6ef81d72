import os
import django
from celery import Celery
from django.conf import settings

# 设置系统环境变量，否则在启动celery时会报错
# etldemo 是当前项目名
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dw.settings')
django.setup()
celery_app = Celery('dw',broker=settings.CELERY_BROKER_URL,backend=settings.CELERY_RESULT_BACKEND)
celery_app.config_from_object('django.conf:settings')
celery_app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)
celery_app.conf.broker_connection_retry_on_startup = True