# Generated by Django 3.2.12 on 2025-03-04 11:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0006_claimpayamountrange'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimPayAgeRange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('age_range', models.CharField(blank=True, max_length=32, null=True, verbose_name='年龄分布范围')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_avg_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='人均赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
                ('insure_person_number', models.IntegerField(blank=True, null=True, verbose_name='投保人数')),
                ('incidence_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='出险率')),
            ],
            options={
                'verbose_name': '理赔-赔付年龄分布情况',
                'verbose_name_plural': '理赔-赔付年龄分布情况',
                'db_table': 'claim_pay_age_range',
            },
        ),
        migrations.CreateModel(
            name='ClaimPayAreaRange',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('area_name', models.CharField(blank=True, max_length=32, null=True, verbose_name='地区名称')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_avg_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='人均赔付金额')),
                ('pay_avg_amount_per_case', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='件均赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
                ('premium_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='保费')),
                ('claim_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付率')),
            ],
            options={
                'verbose_name': '理赔-赔付地区分布',
                'verbose_name_plural': '理赔-赔付地区分布',
                'db_table': 'claim_pay_area_range',
            },
        ),
        migrations.CreateModel(
            name='ClaimPayGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('policy_type', models.CharField(blank=True, max_length=32, null=True, verbose_name='保单类型')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_number_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付人次占比')),
                ('pay_person_number_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付人数占比')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
                ('premium_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='保费')),
                ('claim_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付率')),
            ],
            options={
                'verbose_name': '理赔-个团赔付情况',
                'verbose_name_plural': '理赔-个团赔付情况',
                'db_table': 'claim_pay_group',
            },
        ),
        migrations.CreateModel(
            name='ClaimPayProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('product_type', models.CharField(blank=True, max_length=32, null=True, verbose_name='险种类型')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
                ('premium_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='保费')),
                ('claim_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付率')),
            ],
            options={
                'verbose_name': '理赔-险种赔付情况',
                'verbose_name_plural': '理赔-险种赔付情况',
                'db_table': 'claim_pay_product',
            },
        ),
    ]
