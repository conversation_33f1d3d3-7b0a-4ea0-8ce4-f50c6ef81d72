# Generated by Django 4.2.1 on 2025-06-12 09:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("claim", "0019_alter_claimageoverview_create_time_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ClaimXuePingXianHN",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "product_set_code",
                    models.Char<PERSON>ield(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "claim_number",
                    models.CharField(
                        blank=True,
                        db_comment="报案号",
                        max_length=128,
                        null=True,
                        verbose_name="报案号",
                    ),
                ),
                (
                    "issue_remark",
                    models.CharField(
                        blank=True,
                        db_comment="出单备注",
                        max_length=512,
                        null=True,
                        verbose_name="出单备注",
                    ),
                ),
                (
                    "insured_name",
                    models.CharField(
                        blank=True,
                        db_comment="被保险人",
                        max_length=128,
                        null=True,
                        verbose_name="被保险人",
                    ),
                ),
                (
                    "insured_credential_number",
                    models.CharField(
                        blank=True,
                        db_comment="身份证号",
                        max_length=128,
                        null=True,
                        verbose_name="身份证号",
                    ),
                ),
                (
                    "age",
                    models.IntegerField(
                        blank=True, db_comment="年龄", null=True, verbose_name="年龄"
                    ),
                ),
                (
                    "gender",
                    models.CharField(
                        blank=True,
                        db_comment="性别",
                        max_length=32,
                        null=True,
                        verbose_name="性别",
                    ),
                ),
                (
                    "mobile",
                    models.CharField(
                        blank=True,
                        db_comment="联系电话",
                        max_length=128,
                        null=True,
                        verbose_name="联系电话",
                    ),
                ),
                (
                    "accident_date",
                    models.DateField(
                        blank=True,
                        db_comment="出险日期",
                        null=True,
                        verbose_name="出险日期",
                    ),
                ),
                (
                    "accident_type",
                    models.CharField(
                        blank=True,
                        db_comment="类型",
                        max_length=128,
                        null=True,
                        verbose_name="类型",
                    ),
                ),
                (
                    "pay_type",
                    models.CharField(
                        blank=True,
                        db_comment="赔付类型",
                        max_length=128,
                        null=True,
                        verbose_name="赔付类型",
                    ),
                ),
                (
                    "accident_reason",
                    models.CharField(
                        blank=True,
                        db_comment="事故原因",
                        max_length=128,
                        null=True,
                        verbose_name="事故原因",
                    ),
                ),
                (
                    "complete_date",
                    models.DateField(
                        blank=True,
                        db_comment="结案日期",
                        null=True,
                        verbose_name="结案日期",
                    ),
                ),
                (
                    "medical_reimbursement",
                    models.DecimalField(
                        blank=True,
                        db_comment="医保报销",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="医保报销",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="发票金额",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="发票金额",
                    ),
                ),
                (
                    "non_medical_amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="非医保",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="非医保",
                    ),
                ),
                (
                    "claim_amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="索赔金额",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="索赔金额",
                    ),
                ),
                (
                    "actual_paid_amount",
                    models.DecimalField(
                        blank=True,
                        db_comment="结案支付金额",
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="结案支付金额",
                    ),
                ),
                (
                    "accident_location",
                    models.CharField(
                        blank=True,
                        db_comment="出险地点",
                        max_length=128,
                        null=True,
                        verbose_name="出险地点",
                    ),
                ),
                (
                    "school_type",
                    models.CharField(
                        blank=True,
                        db_comment="学校类型",
                        max_length=128,
                        null=True,
                        verbose_name="公办",
                    ),
                ),
                (
                    "insurance_company",
                    models.CharField(
                        blank=True,
                        db_comment="保司",
                        max_length=128,
                        null=True,
                        verbose_name="保司",
                    ),
                ),
            ],
            options={
                "verbose_name": "理赔-湖南学平险理赔明细",
                "verbose_name_plural": "理赔-湖南学平险理赔明细",
                "db_table": "claim_xuepingxian_new",
            },
        ),
    ]
