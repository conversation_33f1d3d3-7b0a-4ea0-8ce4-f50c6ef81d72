# Generated manually for AI translation fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('public', '0003_create_public_holiday'),
    ]

    operations = [
        migrations.AddField(
            model_name='publiccolumninfo',
            name='ai_suggested_name',
            field=models.CharField(
                blank=True,
                help_text='AI智能翻译建议的中文名称',
                max_length=512,
                null=True,
                verbose_name='AI建议中文名'
            ),
        ),
        migrations.AddField(
            model_name='publiccolumninfo',
            name='ai_translation_status',
            field=models.CharField(
                choices=[
                    ('pending', '待翻译'),
                    ('translated', '已翻译'),
                    ('confirmed', '已确认'),
                    ('rejected', '已拒绝'),
                    ('failed', '翻译失败')
                ],
                default='pending',
                help_text='AI翻译状态：pending-待翻译，translated-已翻译，confirmed-已确认，rejected-已拒绝，failed-翻译失败',
                max_length=32,
                verbose_name='AI翻译状态'
            ),
        ),
        migrations.AddField(
            model_name='publiccolumninfo',
            name='ai_translation_time',
            field=models.DateTimeField(
                blank=True,
                help_text='AI翻译完成时间',
                null=True,
                verbose_name='AI翻译时间'
            ),
        ),
        migrations.AddField(
            model_name='publiccolumninfo',
            name='ai_confidence_score',
            field=models.FloatField(
                blank=True,
                help_text='AI翻译结果的置信度分数（0-1）',
                null=True,
                verbose_name='AI翻译置信度'
            ),
        ),
        migrations.AddIndex(
            model_name='publiccolumninfo',
            index=models.Index(fields=['ai_translation_status'], name='idx_pub_col_ai_status'),
        ),
    ]
