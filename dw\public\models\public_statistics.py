from django.db import models
from common.models import BaseModel
from django.db.models import constraints

class PublicStatistics(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    type = models.CharField(max_length=64, blank=True, null=True, verbose_name='分类') #区分是销售数据还是理赔数据等
    statistical_type = models.CharField(max_length=64, blank=True, null=True, verbose_name='统计分类')
    key = models.CharField(max_length=128, blank=True, null=True, verbose_name='数据标签')
    value = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='结果值')
    # 由于统计可能细化，例如分地区、产品、保司等，会导致表格过大，因此增加一个字段作为补充信息
    additional_info = models.CharField(max_length=128, blank=True, null=True, verbose_name='补充信息')

    class Meta:
        db_table = 'public_statistics'
        verbose_name = '数据统计表'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'publish_time', 'type','statistical_type','key','additional_info'],
                name='unique_public_statistics_combination'
            ),
        ]
