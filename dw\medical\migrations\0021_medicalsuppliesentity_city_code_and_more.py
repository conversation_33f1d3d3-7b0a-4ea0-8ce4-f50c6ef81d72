# Generated by Django 4.2.1 on 2025-06-18 11:09

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("medical", "0020_alter_medicaldrugentity_procure_ceil_price"),
    ]

    operations = [
        migrations.AddField(
            model_name="medicalsuppliesentity",
            name="city_code",
            field=models.CharField(
                blank=True,
                db_comment="城市编码",
                max_length=32,
                null=True,
                verbose_name="城市编码",
            ),
        ),
        migrations.AddField(
            model_name="medicalsuppliesentity",
            name="city_name",
            field=models.CharField(
                blank=True,
                db_comment="城市",
                max_length=128,
                null=True,
                verbose_name="城市",
            ),
        ),
        migrations.AddField(
            model_name="medicalsuppliesentity",
            name="level_type",
            field=models.CharField(
                blank=True,
                db_comment="层级类型",
                max_length=32,
                null=True,
                verbose_name="层级类型",
            ),
        ),
        migrations.AddField(
            model_name="medicalsuppliesentity",
            name="province_code",
            field=models.Char<PERSON>ield(
                blank=True,
                db_comment="省份编码",
                max_length=32,
                null=True,
                verbose_name="省份编码",
            ),
        ),
        migrations.AddField(
            model_name="medicalsuppliesentity",
            name="province_name",
            field=models.CharField(
                blank=True,
                db_comment="省份",
                max_length=128,
                null=True,
                verbose_name="省份",
            ),
        ),
    ]
