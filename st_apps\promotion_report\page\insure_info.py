import datetime
from pprint import pprint

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from pandasql import sqldf
from plotly.subplots import make_subplots
from utils.st import query_sql, text_write, empty_line, sub_text_write
from utils.utils import number_to_chinese
from pyecharts import options as opts
from pyecharts.charts import Sankey
from streamlit_echarts import st_pyecharts
import warnings
import plotly.figure_factory as ff
import matplotlib.pyplot as plt

import sys
import venn

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')
# sales_start_date = '2024-09-12'
# sales_start_datetime = '2024-09-12 16:00:00'

def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)
    # 使用pandas的条件赋值替代np.where，避免数据类型冲突
    df_product_code.loc[
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        'sale_start_time'
    ] = '2023-09-18 00:00:00'
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code



def get_total_product():
    """
    获取产品集代码、名称
    :return:
    """
    SQL = """
    SELECT
    distinct 
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        p.`name` product_name
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id
    WHERE
        ps.code like 'ninghuibao%'
				and p.delete_time is null
    ORDER BY
        ps.CODE DESC
    """
    df = CONNECTOR_JKX.query(SQL, show_spinner='查询中...', ttl=600)
    return df


def get_renewal_info(product_set_code):
    sql = """
        SELECT distinct p.name,count(1) num
    FROM `order` o
    JOIN order_item oi ON oi.order_id = o.id
    JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
    JOIN product_set ps ON ps.id = o.product_set_id AND ps.`code` = '{product_set_code}'
    join product p on p.id = oi.product_id
    WHERE o.order_status = 'PAID_SUCCESS'
    AND oic.renewal_status = 'OPEN'
    group by p.name
        """
    df = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code), ttl=0)
    return df


def get_totalt_count(product_set_code):
    sql = """
    SELECT

	count( 1 ) AS count,count(distinct oic.client_id) person_count,
	sum( oic.premium ) AS amount
FROM
	`order` o
	JOIN order_item oi ON oi.order_id = o.id
	JOIN order_item_client oic ON oic.order_item_id = oi.id 
	AND oic.is_return = 0
	JOIN product_set ps ON ps.id = o.product_set_id 
	AND ps.CODE = '{product_set_code}'
	JOIN product p ON oi.product_id = p.id
WHERE
	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
and o.delete_time is null
    """

    df_total_count = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code), show_spinner='查询中...', ttl=0)
    return df_total_count


def get_renewal_ratio(product_set_code):
    df_total_count = get_totalt_count(product_set_code)
    df_renewal_info = get_renewal_info(product_set_code)
    total_count = df_total_count['count'].sum()
    renewal_ratio = df_renewal_info['num'].sum() / total_count
    return renewal_ratio, total_count, df_renewal_info['num'].sum(), df_renewal_info


def get_product_daily_count(product_set_code,sales_start_date,sale_end_date):
    sql = """
    SELECT
date( o.create_time ) AS date,
p.name,
	count( 1 ) AS count,count(distinct oic.client_id) person_count,
	sum( oic.premium ) AS amount
FROM
	`order` o
	JOIN order_item oi ON oi.order_id = o.id
	JOIN order_item_client oic ON oic.order_item_id = oi.id 
	AND oic.is_return = 0
	JOIN product_set ps ON ps.id = o.product_set_id 
	AND ps.CODE = '{product_set_code}'
	JOIN product p ON oi.product_id = p.id 
WHERE
	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
and o.delete_time is null
GROUP BY
date( o.create_time ),
	p.`name`
    """
    df_product_daily_count = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code),
                                                 show_spinner='查询中...', ttl=0)

    full_date_range = pd.date_range(start=sales_start_date, end=datetime.datetime.now().date(), freq='D')
    df_product = get_total_product()
    df_product = df_product[df_product['product_set_code'] == product_set_code]
    product_list = df_product['product_name'].tolist()

    df_combinations = pd.MultiIndex.from_product(
        [full_date_range, product_list],
        names=['date', 'name'])
    df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
    df_full_combinations['date'] = df_full_combinations['date'].dt.date
    df_product_daily_count = sqldf(
        "select a.*,ifnull(b.count,0) as count,ifnull(b.person_count,0) as person_count,ifnull(b.amount,0) as amount from df_full_combinations a left join df_product_daily_count b on a.date = b.date and a.name = b.name")
    # 只筛选出销售起始日期之前的日期
    df_product_daily_count = df_product_daily_count[df_product_daily_count['date'] >= sales_start_date]
    # 筛选出销售结束日期之后的日期
    df_product_daily_count = df_product_daily_count[df_product_daily_count['date'] <= sale_end_date]

    df_product_count = df_product_daily_count.groupby('name').agg(
        {'count': 'sum', 'person_count': 'sum', 'amount': 'sum'}).reset_index()
    sort_order = {'基础版': 1,'标准版': 2, '升级版': 3, '少儿意外险': 4, '中老年意外险': 5, '疾病住院津贴补充保障': 6}
    df_product_count['sort_order'] = df_product_count['name'].map(sort_order)
    df_product_count = df_product_count.sort_values(by='sort_order')
    df_product_count.reset_index(inplace=True, drop=True)

    return df_product_daily_count, df_product_count


def get_last_24_hour_count(end_datetime, sale_start_time, product_set_code, conn=CONNECTOR_JKX,
                           sql=query_sql('SQL_24_HOUR_CHANNEL')):
    """
    获取过去24小时统计数据
    """
    # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
    start_datetime = max(datetime.datetime.strptime(end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
        hours=24), datetime.datetime.strptime(sale_start_time, '%Y-%m-%d %H:%M:%S'))
    df_last_24_hour_count = conn.query(sql.format(product_set_code=product_set_code,
                                                  start_datetime=start_datetime,
                                                  end_datetime=end_datetime), show_spinner='查询中...', ttl=0)

    df_last_24_hour_count['source'] = df_last_24_hour_count['source'].apply(
        lambda x: '我的南京' if '我的南京' in x
        else '支付宝' if '支付宝' in x
        else '公众号')
    # 创建一个掩码，标记出 is_online 等于 0 的行
    mask = df_last_24_hour_count['is_online'] == 0
    # 使用掩码更新 'source' 列
    df_last_24_hour_count.loc[mask, 'source'] = '代理人'

    df_last_24_hour_count['datetime'] = df_last_24_hour_count['datetime'].apply(
        lambda x: datetime.datetime.strptime(x, '%Y-%m-%d %H:%M:%S'))

    df_last_24_hour_count = df_last_24_hour_count.groupby(['datetime', 'source']).agg({'count': 'sum'}).reset_index()
    # 获取过去24小时的完整日期范围，日期格式为'%Y-%m-%d %H:00:00'
    full_date_range = pd.date_range(start=start_datetime.replace(minute=0, second=0),
                                    end=end_datetime[:13] + ':00:00', freq='H')

    # 创建一个完整的日期与产品名称的组合 DataFrame
    product_names = df_last_24_hour_count['source'].unique()
    date_product_combinations = pd.MultiIndex.from_product([full_date_range, product_names],
                                                           names=['datetime', 'source'])

    df_full_combinations = pd.DataFrame(index=date_product_combinations).reset_index()
    # df_full_combinations['datetime'] = df_full_combinations['datetime'].dt.strftime('%Y-%m-%d')
    # 拼接最终的完整数据
    df_last_24_hour_count = sqldf(
        "select a.*,ifnull(b.count,0) as 'count' from df_full_combinations a left join df_last_24_hour_count b on a.datetime = b.datetime and a.source = b.source")
    return df_last_24_hour_count


def main():
    st.subheader('总览')
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=product_info['product_set_name'],
                                    placeholder="请选择产品")
    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[0]
    sales_start_datetime = product_info[product_info['product_set_name'] == product_set_name]['sale_start_time'].values[
        0]
    sales_start_date = datetime.datetime.strftime(pd.to_datetime(sales_start_datetime), '%Y-%m-%d')
    sales_start_datetime = datetime.datetime.strftime(pd.to_datetime(sales_start_datetime), '%Y-%m-%d %H:%M:%S')
    sale_end_date = datetime.datetime.strftime(pd.to_datetime(product_info[product_info['product_set_name'] == product_set_name]['sale_end_time'].values[0]), '%Y-%m-%d')
    sale_end_datetime = datetime.datetime.strftime(pd.to_datetime(product_info[product_info['product_set_name'] == product_set_name]['sale_end_time'].values[0]), '%Y-%m-%d %H:%M:%S')
    st.divider()
    text_write('续保代扣概况(含附加险)')

    renewal_ratio, total_count, renewal_count, df_renewal_info = get_renewal_ratio(product_set_code)
    cols = st.columns([0.9, 0.3])
    with cols[0]:
        # 使用plotly.express绘制环形图
        df_renewal_info.rename(columns={'name': '产品名称', 'num': '续保单量'}, inplace=True)
        fig = px.pie(df_renewal_info, values='续保单量', names='产品名称', height=350)
        # 调整环形大小及显示格式
        fig.update_traces(textinfo='value+percent+label', textposition='outside', automargin=True, hole=0.65)
        st.plotly_chart(fig)

    with cols[1]:
        st.metric('总单量', total_count)
        st.metric('代扣开通量', renewal_count)
        st.metric('代扣开通率', f'{renewal_ratio:.2%}')

    text_write('产品销售概况')
    empty_line(1)
    df_product_daily_count, df_product_count = get_product_daily_count(product_set_code,sales_start_date,sale_end_date)
    # 查看有多少个产品
    df_product = get_total_product()
    df_product = df_product[df_product['product_set_code'] == product_set_code]
    product_count = df_product.shape[0]
    col_len = [0.1] + [0.2] * product_count
    cols = st.columns(col_len)

    for i in range(df_product_count.shape[0]):
        with cols[i + 1]:
            st.metric(df_product_count.iloc[i]['name'], df_product_count.iloc[i]['count'])

    sub_text_write('产品单量分布')
    # 使用plotly.express绘制环形图
    df_product_count.rename(columns={'name': '产品名称', 'count': '单量'}, inplace=True)
    fig = px.pie(df_product_count, values='单量', names='产品名称', height=350)
    # 调整环形大小及显示格式
    fig.update_traces(textinfo='value+label', textposition='outside', automargin=True, hole=0.65)
    st.plotly_chart(fig, use_container_width=True)

    sub_text_write('产品销售趋势')
    df_product_daily_count.rename(
        columns={'name': '产品名称', 'date': '日期', 'count': '单量', 'person_count': '客户数', 'amount': '金额'},
        inplace=True)
    # 绘制折线图，根据日期、产品名称、单量绘制
    fig = px.line(df_product_daily_count, x='日期', y='单量', color='产品名称', height=400, markers=True)
    # 更新文本位置和样式
    fig.update_traces(
    )
    fig.update_xaxes(tickformat="%Y-%m-%d")
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    st.plotly_chart(fig, use_container_width=True)

    df_last_24_hour_count = get_last_24_hour_count(sale_end_datetime,
                                                   sales_start_datetime, product_set_code)
    sub_text_write('近24小时分渠道主险统计情况')
    df_last_24_hour_count.rename(
        columns={'source': '渠道名称', 'datetime': '时间', 'count': '单量'},
        inplace=True)
    # 按时间和渠道名称分组，计算单量总和
    df_grouped = df_last_24_hour_count.groupby(['时间', '渠道名称'])['单量'].sum().reset_index()
    # 绘制堆积柱状图
    fig = px.bar(df_grouped, x='时间', y='单量', color='渠道名称', barmode='stack', text_auto=True, height=400)
    # 更新文本位置和样式
    fig.update_traces(
        textposition='outside',  # 文本显示在柱子外面
        textfont=dict(size=12)  # 文本字体大小
    )
    fig.update_xaxes(tickformat="%H时")
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    fig.update_layout(
        title=dict(
            text='近24小时分渠道主险单量堆积图',
            x=0.5,  # 居中对齐
            xanchor="center",
            y=0.95,
            font=dict(size=14)
        )
    )
    st.plotly_chart(fig, use_container_width=True)


if __name__ == '__main__':
    main()
