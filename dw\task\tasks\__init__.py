import json
import logging

import requests
from celery import Task
from celery import shared_task
from django.conf import settings

logger = logging.getLogger(__name__)

class MyHookTask(Task):

    def send_feishu_message(self, message, app_id=settings.FEISHU_APP_ID):
        """
        发送消息到飞书机器人。
        参数:
        message -- 要发送的消息内容，格式为字典，包含消息类型和内容
        """
        # 消息内容编码
        msg = {
            "msg_type": "text",
            "content": {"text": message}
        }
        msg_encode = json.dumps(msg, ensure_ascii=True).encode("utf-8")

        # 请求头设置
        headers = {
            "Content-type": "application/json",
            "charset": "utf-8"
        }

        # 发送POST请求
        response = requests.post(
            url="https://open.feishu.cn/open-apis/bot/v2/hook/%s" % app_id,
            data=msg_encode,
            headers=headers
        )
        print(response.status_code)
        return response.status_code

    def on_success(self, retval, task_id, args, kwargs):
        info = f'{self.name}任务成功-- task id:{task_id} , arg:{args} , successful !'
        logger.info(info)

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        info = f'{self.name}任务失败-- task id:{task_id} , arg:{args} , failed ! erros: {exc}'
        logger.info(info)
        self.send_feishu_message('运行失败：' + info)

    def on_retry(self, exc, task_id, args, kwargs, einfo):
        info = f'{self.name}task id:{task_id} , arg:{args} , retry !  erros: {exc}'
        logger.info(info)
        self.send_feishu_message('任务重试：' + info)




############################################################医保高铁######################################

@shared_task(base=MyHookTask, name='task_ybgt_nhb_claim_v4_push', max_retries=3, bind=True)  # 绑定任务实例bind=True，使得任务可以重试
def task_ybgt_nhb_claim_v4_push(self):
    """
    医保高铁-南京宁惠保4期理赔分析报告推送
    """
    try:
        from task.tasks.ybgt.ybgt_nhb_claim_v4_push import ybgt_nhb_claim_v4_push
        data = ybgt_nhb_claim_v4_push()
    except Exception as e:
        logger.error(f'任务执行失败，错误信息：{e}')
        raise self.retry(exc=e, countdown=60)


@shared_task(base=MyHookTask, name='医保高铁四期理赔校验')  # 绑定任务实例bind=True，使得任务可以重试
def task_ybgt_nhb_claim_v4_qa():
    """
    医保高铁-南京宁惠保4期理赔分析报告校验
    """
    from task.tasks.ybgt.ybgt_nhb_claim_v4_qa import YbgtNhbClaimV4Qa
    qa = YbgtNhbClaimV4Qa()
    qa.qa_data(product_code=qa.PRODUCT_CODE)



@shared_task(base=MyHookTask, name='医保五期销售数据更新', max_retries=3, bind=True)  # 绑定任务实例bind=True，使得任务可以重试
def task_yb_nhb_insure_v5_push(self):
    """
    医保高铁-南京宁惠保5期销售分析报告推送
    """
    try:
        from task.tasks.ybgt.yb_nhb_insure_v5_push import yb_nhb_insure_v5_push
        data = yb_nhb_insure_v5_push()
    except Exception as e:
        logger.error(f'任务执行失败，错误信息：{e}')
        raise self.retry(exc=e, countdown=60)


############################################################医保高铁######################################

@shared_task(base=MyHookTask, name='宁惠保保司审核超时')
def task_email_nhb_seller_aduit_time():
    from task.tasks.claim.email_nhb_seller_aduit_time import email_nhb_seller_aduit_time
    email_nhb_seller_aduit_time()


# @shared_task(base=MyHookTask, name='宁惠保5期热敷贴活动')
# def task_email_nhb_rft():
#     from task.tasks.insure.activity.email_nhb_insure_v5_rft import email_nhb_rft
#     email_nhb_rft()


# @shared_task(base=MyHookTask, name='宁惠保5期营销-健康管理统计')
# def task_email_nhb_healthy():
#     from task.tasks.insure.activity.email_nhb_insure_v5_healthy import email_nhb_healthy
#     email_nhb_healthy()

# @shared_task(base=MyHookTask, name='宁惠保5期-支付宝数据加密')
# def task_nhb_v5_alipay_main():
#     from task.tasks.insure.nhb_insure_v5_alipay_encryption import alipay_main
    # alipay_main()

############################################################滨州护学保V2数据更新######################################
@shared_task(base=MyHookTask, name='[子任务]滨州护学保V2-缓存-日度数据更新')
def task_update_cache_daily_sale_bz_hxb_v2():
    from transfrom.tasks.insure.bz_hxb_insure_v2 import BzHxbInsureV2
    source = BzHxbInsureV2()
    source.cache_daily_sale() # 日度数据
    source.cache_area_age_gender_count() # 地区年龄性别数据
    source.cache_all_product() # 所有产品数据
    return True


@shared_task(base=MyHookTask, name='[子任务]滨州护学保V2-普通数据更新')
def task_refresh_normal_bz_hxb_v2(temp):
    from task.tasks.insure.bz_hxb_insure_v2_todb import normal_to_db
    normal_to_db()


@shared_task(base=MyHookTask, name='滨州护学保V2-普通')
def task_refresh_normal_bz_hxb_v2_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_bz_hxb_v2.s() | task_refresh_normal_bz_hxb_v2.s()).delay()


@shared_task(base=MyHookTask, name='[子任务]滨州护学保V2-历史数据更新')
def task_refresh_history_bz_hxb_v2(temp):
    from task.tasks.insure.bz_hxb_insure_v2_todb import history_to_db
    history_to_db()


@shared_task(base=MyHookTask, name='滨州护学保V2-历史')
def task_refresh_history_bz_hxb_v2_pipeline():
    """
    链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_bz_hxb_v2.s() | task_refresh_history_bz_hxb_v2.s()).delay()



@shared_task(base=MyHookTask, name='滨州护学保V2-datav数据缓存')
def task_datav_bz_hxb_v2():
    """
    滨州护学保V2-datav数据缓存
    """
    from task.tasks.insure.bz_hxb_insure_v2_datav import DataVBzHxbInsureV2
    source = DataVBzHxbInsureV2()
    source.cache_content()
############################################################滨州护学保V2数据更新######################################
############################################################滨州护学保V1数据更新######################################
@shared_task(base=MyHookTask, name='滨州护学保V1-datav数据缓存')
def task_datav_bz_hxb_v1():
    """
    滨州护学保V1-datav数据缓存
    """
    from task.tasks.insure.bz_hxb_insure_v1_datav import DataVBzHxbInsureV1
    source = DataVBzHxbInsureV1()
    source.cache_content()
############################################################滨州护学保V1数据更新######################################


############################################################南京宁惠保V5数据更新######################################
@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-缓存-日度数据更新')
def task_update_cache_daily_sale_nhb_v5():
    from transfrom.tasks.insure.nhb_insure_v5 import NhbInsureV5
    source = NhbInsureV5()
    source.cache_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-缓存-主要数据更新')
def task_update_cache_main_sale_nhb_v5():
    from transfrom.tasks.insure.nhb_insure_v5 import NhbInsureV5
    source = NhbInsureV5()
    source.cache_main_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-主要数据更新')
def task_refresh_main_nhb_v5(temp):
    from task.tasks.insure.nhb_insure_v5_todb import main_to_db
    main_to_db()


@shared_task(base=MyHookTask, name='南京宁惠保V5-主要')
def task_refresh_main_nhb_v5_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_main_sale_nhb_v5.s() | task_refresh_main_nhb_v5.s()).delay()




@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-普通数据更新')
def task_refresh_normal_nhb_v5(temp):
    from task.tasks.insure.nhb_insure_v5_todb import normal_to_db
    normal_to_db()

@shared_task(base=MyHookTask, name='南京宁惠保V5-普通')
def task_refresh_normal_nhb_v5_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_nhb_v5.s() | task_refresh_normal_nhb_v5.s()).delay()


@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-历史数据更新')
def task_refresh_history_nhb_v5(temp):
    from task.tasks.insure.nhb_insure_v5_todb import history_to_db
    history_to_db()


@shared_task(base=MyHookTask, name='南京宁惠保V5-历史')
def task_refresh_history_nhb_v5_pipeline():
    """
    链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_nhb_v5.s() | task_refresh_history_nhb_v5.s()).delay()

@shared_task(base=MyHookTask, name='南京宁惠保V5-快速')
def task_refresh_fast_nhb_v5():
    from task.tasks.insure.nhb_insure_v5_todb import fast_to_db
    fast_to_db()


@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-缓存-年龄性别数据更新')
def task_update_cache_age_gender_count_nhb_v5():
    from transfrom.tasks.insure.nhb_insure_v5 import NhbInsureV5
    source = NhbInsureV5()
    source.cache_age_gender_count()
    return True


@shared_task(base=MyHookTask, name='[子任务]南京宁惠保V5-慢速数据更新')
def task_refresh_slow_nhb_v5(temp):
    from task.tasks.insure.nhb_insure_v5_todb import slow_to_db
    slow_to_db()


@shared_task(base=MyHookTask, name='南京宁惠保V5-慢速')
def task_refresh_slow_nhb_v5_pipeline():
    """
    链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_age_gender_count_nhb_v5.s() | task_refresh_slow_nhb_v5.s()).delay()


@shared_task(base=MyHookTask, name='南京宁惠保V5-周度', max_retries=3, bind=True)
def task_refresh_weekly_nhb_v5_pipeline(self):
    """
    周度任务，动态目标更新
    """
    try:
        from task.tasks.insure.nhb_insure_v5_todb import weekly_to_db
        weekly_to_db()
    except Exception as e:
        logger.error(f'南京宁惠保V5-周度任务执行失败，错误信息：{e}')
        raise self.retry(exc=e, countdown=60)

@shared_task(base=MyHookTask, name='南京宁惠保V5-datav数据缓存')
def task_datav_nhb_v5():
    """
    南京宁惠保V5-datav数据缓存
    """
    from task.tasks.insure.nhb_insure_v5_datav import DataVNhbInsureV5
    source = DataVNhbInsureV5()
    source.cache_content()
############################################################南京宁惠保V5数据更新######################################




############################################################日照暖心保V4数据更新######################################
# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-缓存-日度数据更新')
# def task_update_cache_daily_sale_nxb_v4():
#     from transfrom.tasks.insure.rz_nxb_insure_v4 import NXBInsureV4
#     source = NXBInsureV4()
#     source.cache_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-缓存-主要数据更新')
# def task_update_cache_main_sale_nxb_v4():
#     from transfrom.tasks.insure.rz_nxb_insure_v4 import NXBInsureV4
#     source = NXBInsureV4()
#     source.cache_main_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-主要数据更新')
# def task_refresh_main_nxb_v4(temp):
#     from task.tasks.insure.rz_nxb_insure_v4_todb import main_to_db
#     main_to_db()


# @shared_task(base=MyHookTask, name='日照暖心保V4-主要')
# def task_refresh_main_nxb_v4_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_main_sale_nxb_v4.s() | task_refresh_main_nxb_v4.s()).delay()




# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-普通数据更新')
# def task_refresh_normal_nxb_v4(temp):
#     from task.tasks.insure.rz_nxb_insure_v4_todb import normal_to_db
#     normal_to_db()

# @shared_task(base=MyHookTask, name='日照暖心保V4-普通')
# def task_refresh_normal_nxb_v4_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_nxb_v4.s() | task_refresh_normal_nxb_v4.s()).delay()


# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-历史数据更新')
# def task_refresh_history_nxb_v4(temp):
#     from task.tasks.insure.rz_nxb_insure_v4_todb import history_to_db
#     history_to_db()


# @shared_task(base=MyHookTask, name='日照暖心保V4-历史')
# def task_refresh_history_nxb_v4_pipeline():
#     """
#     链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_nxb_v4.s() | task_refresh_history_nxb_v4.s()).delay()

# @shared_task(base=MyHookTask, name='日照暖心保V4-快速')
# def task_refresh_fast_nxb_v4():
#     from task.tasks.insure.rz_nxb_insure_v4_todb import fast_to_db
#     fast_to_db()


# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-缓存-年龄性别数据更新')
# def task_update_cache_age_gender_count_nxb_v4():
#     from transfrom.tasks.insure.rz_nxb_insure_v4 import NXBInsureV4
#     source = NXBInsureV4()
#     source.cache_age_gender_count()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]日照暖心保V4-慢速数据更新')
# def task_refresh_slow_nxb_v4(temp):
#     from task.tasks.insure.rz_nxb_insure_v4_todb import slow_to_db
#     slow_to_db()


# @shared_task(base=MyHookTask, name='日照暖心保V4-慢速')
# def task_refresh_slow_nxb_v4_pipeline():
#     """
#     链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_age_gender_count_nxb_v4.s() | task_refresh_slow_nxb_v4.s()).delay()


@shared_task(base=MyHookTask, name='日照暖心保V4-datav数据缓存')
def task_datav_nxb_v4():
    """
    日照暖心保V4-datav数据缓存
    """
    from task.tasks.insure.rz_nxb_insure_v4_datav import DataVNxbInsureV4
    source = DataVNxbInsureV4()
    source.cache_content()
############################################################日照暖心保V4数据更新######################################




############################################################贵惠保V3数据更新######################################
# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-缓存-日度数据更新')
# def task_update_cache_daily_sale_ghb_v3():
#     from transfrom.tasks.insure.gz_ghb_insure_province_v3 import GzGhbInsureProvinceV3
#     source = GzGhbInsureProvinceV3()
#     source.cache_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-缓存-主要数据更新')
# def task_update_cache_main_sale_ghb_v3():
#     from transfrom.tasks.insure.gz_ghb_insure_province_v3 import GzGhbInsureProvinceV3
#     source = GzGhbInsureProvinceV3()
#     source.cache_main_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-主要数据更新')
# def task_refresh_main_ghb_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import main_to_db
#     main_to_db()


# @shared_task(base=MyHookTask, name='贵惠保V3-主要')
# def task_refresh_main_ghb_v3_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_main_sale_ghb_v3.s() | task_refresh_main_ghb_v3.s()).delay()




# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-普通数据更新')
# def task_refresh_normal_ghb_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import normal_to_db
#     normal_to_db()

# @shared_task(base=MyHookTask, name='贵惠保V3-普通')
# def task_refresh_normal_ghb_v3_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_ghb_v3.s() | task_refresh_normal_ghb_v3.s()).delay()


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-历史数据更新')
# def task_refresh_history_ghb_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import history_to_db
#     history_to_db()


# @shared_task(base=MyHookTask, name='贵惠保V3-历史')
# def task_refresh_history_ghb_v3_pipeline():
#     """
#     链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_ghb_v3.s() | task_refresh_history_ghb_v3.s()).delay()

# @shared_task(base=MyHookTask, name='贵惠保V3-快速')
# def task_refresh_fast_ghb_v3():
#     from task.tasks.insure.gz_ghb_insure_v3_todb import fast_to_db
#     fast_to_db()


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-缓存-年龄性别数据更新')
# def task_update_cache_age_gender_count_ghb_v3():
#     from transfrom.tasks.insure.gz_ghb_insure_province_v3 import GzGhbInsureProvinceV3
#     source = GzGhbInsureProvinceV3()
#     source.cache_age_gender_count()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-慢速数据更新')
# def task_refresh_slow_ghb_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import slow_to_db
#     slow_to_db()


# @shared_task(base=MyHookTask, name='贵惠保V3-慢速')
# def task_refresh_slow_ghb_v3_pipeline():
#     """
#     链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_age_gender_count_ghb_v3.s() | task_refresh_slow_ghb_v3.s()).delay()
# #######################贵惠保V3-分城市数据更新###########################
# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-缓存-分城市日度数据更新')
# def task_update_cache_daily_sale_ghb_city_v3():
#     from transfrom.tasks.insure.gz_ghb_insure_city_v3 import GzGhbInsureCityV3
#     source = GzGhbInsureCityV3()
#     source.cache_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-缓存-分城市主要数据更新')
# def task_update_cache_main_sale_ghb_city_v3():
#     from transfrom.tasks.insure.gz_ghb_insure_city_v3 import GzGhbInsureCityV3
#     source = GzGhbInsureCityV3()
#     source.cache_main_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-分城市主要数据更新')
# def task_refresh_main_ghb_city_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import city_main_to_db
#     city_main_to_db()


# @shared_task(base=MyHookTask, name='贵惠保V3-分城市主要')
# def task_refresh_main_ghb_city_v3_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_main_sale_ghb_city_v3.s() | task_refresh_main_ghb_city_v3.s()).delay()




# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-分城市普通数据更新')
# def task_refresh_normal_ghb_city_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import city_normal_to_db
#     city_normal_to_db()

# @shared_task(base=MyHookTask, name='贵惠保V3-分城市普通')
# def task_refresh_normal_ghb_city_v3_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_ghb_city_v3.s() | task_refresh_normal_ghb_city_v3.s()).delay()


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-分城市历史数据更新')
# def task_refresh_history_ghb_city_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import city_history_to_db
#     city_history_to_db()


# @shared_task(base=MyHookTask, name='贵惠保V3-分城市历史')
# def task_refresh_history_ghb_city_v3_pipeline():
#     """
#     链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_ghb_city_v3.s() | task_refresh_history_ghb_city_v3.s()).delay()

# @shared_task(base=MyHookTask, name='贵惠保V3-分城市快速')
# def task_refresh_fast_ghb_city_v3():
#     from task.tasks.insure.gz_ghb_insure_v3_todb import city_fast_to_db
#     city_fast_to_db()


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-缓存-分城市年龄性别数据更新')
# def task_update_cache_age_gender_count_ghb_city_v3():
#     from transfrom.tasks.insure.gz_ghb_insure_city_v3 import GzGhbInsureCityV3
#     source = GzGhbInsureCityV3()
#     source.cache_age_gender_count()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]贵惠保V3-分城市慢速数据更新')
# def task_refresh_slow_ghb_city_v3(temp):
#     from task.tasks.insure.gz_ghb_insure_v3_todb import city_slow_to_db
#     city_slow_to_db()


# @shared_task(base=MyHookTask, name='贵惠保V3-分城市慢速')
# def task_refresh_slow_ghb_city_v3_pipeline():
#     """
#     链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_age_gender_count_ghb_city_v3.s() | task_refresh_slow_ghb_city_v3.s()).delay()
#######################贵惠保V3-分城市数据更新###########################
@shared_task(base=MyHookTask, name='贵惠保V3-datav数据缓存')
def task_datav_ghb_v3():
    """
    贵惠保V3-datav数据缓存
    """
    from task.tasks.insure.gz_ghb_insure_v3_datav import DataVGhbInsureV3
    source = DataVGhbInsureV3()
    source.cache_content()

@shared_task(base=MyHookTask, name='贵惠保V2-datav数据缓存')
def task_datav_ghb_v2():
    """
    贵惠保V2-datav数据缓存
    """
    from task.tasks.insure.gz_ghb_insure_v2_datav import DataVGhbInsureV2
    source = DataVGhbInsureV2()
    source.cache_content()
############################################################贵惠保V3数据更新######################################




############################################################滨州医惠保V4数据更新######################################
# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-缓存-日度数据更新')
# def task_update_cache_daily_sale_bz_yhb_v4():
#     from transfrom.tasks.insure.bz_yhb_insure_v4 import BzYhbInsureV4
#     source = BzYhbInsureV4()
#     source.cache_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-缓存-主要数据更新')
# def task_update_cache_main_sale_bz_yhb_v4():
#     from transfrom.tasks.insure.bz_yhb_insure_v4 import BzYhbInsureV4
#     source = BzYhbInsureV4()
#     source.cache_main_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-主要数据更新')
# def task_refresh_main_bz_yhb_v4(temp):
#     from task.tasks.insure.bz_yhb_insure_v4_todb import main_to_db
#     main_to_db()


# @shared_task(base=MyHookTask, name='滨州医惠保V4-主要')
# def task_refresh_main_bz_yhb_v4_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_main_sale_bz_yhb_v4.s() | task_refresh_main_bz_yhb_v4.s()).delay()




# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-普通数据更新')
# def task_refresh_normal_bz_yhb_v4(temp):
#     from task.tasks.insure.bz_yhb_insure_v4_todb import normal_to_db
#     normal_to_db()

# @shared_task(base=MyHookTask, name='滨州医惠保V4-普通')
# def task_refresh_normal_bz_yhb_v4_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_bz_yhb_v4.s() | task_refresh_normal_bz_yhb_v4.s()).delay()


# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-历史数据更新')
# def task_refresh_history_bz_yhb_v4(temp):
#     from task.tasks.insure.bz_yhb_insure_v4_todb import history_to_db
#     history_to_db()


# @shared_task(base=MyHookTask, name='滨州医惠保V4-历史')
# def task_refresh_history_bz_yhb_v4_pipeline():
#     """
#     链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_bz_yhb_v4.s() | task_refresh_history_bz_yhb_v4.s()).delay()

# @shared_task(base=MyHookTask, name='滨州医惠保V4-快速')
# def task_refresh_fast_bz_yhb_v4():
#     from task.tasks.insure.bz_yhb_insure_v4_todb import fast_to_db
#     fast_to_db()


# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-缓存-年龄性别数据更新')
# def task_update_cache_age_gender_count_bz_yhb_v4():
#     from transfrom.tasks.insure.bz_yhb_insure_v4 import BzYhbInsureV4
#     source = BzYhbInsureV4()
#     source.cache_age_gender_count()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]滨州医惠保V4-慢速数据更新')
# def task_refresh_slow_bz_yhb_v4(temp):
#     from task.tasks.insure.bz_yhb_insure_v4_todb import slow_to_db
#     slow_to_db()


# @shared_task(base=MyHookTask, name='滨州医惠保V4-慢速')
# def task_refresh_slow_bz_yhb_v4_pipeline():
#     """
#     链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_age_gender_count_bz_yhb_v4.s() | task_refresh_slow_bz_yhb_v4.s()).delay()


@shared_task(base=MyHookTask, name='滨州医惠保V4-datav数据缓存')
def task_datav_bz_yhb_v4():
    """
    滨州医惠保V4-datav数据缓存
    """
    from task.tasks.insure.bz_yhb_insure_v4_datav import DataVBzYhbInsureV4
    source = DataVBzYhbInsureV4()
    source.cache_content()
############################################################滨州医惠保V4数据更新######################################



############################################################德州惠民保V3数据更新######################################
# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-缓存-日度数据更新')
# def task_update_cache_daily_sale_dz_hmb_v3():
#     from transfrom.tasks.insure.dz_hmb_insure_v3 import DzHmbInsureV3
#     source = DzHmbInsureV3()
#     source.cache_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-缓存-主要数据更新')
# def task_update_cache_main_sale_dz_hmb_v3():
#     from transfrom.tasks.insure.dz_hmb_insure_v3 import DzHmbInsureV3
#     source = DzHmbInsureV3()
#     source.cache_main_daily_sale()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-主要数据更新')
# def task_refresh_main_dz_hmb_v3(temp):
#     from task.tasks.insure.dz_hmb_insure_v3_todb import main_to_db
#     main_to_db()


# @shared_task(base=MyHookTask, name='德州惠民保V3-主要')
# def task_refresh_main_dz_hmb_v3_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_main_sale_dz_hmb_v3.s() | task_refresh_main_dz_hmb_v3.s()).delay()




# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-普通数据更新')
# def task_refresh_normal_dz_hmb_v3(temp):
#     from task.tasks.insure.dz_hmb_insure_v3_todb import normal_to_db
#     normal_to_db()

# @shared_task(base=MyHookTask, name='德州惠民保V3-普通')
# def task_refresh_normal_dz_hmb_v3_pipeline():
#     """
#     链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_dz_hmb_v3.s() | task_refresh_normal_dz_hmb_v3.s()).delay()


# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-历史数据更新')
# def task_refresh_history_dz_hmb_v3(temp):
#     from task.tasks.insure.dz_hmb_insure_v3_todb import history_to_db
#     history_to_db()


# @shared_task(base=MyHookTask, name='德州惠民保V3-历史')
# def task_refresh_history_dz_hmb_v3_pipeline():
#     """
#     链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_daily_sale_dz_hmb_v3.s() | task_refresh_history_dz_hmb_v3.s()).delay()

# @shared_task(base=MyHookTask, name='德州惠民保V3-快速')
# def task_refresh_fast_dz_hmb_v3():
#     from task.tasks.insure.dz_hmb_insure_v3_todb import fast_to_db
#     fast_to_db()


# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-缓存-年龄性别数据更新')
# def task_update_cache_age_gender_count_dz_hmb_v3():
#     from transfrom.tasks.insure.dz_hmb_insure_v3 import DzHmbInsureV3
#     source = DzHmbInsureV3()
#     source.cache_age_gender_count()
#     return True


# @shared_task(base=MyHookTask, name='[子任务]德州惠民保V3-慢速数据更新')
# def task_refresh_slow_dz_hmb_v3(temp):
#     from task.tasks.insure.dz_hmb_insure_v3_todb import slow_to_db
#     slow_to_db()


# @shared_task(base=MyHookTask, name='德州惠民保V3-慢速')
# def task_refresh_slow_dz_hmb_v3_pipeline():
#     """
#     链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
#     """
#     return (task_update_cache_age_gender_count_dz_hmb_v3.s() | task_refresh_slow_dz_hmb_v3.s()).delay()


@shared_task(base=MyHookTask, name='德州惠民保V3-datav数据缓存')
def task_datav_dz_hmb_v3():
    """
    滨州医惠保V4-datav数据缓存
    """
    from task.tasks.insure.dz_hmb_insure_v3_datav import DataVDzHmbInsureV3
    source = DataVDzHmbInsureV3()
    source.cache_content()


# @shared_task(base=MyHookTask, name='德州惠民保V3-周报数据')
# def task_email_dz_hmb_v3_weekly_report():
#     from task.tasks.insure.dz_hmb_insure_v3_weekly_report import email_dz_hmb
#     email_dz_hmb()


# @shared_task(base=MyHookTask, name='德州惠民保V3-日报数据')
# def task_email_dz_hmb_v3_daily_report():
#     from task.tasks.insure.dz_hmb_insure_v3_daily_report import email_dz_hmb
#     email_dz_hmb()
############################################################德州惠民保V3数据更新######################################


# @shared_task(base=MyHookTask, name='山东惠民保-周报数据')
# def task_email_shandong_hmb_weekly_report():
#     from task.tasks.insure.report.email_shandong_hmb_insure import email_week_report
#     email_week_report()



############################################################住院津贴数据更新######################################
@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-缓存-日度数据更新')
def task_update_cache_daily_sale_nj_dha_v1():
    from transfrom.tasks.insure.nj_dha_insure_v1 import NjDhaInsureV1
    source = NjDhaInsureV1()
    source.cache_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-缓存-主要数据更新')
def task_update_cache_main_sale_nj_dha_v1():
    from transfrom.tasks.insure.nj_dha_insure_v1 import NjDhaInsureV1
    source = NjDhaInsureV1()
    source.cache_main_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-主要数据更新')
def task_refresh_main_nj_dha_v1(temp):
    from task.tasks.insure.nj_dha_insure_v1_todb import main_to_db
    main_to_db()


@shared_task(base=MyHookTask, name='住院津贴V1-主要')
def task_refresh_main_nj_dha_v1_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_main_sale_nj_dha_v1.s() | task_refresh_main_nj_dha_v1.s()).delay()




@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-普通数据更新')
def task_refresh_normal_nj_dha_v1(temp):
    from task.tasks.insure.nj_dha_insure_v1_todb import normal_to_db
    normal_to_db()

@shared_task(base=MyHookTask, name='住院津贴V1-普通')
def task_refresh_normal_nj_dha_v1_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_nj_dha_v1.s() | task_refresh_normal_nj_dha_v1.s()).delay()


@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-历史数据更新')
def task_refresh_history_nj_dha_v1(temp):
    from task.tasks.insure.nj_dha_insure_v1_todb import history_to_db
    history_to_db()


@shared_task(base=MyHookTask, name='住院津贴V1-历史')
def task_refresh_history_nj_dha_v1_pipeline():
    """
    链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_nj_dha_v1.s() | task_refresh_history_nj_dha_v1.s()).delay()

@shared_task(base=MyHookTask, name='住院津贴V1-快速')
def task_refresh_fast_nj_dha_v1():
    from task.tasks.insure.nj_dha_insure_v1_todb import fast_to_db
    fast_to_db()


@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-缓存-年龄性别数据更新')
def task_update_cache_age_gender_count_nj_dha_v1():
    from transfrom.tasks.insure.nj_dha_insure_v1 import NjDhaInsureV1
    source = NjDhaInsureV1()
    source.cache_age_gender_count()
    return True


@shared_task(base=MyHookTask, name='[子任务]住院津贴V1-慢速数据更新')
def task_refresh_slow_nj_dha_v1(temp):
    from task.tasks.insure.nj_dha_insure_v1_todb import slow_to_db
    slow_to_db()


@shared_task(base=MyHookTask, name='住院津贴V1-慢速')
def task_refresh_slow_nj_dha_v1_pipeline():
    """
    链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_age_gender_count_nj_dha_v1.s() | task_refresh_slow_nj_dha_v1.s()).delay()


@shared_task(base=MyHookTask, name='住院津贴V1-datav数据缓存')
def task_datav_nj_dha_v1():
    """
    住院津贴V1-datav数据缓存
    """
    from task.tasks.insure.nj_dha_insure_v1_datav import DataVNjDhaInsureV1
    source = DataVNjDhaInsureV1()
    source.cache_content()
############################################################住院津贴数据更新######################################

############################################################江苏门诊保数据更新######################################
@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-缓存-日度数据更新')
def task_update_cache_daily_sale_js_op_v1():
    from transfrom.tasks.insure.js_op_insure_v1 import JsOpInsureV1
    source = JsOpInsureV1()
    source.cache_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-缓存-主要数据更新')
def task_update_cache_main_sale_js_op_v1():
    from transfrom.tasks.insure.js_op_insure_v1 import JsOpInsureV1
    source = JsOpInsureV1()
    source.cache_main_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-主要数据更新')
def task_refresh_main_js_op_v1(temp):
    from task.tasks.insure.js_op_insure_v1_todb import main_to_db
    main_to_db()


@shared_task(base=MyHookTask, name='江苏门诊保V1-主要')
def task_refresh_main_js_op_v1_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_main_sale_js_op_v1.s() | task_refresh_main_js_op_v1.s()).delay()




@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-普通数据更新')
def task_refresh_normal_js_op_v1(temp):
    from task.tasks.insure.js_op_insure_v1_todb import normal_to_db
    normal_to_db()

@shared_task(base=MyHookTask, name='江苏门诊保V1-普通')
def task_refresh_normal_js_op_v1_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_js_op_v1.s() | task_refresh_normal_js_op_v1.s()).delay()


@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-历史数据更新')
def task_refresh_history_js_op_v1(temp):
    from task.tasks.insure.js_op_insure_v1_todb import history_to_db
    history_to_db()


@shared_task(base=MyHookTask, name='江苏门诊保V1-历史')
def task_refresh_history_js_op_v1_pipeline():
    """
    链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_js_op_v1.s() | task_refresh_history_js_op_v1.s()).delay()

@shared_task(base=MyHookTask, name='江苏门诊保V1-快速')
def task_refresh_fast_js_op_v1():
    from task.tasks.insure.js_op_insure_v1_todb import fast_to_db
    fast_to_db()


@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-缓存-年龄性别数据更新')
def task_update_cache_age_gender_count_js_op_v1():
    from transfrom.tasks.insure.js_op_insure_v1 import JsOpInsureV1
    source = JsOpInsureV1()
    source.cache_age_gender_count()
    return True


@shared_task(base=MyHookTask, name='[子任务]江苏门诊保V1-慢速数据更新')
def task_refresh_slow_js_op_v1(temp):
    from task.tasks.insure.js_op_insure_v1_todb import slow_to_db
    slow_to_db()


@shared_task(base=MyHookTask, name='江苏门诊保V1-慢速')
def task_refresh_slow_js_op_v1_pipeline():
    """
    链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_age_gender_count_js_op_v1.s() | task_refresh_slow_js_op_v1.s()).delay()


@shared_task(base=MyHookTask, name='江苏门诊保V1-datav数据缓存')
def task_datav_js_op_v1():
    """
    江苏门诊保V1-datav数据缓存
    """
    from task.tasks.insure.js_op_insure_v1_datav import DataVJsOpInsureV1
    source = DataVJsOpInsureV1()
    source.cache_content()
############################################################江苏门诊保数据更新######################################




############################################################南京宁护保数据更新######################################
@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-缓存-日度数据更新')
def task_update_cache_daily_sale_nj_nhb_v1():
    from transfrom.tasks.insure.nj_nhb_insure_v1 import NjNhbInsureV1
    source = NjNhbInsureV1()
    source.cache_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-缓存-主要数据更新')
def task_update_cache_main_sale_nj_nhb_v1():
    from transfrom.tasks.insure.nj_nhb_insure_v1 import NjNhbInsureV1
    source = NjNhbInsureV1()
    source.cache_main_daily_sale()
    return True


@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-主要数据更新')
def task_refresh_main_nj_nhb_v1(temp):
    from task.tasks.insure.nj_nhb_insure_v1_todb import main_to_db
    main_to_db()


@shared_task(base=MyHookTask, name='南京宁护保V1-主要')
def task_refresh_main_nj_nhb_v1_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_main_sale_nj_nhb_v1.s() | task_refresh_main_nj_nhb_v1.s()).delay()




@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-普通数据更新')
def task_refresh_normal_nj_nhb_v1(temp):
    from task.tasks.insure.nj_nhb_insure_v1_todb import normal_to_db
    normal_to_db()

@shared_task(base=MyHookTask, name='南京宁护保V1-普通')
def task_refresh_normal_nj_nhb_v1_pipeline():
    """
    链式任务，普通速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_nj_nhb_v1.s() | task_refresh_normal_nj_nhb_v1.s()).delay()


@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-历史数据更新')
def task_refresh_history_nj_nhb_v1(temp):
    from task.tasks.insure.nj_nhb_insure_v1_todb import history_to_db
    history_to_db()


@shared_task(base=MyHookTask, name='南京宁护保V1-历史')
def task_refresh_history_nj_nhb_v1_pipeline():
    """
    链式任务，更新历史数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_daily_sale_nj_nhb_v1.s() | task_refresh_history_nj_nhb_v1.s()).delay()

@shared_task(base=MyHookTask, name='南京宁护保V1-快速')
def task_refresh_fast_nj_nhb_v1():
    from task.tasks.insure.nj_nhb_insure_v1_todb import fast_to_db
    fast_to_db()


@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-缓存-年龄性别数据更新')
def task_update_cache_age_gender_count_nj_nhb_v1():
    from transfrom.tasks.insure.nj_nhb_insure_v1 import NjNhbInsureV1
    source = NjNhbInsureV1()
    source.cache_age_gender_count()
    return True


@shared_task(base=MyHookTask, name='[子任务]南京宁护保V1-慢速数据更新')
def task_refresh_slow_nj_nhb_v1(temp):
    from task.tasks.insure.nj_nhb_insure_v1_todb import slow_to_db
    slow_to_db()


@shared_task(base=MyHookTask, name='南京宁护保V1-慢速')
def task_refresh_slow_nj_nhb_v1_pipeline():
    """
    链式任务，慢速度更新数据，只有缓存数据处理好后，才会执行后面的任务
    """
    return (task_update_cache_age_gender_count_nj_nhb_v1.s() | task_refresh_slow_nj_nhb_v1.s()).delay()


@shared_task(base=MyHookTask, name='南京宁护保V1-datav数据缓存')
def task_datav_nj_nhb_v1():
    """
    南京宁护保V1-datav数据缓存
    """
    from task.tasks.insure.nj_nhb_insure_v1_datav import DataVNjNhbInsureV1
    source = DataVNjNhbInsureV1()
    source.cache_content()
############################################################南京宁护保数据更新######################################


@shared_task(base=MyHookTask, name='圆心项目保司审核超时')
def task_email_yx_seller_aduit_time():
    from task.tasks.claim.email_yx_seller_aduit_time import email_yx_seller_aduit_time
    email_yx_seller_aduit_time()


@shared_task(base=MyHookTask, name='医保特药实体表字段更新')
def task_yb_entity_update(self):
    """
    医保特药实体表字段更新
    """
    from transfrom.tasks.spider.data_sync.update_drug_entity_fields import main
    main()


@shared_task(base=MyHookTask, name='节假日数据同步')
def task_sync_holiday_data():
    """
    从nhb数据库的drug_holiday_data表同步节假日数据到public_holiday表
    """
    from django.db import connections, transaction
    from public.models import PublicHoliday
    from transfrom.utils.utils import custom_update_or_create
    import logging

    logger = logging.getLogger(__name__)

    try:
        # 获取nhb数据库连接
        cursor = connections['nhb'].cursor()

        # 查询源表数据
        cursor.execute("SELECT date, is_work FROM drug_holiday_data ORDER BY date")
        source_data = cursor.fetchall()

        if not source_data:
            logger.warning("nhb.drug_holiday_data表中没有数据")
            return

        # 获取源表中的所有日期
        source_dates = {row[0] for row in source_data}

        # 获取目标表中的所有日期
        existing_dates = set(PublicHoliday.objects.values_list('date', flat=True))

        # 找出需要删除的记录（目标表中有但源表中没有的日期）
        dates_to_delete = existing_dates - source_dates
        if dates_to_delete:
            deleted_count = PublicHoliday.objects.filter(date__in=dates_to_delete).delete()[0]
            logger.info(f"删除了 {deleted_count} 条过期的节假日记录")

        # 同步数据
        sync_count = 0
        with transaction.atomic():
            for date_str, is_work_str in source_data:
                # 处理日期格式
                if isinstance(date_str, str):
                    from datetime import datetime
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
                else:
                    date_obj = date_str

                # 处理is_work字段：直接使用字符串值，'1'表示工作日，'0'表示节假日
                is_work_value = str(is_work_str) if is_work_str is not None else '1'

                # 使用custom_update_or_create同步数据
                obj = custom_update_or_create(
                    model_class=PublicHoliday,
                    date=date_obj,
                    defaults={
                        'is_work': is_work_value
                    }
                )
                sync_count += 1

        logger.info(f"节假日数据同步完成，共处理 {sync_count} 条记录")

    except Exception as e:
        logger.error(f"节假日数据同步失败：{str(e)}")
        raise
