credentials:
  usernames:
    admin:
      email: <EMAIL>
      failed_login_attempts: 0 # Will be managed automatically
      logged_in: False # Will be managed automatically
      name: admin
      password: EwT6sYmDNks9aSq3 # Will be hashed automatically
    guihuibao:
      email: g<PERSON><PERSON><EMAIL>
      failed_login_attempts: 0 # Will be managed automatically
      logged_in: False # Will be managed automatically
      name: guihuibao
      password: wTVDx7MOIgmUqnjc # Will be hashed automatically
cookie:
  expiry_days: 30
  key: "tde!zkh@QFA4w264yre" # Must be string
  name: "GUIHUIBAO_REPORT"
pre-authorized:
  emails:
  - <EMAIL>