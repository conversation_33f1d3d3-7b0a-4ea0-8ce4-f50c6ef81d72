{% extends "public/data_dictionary/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
/* 页面布局优化 */
.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 增加主内容区域与导航栏的间距 */
main {
    padding-top: 30px;
}

.breadcrumb {
    margin-bottom: 15px;
    padding: 0;
    background: transparent;
}

.search-box {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-card {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
    cursor: pointer;
}

.table-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    min-height: 40px;
}

.table-name {
    font-size: 1.2em;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    word-break: break-all;
    overflow-wrap: break-word;
    line-height: 1.3;
    max-width: 100%;
    display: block;
}

.table-name:hover {
    color: #1890ff;
    text-decoration: none;
}

.table-name-container {
    flex: 1;
    margin-right: 10px;
    min-width: 0;
}

.table-type {
    background: #f0f0f0;
    color: #666;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    white-space: nowrap;
    flex-shrink: 0;
}

.table-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.2em;
    font-weight: bold;
    color: #1890ff;
}

.stat-label {
    font-size: 0.8em;
    color: #666;
}

.table-description {
    color: #666;
    margin-top: 10px;
    font-size: 0.9em;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.filter-options {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.deprecated-badge {
    background: #ff4d4f;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.7em;
}

.table-unique-key {
    margin-top: 10px;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.85em;
    border-left: 3px solid #1890ff;
}

.unique-key-label {
    font-weight: 500;
    color: #666;
    margin-right: 8px;
}

.unique-key-value {
    color: #333;
    font-family: 'Courier New', monospace;
    background: #fff;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #e8e8e8;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'data_dictionary:database_list' %}">数据字典</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ database.name }}数据库</li>
                </ol>
            </nav>

    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>{{ database.name }} 数据库</h1>
        <div>
            <a href="{% url 'data_dictionary:global_search' %}" class="btn btn-outline-primary">
                <i class="fas fa-search"></i> 全局搜索
            </a>
        </div>
    </div>

    <!-- 数据库信息 -->
    <div class="search-box">
        <div class="row">
            <div class="col-md-3">
                <strong>数据库类型:</strong> {{ database.type }}
            </div>
            <div class="col-md-3">
                <strong>数据来源:</strong> {{ database.data_source|default:"未知" }}
            </div>
            <div class="col-md-3">
                <strong>更新频率:</strong> {{ database.update_frequency|default:"未知" }}
            </div>
            <div class="col-md-3">
                <strong>总表数:</strong> {{ tables.paginator.count }}
            </div>
        </div>
        {% if database.description %}
        <div class="row mt-2">
            <div class="col-12">
                <strong>描述:</strong> {{ database.description }}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 搜索和过滤 -->
    <div class="search-box">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <input type="text" class="form-control" name="search" value="{{ search }}" 
                       placeholder="搜索表名或注释...">
            </div>
            <div class="col-md-2">
                <select class="form-select" name="type">
                    <option value="">所有类型</option>
                    {% for type in table_types %}
                    <option value="{{ type }}" {% if type == table_type %}selected{% endif %}>{{ type }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="deprecated" value="true" 
                           {% if show_deprecated %}checked{% endif %} id="showDeprecated">
                    <label class="form-check-label" for="showDeprecated">
                        显示废弃表
                    </label>
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>
            <div class="col-md-2">
                <a href="{% url 'data_dictionary:database_detail' database.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-refresh"></i> 重置
                </a>
            </div>
        </form>
    </div>

    <!-- 表列表 -->
    {% if tables %}
        <div class="row">
            {% for table in tables %}
            <div class="col-lg-6 col-xl-4">
                <div class="table-card" data-href="{% url 'data_dictionary:table_detail' table.id %}" style="cursor: pointer;">
                    <div class="table-header">
                        <div class="table-name-container">
                            <a href="{% url 'data_dictionary:table_detail' table.id %}" class="table-name">
                                {{ table.name }}
                                {% if table.is_deprecated %}
                                    <span class="deprecated-badge">已废弃</span>
                                {% endif %}
                            </a>
                        </div>
                        <span class="table-type">{{ table.type }}</span>
                    </div>
                    
                    <div class="table-stats">
                        <div class="stat-item">
                            <div class="stat-number">{{ table.column_count }}</div>
                            <div class="stat-label">字段数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ table.index_count }}</div>
                            <div class="stat-label">索引数</div>
                        </div>
                    </div>

                    {% if table.unique_key_display.display != '无' %}
                    <div class="table-unique-key">
                        <span class="unique-key-label">
                            <i class="fas fa-key text-warning"></i> 唯一键:
                        </span>
                        <span class="unique-key-value">{{ table.unique_key_display.display }}</span>
                    </div>
                    {% endif %}
                    
                    {% if table.comment %}
                    <div class="table-description">
                        <strong>{{ table.comment }}</strong>
                    </div>
                    {% endif %}
                    
                    {% if table.description %}
                    <div class="table-description">
                        {{ table.description|truncatechars:100 }}
                    </div>
                    {% endif %}
                    
                    {% if table.data_source %}
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-database"></i> 数据来源: {{ table.data_source }}
                        </small>
                    </div>
                    {% endif %}
                    
                    {% if table.update_frequency %}
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> 更新频率: {{ table.update_frequency }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if tables.has_other_pages %}
        <div class="pagination-wrapper">
            <nav aria-label="表列表分页">
                <ul class="pagination">
                    {% if tables.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ tables.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if table_type %}&type={{ table_type }}{% endif %}{% if show_deprecated %}&deprecated=true{% endif %}">上一页</a>
                        </li>
                    {% endif %}
                    
                    {% for num in tables.paginator.page_range %}
                        {% if num == tables.number %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if table_type %}&type={{ table_type }}{% endif %}{% if show_deprecated %}&deprecated=true{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if tables.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ tables.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if table_type %}&type={{ table_type }}{% endif %}{% if show_deprecated %}&deprecated=true{% endif %}">下一页</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    {% else %}
        <div class="empty-state">
            <i class="fas fa-table"></i>
            <h3>暂无表信息</h3>
            <p>该数据库中暂无表信息，或者所有表都被过滤掉了</p>
        </div>
    {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 添加卡片点击效果
    $('.table-card').click(function(e) {
        if (!$(e.target).is('a')) {
            var link = $(this).data('href') || $(this).find('.table-name').attr('href');
            if (link) {
                window.location.href = link;
            }
        }
    });
    
    // 搜索框回车提交
    $('input[name="search"]').keypress(function(e) {
        if (e.which === 13) {
            $(this).closest('form').submit();
        }
    });
    
    // 添加键盘快捷键支持
    $(document).keydown(function(e) {
        // Ctrl+F 聚焦搜索框
        if (e.ctrlKey && e.keyCode === 70) {
            e.preventDefault();
            $('input[name="search"]').focus();
        }
    });
});
</script>
{% endblock %}
