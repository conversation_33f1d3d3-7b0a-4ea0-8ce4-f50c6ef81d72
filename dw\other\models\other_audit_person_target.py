from django.db import models
from common.models import BaseModel


class OtherAuditPersonTarget(BaseModel):
    product_serial_code = models.CharField(max_length=200, blank=True, null=True, verbose_name='产品系列编码')
    name = models.CharField(max_length=512, blank=True, null=True, verbose_name='审核人员姓名')
    is_review = models.IntegerField(blank=True, null=True, verbose_name='是否复审')
    workload_target = models.FloatField(blank=True, null=True, verbose_name='工作量指标')

    class Meta:
        db_table = 'other_audit_person_target'
        verbose_name = '理赔-审核人员产品工作量指标'
        verbose_name_plural = verbose_name
