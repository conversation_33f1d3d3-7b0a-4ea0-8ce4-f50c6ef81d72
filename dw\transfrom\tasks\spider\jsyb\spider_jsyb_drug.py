import io
import sys
# 设置标准输出和标准错误的编码为 UTF-8
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
import asyncio
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from crawl4ai import AsyncWebCrawler
import json
import time
import os
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('crawler.log'),
        logging.StreamHandler()
    ]
)
import re
import random
from selenium.common.exceptions import TimeoutException, WebDriverException

# 在文件顶部添加新的导入
# 修改 setup_driver 函数
def extract_table_data(driver):
    """提取表格数据"""
    try:
        # 获取表头
        header = []
        header_elements = driver.find_elements(By.XPATH, '//table//thead//th')
        for th in header_elements:
            header.append(th.text.strip())

        # 获取表格内容
        data = []
        rows = driver.find_elements(By.XPATH, '//table//tbody//tr')
        for row in rows:
            cells = row.find_elements(By.TAG_NAME, 'td')
            row_data = [cell.text.strip() for cell in cells]
            if row_data:
                data.append(row_data)
        return  data
    except Exception as e:
        print(f"[ERROR] 提取表格数据时发生错误: {str(e)}")
        # 记录详细错误信息
        logging.error(f"提取表格数据失败: {str(e)}", exc_info=True)
        return None

def setup_driver():
    """设置并返回Chrome驱动"""
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--disable-gpu')
    options.add_argument('--log-level=3')
    
    # 增加反爬虫配置
    options.add_argument('--disable-blink-features=AutomationControlled')
    options.add_experimental_option('excludeSwitches', ['enable-automation'])
    options.add_experimental_option('useAutomationExtension', False)
    
    # 随机User-Agent
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Edge/91.0.864.48'
    ]
    selected_ua = random.choice(user_agents)
    options.add_argument(f'user-agent={selected_ua}')
    logging.info(f"使用User-Agent: {selected_ua[:30]}...")
    
    return webdriver.Chrome(
        service=Service(ChromeDriverManager().install()),
        options=options
    )


async def get_total_pages():
    """获取总页数和总数据量"""
    try:
        # 每次创建新的crawler实例
        async with AsyncWebCrawler() as crawler:
            # 使用AsyncWebCrawler获取当前页面数据
            result = await crawler.arun(
                url="https://ybj.jszwfw.gov.cn/hsa-local/web/hallPersonal/#/ggcxDrugsSJ"
            )
            if result and result.markdown:
                # 处理数据
                markdown_text=result.markdown
                lines = markdown_text.split('\n')
                header = ['序号', '医保药品名称', '医保支付类别', '医保剂型', '注册名称', '实际规格', '最小包装数量', '省集中采购上限价', '批准文号', '药品企业', '医保限定支付范围', '国家药品代码', '个人先行自付比例']
                data = []
                total_pages = 0
                total_records_match = re.search(r'总共(\d+)条', markdown_text)
                total_records = int(total_records_match.group(1)) if total_records_match else 0
                for line in lines:
                    if '医保药品名称|' in line:
                        header = [col.strip() for col in line.split('|')]
                # 解析分页信息
                page_numbers = re.findall(r'\* (\d+)', markdown_text)
                if page_numbers:
                    total_pages = max(map(int, page_numbers))
                else:
                    total_pages = 100
                print(f"总页数: {total_pages}, 总记录数: {total_records}")
                return total_pages, total_records,header
            else:
                header = ['序号', '医保药品名称', '医保支付类别', '医保剂型', '注册名称', '实际规格', '最小包装数量', '省集中采购上限价', '批准文号', '药品企业', '医保限定支付范围', '国家药品代码', '个人先行自付比例']

                print("未获取到页面数据")
                return 6915, None,header
    except Exception as e:
        header = ['序号', '医保药品名称', '医保支付类别', '医保剂型', '注册名称', '实际规格', '最小包装数量', '省集中采购上限价', '批准文号', '药品企业', '医保限定支付范围', '国家药品代码', '个人先行自付比例']
        print(f"末页跳转方式失败: {e}")
        print("使用默认估计值100页")
        return 6915, None,header


# 在 crawl_all_pages 函数中添加代理监控
async def crawl_all_pages():
    """爬取所有页面数据"""
    driver = None
    all_data = []
    header = None
    batch_size = 10
    retry_count = 3
    page_counter = 0  # 页面计数器
    # 添加时间统计变量
    start_time = time.time()
    page_times = []
    total_pages = None  # 总页数，初始为None
    total_items = None  # 总数据条数，初始为None
    
    # 添加页面追踪变量
    crawled_pages = set()  # 已爬取页面集合
    page_data_counts = {}  # 每页数据条数记录
    
    
    try:
        driver = setup_driver()
        
        # 访问目标网页
        print("\n" + "="*20 + " 开始访问目标网站 " + "="*20)
        driver.get("https://ybj.jszwfw.gov.cn/hsa-local/web/hallPersonal/#/ggcxDrugsSJ")
        
        # 等待页面加载
        WebDriverWait(driver, 20).until(
            lambda d: d.execute_script('return document.readyState') == 'complete'
        )
        time.sleep(5)
        
        # 获取总页数和总数据量
        total_pages, total_items,page_header = await get_total_pages()
        
        current_page = 1
        batch_data = []
        last_row_number = 0
        
        while True:
            # 添加页码比较检查
            if current_page > total_pages:
                print(f"已达到最大页数 {total_pages}，爬取完成")
                break
                
            # 记录当前页开始爬取时间
            page_start_time = time.time()
            
            # 记录已爬取的页面
            crawled_pages.add(current_page)

            print(f"\n{'='*50}")
            print(f"正在爬取第 {current_page}/{total_pages} 页...")
            print(f"{'='*50}")
            
            # 等待表格数据加载完成
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//table//tbody//tr[1]"))
            )
            time.sleep(2)  # 额外等待确保数据加载
            
            # 添加重试机制
            for retry in range(retry_count):
                page_data = extract_table_data(driver)
                if page_data:
                    # 验证数据连续性
                    first_row_number = int(page_data[0][0]) if page_data[0][0].isdigit() else 0
                    if first_row_number <= last_row_number + 1 or last_row_number == 0:
                        break
                    else:
                        print(f"数据不连续，重试第 {retry + 1} 次...")
                        time.sleep(3)
                if retry == retry_count - 1:
                    print("重试次数用尽，继续处理下一页")
            
            if page_header and not header:
                header = page_header
            if page_data:
                # 记录本页数据条数
                page_data_counts[current_page] = len(page_data)
                
                # 更新最后一行序号
                last_row_number = int(page_data[-1][0]) if page_data[-1][0].isdigit() else last_row_number
                batch_data.extend(page_data)
                all_data.extend(page_data)
                print(f"\n当前页数据统计:")
                print(f"本页数据行数: {len(page_data)}")
                print(f"累计数据行数: {len(all_data)}")
                
                # 验证数据完整性
                if total_items:
                    completion_rate = (len(all_data) / total_items) * 100
                    print(f"数据完整度: {completion_rate:.1f}% ({len(all_data)}/{total_items}条)")
            
            # 每爬取batch_size页保存一次数据
            if current_page % batch_size == 0 and batch_data:
                save_batch_data(batch_data, header, current_page // batch_size)
                batch_data = []
            
            # 尝试翻页
            try:
                # 先检查是否已经到达最后一页
                if current_page >= total_pages:
                    print(f"已到达最后一页 ({current_page}/{total_pages})")
                    if batch_data:
                        save_batch_data(batch_data, header, (current_page // batch_size) + 1)
                    break
                
                next_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'btn-next') or contains(text(), '下一页')]"))
                )
                
                if 'disabled' in next_button.get_attribute('class'):
                    print(f"检测到下一页按钮已禁用，已到达最后一页 ({current_page}/{total_pages})")
                    if batch_data:
                        save_batch_data(batch_data, header, (current_page // batch_size) + 1)
                    break
                
                driver.execute_script("arguments[0].click();", next_button)
                # 双重验证：等待页码更新和表格加载
                WebDriverWait(driver, 15).until(
                    EC.text_to_be_present_in_element((By.XPATH, "//li[contains(@class, 'active')]"), str(current_page + 1))
                )
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//table//tbody//tr[1]"))
                )
                time.sleep(2)  # 最终稳定等待
                
                # 计算并记录本页爬取耗时
                page_time = time.time() - page_start_time
                page_times.append(page_time)
                
                # 计算平均每页耗时和预估剩余时间
                avg_page_time = sum(page_times) / len(page_times)
                remaining_pages = total_pages - current_page
                estimated_remaining_time = remaining_pages * avg_page_time
                
                # 转换为时分秒格式
                hours, remainder = divmod(estimated_remaining_time, 3600)
                minutes, seconds = divmod(remainder, 60)
                
                # 显示爬取进度和预估时间
                progress = (current_page / total_pages) * 100
                print(f"\n 爬取进度: {progress:.1f}% ({current_page}/{total_pages}页)")
                print(f" 本页耗时: {page_time:.1f}秒, 平均每页: {avg_page_time:.1f}秒")
                print(f" 预计剩余时间: {int(hours)}小时{int(minutes)}分钟{int(seconds)}秒")
                
                current_page += 1
                page_counter += 1
                
            except Exception as e:
                print(f"翻页出错: {e}")
                if batch_data:
                    save_batch_data(batch_data, header, (current_page // batch_size) + 1)
                break
        
        # 保存完整数据
        if all_data:
            save_complete_data(all_data, header)
        
        # 计算总耗时
        total_time = time.time() - start_time
        hours, remainder = divmod(total_time, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        # 添加爬取完成度检查
        print("\n" + "="*20 + " 爬取完成度检查 " + "="*20)
        
        # 检查页面完整性
        if len(crawled_pages) == total_pages:
            print(f"页面完整性: 已成功爬取全部 {total_pages} 页")
        else:
            missing_pages = set(range(1, total_pages + 1)) - crawled_pages
            print(f"页面完整性: 爬取了 {len(crawled_pages)}/{total_pages} 页")
            if len(missing_pages) <= 10:
                print(f"缺失页面: {sorted(missing_pages)}")
            else:
                print(f"缺失页面数量: {len(missing_pages)} 页")
            
        # 检查数据完整性
        if total_items:
            if len(all_data) >= total_items:
                print(f"数据完整性: 已爬取 {len(all_data)} 条数据，符合或超过预期的 {total_items} 条")
            else:
                completion_rate = (len(all_data) / total_items) * 100
                print(f"数据完整性: 爬取了 {len(all_data)}/{total_items} 条数据 ({completion_rate:.1f}%)")
        
        # 检查每页数据一致性
        if page_data_counts:
            avg_count = sum(page_data_counts.values()) / len(page_data_counts)
            abnormal_pages = {p: c for p, c in page_data_counts.items() if c < avg_count * 0.5}
            if abnormal_pages:
                print(f"数据一致性: 发现 {len(abnormal_pages)} 页数据异常")
                if len(abnormal_pages) <= 5:
                    for page, count in abnormal_pages.items():
                        print(f"  - 第 {page} 页: 仅有 {count} 条数据 (平均: {avg_count:.1f})")
            else:
                print(f"数据一致性: 各页数据量分布正常，平均每页 {avg_count:.1f} 条")
        
        print(f"\n爬取完成! 总耗时: {int(hours)}小时{int(minutes)}分钟{int(seconds)}秒")
        print(f"共爬取 {len(all_data)} 条数据, {len(crawled_pages)}/{total_pages} 页")
            
        return all_data, header
        
    except (TimeoutException, WebDriverException) as e:
        print(f"[ERROR] 网络/浏览器错误: {str(e)}")
        # 记录详细错误信息
        logging.error(f"网络/浏览器错误: {str(e)}", exc_info=True)
        if driver:
            driver.save_screenshot('error_screenshot.png')
    except Exception as e:
        print(f"[ERROR] 数据解析错误: {str(e)}")
        # 记录详细错误信息
        logging.error(f"数据解析错误: {str(e)}", exc_info=True)

        return all_data, header
        
    finally:
        if driver:
            driver.quit()
        
def save_complete_data(data, header):
    """保存完整数据"""
    if not data or not header:
        return
        
    # 创建下载文件夹
    download_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'download')
    save_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'download')
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
    
    # 保存到Excel文件
    df = pd.DataFrame(data, columns=header)
    excel_file = os.path.join(save_dir, "医保药品数据_完整数据.xlsx")
    df.to_excel(excel_file, index=False)
    logging.info(f"已保存完整数据到: {excel_file}")

def save_batch_data(data, header, batch_num):
    """保存批次数据"""
    if not data or not header:
        return
        
    try:
        # 创建下载文件夹
        download_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'download')
        if not os.path.exists(download_dir):
            os.makedirs(download_dir)
        
        # 数据验证和修正
        if any(len(row) != len(header) for row in data):
            print("检测到数据列数不一致，正在修正...")
            fixed_data = []
            for row in data:
                if len(row) < len(header):
                    row = row + [''] * (len(header) - len(row))
                elif len(row) > len(header):
                    row = row[:len(header)]
                fixed_data.append(row)
            data = fixed_data
        
        # 修改保存方式，移除encoding参数
        df = pd.DataFrame(data, columns=header)
        excel_file = os.path.join(download_dir, f"医保药品数据_第{(batch_num-1)*10+1}页到第{batch_num*10}页.xlsx")
        df.to_excel(excel_file, index=False)
        print(f"已保存第{(batch_num-1)*10+1}页到第{batch_num*10}页的数据到: {excel_file}")
        
    except PermissionError as e:
        print(f"文件占用错误，尝试备用方案: {e}")
        timestamp = time.strftime("%Y%m%d%H%M%S")
        excel_file = os.path.join(download_dir, f"医保药品数据_备用_{timestamp}.xlsx")
        df.to_excel(excel_file, index=False)
    except Exception as e:
        print(f"保存数据时出错: {e}")
        print("错误数据示例:")
        print(f"表头 ({len(header)}列): {header}")
        if data:
            print(f"首行数据 ({len(data[0])}列): {data[0]}")

if __name__ == "__main__":
    all_data, header = asyncio.run(crawl_all_pages())
    if all_data and header:
        df = pd.DataFrame(all_data, columns=header)
        print("\n数据预览:")
        print(df.head().to_string(index=False))
        print(f"\n总共爬取 {len(all_data)} 条数据")