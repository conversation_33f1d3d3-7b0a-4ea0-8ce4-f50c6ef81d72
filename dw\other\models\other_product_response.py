from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class OtherProductResponse(BaseModel):
    product_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='产品名称')
    type = models.CharField(max_length=200, blank=True, null=True,  verbose_name='责任类型')
    amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='赔付金额')

    class Meta:
        db_table = 'other_product_response'
        verbose_name = '理赔-责任赔付金额'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_name', 'type'],
                name='unique_other_product_response_combination'
            ),
        ]
