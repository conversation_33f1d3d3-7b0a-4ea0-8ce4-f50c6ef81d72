import os
import pandas as pd
import logging
import openpyxl
from datetime import datetime
import json
from sqlalchemy import create_engine
import django
from django.conf import settings

def clean_data(df):
    """
    数据清洗处理
    :param df: 原始DataFrame
    :return: 清洗后的DataFrame
    """
    try:
        # 处理列表类型的数据
        for column in df.columns:
            if df[column].apply(lambda x: isinstance(x, list)).any():
                df[column] = df[column].apply(lambda x: ','.join(x) if isinstance(x, list) else x)

        # 删除重复数据
        df = df.drop_duplicates()

        # 填充空值
        df = df.fillna('')

        # 重置索引
        df = df.reset_index(drop=True)

        return df

    except Exception as e:
        logging.error(f'数据清洗时发生错误：{str(e)}')
        raise

def fix_chemical_symbols(df):
    """
    修复包含Spec关键字字段中的化学符号编码问题
    :param df: DataFrame
    :return: 修复后的DataFrame
    """
    try:
        # 找到包含Spec关键字或eachDos关键字的列
        spec_columns = [col for col in df.columns if 'Spec' in col or 'eachDos' in col or 'rid' in col]

        for col in spec_columns:
            if col in df.columns:
                # 确保字符串类型并处理编码
                df[col] = df[col].astype(str)
                # 处理常见的化学符号编码问题
                df[col] = df[col].str.replace('?', '', regex=False)  # 移除问号

        logging.info(f'已处理包含Spec或eachDos的字段: {spec_columns}')
        return df

    except Exception as e:
        logging.error(f'修复化学符号时发生错误：{str(e)}')
        return df

def export_data(df, output_format='excel', file_name='merged_data'):
    """
    导出数据
    :param df: DataFrame数据
    :param output_format: 输出格式（csv/excel/json）
    :param file_name: 输出文件名前缀
    :return: 导出文件的路径
    """
    try:
        # 验证输出格式
        output_format = output_format.lower()
        if output_format not in ['csv', 'excel', 'json']:
            raise ValueError(f'不支持的输出格式：{output_format}，支持的格式有：csv, excel, json')

        # 获取程序执行目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(current_dir, f'{file_name}_{timestamp}.{"xlsx" if output_format == "excel" else output_format}')

        # 根据格式导出数据
        if output_format == 'csv':
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
        elif output_format == 'excel':
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 批量处理时间列和rid列
                time_columns = df.columns[df.columns.str.contains('Time|date|rid', case=True)]
                if not time_columns.empty:
                    for col in time_columns:
                        df[col] = df[col].astype(str)
                
                df.to_excel(writer, index=False)
                
                # 获取工作表并设置文本格式
                worksheet = writer.sheets['Sheet1']
                for col in time_columns:
                    if 'rid' in col.lower():
                        col_letter = openpyxl.utils.get_column_letter(df.columns.get_loc(col) + 1)
                        for row in range(2, len(df) + 2):
                            cell = worksheet[f'{col_letter}{row}']
                            cell.number_format = '@'
        elif output_format == 'json':
            df.to_json(output_file, orient='records', force_ascii=False, indent=2)

        logging.info(f'数据已导出到文件：{output_file}')
        return output_file

    except Exception as e:
        logging.error(f'导出数据时发生错误：{str(e)}')
        raise

def merge_western_medicine_json_to_db(folder_path):
    """
    合并西药JSON文件并写入spider_fuwu_western_medicine表
    :param folder_path: JSON文件夹路径
    """
    if not os.path.exists(folder_path):
        print(f"文件夹 {folder_path} 不存在")
        return

    # 获取所有wm_x_开头的JSON文件
    json_files = [f for f in os.listdir(folder_path)
                 if f.endswith('.json') and f.startswith('wm_x_')]

    if not json_files:
        print(f"在 {folder_path} 中未找到以wm_x_开头的JSON文件")
        return

    print(f"\n开始合并 {len(json_files)} 个西药JSON文件...")

    all_data = []
    for file in json_files:
        file_path = os.path.join(folder_path, file)
        try:
            # 使用UTF-8编码读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 转换为DataFrame
            df = pd.DataFrame(json_data)
            all_data.append(df)
            print(f"成功读取: {file} ({len(df)} 条记录)")
        except Exception as e:
            print(f"读取文件 {file} 失败: {e}")

    if not all_data:
        print("未找到可合并的数据")
        return

    # 合并所有数据框
    merged_df = pd.concat(all_data, ignore_index=True)
    print(f"合并完成，总计 {len(merged_df)} 条记录")

    # 数据清洗
    merged_df = clean_data(merged_df)

    # 修复化学符号编码问题
    merged_df = fix_chemical_symbols(merged_df)

    # 写入数据库
    write_to_database(merged_df, 'spider_fuwu_western_medicine')

def write_to_database(df, table_name):
    """
    将DataFrame写入数据库
    :param df: 要写入的DataFrame
    :param table_name: 目标表名
    """
    try:
        # 添加项目根目录到Python路径
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.join(current_dir, '..', '..', '..', '..')
        project_root = os.path.abspath(project_root)
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dw.settings')
        django.setup()

        # 获取数据库配置
        from django.conf import settings
        db_config = settings.DATABASES['default']

        # 创建数据库连接字符串
        if db_config['ENGINE'] == 'django.db.backends.mysql':
            connection_string = (
                f"mysql+pymysql://{db_config['USER']}:{db_config['PASSWORD']}"
                f"@{db_config['HOST']}:{db_config['PORT']}/{db_config['NAME']}"
                f"?charset=utf8mb4"
            )
        else:
            raise ValueError(f"不支持的数据库类型: {db_config['ENGINE']}")

        # 创建数据库引擎
        from sqlalchemy import create_engine
        engine = create_engine(connection_string, echo=False)

        # 写入数据库
        print(f"开始写入数据到表 {table_name}...")
        df.to_sql(
            name=table_name,
            con=engine,
            if_exists='replace',  # 替换现有表
            index=False,
            chunksize=1000
        )

        print(f"成功写入 {len(df)} 条记录到表 {table_name}")

    except Exception as e:
        logging.error(f"写入数据库时发生错误：{str(e)}")
        print(f"写入数据库失败: {e}")
        raise

def merge_disease_catalog_json(folder_path, output_excel_name='中草药'):
    """合并disease_catalog_开头的JSON文件到Excel"""
    if not os.path.exists(folder_path):
        print(f"文件夹 {folder_path} 不存在")
        return

    # 获取所有tcmherb_开头的JSON文件
    json_files = [f for f in os.listdir(folder_path)
                 if f.endswith('.json') and f.startswith('tcmherb_')]

    if not json_files:
        print(f"在 {folder_path} 中未找到以tcmherb_开头的JSON文件")
        return

    print(f"\n开始合并 {len(json_files)} 个tcmherb_开头的JSON文件...")

    all_data = []
    for file in json_files:
        file_path = os.path.join(folder_path, file)
        try:
            # 读取JSON时指定rid列为字符串类型
            df = pd.read_json(file_path, encoding='utf-8', dtype={'rid': str,'ver':'str'})
            all_data.append(df)
            print(f"成功读取: {file}")
        except Exception as e:
            print(f"读取文件 {file} 失败: {e}")

    if not all_data:
        print("未找到可合并的数据")
        return

    # 合并所有数据框
    merged_df = pd.concat(all_data, ignore_index=True)

    # 数据清洗
    merged_df = clean_data(merged_df)

    # 导出到Excel
    output_file = export_data(merged_df, output_format='excel', file_name=output_excel_name)
    print(f"\n数据已成功导出到: {output_file}")
    print(f"总数据条数: {len(merged_df)}")

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 指定JSON文件夹路径
    json_folder = os.path.join(os.path.dirname(__file__), 'download', 'json')

    # 合并西药JSON文件并写入数据库
    print("开始处理西药数据...")
    merge_western_medicine_json_to_db(json_folder)

    # 如果需要，也可以合并中草药JSON文件并导出为Excel
    # merge_disease_catalog_json(json_folder)