#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的数据库连接管理器
解决连接重复关闭问题
"""

import logging
import contextlib
from .database_utils import DatabaseConnectionManager

logger = logging.getLogger(__name__)


class SafeConnectionManager:
    """
    安全的数据库连接管理器
    防止重复关闭连接导致的错误
    """
    
    def __init__(self, db_config=None):
        """
        初始化安全连接管理器
        
        Args:
            db_config (dict): 数据库配置
        """
        self.db_manager = DatabaseConnectionManager(db_config)
        self.active_connections = {}  # 跟踪活跃连接
        self.connection_counter = 0
    
    @contextlib.contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        自动处理连接的打开和关闭
        
        Yields:
            connection: 数据库连接对象
        """
        conn = None
        conn_id = None
        
        try:
            # 获取新连接
            conn = self.db_manager.get_connection()
            self.connection_counter += 1
            conn_id = f"conn_{self.connection_counter}"
            
            # 记录活跃连接
            self.active_connections[conn_id] = {
                'connection': conn,
                'closed': False
            }
            
            logger.debug(f"创建数据库连接: {conn_id}")
            yield conn
            
        except Exception as e:
            logger.error(f"数据库连接操作失败: {str(e)}")
            raise
        finally:
            # 安全关闭连接
            self._safe_close_connection(conn_id, conn)
    
    @contextlib.contextmanager
    def get_cursor(self):
        """
        获取数据库游标的上下文管理器
        自动处理连接和游标的打开和关闭
        
        Yields:
            cursor: 数据库游标对象
        """
        with self.get_connection() as conn:
            cursor = None
            try:
                cursor = conn.cursor()
                logger.debug("创建数据库游标")
                yield cursor
            except Exception as e:
                logger.error(f"数据库游标操作失败: {str(e)}")
                raise
            finally:
                if cursor:
                    try:
                        cursor.close()
                        logger.debug("关闭数据库游标")
                    except Exception as e:
                        logger.debug(f"关闭游标时出错（可能已关闭）: {str(e)}")
    
    def _safe_close_connection(self, conn_id, conn):
        """
        安全关闭数据库连接
        
        Args:
            conn_id (str): 连接ID
            conn: 数据库连接对象
        """
        if not conn_id or conn_id not in self.active_connections:
            return
        
        conn_info = self.active_connections[conn_id]
        
        if conn_info['closed']:
            logger.debug(f"连接 {conn_id} 已经关闭，跳过")
            return
        
        try:
            if conn:
                conn.close()
                logger.debug(f"成功关闭数据库连接: {conn_id}")
            
            # 标记为已关闭
            conn_info['closed'] = True
            
        except Exception as e:
            logger.debug(f"关闭连接 {conn_id} 时出错（可能已关闭）: {str(e)}")
            # 即使关闭失败，也标记为已关闭，避免重复尝试
            conn_info['closed'] = True
        finally:
            # 从活跃连接中移除
            if conn_id in self.active_connections:
                del self.active_connections[conn_id]
    
    def close_all_connections(self):
        """
        关闭所有活跃连接
        """
        logger.info(f"关闭所有活跃连接，共 {len(self.active_connections)} 个")
        
        for conn_id, conn_info in list(self.active_connections.items()):
            if not conn_info['closed']:
                self._safe_close_connection(conn_id, conn_info['connection'])
    
    def get_connection_status(self):
        """
        获取连接状态信息
        
        Returns:
            dict: 连接状态统计
        """
        total_connections = len(self.active_connections)
        active_connections = sum(1 for info in self.active_connections.values() if not info['closed'])
        closed_connections = total_connections - active_connections
        
        return {
            'total': total_connections,
            'active': active_connections,
            'closed': closed_connections,
            'connection_counter': self.connection_counter
        }


class LegacyConnectionWrapper:
    """
    传统连接方式的包装器
    为现有代码提供向后兼容性
    """
    
    def __init__(self, db_config=None):
        """
        初始化传统连接包装器
        
        Args:
            db_config (dict): 数据库配置
        """
        self.safe_manager = SafeConnectionManager(db_config)
        self._current_conn = None
        self._current_cursor = None
        self._conn_closed = False
    
    def get_connection(self):
        """
        获取数据库连接（传统方式）
        
        Returns:
            connection: 数据库连接对象
        """
        if self._current_conn is None:
            self._current_conn = self.safe_manager.db_manager.get_connection()
            self._conn_closed = False
        return self._current_conn
    
    def get_cursor(self):
        """
        获取数据库游标（传统方式）
        
        Returns:
            cursor: 数据库游标对象
        """
        if self._current_cursor is None:
            conn = self.get_connection()
            self._current_cursor = conn.cursor()
        return self._current_cursor
    
    def close_connection(self):
        """
        关闭当前连接（传统方式）
        """
        if self._conn_closed:
            logger.debug("连接已经关闭，跳过")
            return
        
        try:
            if self._current_cursor:
                self._current_cursor.close()
                self._current_cursor = None
                logger.debug("关闭游标")
        except Exception as e:
            logger.debug(f"关闭游标时出错: {str(e)}")
        
        try:
            if self._current_conn:
                self._current_conn.close()
                self._current_conn = None
                self._conn_closed = True
                logger.debug("关闭连接")
        except Exception as e:
            logger.debug(f"关闭连接时出错: {str(e)}")
            self._conn_closed = True  # 即使失败也标记为已关闭


# 创建全局安全连接管理器实例
safe_connection_manager = SafeConnectionManager()


def get_safe_connection():
    """
    获取安全连接的便捷函数
    
    Returns:
        context manager: 安全连接上下文管理器
    """
    return safe_connection_manager.get_connection()


def get_safe_cursor():
    """
    获取安全游标的便捷函数
    
    Returns:
        context manager: 安全游标上下文管理器
    """
    return safe_connection_manager.get_cursor()


def close_all_connections():
    """
    关闭所有连接的便捷函数
    """
    safe_connection_manager.close_all_connections()


def get_connection_status():
    """
    获取连接状态的便捷函数
    
    Returns:
        dict: 连接状态信息
    """
    return safe_connection_manager.get_connection_status()
