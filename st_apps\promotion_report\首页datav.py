import streamlit as st

st.set_page_config(
    page_title="南京宁惠保五期",
    layout="wide",
    initial_sidebar_state="expanded",
    page_icon="📊"
)

from page.marketing_plan import main as marketing_activities
from page.personal_sale import main as personal_sale
from page.renewal_info import main as renewal_info
from page.beacon_analysis import main as beacon_analysis
from page.insure_info import main as insure_info
from page.material_forward import main as material_forward
from page.previous_compare import main as previous_compare

pg = st.navigation(
    [st.Page(insure_info, title='总览', icon='🗒', url_path='/insure-info'),
     st.Page(material_forward, title='保司素材分享', icon='🗒', url_path='/material-forward'),
     st.<PERSON>(marketing_activities, title='营销', icon='🗒', url_path='/marketing-activities'),
     st.Page(personal_sale, title='目标达成', icon='🗒', url_path='/personal-sale'),
     st.Page(previous_compare, title='往期对比', icon='🗒', url_path='/previous-compare'),
     st.Page(renewal_info, title='续保分析', icon='🗒', url_path='/renewal-info'),
     st.Page(beacon_analysis, title='埋点分析', icon='🗒', url_path='/beacon-analysis')
     ])
pg.run()
