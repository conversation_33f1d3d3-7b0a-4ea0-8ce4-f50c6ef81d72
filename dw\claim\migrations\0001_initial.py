# Generated by Django 3.2.12 on 2024-12-06 10:41

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimProtonHeavyIon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('name', models.CharField(blank=True, max_length=256, null=True, verbose_name='姓名')),
                ('credential_number', models.CharField(blank=True, max_length=64, null=True, verbose_name='证件号码')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
            ],
            options={
                'verbose_name': '理赔-质子重离子情况',
                'verbose_name_plural': '理赔-质子重离子情况',
                'db_table': 'claim_proton_heavy_ion',
            },
        ),
    ]
