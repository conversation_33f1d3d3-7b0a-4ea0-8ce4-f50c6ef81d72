from django.db import models
from common.models import BaseModel


class PublicSqlScope(BaseModel):
    scope = models.CharField(max_length=50, unique=True, verbose_name='sql适用产品范围')
    value = models.CharField(max_length=100, verbose_name='结果值')

    def __str__(self):
        return self.value

    class Meta:
        db_table = 'public_sql_scope'
        verbose_name = 'sql适用范围表'
        verbose_name_plural = verbose_name


class PublicSqlType(BaseModel):
    type = models.CharField(max_length=50, unique=True, verbose_name='sql分类')
    value = models.CharField(max_length=100, verbose_name='结果值')

    def __str__(self):
        return self.value

    class Meta:
        db_table = 'public_sql_type'
        verbose_name = 'sql类型参数表'
        verbose_name_plural = verbose_name


class PublicSqlTemplate(BaseModel):
    name = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='sql模板名称')
    name_en = models.CharField(max_length=128, blank=True, null=True, unique=True, verbose_name='sql模板英文名称')
    type = models.ManyToManyField(PublicSqlType, blank=True, verbose_name='sql分类')
    scope = models.ManyToManyField(PublicSqlScope, blank=True, verbose_name='sql适用产品范围')
    description = models.TextField(blank=True, null=True, verbose_name='sql描述')
    template = models.TextField(blank=True, null=True, verbose_name='sql模板')
    is_param = models.BooleanField(default=True, blank=True, null=True, verbose_name='是否含参数')
    param_names = models.CharField(max_length=256, blank=True, null=True, verbose_name='参数名称(分号分隔)')
    param_values = models.CharField(max_length=256, blank=True, null=True, verbose_name='参数值(分号分隔)')
    param_description = models.TextField(blank=True, null=True, verbose_name='参数描述')
    is_complete = models.BooleanField(default=True, blank=True, null=True,
                                      verbose_name='sql语句是否完整')

    class Meta:
        db_table = 'public_sql_template'
        verbose_name = 'sql模板表'
        verbose_name_plural = verbose_name
