import json
import sys
import pandas as pd
import subprocess
import os
import logging
import time
from datetime import datetime
from logging.handlers import RotatingFileHandler
import os

# 配置日志
logger = logging.getLogger('spider_fuwu_nhsa')
logger.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 创建文件处理器
current_dir = os.path.dirname(os.path.abspath(__file__))
log_dir = os.path.join(current_dir, 'download', 'log')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'log_fuwu_nhsa_wm_tcmpatinfo_x.log')

# 设置日志文件最大为10MB，保留3个备份文件
file_handler = RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=3, encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)

def get_hospital_data(max_pages=5, api_url='https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital', url='/nthl/api/CommQuery/queryFixedHospital', data={}, page_type=2, filename='wm_x', max_retries=3):
    """
    获取医院数据
    :param max_pages: 获取的最大页数
    :param api_url: API接口地址
    :param url: 请求的URL
    :param data: 请求的数据
    :param page_type: 页码类型
    :param filename: 输出文件名(不带扩展名)
    :param max_retries: 最大重试次数
    :return: DataFrame格式的医院数据
    """
    # 检查断点续传文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    checkpoint_file = os.path.join(current_dir, 'download', 'checkpoint_wx_x.json')
    start_page = 1
    
    # 从命令行参数获取起始页
    if 'start_page' in data:
        start_page = data['start_page']
        logging.info(f'使用命令行参数指定的起始页：{start_page}')
        # 如果指定了起始页，创建或更新断点文件
        os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump({'last_page': start_page - 1, 'total_records': (start_page - 1) * 100}, f)
        logging.info(f'已创建断点文件，记录上次爬取到第{start_page - 1}页')
    # 如果没有指定起始页，则尝试从断点文件获取
    elif os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint = json.load(f)
                start_page = checkpoint.get('last_page', 1)
                logging.info(f'发现断点续传文件，从第{start_page}页继续爬取')
        except Exception as e:
            logging.warning(f'读取断点续传文件失败：{str(e)}')
    
    # 更新最大页数
    if start_page > max_pages:
        logging.warning(f'起始页{start_page}大于最大页数{max_pages}，重置为1')
        start_page = 1
    try:
        # 运行Node.js脚本获取数据
        script_path = os.path.join(os.path.dirname(__file__), 'yibao.js')
        
        # 检查文件是否存在
        if not os.path.exists(script_path):
            raise FileNotFoundError(f'Node.js脚本文件不存在：{script_path}')
            
        logger.info(f'{api_url}开始获取医院数据，起始页：{start_page}，最大页数：{max_pages}')
        # 从data中移除start_page参数
        if 'start_page' in data:
            data.pop('start_page')
        print(data)
        # 构建Node.js命令，使用数组形式传递参数
        script_path = script_path.replace('\\', '\\\\')
        node_script = f"require('{script_path}').fetchHospitalData({max_pages}, '{api_url}', '{url}', {json.dumps(data)}, {page_type}, {start_page}, '{filename}').catch(console.error)"
        
        # 执行Node.js脚本并实时显示输出的同时捕获结果
        result = subprocess.run(['node', '-e', node_script], check=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
        # 由于使用了sys.stdout和sys.stderr，result.stdout和result.stderr将为None
        # 但实时输出已经显示在控制台上
        logger.info("Node.js脚本执行完成")
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        download_dir = os.path.join(current_dir, 'download')
        json_dir = os.path.join(download_dir, 'json')
        os.makedirs(json_dir, exist_ok=True)
        
        # 查找所有批次数据文件
        batch_files = []
        for file in os.listdir(json_dir):
            if file.startswith(f'{filename}_') and file.endswith('.json'):
                batch_files.append(os.path.join(json_dir, file))
        
        # 如果没有找到批次文件，尝试使用总文件
        if not batch_files:
            temp_file = os.path.join(json_dir, f'{filename}.json')
            if os.path.exists(temp_file):
                logging.info(f'未找到批次数据文件，使用总数据文件: {temp_file}')
                with open(temp_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                # 删除临时JSON文件
                os.remove(temp_file)
            else:
                raise FileNotFoundError(f'未找到任何数据文件')
        else:
            # 读取并合并所有批次数据文件
            logging.info(f'找到{len(batch_files)}个批次数据文件')
            data = []
            retry_batches = []
            
            # 检查每个批次文件的数据完整性
            for batch_file in sorted(batch_files):
                try:
                    with open(batch_file, 'r', encoding='utf-8') as f:
                        batch_data = json.load(f)
                        
                        # 从文件名中提取页码范围
                        file_name = os.path.basename(batch_file)
                        start_end_pages = file_name.replace(f'{filename}_', '').replace('.json', '').split('_')
                        batch_start_page = int(start_end_pages[0])
                        batch_end_page = int(start_end_pages[1])
                        expected_count = (batch_end_page - batch_start_page + 1) * 100
                        
                        # 检查是否为最后一批次
                        is_last_batch = batch_end_page == max_pages
                        
                        # 如果不是最后一批次，检查数据条数
                        if not is_last_batch and len(batch_data) != expected_count:
                            logging.warning(f'批次{batch_start_page}-{batch_end_page}数据不完整，预期{expected_count}条，实际{len(batch_data)}条，标记为重新爬取')
                            retry_batches.append((batch_start_page, batch_end_page))
                            os.remove(batch_file)  # 删除不完整的数据文件
                        else:
                            data.extend(batch_data)
                            # os.remove(batch_file)
                            logging.info(f'已处理批次文件: {batch_file}（文件保留）')
                except Exception as e:
                    logging.error(f'处理批次文件{batch_file}时出错: {str(e)}')
                    retry_batches.append((batch_start_page, batch_end_page))
                    os.remove(batch_file)
            
            # 重新爬取不完整的批次
            if retry_batches:
                logging.info(f'开始重新爬取{len(retry_batches)}个不完整的批次')
                for start_page, end_page in retry_batches:
                    retry_max_pages = end_page - start_page + 1
                    logging.info(f'重新爬取批次{start_page}-{end_page}')
                    # 从data中移除start_page参数
                    if 'start_page' in data:
                        data.pop('start_page')
                    print(data)
                    # 构建Node.js命令重新爬取
                    node_script = f"require('{script_path}').fetchHospitalData({retry_max_pages}, '{api_url}', '{url}', {json.dumps(data)}, {page_type}, {start_page}, '{filename}').catch(console.error)"
                    result = subprocess.run(['node', '-e', node_script], check=True, stdout=sys.stdout, stderr=sys.stderr, text=True)
                    # 由于使用了sys.stdout和sys.stderr，实时输出已经显示在控制台上
                    logger.info("重新爬取批次Node.js脚本执行完成")
                    
                    # 读取重新爬取的数据
                    retry_file = os.path.join(json_dir, f'{filename}_{start_page}_{end_page}.json')
                    if os.path.exists(retry_file):
                        with open(retry_file, 'r', encoding='utf-8') as f:
                            retry_data = json.load(f)
                            if len(retry_data) == expected_count or (end_page == max_pages):
                                data.extend(retry_data)
                                # os.remove(retry_file)
                                logging.info(f'重新爬取批次{start_page}-{end_page}成功（文件保留）')
                            else:
                                logging.error(f'重新爬取批次{start_page}-{end_page}后数据仍不完整，请检查网络或接口状态')
                                raise Exception(f'批次{start_page}-{end_page}数据无法完整获取')
        
            # 删除总数据文件（如果存在）
            total_file = os.path.join(json_dir, f'{filename}.json')
            if os.path.exists(total_file):
                # os.remove(total_file)
                logging.info(f'总数据文件: {total_file}（文件保留）')
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        
        # 数据清洗
        df = clean_data(df)
        
        # 验证数据完整性
        expected_total = max_pages * 100
        actual_total = len(df)
        completion_rate = (actual_total / expected_total) * 100
        
        logging.info(f'数据获取完成：\n'
                     f'实际获取：{actual_total}条记录\n'
                     f'预期总量：{expected_total}条记录\n'
                     f'完整率：{completion_rate:.2f}%')
        
        # 保存断点信息
        os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            # 保存当前批次的最后页码，而不是最大页数
            # 记录实际完成的最后页码
            current_page = start_page  # 修改为记录当前页码
            checkpoint_data = {'last_page': current_page, 'total_records': actual_total}
            json.dump(checkpoint_data, f)
            logger.info(f'已更新断点文件，当前页码：{current_page}，总记录数：{actual_total}')
        return df
    
    except Exception as e:
        logging.error(f'获取医院数据时发生错误：{str(e)}')
        
        # 从checkpoint文件中获取重试次数
        retry_count = 0
        if os.path.exists(checkpoint_file):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint = json.load(f)
                    retry_count = checkpoint.get('retry_count', 0)
            except:
                pass
        
        if retry_count < max_retries:
            retry_count += 1
            logging.info(f'准备第{retry_count}次重试...')
            
            # 保存错误信息到checkpoint文件
            os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                checkpoint_data = {
                    'last_page': start_page,  # 修改为记录当前页码
                    'total_records': (start_page - 1) * 100,
                    'error': str(e),
                    'retry_count': retry_count
                }
                json.dump(checkpoint_data, f)
            
            # 等待一段时间后重试
            time.sleep(5 * retry_count)
            return get_hospital_data(max_pages, api_url, url, data, page_type, filename, max_retries)
        else:
            logging.error(f'已达到最大重试次数{max_retries}，停止尝试')
            raise

def clean_data(df):
    """
    数据清洗处理
    :param df: 原始DataFrame
    :return: 清洗后的DataFrame
    """
    try:
        # 处理列表类型的数据
        for column in df.columns:
            if df[column].apply(lambda x: isinstance(x, list)).any():
                df[column] = df[column].apply(lambda x: ','.join(x) if isinstance(x, list) else x)
        
        # 删除重复数据
        df = df.drop_duplicates()
        
        # 填充空值
        df = df.fillna('')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        return df
    
    except Exception as e:
        logging.error(f'数据清洗时发生错误：{str(e)}')
        raise

def export_data(df, output_format='csv'):
    """
    导出数据
    :param df: DataFrame数据
    :param output_format: 输出格式（csv/excel/json）
    :return: 导出文件的路径
    :raises ValueError: 当output_format不是支持的格式时
    """
    try:
        # 验证输出格式
        output_format = output_format.lower()
        if output_format not in ['csv', 'excel', 'json']:
            raise ValueError(f'不支持的输出格式：{output_format}，支持的格式有：csv, excel, json')

        # 获取程序执行目录并创建download文件夹
        current_dir = os.path.dirname(os.path.abspath(__file__))
        download_dir = os.path.join(current_dir, 'download')
        os.makedirs(download_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(download_dir, f'wm_tcmpatinfo_x_data_{timestamp}.{"xlsx" if output_format == "excel" else output_format}')
         
        # 根据格式导出数据
        if output_format == 'csv':
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
        elif output_format == 'excel':
            # 批量处理时间列
            time_columns = df.columns[df.columns.str.contains('Time|date', case=True)]
            if not time_columns.empty:
                # 修改处理逻辑，使用pd.to_numeric安全转换数值
                df[time_columns] = df[time_columns].apply(lambda x: pd.to_numeric(x, errors='coerce')).fillna('')
                # 对非空值进行整数转换
                df[time_columns] = df[time_columns].apply(lambda x: x.apply(lambda y: str(int(y)) if pd.notnull(y) and y != '' else ''))
            other_columns = df.columns[df.columns.str.contains('rid|Spec', case=True)]
            if not other_columns.empty:
                df[other_columns] = df[other_columns].apply(lambda x: x.apply(lambda y: str(y) if pd.notnull(y) and y != '' else ''))
            df.to_excel(output_file, index=False)
        elif output_format == 'json':
            df.to_json(output_file, orient='records', force_ascii=False, indent=2)
        
        logging.info(f'数据已导出到文件：{output_file}')
        return output_file
    
    except Exception as e:
        logging.error(f'导出数据时发生错误：{str(e)}')
        raise

if __name__ == '__main__':
    try:
        # 设置参数
        max_pages = 1461  # 总页数
        # max_pages = 132  # 总页数
        start_page = 957  # 开始爬取
        api_url = 'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryWmTcmpatInfoBFromEs'
        url = api_url.replace('https://fuwu.nhsa.gov.cn/ebus/fuwu/api','')
        data = {
            'drugType': "",
            'keyWords': "",
            'medListCodg': "X",
            'pageSize': 100,
            'start_page': start_page  # 添加起始页参数
        }
        
        # 获取医院数据
        hospital_df = get_hospital_data(max_pages, api_url, url, data, 2)
        
        # 显示数据基本信息
        print('\n数据基本信息：')
        print(hospital_df.info())
        
        # 显示前几行数据
        print('\n数据预览：')
        print(hospital_df.head())
        
        # 导出数据
        export_data(hospital_df, 'excel')
        
    except Exception as e:
        logging.error(f'程序执行出错：{str(e)}')