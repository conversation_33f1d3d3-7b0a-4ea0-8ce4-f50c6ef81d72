# DW 数据仓库项目

## 📖 项目概述

DW 是一个基于 Django 的数据仓库项目，主要用于处理健康险销售数据、理赔数据等业务数据的存储、转换和分析。

## 🚀 快速开始

### 本地环境部署

```shell
# 启动 Django 服务器
python manage.py runserver 0.0.0.0:8000

# 启动 Celery Beat 调度器
celery -A dw beat -l INFO

# 启动 Celery Worker
celery -A dw worker -l INFO -P eventlet

# 启动 Flower 监控（需要账号密码）
celery -A dw flower --port=5566 --basic-auth=admin:123456
```

### 数据库迁移

项目包含跨数据库兼容的迁移工具，支持 MySQL、PostgreSQL、SQLite、Oracle。

**⚠️ 重要：必须按以下顺序执行迁移命令**

```bash
# 1. 首先执行 Django 标准迁移（创建表结构）
python manage.py makemigrations
python manage.py migrate

# 2. 然后执行自定义时间戳默认值迁移（添加数据库级默认值）
python manage.py safe_migrate --dry-run  # 先预览
python manage.py safe_migrate            # 实际执行
```

**为什么需要两步迁移？**

- `migrate`：创建表结构和字段，但 Django 模型的默认值不会在数据库层面生效
- `safe_migrate`：为已存在的表添加数据库级别的时间戳默认值和 `ON UPDATE` 行为

## 📁 项目结构

### 应用模块说明

- **public** - 公用数据模型和基础功能
- **insure** - 健康险销售数据
- **claim** - 理赔数据
- **other** - 其他业务数据
- **common** - 通用工具和数据库迁移功能
- **task** - 调度任务管理
- **transfrom** - 数据转换逻辑

### 数据处理流程

1. **数据转换逻辑** - 放在 `transfrom.tasks` 文件中
2. **调度任务** - 放在 `task.tasks` 文件中
3. **数据存储** - 按业务模块分别存储在对应的 models 中

## 🗄️ 数据库管理

### 数据字典同步

项目提供完整的数据字典管理功能，支持从多个数据库自动同步元数据信息。

#### 同步范围
- **目标数据库**: `jkx`、`nhb`、`dw`、`umami`、`tracker`
- **系统表过滤**: dw数据库自动跳过 `auth_`、`database_`、`django_` 开头的系统表

#### 同步规则
1. **数据库信息表** (`public_database_info`) - 不更新，只查找已存在记录
2. **表信息表** (`public_table_info`) - 按唯一键更新，保护表名称和中文名不被修改
3. **字段信息表** (`public_column_info`) - 特殊注释保护：如果字段表中有注释但数据库没有，保留原有注释
4. **索引信息表** (`public_index_info`) - 正常处理新增、更新、删除

#### 使用方法
```bash
# 命令行同步所有数据库
python manage.py sync_multiple_databases --force

# 同步指定数据库
python manage.py sync_multiple_databases --databases dw jkx --force

# 预览模式（不实际修改）
python manage.py sync_multiple_databases --dry-run
```

**Web界面同步**: 访问数据字典统计页面，点击"同步数据"按钮即可触发同步。

### 时间戳字段处理

项目使用智能的时间戳默认值管理：

- **create_time**: 在 INSERT 时自动设置，UPDATE 时保持不变
- **update_time**: 在 INSERT 时自动设置，UPDATE 时自动更新

### 跨数据库兼容性

支持多种数据库的时间戳函数：

| 数据库     | 推荐函数          | 兼容性        |
| ---------- | ----------------- | ------------- |
| MySQL      | CURRENT_TIMESTAMP | ✅ 原生支持   |
| PostgreSQL | CURRENT_TIMESTAMP | ✅ 触发器实现 |
| SQLite     | CURRENT_TIMESTAMP | ✅ 触发器实现 |
| Oracle     | CURRENT_TIMESTAMP | ✅ 基本支持   |

### 迁移最佳实践

1. **使用标准 SQL 函数** - 统一使用 `CURRENT_TIMESTAMP` 而不是 `NOW()`
2. **预览后执行** - 始终先使用 `--dry-run` 预览 SQL
3. **备份数据** - 生产环境执行前务必备份
4. **测试验证** - 在测试环境充分验证后再部署

## 📚 文档参考

### 数据库相关文档

- **建表说明**: [腾讯文档 - 数据建表说明](https://docs.qq.com/doc/DS1BxSldkaVdkbFNV)
- **命名规范**: [腾讯文档 - 指标表命名规范](https://docs.qq.com/doc/DS3FKYVdJS01Hb1pS)

### 重要提醒

⚠️ **Django Model 字段默认值注意事项**：
Django model 中添加的字段默认值对数据库不生效。如需直接操作数据库表，需要使用本项目提供的迁移工具为数据库字段添加默认值。

## 🛠️ 数据库迁移工具详细说明

### 智能自动发现功能

项目提供智能的数据库迁移工具，能够：

- 🔍 **自动扫描** - 发现所有包含 `create_time` 和 `update_time` 字段的表
- 🎯 **精确过滤** - 支持按 app 名称、表名模式、排除列表等过滤
- 🔧 **自动适配** - 检测数据库类型并生成相应的 SQL 语句
- 📝 **完整日志** - 记录所有操作，便于追踪和调试

### 完整部署流程

```bash
# 完整的项目部署流程
# 1. 安装依赖
pip install -r requirements.txt

# 2. 执行 Django 标准迁移（创建表结构）
python manage.py migrate

# 3. 执行自定义时间戳默认值迁移（添加数据库级默认值）
python manage.py safe_migrate --dry-run  # 先预览
python manage.py safe_migrate            # 实际执行

# 4. 验证迁移结果
python manage.py test_timestamp_behavior

# 5. 启动服务
python manage.py runserver 0.0.0.0:8000
```

### 高级迁移选项

如果需要更精细的控制，也可以使用 `migrate_with_defaults` 命令：

```bash
# 查看数据库信息
python manage.py migrate_with_defaults --action=info

# 预览所有符合条件的表
python manage.py migrate_with_defaults --action=auto_discover --dry-run

# 只处理特定 app 的表
python manage.py migrate_with_defaults --action=auto_discover --app-names insure claim

# 按表名模式匹配
python manage.py migrate_with_defaults --action=auto_discover --table-patterns "insure_*" "claim_*"

# 排除某些表
python manage.py migrate_with_defaults --action=auto_discover --exclude-tables "test_*" "temp_*"
```

### 在 Django 迁移中集成

```python
# your_app/migrations/xxxx_add_timestamp_defaults.py
from django.db import migrations
from common.migrations.utils import auto_add_timestamp_defaults

class Migration(migrations.Migration):
    dependencies = [
        ('your_app', 'previous_migration'),
    ]

    operations = [
        # 自动发现并处理所有符合条件的表
        auto_add_timestamp_defaults(),
    ]
```

## 🧪 测试验证

### 时间戳行为测试

```bash
# 测试默认数据库
python manage.py test_timestamp_behavior

# 测试指定数据库
python manage.py test_timestamp_behavior --database=your_db_alias
```

### 预期测试结果

```
开始测试数据库时间戳行为 (数据库类型: mysql)
==================================================
✓ 测试表创建成功 (mysql)
✓ 时间戳默认值添加成功

=== 测试INSERT行为 ===
✓ INSERT时create_time和update_time都已设置

=== 测试UPDATE行为 ===
✓ create_time在UPDATE时保持不变
✓ update_time在UPDATE时自动更新

==================================================
🎉 时间戳行为测试通过！
```

## 🔧 故障排除

### 常见问题

1. **迁移失败** - 检查迁移日志表中的错误信息
2. **权限问题** - 确保数据库用户有 ALTER TABLE 权限
3. **语法错误** - 检查生成的 SQL 是否符合目标数据库的语法
4. **表不存在** - 使用 check 命令验证表的存在性

### 迁移日志查看

```python
# 在 Django shell 中查看迁移日志
python manage.py shell

from common.models import DatabaseMigrationLog
logs = DatabaseMigrationLog.objects.all().order_by('-executed_at')
for log in logs:
    print(f"{log.migration_name}: {log.status}")
```

## 📋 开发规范

1. **代码组织** - 按业务模块组织代码，保持清晰的目录结构
2. **数据库操作** - 优先使用项目提供的跨数据库兼容工具
3. **时间戳处理** - 统一使用 `CURRENT_TIMESTAMP` 函数
4. **迁移管理** - 使用智能自动发现功能，避免硬编码表名列表
5. **测试验证** - 在生产环境部署前充分测试
