# Generated by Django 3.2.12 on 2024-10-17 11:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('insure', '0003_auto_20240812_1051'),
    ]

    operations = [
        migrations.AddField(
            model_name='insureagent',
            name='week_complete_ratio',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='序时完成率'),
        ),
        migrations.AddField(
            model_name='insureagent',
            name='week_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='本周销售量'),
        ),
        migrations.AddField(
            model_name='insureagent',
            name='week_target',
            field=models.IntegerField(blank=True, null=True, verbose_name='周销售目标'),
        ),
        migrations.AddField(
            model_name='insureagent',
            name='week_target_ratio',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='周目标完成率'),
        ),
        migrations.AddField(
            model_name='insureonline',
            name='week_complete_ratio',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='序时完成率'),
        ),
        migrations.AddField(
            model_name='insureonline',
            name='week_count',
            field=models.IntegerField(blank=True, null=True, verbose_name='本周销售量'),
        ),
        migrations.AddField(
            model_name='insureonline',
            name='week_target',
            field=models.IntegerField(blank=True, null=True, verbose_name='周销售目标'),
        ),
        migrations.AddField(
            model_name='insureonline',
            name='week_target_ratio',
            field=models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='周目标完成率'),
        ),
    ]
