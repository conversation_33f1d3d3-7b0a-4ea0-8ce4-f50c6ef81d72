#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏医保局(jsyb)pipeline配置模块初始化
自动注册配置到全局配置管理器
"""

from transfrom.utils.field_config import register_province_config
from transfrom.utils.data_cleaning_config import register_province_cleaning_config
from .field_config import jsyb_field_config
from .data_cleaning_config import jsyb_data_cleaning_config

# 自动注册江苏医保局配置
register_province_config('jsyb', jsyb_field_config)
register_province_cleaning_config('jsyb', jsyb_data_cleaning_config)
