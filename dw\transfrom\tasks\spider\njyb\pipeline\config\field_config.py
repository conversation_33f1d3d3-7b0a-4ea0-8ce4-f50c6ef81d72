#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南京医保局(njyb)字段处理配置
"""

import logging
from typing import Dict
from transfrom.utils.field_config import BaseFieldProcessingConfig

logger = logging.getLogger(__name__)


class NjybFieldProcessingConfig(BaseFieldProcessingConfig):
    """
    南京医保局字段处理配置
    """

    def get_field_strategies(self) -> Dict:
        """
        获取南京医保局的字段处理策略配置

        Returns:
            Dict: 字段策略配置，格式为 {源表: {目标表: {字段名: 处理策略}}}
        """
        return {
            # 南京医保定点机构数据 - 统一使用NULL策略
            'spider_njyb_service_facilities': {
                'medical_service_entity': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 南京医保定点药店数据 - 统一使用NULL策略
            'spider_njyb_medical_supplies': {
                'medical_supplies_entity': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            # 南京可能有特有的源表和目标表配置
            'spider_njyb_drug': {
                'medical_drug_entity': {
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            }
        }


# 创建南京医保局配置实例
njyb_field_config = NjybFieldProcessingConfig()


# 提供便捷的函数接口
def get_njyb_data_cleaning_config(source_table: str):
    """获取南京医保局数据清洗配置"""
    return njyb_field_config.get_data_cleaning_config(source_table)


def get_njyb_field_mapping_config(source_table: str, target_table: str):
    """获取南京医保局字段映射配置"""
    return njyb_field_config.get_field_mapping_config(source_table, target_table)


def get_njyb_data_normalization_config(target_table: str):
    """获取南京医保局数据标准化配置"""
    return njyb_field_config.get_data_normalization_config(target_table)
