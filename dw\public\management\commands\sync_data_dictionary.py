"""
数据字典同步管理命令
从数据库中提取元数据信息并同步到数据字典表中
"""
import logging
from django.core.management.base import BaseCommand
from django.db import connections, transaction
from django.conf import settings
from public.models import PublicDatabaseInfo, PublicTableInfo, PublicColumnInfo, PublicIndexInfo

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '从数据库中同步元数据信息到数据字典表'

    def add_arguments(self, parser):
        parser.add_argument(
            '--database',
            type=str,
            default='default',
            help='指定要同步的数据库连接名称 (默认: default)'
        )
        parser.add_argument(
            '--tables',
            type=str,
            nargs='*',
            help='指定要同步的表名列表，不指定则同步所有表'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅预览操作，不实际执行数据库更新'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制更新已存在的记录'
        )

    def handle(self, *args, **options):
        database_name = options['database']
        target_tables = options.get('tables', [])
        dry_run = options['dry_run']
        force_update = options['force']

        self.stdout.write(f"开始同步数据库 '{database_name}' 的元数据信息...")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("预览模式：不会实际修改数据库"))

        try:
            with transaction.atomic():
                # 获取或创建数据库信息记录
                db_info = self.get_or_create_database_info(database_name, dry_run)
                
                if not dry_run and db_info:
                    # 同步表信息
                    self.sync_table_info(db_info, database_name, target_tables, force_update)
                    
                    self.stdout.write(
                        self.style.SUCCESS(f"成功同步数据库 '{database_name}' 的元数据信息")
                    )
                elif dry_run:
                    self.preview_sync(database_name, target_tables)
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"同步过程中发生错误: {str(e)}")
            )
            logger.exception("数据字典同步失败")

    def get_or_create_database_info(self, database_name, dry_run=False):
        """获取或创建数据库信息记录"""
        try:
            db_info = PublicDatabaseInfo.objects.get(name=database_name)
            self.stdout.write(f"找到已存在的数据库记录: {database_name}")
            return db_info
        except PublicDatabaseInfo.DoesNotExist:
            if not dry_run:
                # 获取数据库配置信息
                db_config = settings.DATABASES.get(database_name, {})
                engine = db_config.get('ENGINE', '')
                
                # 根据引擎确定数据库类型
                if 'mysql' in engine.lower():
                    db_type = 'MySQL'
                elif 'postgresql' in engine.lower():
                    db_type = 'PostgreSQL'
                elif 'sqlite' in engine.lower():
                    db_type = 'SQLite'
                elif 'oracle' in engine.lower():
                    db_type = 'Oracle'
                else:
                    db_type = 'Unknown'
                
                db_info = PublicDatabaseInfo.objects.create(
                    name=database_name,
                    type=db_type,
                    description=f"自动创建的数据库记录 - {database_name}",
                    data_source="系统自动同步"
                )
                self.stdout.write(f"创建新的数据库记录: {database_name} ({db_type})")
                return db_info
            else:
                self.stdout.write(f"[预览] 将创建数据库记录: {database_name}")
                return None

    def sync_table_info(self, db_info, database_name, target_tables, force_update):
        """同步表信息"""
        connection = connections[database_name]
        
        with connection.cursor() as cursor:
            # 获取所有表信息
            tables_info = self.get_tables_info(cursor, target_tables)
            
            for table_info in tables_info:
                table_name = table_info['table_name']
                
                # 标准化表类型
                table_type = table_info.get('table_type', 'BASE TABLE')
                if table_type == 'BASE TABLE':
                    table_type = '数据表'
                elif table_type == 'VIEW':
                    table_type = '视图'
                elif table_type == 'SYSTEM VIEW':
                    table_type = '系统视图'
                else:
                    table_type = table_type.replace('_', ' ').title()

                # 获取或创建表记录
                table_obj, created = PublicTableInfo.objects.get_or_create(
                    database_name=db_info.name,
                    name=table_name,
                    defaults={
                        'database_id': db_info.id,
                        'comment': table_info.get('table_comment', ''),
                        'type': table_type,
                        'engine': table_info.get('engine', ''),
                        'charset': table_info.get('charset', ''),
                        'collation': table_info.get('collation', ''),
                    }
                )
                
                if created:
                    self.stdout.write(f"创建表记录: {table_name}")
                elif force_update:
                    # 更新表信息
                    table_obj.database_id = db_info.id
                    table_obj.comment = table_info.get('table_comment', '')
                    table_obj.type = table_type  # 使用标准化后的表类型
                    table_obj.engine = table_info.get('engine', '')
                    table_obj.charset = table_info.get('charset', '')
                    table_obj.collation = table_info.get('collation', '')
                    table_obj.save()
                    self.stdout.write(f"更新表记录: {table_name}")
                
                # 同步字段信息
                self.sync_column_info(cursor, table_obj, table_name, force_update)
                
                # 同步索引信息
                self.sync_index_info(cursor, table_obj, table_name, force_update)

    def get_tables_info(self, cursor, target_tables):
        """获取表信息"""
        # 这里需要根据不同数据库类型编写不同的SQL
        # 以MySQL为例
        if target_tables:
            table_filter = f"AND table_name IN ({','.join(['%s'] * len(target_tables))})"
            params = target_tables
        else:
            table_filter = ""
            params = []
            
        sql = f"""
        SELECT 
            table_name,
            table_comment,
            table_type,
            engine,
            table_collation as collation
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() {table_filter}
        ORDER BY table_name
        """
        
        cursor.execute(sql, params)
        columns = [col[0] for col in cursor.description]
        return [dict(zip(columns, row)) for row in cursor.fetchall()]

    def sync_column_info(self, cursor, table_obj, table_name, force_update):
        """同步字段信息"""
        # 获取字段信息的SQL（MySQL）
        sql = """
        SELECT 
            column_name,
            column_comment,
            data_type,
            column_type,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            is_nullable,
            column_default,
            extra,
            column_key,
            ordinal_position
        FROM information_schema.columns 
        WHERE table_schema = DATABASE() AND table_name = %s
        ORDER BY ordinal_position
        """
        
        cursor.execute(sql, [table_name])
        columns = [col[0] for col in cursor.description]
        columns_info = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        for col_info in columns_info:
            column_name = col_info['column_name']
            
            # 处理字段属性
            is_primary_key = col_info['column_key'] == 'PRI'
            is_unique = col_info['column_key'] == 'UNI'
            is_auto_increment = 'auto_increment' in (col_info['extra'] or '').lower()
            is_nullable = col_info['is_nullable'] == 'YES'
            
            column_obj, created = PublicColumnInfo.objects.get_or_create(
                table_name=table_obj.name,
                name=column_name,
                defaults={
                    'table_id': table_obj.id,
                    'comment': col_info.get('column_comment', ''),
                    'data_type': col_info['data_type'],
                    'type': col_info['column_type'],
                    'max_length': col_info.get('character_maximum_length'),
                    'numeric_precision': col_info.get('numeric_precision'),
                    'numeric_scale': col_info.get('numeric_scale'),
                    'is_nullable': is_nullable,
                    'default': col_info.get('column_default'),
                    'is_auto_increment': is_auto_increment,
                    'is_primary_key': is_primary_key,
                    'is_unique': is_unique,
                    'ordinal_position': col_info['ordinal_position'],
                }
            )
            
            if created:
                self.stdout.write(f"  创建字段记录: {table_name}.{column_name}")
            elif force_update:
                # 更新字段信息
                column_obj.table_id = table_obj.id
                column_obj.comment = col_info.get('column_comment', '')
                column_obj.data_type = col_info['data_type']
                column_obj.type = col_info['column_type']
                column_obj.max_length = col_info.get('character_maximum_length')
                column_obj.numeric_precision = col_info.get('numeric_precision')
                column_obj.numeric_scale = col_info.get('numeric_scale')
                column_obj.is_nullable = is_nullable
                column_obj.default = col_info.get('column_default')
                column_obj.is_auto_increment = is_auto_increment
                column_obj.is_primary_key = is_primary_key
                column_obj.is_unique = is_unique
                column_obj.ordinal_position = col_info['ordinal_position']
                column_obj.save()
                self.stdout.write(f"  更新字段记录: {table_name}.{column_name}")

    def sync_index_info(self, cursor, table_obj, table_name, force_update):
        """同步索引信息"""
        # 获取索引信息的SQL（MySQL）
        sql = """
        SELECT 
            index_name,
            non_unique,
            column_name,
            seq_in_index,
            index_type,
            index_comment
        FROM information_schema.statistics 
        WHERE table_schema = DATABASE() AND table_name = %s
        ORDER BY index_name, seq_in_index
        """
        
        cursor.execute(sql, [table_name])
        index_data = cursor.fetchall()
        
        # 按索引名分组
        indexes = {}
        for row in index_data:
            index_name, non_unique, column_name, seq_in_index, index_type, index_comment = row
            
            if index_name not in indexes:
                indexes[index_name] = {
                    'name': index_name,
                    'is_unique': non_unique == 0,
                    'is_primary': index_name == 'PRIMARY',
                    'type': index_type or 'BTREE',
                    'comment': index_comment or '',
                    'columns': []
                }
            
            indexes[index_name]['columns'].append(column_name)
        
        # 创建或更新索引记录
        for index_info in indexes.values():
            column_names = ','.join(index_info['columns'])
            
            index_obj, created = PublicIndexInfo.objects.get_or_create(
                table_name=table_obj.name,
                name=index_info['name'],
                defaults={
                    'table_id': table_obj.id,
                    'type': index_info['type'],
                    'is_unique': index_info['is_unique'],
                    'is_primary': index_info['is_primary'],
                    'column_names': column_names,
                    'comment': index_info['comment'],
                }
            )
            
            if created:
                self.stdout.write(f"  创建索引记录: {table_name}.{index_info['name']}")
            elif force_update:
                # 更新索引信息
                index_obj.table_id = table_obj.id
                index_obj.type = index_info['type']
                index_obj.is_unique = index_info['is_unique']
                index_obj.is_primary = index_info['is_primary']
                index_obj.column_names = column_names
                index_obj.comment = index_info['comment']
                index_obj.save()
                self.stdout.write(f"  更新索引记录: {table_name}.{index_info['name']}")

    def preview_sync(self, database_name, target_tables):
        """预览同步操作"""
        connection = connections[database_name]
        
        with connection.cursor() as cursor:
            tables_info = self.get_tables_info(cursor, target_tables)
            
            self.stdout.write(f"\n[预览] 将同步 {len(tables_info)} 个表:")
            for table_info in tables_info:
                table_name = table_info['table_name']
                self.stdout.write(f"  - {table_name}")
                
                # 预览字段数量
                cursor.execute(
                    "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = %s",
                    [table_name]
                )
                column_count = cursor.fetchone()[0]
                
                # 预览索引数量
                cursor.execute(
                    "SELECT COUNT(DISTINCT index_name) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = %s",
                    [table_name]
                )
                index_count = cursor.fetchone()[0]
                
                self.stdout.write(f"    字段数: {column_count}, 索引数: {index_count}")
