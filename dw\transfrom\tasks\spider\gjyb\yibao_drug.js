require('./loader')

var { a: encrypt, b: decrypt } = window.__o("7d92")

// 常用浏览器的User-Agent列表
const userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15'
];

// 随机获取User-Agent
function getRandomUserAgent() {
    const randomIndex = Math.floor(Math.random() * userAgents.length);
    return userAgents[randomIndex];
}

async function fetchHospitalData(maxPages = 5, apiUrl = 'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital', url = '/nthl/api/CommQuery/queryRtalPhacBInfo', data = {}, page_type = 1, startPage = 1, filename = 'hospital_data') {
    if (maxPages < 1) {
        throw new Error('页数必须大于0');
    }
    if (!apiUrl) {
        throw new Error('API URL不能为空');
    }

    const fs = require('fs');
    const path = require('path');
    const downloadDir = path.join(__dirname, 'download', 'json');
    fs.mkdirSync(downloadDir, { recursive: true });
    const batchSize = 2; // 每10页保存一次数据，平衡请求频率和数据量
    const allData = [];
    const savedFiles = [];
    const maxRetries = 10; // 增加最大重试次数以提高成功率
    const initialRetryDelay = 5000; // 初始重试延迟时间（毫秒）
    const maxRetryDelay = 60000; // 最大重试延迟时间（毫秒）
    const requestTimeout = 60000; // 单个请求超时时间（毫秒）
    let totalRecords = 0; // 记录总数据条数
    
    // 计算需要爬取的批次数
    const remainingPages = maxPages - startPage + 1;
    const batchCount = Math.ceil(remainingPages / batchSize);
    
    console.log(`开始从第${startPage}页爬取，共需爬取${remainingPages}页，分${batchCount}个批次`);
    
    try {
        for (let batch = 0; batch < batchCount; batch++) {
            const currentStartPage = startPage + batch * batchSize;
            const endPage = Math.min(currentStartPage + batchSize - 1, maxPages);
            console.log(`开始爬取第${currentStartPage}页到第${endPage}页数据...`);
            
            var reqs = [];
            for (let i = currentStartPage; i <= endPage; i++) {
                console.log(`正在获取第${i}页数据...`);
                // 如果不是第一页，添加随机等待，爬虫友好操作
                if (i > startPage) {
                    const delay = Math.floor(Math.random() * 3000) + 3000; // 3000-5000毫秒的随机延迟
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                

                var page_num = i;
                var e = {
                    "transformRequest": {},
                    "transformResponse": {},
                    "timeout": 30000,
                    "xsrfCookieName": "XSRF-TOKEN",
                    "xsrfHeaderName": "X-XSRF-TOKEN",
                    "maxContentLength": -1,
                    "headers": {
                        "common": {
                            "Accept": "application/json, text/plain, */*"
                        },
                        "delete": {},
                        "get": {},
                        "head": {},
                        "post": {
                            "Content-Type": "application/x-www-form-urlencoded"
                        },
                        "put": {
                            "Content-Type": "application/x-www-form-urlencoded"
                        },
                        "patch": {
                            "Content-Type": "application/x-www-form-urlencoded"
                        },
                        "Accept": "application/json",
                        "Content-Type": "application/json",
                        "channel": "web"
                    },
                    "withCredentials": false,
                    "baseURL": "/ebus/fuwu/api",
                    "method": "post",
                    "url": url,
                    "data": {
                        ...data,
                        [page_type === 1 ? "pageNum" : "pageNo"]: page_num
                    }
                }
                var payload = encrypt(e);
                headers = {
                    'Accept': 'application/json',
                    'Accept-Language': 'zh,en-US;q=0.9,en;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Connection': 'keep-alive',
                    'Content-Type': 'application/json',
                    'Origin': 'https://fuwu.nhsa.gov.cn',
                    'Pragma': 'no-cache',
                    'Referer': 'https://fuwu.nhsa.gov.cn/nationalHallSt/',
                    'User-Agent': getRandomUserAgent(),
                    'X-Tingyun': 'c=B|4Nl_NnGbjwY;x=799720dc6f354fd6',
                    'channel': 'web',
                    'contentType': 'application/x-www-form-urlencoded',
                    'x-tif-nonce': payload['x-tif-nonce'],
                    'x-tif-paasid': payload['x-tif-passid'],
                    'x-tif-signature': payload['x-tif-signature'],
                    'x-tif-timestamp': new Date().getTime(),
                    'Cookie': 'acw_tc=276aedd317441711415763679e689fe5c6ec72da4f0864aa86385aa56fbdc5; yb_header_active=-1; amap_local=320100'
                };
                // 添加重试逻辑
                let retryCount = 0;
                const makeRequest = async () => {
                    try {
                        const controller = new AbortController();
                        const timeoutId = setTimeout(() => controller.abort(), requestTimeout);

                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: headers,
                            body: payload['data'],
                            signal: controller.signal
                        }).finally(() => clearTimeout(timeoutId));
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response;
                    } catch (error) {
                        if (retryCount < maxRetries) {
                            retryCount++;
                            console.log(`第${i}页请求失败，正在进行第${retryCount}次重试...`);
                            const retryDelay = Math.min(initialRetryDelay * Math.pow(2, retryCount - 1), maxRetryDelay);
                            console.log(`等待${retryDelay}毫秒后进行第${retryCount}次重试...`);
                            await new Promise(resolve => setTimeout(resolve, retryDelay)); // 指数退避策略
                            return makeRequest();
                        }
                        throw error;
                    }
                };
                reqs.push(makeRequest());
            }

            // 处理当前批次的请求
            const responses = await Promise.all(reqs);
            const results = await Promise.all(responses.map(resp => resp.json()));
            
            // 批次间增加较长的等待时间，避免触发服务器限制
            if (batch < batchCount - 1) {
                const batchDelay = Math.floor(Math.random() * 5000) + 2000; // 3000-5000毫秒
                console.log(`批次${batch + 1}完成，等待${batchDelay}毫秒后继续下一批次...`);
                await new Promise(resolve => setTimeout(resolve, batchDelay));
            }
            
            // 解析数据并保存到临时文件
            const batchData = [];
            let shouldBreak = false;
            results.forEach((result, index) => {
                var data = decrypt('SM4', result);
                // console.log(`第${currentStartPage + index}页解析数据:`, data);
                if (data['list'] && Array.isArray(data['list'])) {
                    const pageData = data['list'];
                    // 检查是否为空页面
                    if (pageData.length === 0) {
                        console.log(`第${currentStartPage + index}页数据为空，停止爬取`);
                        shouldBreak = true;
                        return;
                    }
                    // 记录实际获取的数据条数
                    console.log(`第${currentStartPage + index}页获取到${pageData.length}条数据`);
                    batchData.push(...pageData);
                    allData.push(...pageData);
                    totalRecords += pageData.length;
                }
            });
            
            // 保存当前批次数据到文件
            if (batchData.length > 0) {
                const batchFilePath = path.join(downloadDir, `${filename}_${currentStartPage}_${endPage}.json`);
                fs.writeFileSync(batchFilePath, JSON.stringify(batchData, null, 2));
                savedFiles.push(batchFilePath);
                console.log(`第${currentStartPage}页到第${endPage}页数据已保存到${batchFilePath}`);
                // console.log(`已处理批次文件: ${batchFilePath}`);
            }
            
            // 如果检测到空页面，提前结束爬取
            if (shouldBreak) {
                console.log('检测到空页面，提前结束爬取');
                break;
            }
            
            // 保存当前批次数据到文件
            const batchFilePath = path.join(downloadDir, `${filename}_${currentStartPage}_${endPage}.json`);
            fs.writeFileSync(batchFilePath, JSON.stringify(batchData, null, 2));
            savedFiles.push(batchFilePath);
            console.log(`第${currentStartPage}页到第${endPage}页数据已保存到${batchFilePath}`);
            // 即使数据不完整也保留文件
            // console.log(`已处理批次文件: ${batchFilePath}`)
        }
        
        const expectedTotal = maxPages * 100; // 预期总数据量
        console.log(`所有数据获取完成：
        实际获取：${totalRecords}条记录
        预期总量：${expectedTotal}条记录
        完整率：${((totalRecords/expectedTotal)*100).toFixed(2)}%`);
        
        // 返回保存的文件列表，以便Python脚本处理
        return { batchFiles: savedFiles };
    } catch (err) {
        console.error('获取数据时发生错误:', err);
        throw err;
    }
}

// 示例用法：
// fetchHospitalData(maxPages = 3, apiUrl = 'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryWmTcmpatInfoBFromEs', url = '/nthl/api/CommQuery/queryWmTcmpatInfoBFromEs', data = {'drugType': "",
//             'keyWords': "",
//             'medListCodg': "Z",
//             'pageSize': 100}, page_type = 2)
//   .catch(console.error);

module.exports = { fetchHospitalData };