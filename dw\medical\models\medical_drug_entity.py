from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalDrugEntity(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='药品代码', **_db_comment_kwarg('药品代码'))
    central_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='中心编码', **_db_comment_kwarg('中心编码'))
    type = models.Char<PERSON>ield(max_length=32, blank=True, null=True, verbose_name='药品分类', **_db_comment_kwarg('药品分类'))
    type_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='药品分类名称', **_db_comment_kwarg('药品分类名称'))
    level_type = models.Char<PERSON>ield(max_length=32, blank=True, null=True, verbose_name='层级类型', **_db_comment_kwarg('层级类型'))
    province_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='省份编码', **_db_comment_kwarg('省份编码'))
    province_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='省份', **_db_comment_kwarg('省份'))
    city_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='城市编码', **_db_comment_kwarg('城市编码'))
    city_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='城市', **_db_comment_kwarg('城市'))
    category_name = models.CharField(max_length=32, blank=True, null=True, verbose_name='药品类别', **_db_comment_kwarg('药品类别'))
    registered_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='注册名称', **_db_comment_kwarg('注册名称'))
    registered_specifications = models.CharField(max_length=128, blank=True, null=True, verbose_name='注册规格', **_db_comment_kwarg('注册规格'))
    product_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='商品名称', **_db_comment_kwarg('商品名称'))
    specifications = models.CharField(max_length=255, blank=True, null=True, verbose_name='规格', **_db_comment_kwarg('规格'))
    packaging_material = models.CharField(max_length=255, blank=True, null=True, verbose_name='包装材质', **_db_comment_kwarg('包装材质'))
    minimum_packaging_count = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小包装数量', **_db_comment_kwarg('最小包装数量'))
    minimum_packaging_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小包装单位', **_db_comment_kwarg('最小包装单位'))
    minimum_prescription_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小处方单位', **_db_comment_kwarg('最小处方单位'))
    otc_flag = models.IntegerField(blank=True, null=True, verbose_name='是否处方药', **_db_comment_kwarg('是否处方药'))
    production_company_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    approval_number = models.CharField(max_length=255, blank=True, null=True, verbose_name='批准文号', **_db_comment_kwarg('批准文号'))
    categories_national = models.CharField(max_length=32, blank=True, null=True, verbose_name='甲乙类(国家医保药品目录)', **_db_comment_kwarg('甲乙类(国家医保药品目录)'))
    number_national = models.CharField(max_length=255, blank=True, null=True, verbose_name='编号(国家医保药品目录)', **_db_comment_kwarg('编号(国家医保药品目录)'))
    generic_name_national = models.CharField(max_length=255, blank=True, null=True, verbose_name='药品名称(国家医保药品目录)', **_db_comment_kwarg('药品名称(国家医保药品目录)'))
    dosage_form_national = models.CharField(max_length=255, blank=True, null=True, verbose_name='剂型(国家医保药品目录)', **_db_comment_kwarg('剂型(国家医保药品目录)'))
    procure_ceil_price = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='集中采购上限价', **_db_comment_kwarg('集中采购上限价'))
    payment_upper_limit = models.CharField(max_length=255, blank=True, null=True, verbose_name='医保支付上限', **_db_comment_kwarg('医保支付上限'))
    person_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='人员类别', **_db_comment_kwarg('人员类别'))
    initial_payment_ratio = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='先行自付比例', **_db_comment_kwarg('先行自付比例'))
    pharmacy_sale_allowed = models.IntegerField(blank=True, null=True, verbose_name='药店是否可以出售', **_db_comment_kwarg('药店是否可以出售'))
    payment_restricted_scope = models.TextField(blank=True, null=True, verbose_name='医保限定支付范围', **_db_comment_kwarg('医保限定支付范围'))
    government_guided_price = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='政府指导价', **_db_comment_kwarg('政府指导价'))
    begin_date = models.DateField(blank=True, null=True, verbose_name='开始日期', **_db_comment_kwarg('开始日期'))
    end_date = models.DateField(blank=True, null=True, verbose_name='结束日期', **_db_comment_kwarg('结束日期'))

    class Meta:
        db_table = 'medical_drug_entity'
        verbose_name = '医保药品省市实体信息表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

