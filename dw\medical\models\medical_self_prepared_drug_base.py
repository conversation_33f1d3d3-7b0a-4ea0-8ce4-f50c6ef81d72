from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalSelfPreparedDrugBase(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='制剂代码', **_db_comment_kwarg('制剂代码'))
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='制剂名称', **_db_comment_kwarg('制剂名称'))
    minimum_packaging_count = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小包装数量', **_db_comment_kwarg('最小包装数量'))
    minimum_preparation_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小制剂单位', **_db_comment_kwarg('最小制剂单位'))
    minimum_packaging_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小包装单位', **_db_comment_kwarg('最小包装单位'))
    each_dose = models.TextField(blank=True, null=True, verbose_name='每次剂量', **_db_comment_kwarg('每次剂量'))
    dosage_form = models.CharField(max_length=128, blank=True, null=True, verbose_name='剂型', **_db_comment_kwarg('剂型'))
    specifications = models.CharField(max_length=255, blank=True, null=True, verbose_name='规格', **_db_comment_kwarg('规格'))
    efficacy_information = models.CharField(max_length=1024, blank=True, null=True, verbose_name='疗效说明', **_db_comment_kwarg('疗效说明'))
    packaging_material = models.CharField(max_length=255, blank=True, null=True, verbose_name='包装材质', **_db_comment_kwarg('包装材质'))
    license_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='许可证号', **_db_comment_kwarg('许可证号'))
    approval_number = models.CharField(max_length=255, blank=True, null=True, verbose_name='批准文号', **_db_comment_kwarg('批准文号'))
    approval_begin_date = models.DateField(blank=True, null=True, verbose_name='批准文号开始日期', **_db_comment_kwarg('批准文号开始日期'))
    elder_medication_precautions = models.CharField(max_length=512, blank=True, null=True, verbose_name='老年患者用药注意事项', **_db_comment_kwarg('老年患者用药注意事项'))
    child_medication_precautions = models.CharField(max_length=512, blank=True, null=True, verbose_name='儿童患者用药注意事项', **_db_comment_kwarg('儿童患者用药注意事项'))
    hospital_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='医疗机构名称', **_db_comment_kwarg('医疗机构名称'))
    hospital_contact_person = models.CharField(max_length=64, blank=True, null=True, verbose_name='医疗机构联系人', **_db_comment_kwarg('医疗机构联系人'))
    hospital_address = models.CharField(max_length=255, blank=True, null=True, verbose_name='医疗机构地址', **_db_comment_kwarg('医疗机构地址'))
    hospital_regional_code = models.CharField(max_length=64, blank=True, null=True, verbose_name='医疗机构区域编码', **_db_comment_kwarg('医疗机构区域编码'))
    hospital_mobile = models.CharField(max_length=255, blank=True, null=True, verbose_name='医疗机构电话号码', **_db_comment_kwarg('医疗机构电话号码'))
    production_company_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    production_company_address = models.CharField(max_length=512, blank=True, null=True, verbose_name='生产企业地址', **_db_comment_kwarg('生产企业地址'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_self_prepared_drug_base'
        verbose_name = '医保自制药信息基础表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

