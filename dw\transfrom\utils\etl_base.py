#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETL基础类
提供通用的ETL处理框架和工具函数
"""

import logging
import pandas as pd
from .database_utils import get_connection, DEFAULT_DB
from .data_cleaning import clean_source_data
from .field_mapping import transform_data
from .batch_operations import BatchOperationManager

logger = logging.getLogger(__name__)

# 注意：BatchOperationManager的实现方法已在batch_operations.py中自动加载


class ETLProcessor:
    """
    ETL处理器基础类
    提供完整的ETL处理流程
    """

    def __init__(self, db_config=None, output_dir=None, excel_save_operations=None, save_format='csv'):
        """
        初始化ETL处理器

        Args:
            db_config (dict): 数据库配置，如果为None则使用默认配置
            output_dir (str): 输出目录路径，如果为None则自动检测执行文件目录
            excel_save_operations (list): 需要保存Excel的操作类型列表，可选值：['update', 'delete', 'insert']
                                        None表示不保存任何Excel文件，[]表示不保存
            save_format (str): 保存格式，可选值：'csv', 'excel', 'both'，默认'csv'
        """
        self.db_config = db_config or DEFAULT_DB
        self.batch_manager = BatchOperationManager(
            db_config=db_config,
            output_dir=output_dir,
            excel_save_operations=excel_save_operations,
            save_format=save_format
        )

    def get_source_data_sample(self, source_table, limit=10):
        """
        获取源表数据样本，用于测试和验证

        Args:
            source_table (str): 源表名
            limit (int): 限制返回的行数

        Returns:
            pd.DataFrame: 源表数据样本
        """
        sql = f"SELECT * FROM {source_table} LIMIT {limit}"

        with get_connection(self.db_config) as conn:
            df = pd.read_sql(sql, conn)

        return df

    def get_source_data(self, source_table, where_clause=None, limit=None):
        """
        获取源表数据（性能优化版本）

        Args:
            source_table (str): 源表名
            where_clause (str): WHERE条件子句
            limit (int): 限制返回的行数

        Returns:
            pd.DataFrame: 源表数据
        """
        # 特殊处理：对于spider_fuwu_service_facilities表，确保包含国家标准数据
        if source_table == 'spider_fuwu_service_facilities' and limit:
            logger.info(f"检测到医疗服务项目表，使用特殊查询策略确保包含国家标准数据")

            # 分别获取国家标准数据和地方数据
            national_sql = f"SELECT * FROM {source_table} WHERE admdvs = '100000'"
            local_sql = f"SELECT * FROM {source_table} WHERE admdvs != '100000'"

            if where_clause:
                national_sql += f" AND ({where_clause})"
                local_sql += f" AND ({where_clause})"

            # 计算地方数据的限制数量（为国家标准数据预留空间）
            local_limit = max(1, limit - 1000)  # 预留1000条给国家标准数据
            local_sql += f" LIMIT {local_limit}"

            logger.info(f"执行国家标准数据查询: {national_sql}")
            logger.info(f"执行地方数据查询: {local_sql}")

            with get_connection(self.db_config) as conn:
                # 获取国家标准数据
                national_df = pd.read_sql(national_sql, conn)
                logger.info(f"获取国家标准数据: {len(national_df)} 条")

                # 获取地方数据
                local_df = pd.read_sql(local_sql, conn)
                logger.info(f"获取地方数据: {len(local_df)} 条")

                # 合并数据
                if not national_df.empty and not local_df.empty:
                    df = pd.concat([national_df, local_df], ignore_index=True)
                elif not national_df.empty:
                    df = national_df
                elif not local_df.empty:
                    df = local_df
                else:
                    df = pd.DataFrame()

                logger.info(f"合并后总数据: {len(df)} 条")
                return df

        # 原有的通用逻辑
        sql = f"SELECT * FROM {source_table}"

        if where_clause:
            sql += f" WHERE {where_clause}"

        if limit:
            sql += f" LIMIT {limit}"

        logger.info(f"执行源数据查询: {sql}")

        with get_connection(self.db_config) as conn:
            # 性能优化：对于大数据集使用分块读取
            if limit is None or limit > 100000:
                logger.info("检测到大数据集，使用分块读取优化...")
                chunk_size = 50000  # 每次读取5万条
                chunks = []

                try:
                    for chunk in pd.read_sql(sql, conn, chunksize=chunk_size):
                        chunks.append(chunk)
                        logger.info(f"已读取 {len(chunk)} 条数据，累计 {sum(len(c) for c in chunks)} 条")

                    df = pd.concat(chunks, ignore_index=True) if chunks else pd.DataFrame()
                except Exception as e:
                    logger.warning(f"分块读取失败，回退到普通读取: {e}")
                    df = pd.read_sql(sql, conn)
            else:
                df = pd.read_sql(sql, conn)

        logger.info(f"获取源数据完成: {len(df)} 条")
        return df

    def process_etl(self, source_table, target_table, unique_fields,
                   operation_mode='upsert', batch_size=10000,
                   where_clause=None, limit=None, target_where_clause=None, province='gjyb'):
        """
        执行完整的ETL处理流程

        Args:
            source_table (str): 源表名
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表
            operation_mode (str): 操作模式，'upsert' 或 'traditional'
            batch_size (int): 批处理大小
            where_clause (str): 源数据WHERE条件
            limit (int): 源数据限制条数
            target_where_clause (str): 目标表数据过滤条件，可选
            province (str): 省份标识，默认为'gjyb'

        Returns:
            dict: 处理统计信息
        """
        logger.info(f"开始ETL处理: {source_table} -> {target_table}")

        try:
            # 步骤1: 获取源数据
            logger.info("步骤1: 获取源数据...")
            source_df = self.get_source_data(source_table, where_clause, limit)
            logger.info(f"获取到 {len(source_df)} 条源数据")

            if source_df.empty:
                logger.warning("没有源数据需要处理")
                return {'total': 0, 'inserted': 0, 'updated': 0, 'deleted': 0, 'errors': 0}

            # 步骤2: 数据清洗
            logger.info(f"步骤2: 数据清洗... (省份: {province}, 目标表: {target_table})")
            cleaned_df = clean_source_data(source_df, source_table, province, target_table)
            logger.info(f"清洗后数据: {len(cleaned_df)} 条")

            # 步骤3: 数据转换（包含类型标准化）
            logger.info(f"步骤3: 数据转换和类型标准化... (省份: {province})")
            transformed_df = transform_data(cleaned_df, source_table, target_table, province)
            logger.info(f"转换后数据: {len(transformed_df)} 条")

            if transformed_df.empty:
                logger.warning("转换后没有有效数据")
                return {'total': 0, 'inserted': 0, 'updated': 0, 'deleted': 0, 'errors': 0}

            # 步骤4: 保存数据（使用优化的批量更新）
            logger.info("步骤4: 批量更新数据...")
            stats = self.save_transformed_data(
                df=transformed_df,
                target_table=target_table,
                unique_fields=unique_fields,
                operation_mode=operation_mode,
                batch_size=batch_size,
                target_where_clause=target_where_clause
            )

            logger.info(f"ETL处理完成: 新增 {stats.get('inserted', 0)} 条, "
                       f"更新 {stats.get('updated', 0)} 条, "
                       f"删除 {stats.get('deleted', 0)} 条, "
                       f"错误 {stats.get('errors', 0)} 条")

            return stats

        except Exception as e:
            logger.error(f"ETL处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {'total': 0, 'inserted': 0, 'updated': 0, 'deleted': 0, 'errors': 1}

    def save_transformed_data(self, df, target_table, unique_fields,
                            operation_mode='upsert', batch_size=10000, target_where_clause=None):
        """
        保存转换后的数据到目标表

        Args:
            df (pd.DataFrame): 转换后的数据
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表
            operation_mode (str): 操作模式
            batch_size (int): 批处理大小
            target_where_clause (str): 目标表数据过滤条件，可选

        Returns:
            dict: 操作统计信息
        """
        if operation_mode == 'upsert':
            return self.batch_manager.upsert_dataframe(
                df=df,
                target_table=target_table,
                unique_fields=unique_fields,
                batch_size=batch_size,
                target_where_clause=target_where_clause
            )
        else:
            # 传统模式：只插入新数据
            return self._traditional_insert(df, target_table, batch_size)

    def _traditional_insert(self, df, target_table, batch_size):
        """
        传统插入模式：只插入新数据

        Args:
            df (pd.DataFrame): 要插入的数据
            target_table (str): 目标表名
            batch_size (int): 批处理大小

        Returns:
            dict: 操作统计信息
        """
        stats = {'total': len(df), 'inserted': 0, 'updated': 0, 'deleted': 0, 'errors': 0}

        try:
            from .batch_operations_impl import BatchOperationImplementation
            impl = BatchOperationImplementation(self.db_config)

            # 将DataFrame转换为Series列表
            data_list = [row for _, row in df.iterrows()]

            # 分批插入
            total_batches = (len(data_list) + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min((batch_idx + 1) * batch_size, len(data_list))
                batch_data = data_list[start_idx:end_idx]

                batch_stats = impl.batch_insert_with_tosql(batch_data, target_table)
                stats['inserted'] += batch_stats['inserted']
                stats['errors'] += batch_stats['errors']

        except Exception as e:
            logger.error(f"传统插入模式失败: {str(e)}")
            stats['errors'] = len(df)

        return stats


def print_processing_summary(operation_mode, unique_fields, batch_size, source_table, target_table):
    """
    打印处理流程总结

    Args:
        operation_mode (str): 操作模式
        unique_fields (list): 唯一性字段
        batch_size (int): 批处理大小
        source_table (str): 源表名
        target_table (str): 目标表名
    """
    print("=" * 80)
    print("ETL处理流程总结")
    print("=" * 80)
    print(f"源表: {source_table}")
    print(f"目标表: {target_table}")
    print(f"操作模式: {operation_mode}")
    print(f"唯一性字段: {unique_fields}")
    print(f"批处理大小: {batch_size}")
    print()
    print("处理步骤:")
    print("1. 获取源数据")
    print("2. 数据清洗")
    print("3. 数据转换和类型标准化")
    print("4. 批量更新数据")
    print("   - 4.1 分批删除操作")
    print("   - 4.2 分批更新操作")
    print("   - 4.3 分批新增操作")
    print("=" * 80)


# 创建全局实例，方便直接使用
default_etl_processor = ETLProcessor()

# 提供便捷的函数接口
def process_etl(source_table, target_table, unique_fields, province='gjyb', **kwargs):
    """
    ETL处理的便捷函数

    Args:
        source_table (str): 源表名
        target_table (str): 目标表名
        unique_fields (list): 唯一性字段列表
        province (str): 省份标识，默认为'gjyb'
        **kwargs: 其他参数

    Returns:
        dict: 处理统计信息
    """
    return default_etl_processor.process_etl(source_table, target_table, unique_fields, province=province, **kwargs)


def save_transformed_data(df, target_table, unique_fields, **kwargs):
    """
    保存转换数据的便捷函数

    Args:
        df (pd.DataFrame): 转换后的数据
        target_table (str): 目标表名
        unique_fields (list): 唯一性字段列表
        **kwargs: 其他参数

    Returns:
        dict: 操作统计信息
    """
    return default_etl_processor.save_transformed_data(df, target_table, unique_fields, **kwargs)
