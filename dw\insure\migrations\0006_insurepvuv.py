# Generated by Django 3.2.12 on 2024-11-13 17:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('insure', '0005_insuremobile'),
    ]

    operations = [
        migrations.CreateModel(
            name='InsurePvUv',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='截止日期')),
                ('source_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='渠道编码')),
                ('type', models.CharField(blank=True, max_length=32, null=True, verbose_name='类型')),
                ('count', models.IntegerField(blank=True, null=True, verbose_name='数量')),
            ],
            options={
                'verbose_name': '健康险PVUV',
                'verbose_name_plural': '健康险PVUV',
                'db_table': 'insure_pvuv',
            },
        ),
    ]
