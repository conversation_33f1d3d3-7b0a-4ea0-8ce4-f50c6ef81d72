{% extends "public/data_dictionary/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- Chart.js (需要在页面内容之前加载) -->
<script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<style>
/* 增加主内容区域与导航栏的间距 */
main {
    padding-top: 30px;
}

.stats-container {
    padding: 20px;
}

.stats-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #f0f0f0;
}

.stats-header {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 统一深蓝色渐变配色方案 - 明显的渐变效果 */
.stat-item {
    text-align: center;
    padding: 15px 20px;
    color: white;
    border-radius: 8px;
    background: linear-gradient(135deg, #0D6EFD 0%, #6BB6FF 100%);
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(13, 110, 253, 0.4);
}

.stat-number {
    font-size: 2.2em;
    font-weight: bold;
    margin-bottom: 3px;
    line-height: 1;
}

.stat-label {
    font-size: 13px;
    opacity: 0.9;
    line-height: 1;
}

.database-stats-table {
    width: 100%;
    border-collapse: collapse;
}

.database-stats-table th,
.database-stats-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.database-stats-table th {
    background: #f8f9fa;
    font-weight: 500;
    color: #333;
}

.database-stats-table tr:hover {
    background: #f8f9fa;
}

.chart-container {
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-placeholder {
    color: #999;
    text-align: center;
}

.type-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.type-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.type-count {
    font-size: 1.5em;
    font-weight: bold;
    color: #1890ff;
}

.type-name {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.progress-bar {
    background: #e9ecef;
    border-radius: 10px;
    height: 8px;
    margin-top: 8px;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #1890ff, #40a9ff);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* 页面加载动效样式 */
.page-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.page-loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 0.3rem solid #e9ecef;
    border-top: 0.3rem solid #0D6EFD;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.loading-subtext {
    color: #6c757d;
    font-size: 0.9rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block content %}


<div class="stats-container">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1>数据字典统计</h1>
            {% if load_time %}
            <small class="text-muted">
                <i class="fas fa-clock"></i> 加载时间: {{ load_time }}秒
                <span class="ms-2">
                    <i class="fas fa-memory"></i> 已缓存
                </span>
            </small>
            {% endif %}
        </div>
        <div>
            <button id="syncBtn" class="btn btn-success me-2" onclick="syncDataDictionary()">
                <i class="fas fa-sync-alt"></i> 同步数据
            </button>
            <a href="{% url 'data_dictionary:database_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> 返回数据库列表
            </a>
        </div>
    </div>

    <!-- 总体统计 -->
    <div class="overview-stats">
        <div class="stat-item">
            <div class="stat-number">{{ total_stats.databases }}</div>
            <div class="stat-label">数据库</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_stats.tables }}</div>
            <div class="stat-label">总表数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_stats.active_tables }}</div>
            <div class="stat-label">活跃表</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_stats.columns }}</div>
            <div class="stat-label">字段数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_stats.indexes }}</div>
            <div class="stat-label">索引数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ total_stats.primary_keys }}</div>
            <div class="stat-label">主键数</div>
        </div>
    </div>

    <!-- 数据库详细统计 -->
    <div class="stats-card">
        <div class="stats-header">
            <i class="fas fa-database"></i> 数据库详细统计
        </div>
        <div class="table-responsive">
            <table class="database-stats-table">
                <thead>
                    <tr>
                        <th>数据库名称</th>
                        <th>类型</th>
                        <th>总表数</th>
                        <th>活跃表数</th>
                        <th>字段数</th>
                        <th>索引数</th>
                        <th>数据来源</th>
                    </tr>
                </thead>
                <tbody>
                    {% for db in database_stats %}
                    <tr>
                        <td>
                            <a href="{% url 'data_dictionary:database_detail' db.id %}" class="text-decoration-none">
                                <strong>{{ db.name }}</strong>
                            </a>
                        </td>
                        <td><span class="badge bg-secondary">{{ db.type }}</span></td>
                        <td>{{ db.table_count }}</td>
                        <td>{{ db.active_table_count }}</td>
                        <td>{{ db.column_count }}</td>
                        <td>{{ db.index_count }}</td>
                        <td>{{ db.data_source|default:"未知" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="row">
        <!-- 数据库表分布 -->
        <div class="col-md-6">
            <div class="stats-card">
                <div class="stats-header">
                    <i class="fas fa-chart-pie"></i> 数据库表分布
                </div>
                <div style="position: relative; height: 300px;">
                    <canvas id="databasePieChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 数据类型分布 -->
        <div class="col-md-6">
            <div class="stats-card">
                <div class="stats-header">
                    <i class="fas fa-chart-bar"></i> 常用数据类型 (Top 10)
                </div>
                <div style="position: relative; height: 300px;">
                    <canvas id="dataTypeBarChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统信息 -->
    <div class="stats-card">
        <div class="stats-header">
            <i class="fas fa-info-circle"></i> 系统信息
        </div>
        <div class="row">
            <div class="col-md-4">
                <strong>数据完整性:</strong>
                <div class="mt-2">
                    <div>主键覆盖率: <strong>{{ total_stats.primary_key_coverage }}%</strong></div>
                    <div>唯一约束: <strong>{{ total_stats.unique_columns }}</strong> 个字段</div>
                </div>
            </div>
            <div class="col-md-4">
                <strong>平均统计:</strong>
                <div class="mt-2">
                    <div>平均字段数/表: <strong>{{ total_stats.avg_columns_per_table }}</strong></div>
                    <div>平均索引数/表: <strong>{{ total_stats.avg_indexes_per_table }}</strong></div>
                </div>
            </div>
            <div class="col-md-4">
                <strong>数据质量:</strong>
                <div class="mt-2">
                    <div>活跃表比例: <strong>{{ total_stats.active_table_ratio }}%</strong></div>
                    <div>废弃表数量: <strong>{{ total_stats.deprecated_tables }}</strong></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作建议 -->
    <div class="stats-card">
        <div class="stats-header">
            <i class="fas fa-lightbulb"></i> 优化建议
        </div>
        <div class="row">
            <div class="col-md-6">
                <h6>数据质量优化:</h6>
                <ul class="list-unstyled">
                    {% if total_stats.deprecated_tables > 0 %}
                    <li><i class="fas fa-exclamation-triangle text-warning"></i> 发现 {{ total_stats.deprecated_tables }} 个废弃表，建议清理</li>
                    {% endif %}
                    {% if total_stats.primary_keys < total_stats.tables %}
                    <li><i class="fas fa-key text-info"></i> 部分表缺少主键，建议检查数据完整性</li>
                    {% endif %}
                    <li><i class="fas fa-check text-success"></i> 定期同步数据字典以保持最新状态</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>性能优化:</h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-rocket text-primary"></i> 为常用查询字段添加索引</li>
                    <li><i class="fas fa-database text-info"></i> 监控表大小和增长趋势</li>
                    <li><i class="fas fa-chart-line text-success"></i> 定期分析查询性能</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 图表数据 -->
{{ database_distribution|json_script:"database-distribution-data" }}
{{ data_type_stats|json_script:"data-type-stats-data" }}

<script>
// 页面加载完成标志
let pageLoadingComplete = false;

// 等待jQuery和Chart.js加载完成后初始化
function initializePageWhenReady() {
    if (typeof $ !== 'undefined' && typeof Chart !== 'undefined') {
        // 初始化图表
        try {
            initCharts();
        } catch (error) {
            console.error('图表初始化失败:', error);
        }

        // 添加数字动画效果
        $('.stat-number').each(function() {
            var $this = $(this);
            var countTo = parseInt($this.text().replace(/,/g, ''));

            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 1500,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum).toLocaleString());
                },
                complete: function() {
                    $this.text(countTo.toLocaleString());
                }
            });
        });

        // 进度条动画
        setTimeout(function() {
            $('.progress-fill').each(function() {
                var width = $(this).css('width');
                $(this).css('width', '0').animate({ width: width }, 1000);
            });
        }, 500);

        // 标记页面DOM加载完成
        pageLoadingComplete = true;
    } else {
        // 如果库还没加载完成，100ms后重试
        setTimeout(initializePageWhenReady, 100);
    }
}

// DOM加载完成后开始检查库是否加载完成
document.addEventListener('DOMContentLoaded', function() {
    initializePageWhenReady();
});

// 检查加载是否完成（简化版本）
function checkLoadingComplete() {
    if (pageLoadingComplete) {
        setTimeout(function() {
            hideLoadingOverlay();
        }, 500);
    }
}

// 隐藏加载覆盖层
function hideLoadingOverlay() {
    const overlay = document.getElementById('pageLoadingOverlay');
    if (overlay) {
        overlay.classList.add('fade-out');
        // 动画完成后移除元素
        setTimeout(function() {
            overlay.style.display = 'none';
        }, 500);
    }
}

// 同步数据字典功能
function syncDataDictionary() {
    const syncBtn = document.getElementById('syncBtn');
    const originalText = syncBtn.innerHTML;

    // 显示加载状态
    syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 同步中...';
    syncBtn.disabled = true;

    // 发送同步请求
    fetch('{% url "data_dictionary:sync_data_dictionary" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: 'databases=jkx&databases=nhb&databases=dw&databases=umami&force=true'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 根据是否异步模式决定处理方式
            if (data.async_mode) {
                // Gevent异步模式：保持同步中状态，定期检查完成状态
                showMessage('info', '数据同步已开始，正在后台处理...');

                // 开始轮询同步状态
                pollSyncStatus(syncBtn, originalText);

            } else {
                // 同步模式：同步已完成，立即处理
                showMessage('success', data.message);

                // 如果有输出信息，显示在控制台
                if (data.output) {
                    console.log('同步输出:', data.output);
                }

                // 如果有错误输出，也显示在控制台
                if (data.stderr) {
                    console.warn('同步警告/错误:', data.stderr);
                }

                // 恢复按钮状态
                syncBtn.innerHTML = originalText;
                syncBtn.disabled = false;

                // 延迟刷新页面
                setTimeout(() => {
                    location.reload();
                }, 1500);
            }
        } else {
            // 显示详细错误信息
            let errorMsg = data.message;
            if (data.error_detail) {
                errorMsg += '\n详细错误: ' + data.error_detail;
            }
            if (data.stderr) {
                errorMsg += '\n错误输出: ' + data.stderr;
            }

            showMessage('error', data.message);
            console.error('同步失败详情:', data);

            // 恢复按钮状态
            syncBtn.innerHTML = originalText;
            syncBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('同步失败:', error);
        showMessage('error', '同步失败，请稍后重试');
        // 恢复按钮状态
        syncBtn.innerHTML = originalText;
        syncBtn.disabled = false;
    });
}

// 轮询同步状态
function pollSyncStatus(syncBtn, originalText, maxAttempts = 60, currentAttempt = 0) {
    // 最多轮询60次，每次间隔5秒，总共5分钟
    if (currentAttempt >= maxAttempts) {
        showMessage('warning', '同步时间较长，请稍后手动刷新页面查看结果');
        syncBtn.innerHTML = originalText;
        syncBtn.disabled = false;
        return;
    }

    // 更新按钮文本显示进度
    const dots = '.'.repeat((currentAttempt % 3) + 1);
    syncBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 同步中${dots}`;

    // 检查同步状态
    fetch('{% url "data_dictionary:sync_status" %}', {
        method: 'GET',
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.status === 'completed') {
            // 同步完成
            showMessage('success', '数据同步完成');
            syncBtn.innerHTML = originalText;
            syncBtn.disabled = false;
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            // 继续轮询
            setTimeout(() => {
                pollSyncStatus(syncBtn, originalText, maxAttempts, currentAttempt + 1);
            }, 5000); // 5秒后再次检查
        }
    })
    .catch(error => {
        console.error('状态检查失败:', error);
        // 继续轮询，但减少剩余次数
        setTimeout(() => {
            pollSyncStatus(syncBtn, originalText, maxAttempts, currentAttempt + 2);
        }, 5000);
    });
}

// 显示消息提示
function showMessage(type, message) {
    let alertClass, iconClass;

    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            iconClass = 'fa-check-circle';
            break;
        case 'info':
            alertClass = 'alert-info';
            iconClass = 'fa-info-circle';
            break;
        case 'error':
        default:
            alertClass = 'alert-danger';
            iconClass = 'fa-exclamation-circle';
            break;
    }

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 在页面顶部显示消息
    const container = document.querySelector('.stats-container');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 5秒后自动隐藏
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 初始化图表
function initCharts() {
    // 从DOM中获取数据
    const databaseDistributionData = JSON.parse(document.getElementById('database-distribution-data').textContent);

    // 数据库分布饼图
    const databaseCtx = document.getElementById('databasePieChart').getContext('2d');
    const databaseData = {
        labels: databaseDistributionData.map(item => item.database_name),
        datasets: [{
            data: databaseDistributionData.map(item => item.count),
            backgroundColor: [
                '#0D6EFD',
                '#6BB6FF',
                '#20C997',
                '#FFC107',
                '#DC3545',
                '#6F42C1',
                '#FD7E14',
                '#198754'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };

    new Chart(databaseCtx, {
        type: 'pie',
        data: databaseData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                        }
                    }
                }
            },
            animation: {
                duration: 1000
            }
        }
    });

    // 数据类型条形图
    const dataTypeStatsData = JSON.parse(document.getElementById('data-type-stats-data').textContent);
    const dataTypeCtx = document.getElementById('dataTypeBarChart').getContext('2d');
    const dataTypeData = {
        labels: dataTypeStatsData.map(item => item.data_type),
        datasets: [{
            label: '使用次数',
            data: dataTypeStatsData.map(item => item.count),
            backgroundColor: 'rgba(13, 110, 253, 0.8)',
            borderColor: '#0D6EFD',
            borderWidth: 1
        }]
    };

    new Chart(dataTypeCtx, {
        type: 'bar',
        data: dataTypeData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    },
                    grid: {
                        display: false
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    },
                    grid: {
                        display: false
                    }
                }
            },
            animation: {
                duration: 1000
            }
        }
    });
}

// 清除统计缓存功能
function clearStatisticsCache() {
    const clearBtn = document.getElementById('clearCacheBtn');
    const originalText = clearBtn.innerHTML;

    // 显示加载状态
    clearBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 清除中...';
    clearBtn.disabled = true;

    // 发送清除缓存请求
    fetch('{% url "data_dictionary:clear_cache" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: 'type=statistics'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            // 延迟刷新页面
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showMessage('error', data.message || '清除缓存失败');
        }
    })
    .catch(error => {
        console.error('清除缓存错误:', error);
        showMessage('error', '网络错误，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        clearBtn.innerHTML = originalText;
        clearBtn.disabled = false;
    });
}
</script>
{% endblock %}
