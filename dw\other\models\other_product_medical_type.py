from django.db import models
from common.models import BaseModel
from django.db.models import constraints

class OtherProductMedicalType(BaseModel):
    product_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='名称')
    medicare_type = models.CharField(max_length=200, blank=True, null=True, verbose_name='医保类型')
    person_num = models.IntegerField(blank=True, null=True,verbose_name='参保人数')

    class Meta:
        db_table = 'other_product_medical_type'
        verbose_name = '销售-投保人员医保类型'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_name', 'medicare_type'],
                name='unique_other_product_medical_type_combination'
            ),
        ]
