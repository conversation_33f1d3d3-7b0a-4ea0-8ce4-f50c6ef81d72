# Generated by Django 3.2.12 on 2025-03-14 14:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0013_auto_20250312_1051'),
    ]

    operations = [
        migrations.AddField(
            model_name='claimareapay',
            name='insured_person_number',
            field=models.IntegerField(blank=True, null=True, verbose_name='参保人数'),
        ),
        migrations.AddField(
            model_name='claimtopcaseinfo',
            name='city',
            field=models.CharField(blank=True, max_length=32, null=True, verbose_name='地市'),
        ),
        migrations.AddField(
            model_name='claimtopcaseinfo',
            name='is_provincial',
            field=models.IntegerField(blank=True, null=True, verbose_name='是否省级数据'),
        ),
    ]
