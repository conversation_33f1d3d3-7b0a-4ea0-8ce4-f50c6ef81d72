import django

from django.db import models
from common.models import BaseModel
from django.db.models import constraints
from utils.column_name import db_comment_kwarg


class InsurePvUv(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码', **db_comment_kwarg('产品集编码'))
    end_date = models.DateField(blank=True, null=True, verbose_name='截止日期', **db_comment_kwarg('截止日期'))
    source_group = models.CharField(max_length=32, blank=True, null=True, verbose_name='渠道分组', **db_comment_kwarg('渠道分组'))
    source_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='渠道编码', **db_comment_kwarg('渠道编码'))
    type = models.CharField(max_length=32, blank=True, null=True, verbose_name='类型', **db_comment_kwarg('类型'))
    count = models.IntegerField(blank=True, null=True, verbose_name='数量', **db_comment_kwarg('数量'))

    class Meta:
        db_table = 'insure_pvuv'
        verbose_name = '健康险PVUV'
        verbose_name_plural = verbose_name

        # Django 4.2+会自动识别这个属性，低版本会忽略
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'end_date', 'source_group','source_code', 'type'],
                name='unique_insure_pvuv_combination'
            ),
        ]