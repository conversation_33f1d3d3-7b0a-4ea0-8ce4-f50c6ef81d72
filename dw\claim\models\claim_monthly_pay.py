from django.db import models
from common.models import BaseModel


class ClaimMonthlyPay(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市')
    pay_month = models.CharField(max_length=32, blank=True, null=True, verbose_name='赔付月份')
    pay_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人次')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')

    class Meta:
        db_table = 'claim_monthly_pay'
        verbose_name = '理赔-月度赔付情况'
        verbose_name_plural = verbose_name

