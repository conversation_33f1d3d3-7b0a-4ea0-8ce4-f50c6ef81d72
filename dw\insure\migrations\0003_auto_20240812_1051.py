# Generated by Django 3.2.18 on 2024-08-12 10:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('insure', '0002_auto_20240812_1020'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='insureagent',
            name='unique_insure_agent_combination',
        ),
        migrations.RemoveConstraint(
            model_name='insureagesex',
            name='unique_insure_age_sex_combination',
        ),
        migrations.RemoveConstraint(
            model_name='insurearea',
            name='unique_insure_area_combination',
        ),
        migrations.RemoveConstraint(
            model_name='insureonline',
            name='unique_insure_online_combination',
        ),
        migrations.RemoveField(
            model_name='insureagent',
            name='description',
        ),
        migrations.RemoveField(
            model_name='insureagesex',
            name='description',
        ),
        migrations.RemoveField(
            model_name='insurearea',
            name='description',
        ),
        migrations.RemoveField(
            model_name='insureonline',
            name='description',
        ),
        migrations.AddField(
            model_name='insureagent',
            name='additional_info',
            field=models.CharField(blank=True, max_length=128, null=True, verbose_name='补充信息'),
        ),
        migrations.AddField(
            model_name='insureagesex',
            name='additional_info',
            field=models.CharField(blank=True, max_length=128, null=True, verbose_name='补充信息'),
        ),
        migrations.AddField(
            model_name='insurearea',
            name='additional_info',
            field=models.CharField(blank=True, max_length=128, null=True, verbose_name='补充信息'),
        ),
        migrations.AddField(
            model_name='insureonline',
            name='additional_info',
            field=models.CharField(blank=True, max_length=128, null=True, verbose_name='补充信息'),
        ),
        migrations.AddConstraint(
            model_name='insureagent',
            constraint=models.UniqueConstraint(fields=('product_set_code', 'publish_time', 'name', 'additional_info'), name='unique_insure_agent_combination'),
        ),
        migrations.AddConstraint(
            model_name='insureagesex',
            constraint=models.UniqueConstraint(fields=('product_set_code', 'publish_time', 'sex', 'age_distribution', 'additional_info'), name='unique_insure_age_sex_combination'),
        ),
        migrations.AddConstraint(
            model_name='insurearea',
            constraint=models.UniqueConstraint(fields=('product_set_code', 'publish_time', 'name', 'additional_info'), name='unique_insure_area_combination'),
        ),
        migrations.AddConstraint(
            model_name='insureonline',
            constraint=models.UniqueConstraint(fields=('product_set_code', 'publish_time', 'channel_name', 'additional_info'), name='unique_insure_online_combination'),
        ),
    ]
