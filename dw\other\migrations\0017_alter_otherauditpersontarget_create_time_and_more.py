# Generated by Django 4.2.1 on 2025-05-26 09:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("other", "0016_alter_otherauditpersontarget_product_serial_code"),
    ]

    operations = [
        migrations.AlterField(
            model_name="otherauditpersontarget",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherauditpersontarget",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="othermanagementstaff",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="othermanagementstaff",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproduct",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproduct",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductauditperson",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductauditperson",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductcode",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductcode",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductinsuretype",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductinsuretype",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductmedicaltype",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductmedicaltype",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductproton",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductproton",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductresponse",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherproductresponse",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="othersentryreplayfiles",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="othersentryreplayfiles",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherstreamlitkeymapping",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherstreamlitkeymapping",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherybstatisticnhb",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="otherybstatisticnhb",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
    ]
