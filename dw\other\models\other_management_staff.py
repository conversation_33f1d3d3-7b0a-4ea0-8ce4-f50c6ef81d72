from django.db import models
from common.models import BaseModel


class OtherManagementStaff(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    short_name = models.CharField(max_length=32, blank=True, null=True, verbose_name='保司简称')
    agent_id = models.BigIntegerField(blank=True, null=True, verbose_name='人员id')
    name = models.CharField(max_length=32, blank=True, null=True, verbose_name='人员姓名')
    type = models.CharField(max_length=32, blank=True, null=True, verbose_name='人员类型')
    additional_info = models.CharField(max_length=128, blank=True, null=True, verbose_name='补充信息')

    class Meta:
        db_table = 'other_management_staff'
        verbose_name = '保司管理层人员信息'
        verbose_name_plural = verbose_name
