from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class PromotionPlan(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    name = models.CharField(max_length=500, blank=True, null=True, verbose_name='活动名称')
    person_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='责任人')
    promotion_type = models.CharField(max_length=64, blank=True, null=True, verbose_name='营销活动大类')
    type = models.CharField(max_length=64, blank=True, null=True, verbose_name='活动类型')
    start_time = models.DateTimeField(blank=True, null=True, verbose_name='开始时间')
    end_time = models.DateTimeField(blank=True, null=True, verbose_name='结束时间')
    additional_info = models.CharField(max_length=1000, blank=True, null=True,  verbose_name='补充信息')

    class Meta:
        db_table = 'promotion_plan'
        verbose_name = '营销活动计划表'
        verbose_name_plural = verbose_name
