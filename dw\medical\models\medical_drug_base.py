from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalDrugBase(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='药品代码', **_db_comment_kwarg('药品代码'))
    type = models.CharField(max_length=32, blank=True, null=True, verbose_name='药品分类', **_db_comment_kwarg('药品分类'))
    type_name = models.Char<PERSON>ield(max_length=64, blank=True, null=True, verbose_name='药品分类名称', **_db_comment_kwarg('药品分类名称'))
    category_name = models.CharField(max_length=32, blank=True, null=True, verbose_name='药品类别', **_db_comment_kwarg('药品类别'))
    market_status = models.Char<PERSON>ield(max_length=32, blank=True, null=True, verbose_name='药品上市状态', **_db_comment_kwarg('药品上市状态'))
    registered_name = models.Char<PERSON>ield(max_length=128, blank=True, null=True, verbose_name='注册名称', **_db_comment_kwarg('注册名称'))
    registered_dosage_form = models.CharField(max_length=32, blank=True, null=True, verbose_name='注册剂型', **_db_comment_kwarg('注册剂型'))
    registered_specifications = models.CharField(max_length=1024, blank=True, null=True, verbose_name='注册规格', **_db_comment_kwarg('注册规格'))
    product_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='商品名称', **_db_comment_kwarg('商品名称'))
    expiration = models.CharField(max_length=255, blank=True, null=True, verbose_name='有效期', **_db_comment_kwarg('有效期'))
    dosage_form = models.CharField(max_length=128, blank=True, null=True, verbose_name='剂型', **_db_comment_kwarg('剂型'))
    specifications = models.CharField(max_length=255, blank=True, null=True, verbose_name='规格', **_db_comment_kwarg('规格'))
    packaging_material = models.CharField(max_length=255, blank=True, null=True, verbose_name='包装材质', **_db_comment_kwarg('包装材质'))
    each_dose = models.TextField(blank=True, null=True, verbose_name='每次剂量说明', **_db_comment_kwarg('每次剂量说明'))
    efficacy_information = models.TextField(blank=True, null=True, verbose_name='疗效说明', **_db_comment_kwarg('疗效说明'))
    minimum_packaging_count = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小包装数量', **_db_comment_kwarg('最小包装数量'))
    minimum_preparation_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小制剂单位', **_db_comment_kwarg('最小制剂单位'))
    minimum_packaging_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小包装单位', **_db_comment_kwarg('最小包装单位'))
    minimum_prescription_unit = models.CharField(max_length=64, blank=True, null=True, verbose_name='最小处方单位', **_db_comment_kwarg('最小处方单位'))
    otc_flag = models.IntegerField(blank=True, null=True, verbose_name='是否处方药', **_db_comment_kwarg('是否处方药'))
    listed_license_holder = models.CharField(max_length=255, blank=True, null=True, verbose_name='药品持有企业', **_db_comment_kwarg('药品持有企业'))
    production_company_name = models.CharField(max_length=2028, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    approval_number = models.CharField(max_length=255, blank=True, null=True, verbose_name='批准文号', **_db_comment_kwarg('批准文号'))
    standard_code = models.CharField(max_length=255, blank=True, null=True, verbose_name='药品本位码', **_db_comment_kwarg('药品本位码'))
    categories_national = models.CharField(max_length=32, blank=True, null=True, verbose_name='甲乙类(国家医保药品目录)', **_db_comment_kwarg('甲乙类(国家医保药品目录)'))
    number_national = models.CharField(max_length=255, blank=True, null=True, verbose_name='编号(国家医保药品目录)', **_db_comment_kwarg('编号(国家医保药品目录)'))
    generic_name_national = models.CharField(max_length=255, blank=True, null=True, verbose_name='药品名称(国家医保药品目录)', **_db_comment_kwarg('药品名称(国家医保药品目录)'))
    dosage_form_national = models.CharField(max_length=255, blank=True, null=True, verbose_name='剂型(国家医保药品目录)', **_db_comment_kwarg('剂型(国家医保药品目录)'))
    remark_national = models.TextField(blank=True, null=True, verbose_name='备注(国家医保药品目录)', **_db_comment_kwarg('备注(国家医保药品目录)'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_drug_base'
        verbose_name = '医保药品信息基础表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name
