# Generated by Django 4.2.1 on 2025-07-25 13:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0018_add_table_comments"),
    ]

    operations = [
        migrations.AlterModelTableComment(
            name="insurebeacon",
            table_comment="销售埋点统计表",
        ),
        migrations.AlterModelTableComment(
            name="insurepvuv",
            table_comment="健康险PVUV",
        ),
        migrations.AlterModelTableComment(
            name="insurewebsitestatistics",
            table_comment="销售网站埋点统计",
        ),
        migrations.AlterField(
            model_name="insurewebsitestatistics",
            name="avg_visit_duration",
            field=models.DecimalField(
                blank=True,
                db_comment="平均访问时长(秒)",
                decimal_places=2,
                max_digits=20,
                null=True,
                verbose_name="平均访问时长(秒)",
            ),
        ),
        migrations.AlterField(
            model_name="insurewebsitestatistics",
            name="bounce_rate",
            field=models.DecimalField(
                blank=True,
                db_comment="跳出率",
                decimal_places=2,
                max_digits=5,
                null=True,
                verbose_name="跳出率",
            ),
        ),
    ]
