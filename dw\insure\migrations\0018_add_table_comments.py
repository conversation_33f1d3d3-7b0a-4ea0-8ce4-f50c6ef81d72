# Generated by Django 4.2.1 on 2025-07-25 11:08

from django.db import migrations


def add_table_comments(apps, schema_editor):
    """添加表注释"""
    with schema_editor.connection.cursor() as cursor:
        # 添加表注释
        cursor.execute("ALTER TABLE insure_pvuv COMMENT = '健康险PVUV统计表'")
        cursor.execute("ALTER TABLE insure_website_statistics COMMENT = '保险产品网站统计表'")
        cursor.execute("ALTER TABLE insure_beacon COMMENT = '保险产品埋点统计表'")


def remove_table_comments(apps, schema_editor):
    """移除表注释"""
    with schema_editor.connection.cursor() as cursor:
        # 移除表注释
        cursor.execute("ALTER TABLE insure_pvuv COMMENT = ''")
        cursor.execute("ALTER TABLE insure_website_statistics COMMENT = ''")
        cursor.execute("ALTER TABLE insure_beacon COMMENT = ''")


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0017_insurebeacon_insurewebsitestatistics_and_more"),
    ]

    operations = [
        migrations.RunPython(add_table_comments, remove_table_comments),
    ]
