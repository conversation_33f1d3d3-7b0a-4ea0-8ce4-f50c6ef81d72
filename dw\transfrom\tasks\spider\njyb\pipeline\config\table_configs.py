#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
南京医保局(njyb)表配置
定义ETL处理的表映射关系和配置参数
"""

# 南京医保局ETL表配置
NJYB_TABLE_CONFIGS = {
    "南京医疗服务项目": {
        "source_table": "spider_njyb_service_facilities",
        "target_table": "medical_service_entity",
        "unique_fields": ['code', 'central_code'],
        "description": "南京医保局医疗服务项目数据",
        "icon": "[MEDICAL]",
        "target_where_clause": "province_name='江苏省' AND level_type='city' AND city_name='南京市'",
        "notes": "来源表spider_njyb_service_facilities，目标表medical_service_entity，additional_info字段使用字典存储，key为来源表的字段名改成用_进行分割"
    },
    "南京医保耗材": {
        "source_table": "spider_njyb_medical_supplies",
        "target_table": "medical_supplies_entity",
        "unique_fields": ['code', 'central_code'],
        "description": "南京医保局耗材数据",
        "icon": "[SUPPLIES]",
        "target_where_clause": "province_name='江苏省' AND level_type='city' AND city_name='南京市'",
        "notes": ""
    },
    "南京医保药品": {
        "source_table": "spider_njyb_drug",
        "target_table": "medical_drug_entity",
        "unique_fields": ['code', 'central_code'],
        "description": "南京医保局药品数据",
        "icon": "[DRUG]",
        "target_where_clause": "province_name='江苏省' AND level_type='city' AND city_name='南京市'",
        "notes": ""
    }
}

# 处理模式配置
PROCESSING_MODES = {
    "1": {
        "name": "全量处理",
        "description": "处理所有数据",
        "icon": "[ALL]",
        "limit": None,
        "process_all": True
    },
    "2": {
        "name": "测试模式",
        "description": "处理前100条数据",
        "icon": "[TEST]",
        "limit": 100,
        "process_all": False
    },
    "3": {
        "name": "小批量",
        "description": "处理前1000条数据",
        "icon": "[SMALL]",
        "limit": 1000,
        "process_all": False
    },
    "4": {
        "name": "中批量",
        "description": "处理前5000条数据",
        "icon": "[MEDIUM]",
        "limit": 5000,
        "process_all": False
    }
}

# 操作模式配置
OPERATION_MODES = {
    "1": {
        "name": "智能更新模式",
        "description": "自动判断新增、更新、删除操作",
        "icon": "[AUTO]",
        "mode": "upsert"
    },
    "2": {
        "name": "传统模式",
        "description": "传统的ETL处理方式",
        "icon": "[TRAD]",
        "mode": "traditional"
    }
}

# 字段映射配置
FIELD_MAPPING_CONFIG = {
    "spider_njyb_service_facilities": {
        "target_table": "medical_service_entity",
        "field_mappings": {
            # 基础信息字段 - 根据实际源表字段名调整
            "serviceCode": "code",  # 服务项目代码
            "serviceName": "name",  # 服务项目名称
            "chargeItemCode": "charge_item_code",  # 收费项目代码
            "chargeItemName": "charge_item_name",  # 收费项目名称
            "centralCode": "central_code",  # 中心编码
            "treatmentContent": "treatment_item_content",  # 诊疗项目内涵
            "treatmentDescription": "treatment_item_description",  # 诊疗项目说明
            "excludedContent": "treatment_excluded_content",  # 诊疗除外内容
            "pricingUnit": "pricing_unit",  # 计价单位
            "paymentType": "payment_type",  # 医保支付类别
            "paymentUpperLimit": "payment_upper_limit",  # 医保支付上限
            "price": "price",  # 供应价格
            "priceComposition": "price_composition",  # 价格构成
            "serviceOutput": "service_output",  # 服务产出
            "initialPaymentRatio": "initial_payment_ratio",  # 先行自付比例
            "limitedPayment": "limited_payment",  # 限定支付
            "beginDate": "begin_date",  # 开始日期
            "remark": "remark"  # 备注
        },
        "additional_info_fields": [
            # 这里列出需要放入additional_info的字段
            # 所有不在field_mappings中的字段都会被转换为下划线格式放入additional_info
        ],
        "geographic_info": {
            "province_code": "32",
            "province_name": "江苏省",
            "city_code": "3201",  # 南京市编码
            "city_name": "南京市",
            "level_type": "city"  # 市级数据
        }
    }
}

# 数据验证配置
VALIDATION_CONFIG = {
    "spider_njyb_service_facilities": {
        "required_fields": ["medListCodg", "itemname"],  # 必需字段（使用源表字段名）
        "unique_constraints": ["medListCodg", "centCodg"],  # 唯一性约束字段（与table_configs的unique_fields对应）
        "data_types": {
            "lv1Pric": "numeric",  # 价格字段（源表字段名）
            "selfpayProp": "numeric"  # 先行自付比例（源表字段名）
        }
    },
    "spider_njyb_medical_supplies": {
        "required_fields": ["medListCodg", "useName"],  # 必需字段（使用源表字段名）
        "unique_constraints": ["medListCodg", "centCodg"]  # 唯一性约束字段（与table_configs的unique_fields对应）
    }
}

# 清洗规则配置
CLEANING_RULES = {
    "spider_njyb_service_facilities": {
        "null_values": ['-', '--', '—', '/', '\\', '', 'nan', 'NaN', 'null', 'NULL'],
        "camel_to_snake_conversion": True,  # 启用驼峰转下划线转换
        "additional_info_processing": True,  # 启用additional_info处理
        "geographic_info_injection": True  # 启用地理信息注入
    },
    "spider_njyb_medical_supplies": {
        "null_values": ['-', '--', '—', '/', '\\', '', 'nan', 'NaN', 'null', 'NULL'],
        "geographic_info_injection": True  # 启用地理信息注入
    }
}


def get_table_config(table_name):
    """
    获取指定表的配置信息
    
    Args:
        table_name (str): 表名
        
    Returns:
        dict: 表配置信息
    """
    return NJYB_TABLE_CONFIGS.get(table_name, {})


def get_all_table_configs():
    """
    获取所有表配置信息
    
    Returns:
        dict: 所有表配置信息
    """
    return NJYB_TABLE_CONFIGS


def get_processing_modes():
    """
    获取处理模式配置
    
    Returns:
        dict: 处理模式配置
    """
    return PROCESSING_MODES


def get_operation_modes():
    """
    获取操作模式配置
    
    Returns:
        dict: 操作模式配置
    """
    return OPERATION_MODES


def get_field_mapping_config(source_table):
    """
    获取字段映射配置
    
    Args:
        source_table (str): 源表名
        
    Returns:
        dict: 字段映射配置
    """
    return FIELD_MAPPING_CONFIG.get(source_table, {})


def get_validation_config(source_table):
    """
    获取数据验证配置
    
    Args:
        source_table (str): 源表名
        
    Returns:
        dict: 数据验证配置
    """
    return VALIDATION_CONFIG.get(source_table, {})


def get_cleaning_rules(source_table):
    """
    获取清洗规则配置
    
    Args:
        source_table (str): 源表名
        
    Returns:
        dict: 清洗规则配置
    """
    return CLEANING_RULES.get(source_table, {})


# 导出主要配置
__all__ = [
    'NJYB_TABLE_CONFIGS',
    'PROCESSING_MODES', 
    'OPERATION_MODES',
    'FIELD_MAPPING_CONFIG',
    'VALIDATION_CONFIG',
    'CLEANING_RULES',
    'get_table_config',
    'get_all_table_configs',
    'get_processing_modes',
    'get_operation_modes',
    'get_field_mapping_config',
    'get_validation_config',
    'get_cleaning_rules'
]
