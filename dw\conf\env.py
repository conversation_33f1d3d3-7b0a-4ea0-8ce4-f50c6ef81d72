import os

USER = 'shizy'
PASSWORD = 'rab8btd-bfe9kug'
PASSWORD_GZ = "gTlfX5wPUSUEEpXM"
DEFAULT_PASSWORD = 'WKG9yar1btm9xjq_rjf'

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dw',
        'HOST': '**************',
        'PORT': 3306,
        'USER': 'root',
        'PASSWORD': DEFAULT_PASSWORD,
        'OPTIONS': {
            'charset': 'utf8mb4',
            'use_unicode': True,
        }
    },
    # 'default': {
    #     'ENGINE': 'django.db.backends.mysql',
    #     'NAME': 'dw',
    #     'HOST': 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
    #     'PORT': 3306,
    #     'USER': USER,
    #     'PASSWORD': PASSWORD,
    # },
    'local_default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'dw',
        'HOST': '**************',
        'PORT': 3306,
        'USER': 'root',
        'PASSWORD': DEFAULT_PASSWORD,
    },
    'dw': {
        "ENGINE": "django.db.backends.mysql",  # 本地用，用于写入正式数据库
        "NAME": 'dw',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'jkx': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'jkx_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    # 'jkx': {
    #         "ENGINE": "django.db.backends.mysql",
    #         "NAME": 'jkx', # 测试环境用
    #         "USER": USER,
    #         "PASSWORD": PASSWORD,
    #         "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
    #         "PORT": '3306',
    #     },
    'nhb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'nhb-claim_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'claim': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'claim_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192ao.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'jkx_ghb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'jkx_slave',
        "USER": USER,
        "PASSWORD": PASSWORD_GZ,
        "HOST": 'rm-2zev32g9b4fvb27o3fo.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'ghb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'insurance-claim-prod',
        "USER": USER,
        "PASSWORD": PASSWORD_GZ,
        "HOST": 'jkx-ghb-public.mysql.polardb.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'umami': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'umami',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'polardb-ai.rwlb.rds.aliyuncs.com',
        "PORT": '3306',
    }
}

# ================================================= #
# ******** redis配置，用于缓存数据  ******** #
# ================================================= #
REDIS_PASSWORD = '2279856'
REDIS_HOST = '127.0.0.1'
REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:6379/1'
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

###----Celery redis 配置-----###
# Broker配置，使用Redis作为消息中间件
CELERY_BROKER_BACKEND = 'redis'
# redis沒有設置密碼的
CELERY_REDIS_PASSWORD = '2279856'
CELERY_REDIS_HOST = '127.0.0.1'
CELERY_BROKER_URL = f'redis://:{CELERY_REDIS_PASSWORD or ""}@{CELERY_REDIS_HOST}:6379/0'
# 設置密碼用下面的
# CELERY_BROKER_URL = 'redis://:password@127.0.0.1:6379/0'

# ================================================= #
# ******** AI智能翻译配置  ******** #
# ================================================= #

# SiliconFlow API配置 - 支持多模型配置
AI_TRANSLATION_CONFIG = {
    'api_url': 'https://api.siliconflow.cn/v1/chat/completions',
    'api_key': 'sk-cilvwwqpcayczwsfrmjutphgpqckzmzleeueteeobbfdqyeg',
    'enabled': True,  # 是否启用AI翻译功能
    'timeout': 30,  # 请求超时时间（秒）

    # 模型配置列表，按优先级排序
    'models': [
        {
            'name': 'THUDM/GLM-4-9B-0414',
            'display_name': 'GLM-4快速模型',
            'max_tokens': 512,  # 适中的输出长度
            'temperature': 0.2,
            'timeout': 20,  # 较短的超时时间
            'priority': 1,  # 优先级最高
            'description': 'GLM-4-9B模型，32k上下文，速度快，适合字段翻译'
        },
        {
            'name': 'Qwen/Qwen3-8B',
            'display_name': 'Qwen3标准模型',
            'max_tokens': 1024,
            'temperature': 0.3,
            'timeout': 30,
            'priority': 2,  # 备用模型
            'description': 'Qwen3标准模型，翻译质量高，适合复杂字段'
        }
    ],

    # 默认使用的模型（向后兼容）
    'model': 'THUDM/GLM-4-9B-0414',
    'max_tokens': 512,
    'temperature': 0.2,
}
