# Generated by Django 3.2.12 on 2025-03-04 13:25

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0008_claimpaygender_claimpaytype_claimsellerpay'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='ClaimPayAgeRange',
            new_name='ClaimAgeRangePay',
        ),
        migrations.RenameModel(
            old_name='ClaimPayAmountRange',
            new_name='ClaimAmountRangePay',
        ),
        migrations.RenameModel(
            old_name='ClaimPayAreaRange',
            new_name='ClaimAreaPay',
        ),
        migrations.RenameModel(
            old_name='ClaimPayGender',
            new_name='ClaimGenderPay',
        ),
        migrations.RenameModel(
            old_name='ClaimPayGroup',
            new_name='ClaimGroupPay',
        ),
        migrations.RenameModel(
            old_name='ClaimPayProduct',
            new_name='ClaimProductPay',
        ),
        migrations.AlterModelTable(
            name='claimagerangepay',
            table='claim_age_range_pay',
        ),
        migrations.AlterModelTable(
            name='claimamountrangepay',
            table='claim_amount_range_pay',
        ),
        migrations.AlterModelTable(
            name='claimareapay',
            table='claim_area_pay',
        ),
        migrations.AlterModelTable(
            name='claimgenderpay',
            table='claim_gender_pay',
        ),
        migrations.AlterModelTable(
            name='claimgrouppay',
            table='claim_group_pay',
        ),
        migrations.AlterModelTable(
            name='claimproductpay',
            table='claim_product_pay',
        ),
    ]
