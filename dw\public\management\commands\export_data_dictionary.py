"""
数据字典导出管理命令
将数据字典信息导出为各种格式（Excel、CSV、Markdown等）
"""
import os
import logging
from datetime import datetime
from django.core.management.base import BaseCommand
from django.db.models import Q
from public.models import PublicDatabaseInfo, PublicTableInfo, PublicColumnInfo, PublicIndexInfo

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '导出数据字典信息为指定格式'

    def add_arguments(self, parser):
        parser.add_argument(
            '--database',
            type=str,
            help='指定要导出的数据库名称，不指定则导出所有数据库'
        )
        parser.add_argument(
            '--tables',
            type=str,
            nargs='*',
            help='指定要导出的表名列表，不指定则导出所有表'
        )
        parser.add_argument(
            '--format',
            type=str,
            choices=['excel', 'csv', 'markdown', 'json'],
            default='excel',
            help='导出格式 (默认: excel)'
        )
        parser.add_argument(
            '--output',
            type=str,
            help='输出文件路径，不指定则使用默认路径'
        )
        parser.add_argument(
            '--include-deprecated',
            action='store_true',
            help='包含已废弃的表'
        )

    def handle(self, *args, **options):
        database_name = options.get('database')
        target_tables = options.get('tables', [])
        export_format = options['format']
        output_path = options.get('output')
        include_deprecated = options['include_deprecated']

        self.stdout.write("开始导出数据字典...")

        try:
            # 构建查询条件
            query_filters = Q()
            if database_name:
                query_filters &= Q(database_name=database_name)
            if target_tables:
                query_filters &= Q(name__in=target_tables)
            if not include_deprecated:
                query_filters &= Q(is_deprecated=False)

            # 获取表信息
            tables = PublicTableInfo.objects.filter(query_filters).order_by('database_name', 'name')
            
            if not tables.exists():
                self.stdout.write(self.style.WARNING("没有找到符合条件的表"))
                return

            # 生成输出文件路径
            if not output_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                db_suffix = f"_{database_name}" if database_name else "_all"
                output_path = f"data_dictionary{db_suffix}_{timestamp}.{export_format}"

            # 根据格式导出
            if export_format == 'excel':
                self.export_to_excel(tables, output_path)
            elif export_format == 'csv':
                self.export_to_csv(tables, output_path)
            elif export_format == 'markdown':
                self.export_to_markdown(tables, output_path)
            elif export_format == 'json':
                self.export_to_json(tables, output_path)

            self.stdout.write(
                self.style.SUCCESS(f"数据字典已导出到: {output_path}")
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"导出过程中发生错误: {str(e)}")
            )
            logger.exception("数据字典导出失败")

    def export_to_excel(self, tables, output_path):
        """导出为Excel格式"""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
        except ImportError:
            self.stdout.write(
                self.style.ERROR("需要安装 pandas 和 openpyxl 库: pip install pandas openpyxl")
            )
            return

        wb = Workbook()

        # 删除默认工作表
        wb.remove(wb.active)

        # 创建数据库概览工作表
        self.create_database_overview_sheet(wb, tables, Font, PatternFill, Alignment)

        # 为每个数据库创建详细工作表
        databases = set(table.database for table in tables)
        for database in databases:
            db_tables = [table for table in tables if table.database == database]
            self.create_database_detail_sheet(wb, database, db_tables, Font, PatternFill, Alignment)

        wb.save(output_path)

    def create_database_overview_sheet(self, wb, tables, Font, PatternFill, Alignment):
        """创建数据库概览工作表"""
        ws = wb.create_sheet("数据库概览")
        
        # 设置标题
        headers = ['数据库名称', '数据库类型', '表数量', '字段总数', '索引总数', '描述']
        ws.append(headers)
        
        # 设置标题样式
        for cell in ws[1]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 统计数据
        databases = {}
        for table in tables:
            db_name = table.database_name
            if db_name not in databases:
                # 获取数据库信息
                try:
                    from public.models import PublicDatabaseInfo
                    db_info = PublicDatabaseInfo.objects.get(name=db_name)
                except PublicDatabaseInfo.DoesNotExist:
                    db_info = None

                databases[db_name] = {
                    'database': db_info,
                    'table_count': 0,
                    'column_count': 0,
                    'index_count': 0
                }

            databases[db_name]['table_count'] += 1
            # 使用字段名查询
            from public.models import PublicColumnInfo, PublicIndexInfo
            databases[db_name]['column_count'] += PublicColumnInfo.objects.filter(
                table_name=table.name
            ).count()
            databases[db_name]['index_count'] += PublicIndexInfo.objects.filter(
                table_name=table.name
            ).count()
        
        # 添加数据行
        for db_info in databases.values():
            db = db_info['database']
            row = [
                db.name,
                db.type,
                db_info['table_count'],
                db_info['column_count'],
                db_info['index_count'],
                db.description or ''
            ]
            ws.append(row)

    def create_database_detail_sheet(self, wb, database, tables, Font, PatternFill, Alignment):
        """创建数据库详细信息工作表"""
        sheet_name = f"{database.name}_详细信息"[:31]  # Excel工作表名称限制
        ws = wb.create_sheet(sheet_name)
        
        # 数据库信息
        ws.append(['数据库信息'])
        if database:
            ws.append(['数据库名称', database.name])
            ws.append(['数据库类型', database.type])
            ws.append(['描述', database.description or ''])
            ws.append(['数据来源', database.data_source or ''])
            ws.append(['更新频率', database.update_frequency or ''])
        else:
            ws.append(['数据库名称', tables[0].database_name if tables else ''])
            ws.append(['数据库类型', 'Unknown'])
            ws.append(['描述', ''])
            ws.append(['数据来源', ''])
            ws.append(['更新频率', ''])
        ws.append([])  # 空行
        
        # 表信息标题
        table_headers = [
            '表名', '表注释', '表类型', '数据来源', '更新频率', 
            '是否废弃', '存储引擎', '字符集', '唯一索引字段'
        ]
        ws.append(['表信息'])
        ws.append(table_headers)
        
        # 设置表头样式
        header_row = ws.max_row
        for cell in ws[header_row]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
        
        # 添加表数据
        for table in tables:
            row = [
                table.name,
                table.comment or '',
                table.type,
                table.data_source or '',
                table.update_frequency or '',
                '是' if table.is_deprecated else '否',
                table.engine or '',
                table.charset or '',
                table.unique_key_fields or ''
            ]
            ws.append(row)
        
        ws.append([])  # 空行
        
        # 字段信息
        ws.append(['字段信息'])
        column_headers = [
            '表名', '字段名', '字段注释', '数据类型', '完整类型', 
            '最大长度', '精度', '小数位', '允许空值', '默认值',
            '自增', '主键', '唯一', '有索引', '位置'
        ]
        ws.append(column_headers)
        
        # 设置字段表头样式
        header_row = ws.max_row
        for cell in ws[header_row]:
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
        
        # 添加字段数据
        for table in tables:
            from public.models import PublicColumnInfo
            columns = PublicColumnInfo.objects.filter(
                table_name=table.name
            ).order_by('ordinal_position')
            for column in columns:
                row = [
                    table.name,
                    column.name,
                    column.comment or '',
                    column.data_type,
                    column.type,
                    column.max_length,
                    column.numeric_precision,
                    column.numeric_scale,
                    '是' if column.is_nullable else '否',
                    column.default or '',
                    '是' if column.is_auto_increment else '否',
                    '是' if column.is_primary_key else '否',
                    '是' if column.is_unique else '否',
                    '是' if column.is_indexed else '否',
                    column.ordinal_position
                ]
                ws.append(row)

    def export_to_markdown(self, tables, output_path):
        """导出为Markdown格式"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 数据字典\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 按数据库分组
            databases = {}
            for table in tables:
                db_name = table.database_name
                if db_name not in databases:
                    databases[db_name] = []
                databases[db_name].append(table)
            
            for db_name, db_tables in databases.items():
                # 获取数据库信息
                try:
                    from public.models import PublicDatabaseInfo
                    database = PublicDatabaseInfo.objects.get(name=db_name)
                except PublicDatabaseInfo.DoesNotExist:
                    database = None

                f.write(f"## {db_name} 数据库\n\n")
                if database:
                    f.write(f"- **数据库类型**: {database.type}\n")
                    f.write(f"- **描述**: {database.description or '无'}\n")
                    f.write(f"- **数据来源**: {database.data_source or '无'}\n")
                    f.write(f"- **更新频率**: {database.update_frequency or '无'}\n\n")
                else:
                    f.write(f"- **数据库类型**: Unknown\n")
                    f.write(f"- **描述**: 无\n")
                    f.write(f"- **数据来源**: 无\n")
                    f.write(f"- **更新频率**: 无\n\n")
                
                for table in db_tables:
                    f.write(f"### {table.name}\n\n")
                    f.write(f"- **表注释**: {table.comment or '无'}\n")
                    f.write(f"- **表类型**: {table.type}\n")
                    f.write(f"- **数据来源**: {table.data_source or '无'}\n")
                    f.write(f"- **更新频率**: {table.update_frequency or '无'}\n")
                    f.write(f"- **是否废弃**: {'是' if table.is_deprecated else '否'}\n\n")
                    
                    # 字段信息表格
                    f.write("#### 字段信息\n\n")
                    f.write("| 字段名 | 注释 | 数据类型 | 允许空值 | 默认值 | 主键 | 唯一 |\n")
                    f.write("|--------|------|----------|----------|--------|------|------|\n")

                    from public.models import PublicColumnInfo
                    columns = PublicColumnInfo.objects.filter(
                        table_name=table.name
                    ).order_by('ordinal_position')
                    for column in columns:
                        f.write(f"| {column.name} | {column.comment or ''} | {column.type} | "
                               f"{'是' if column.is_nullable else '否'} | {column.default or ''} | "
                               f"{'是' if column.is_primary_key else '否'} | "
                               f"{'是' if column.is_unique else '否'} |\n")
                    
                    f.write("\n")
                    
                    # 索引信息
                    from public.models import PublicIndexInfo
                    indexes = PublicIndexInfo.objects.filter(
                        table_name=table.name
                    )
                    if indexes.exists():
                        f.write("#### 索引信息\n\n")
                        f.write("| 索引名 | 类型 | 唯一 | 字段 | 注释 |\n")
                        f.write("|--------|------|------|------|------|\n")

                        for index in indexes:
                            f.write(f"| {index.name} | {index.type} | "
                                   f"{'是' if index.is_unique else '否'} | {index.column_names} | "
                                   f"{index.comment or ''} |\n")
                        
                        f.write("\n")

    def export_to_csv(self, tables, output_path):
        """导出为CSV格式"""
        try:
            import pandas as pd
        except ImportError:
            self.stdout.write(
                self.style.ERROR("需要安装 pandas 库: pip install pandas")
            )
            return
        
        # 准备数据
        data = []
        for table in tables:
            from public.models import PublicColumnInfo
            columns = PublicColumnInfo.objects.filter(
                table_name=table.name
            ).order_by('ordinal_position')
            for column in columns:
                data.append({
                    '数据库名': table.database_name,
                    '数据库类型': 'MySQL',  # 默认类型，可以从数据库信息表获取
                    '表名': table.name,
                    '表注释': table.comment or '',
                    '字段名': column.name,
                    '字段注释': column.comment or '',
                    '数据类型': column.data_type,
                    '完整类型': column.type,
                    '允许空值': '是' if column.is_nullable else '否',
                    '默认值': column.default or '',
                    '主键': '是' if column.is_primary_key else '否',
                    '唯一': '是' if column.is_unique else '否',
                    '自增': '是' if column.is_auto_increment else '否',
                    '位置': column.ordinal_position
                })
        
        df = pd.DataFrame(data)
        df.to_csv(output_path, index=False, encoding='utf-8-sig')

    def export_to_json(self, tables, output_path):
        """导出为JSON格式"""
        import json
        
        data = {
            'export_time': datetime.now().isoformat(),
            'databases': {}
        }
        
        # 按数据库分组
        for table in tables:
            db_name = table.database.name
            if db_name not in data['databases']:
                data['databases'][db_name] = {
                    'name': table.database.name,
                    'type': table.database.type,
                    'description': table.database.description,
                    'data_source': table.database.data_source,
                    'update_frequency': table.database.update_frequency,
                    'tables': {}
                }
            
            # 表信息
            table_data = {
                'name': table.name,
                'comment': table.comment,
                'type': table.type,
                'data_source': table.data_source,
                'update_frequency': table.update_frequency,
                'is_deprecated': table.is_deprecated,
                'engine': table.engine,
                'charset': table.charset,
                'unique_key_fields': table.unique_key_fields,
                'columns': [],
                'indexes': []
            }
            
            # 字段信息
            columns = table.publiccolumninfo_set.all().order_by('ordinal_position')
            for column in columns:
                table_data['columns'].append({
                    'name': column.name,
                    'comment': column.comment,
                    'data_type': column.data_type,
                    'type': column.type,
                    'max_length': column.max_length,
                    'numeric_precision': column.numeric_precision,
                    'numeric_scale': column.numeric_scale,
                    'is_nullable': column.is_nullable,
                    'default': column.default,
                    'is_auto_increment': column.is_auto_increment,
                    'is_primary_key': column.is_primary_key,
                    'is_unique': column.is_unique,
                    'is_indexed': column.is_indexed,
                    'ordinal_position': column.ordinal_position
                })
            
            # 索引信息
            indexes = table.publicindexinfo_set.all()
            for index in indexes:
                table_data['indexes'].append({
                    'name': index.name,
                    'type': index.type,
                    'is_unique': index.is_unique,
                    'is_primary': index.is_primary,
                    'column_names': index.column_names,
                    'column_orders': index.column_orders,
                    'comment': index.comment
                })
            
            data['databases'][db_name]['tables'][table.name] = table_data
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
