import warnings
from pathlib import Path

import pandas as pd
import streamlit as st

from utils.st import query_sql, empty_line
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        ps.name AS product_set_name,
        ps.code AS product_set_code,
        DATE_FORMAT(MIN(p.sale_from), '%Y-%m-%d') AS sale_from,
        DATE_FORMAT(MIN(p.pre_sale_from), '%Y-%m-%d') AS pre_sale_from
    FROM
        product_set ps
    JOIN
        product p ON p.product_set_id = ps.id AND p.main = 1
    WHERE
        LEFT(ps.code, 10) in ('rizhao_nxb','dezhou_hmb','binzhou_yh')
    GROUP BY
        ps.name
    ORDER BY
        ps.name DESC;

        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_phone_number(product_set_code, sql=query_sql('SQL_SALE_PERSON_INFO')):
    """
    获取产品集的电话号码
    :param product_set_code:
    :return:
    """
    df_phone_number = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code), show_spinner='查询中...',
                                          ttl=0)
    return df_phone_number


@st.fragment
def download_excel(name):
    # 下载数据
    excel_name = Path.cwd().joinpath('temp_files').joinpath(name)
    empty_line(1)
    st.download_button(
        label='下载数据',
        data=open(excel_name, 'rb').read(),
        file_name=name,
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


def main():
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("手机号码匹配")
    product_info = get_product_code()

    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                product_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                product_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")

            if product_name:
                product_set_code = product_info[product_info['product_set_name'] == product_name]['product_set_code'].values[0]
            else:
                product_set_code = None

    # 文件上传器
    uploaded_files = st.file_uploader("上传文件", accept_multiple_files=True,
                                      help='上传文件第一列为手机号，需要有列头')
    # 初始化一个空的 DataFrame 用于存储所有数据
    all_dataframes = []
    uploaded_file_name = ''
    if len(uploaded_files) > 0:
        for uploaded_file in uploaded_files:
            if uploaded_file is not None:
                # 检查文件扩展名是否为 .xlsx 或 .xls
                if not (uploaded_file.name.endswith('.xlsx') or uploaded_file.name.endswith('.xls')):
                    st.error(f"无效的文件格式：{uploaded_file.name}。请上传 .xlsx 或 .xls 文件。")
                    continue

                try:
                    # 读取文件为 DataFrame
                    dataframe = pd.read_excel(uploaded_file, header=0,dtype='str')
                    uploaded_file_name = uploaded_file_name + uploaded_file.name

                    # 将第一行设置为列名，并删除第一行
                    dataframe.columns = dataframe.iloc[0]
                    dataframe = dataframe.iloc[1:]

                    # 将第一列的列名改为 'mobile'
                    dataframe.rename(columns={dataframe.columns[0]: 'mobile'}, inplace=True)
                    dataframe = dataframe[['mobile']]

                    # 将当前 DataFrame 添加到列表中
                    all_dataframes.append(dataframe)

                except Exception as e:
                    st.error(f"读取文件 {uploaded_file.name} 时发生错误：{e}")

        if st.button('开始匹配'):
            if product_set_code is None:
                st.warning("请选择产品")
            else:
                with st.spinner('匹配中...'):
                    # 拼接所有 DataFrame
                    if all_dataframes:
                        combined_dataframe = pd.concat(all_dataframes, ignore_index=True)
                        combined_dataframe.drop_duplicates(subset=['mobile'], inplace=True)
                        uploaded_file_name = ('更新_' + uploaded_file_name.replace('.xlsx', '').replace('.xls', ''))[:30]

                        df_phone_number = get_phone_number(product_set_code)
                        df_phone_number['is_buy'] = 1
                        # 上传文件匹配出没有购买的手机号
                        # print(combined_dataframe.head())
                        combined_dataframe['mobile'] = combined_dataframe['mobile'].astype(str).str.strip()
                        df_phone_number['mobile'] = df_phone_number['mobile'].astype(str).str.strip()
                        df_phone_number_not_buy = pd.merge(combined_dataframe, df_phone_number, how='left', on='mobile')
                        df_phone_number_not_buy = df_phone_number_not_buy[df_phone_number_not_buy['is_buy'].isna()]
                        # print(df_phone_number_not_buy.head())
                        df_phone_number_not_buy[['mobile']].to_excel(f'temp_files/{uploaded_file_name}.xlsx', index=False)
                download_excel(f'{uploaded_file_name}.xlsx')
    else:
        st.info("请上传文件")