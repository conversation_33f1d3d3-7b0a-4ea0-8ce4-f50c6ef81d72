import datetime
import logging
import warnings
from decimal import Decimal

import idna
import numpy as np
import pandas as pd
import pymysql
from django.db import transaction
from django.db.models import Q
from pandasql import sqldf

from dw import settings
from insure.models import InsureOnline, InsureAgent, InsureArea, InsureAgeSex
from public.models import PublicIndicatorData, PublicIndicatorMain, PublicStatistics, PublicAreaBaseInsure, \
    PublicTarget, PublicMapping
from task.utils.cache_manager import CacheManager
from transfrom.utils.utils import simplify_replace, query_sql, sum_or_combine, age_group, custom_update_or_create, \
    send_feishu_message, query_indicator_code, query_indicator_code_v1

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class GzGhbInsureCityV2(CacheManager):
    """
    贵惠保-三期数据处理（城市数据处理）
    指标名称数据需要手动添加，不自动生成
    关于所有的参保，只统计主险
    """

    def __init__(self):
        super().__init__()
        self.product_set_code = 'guihuibaoV2'
        self.previous_product_set_code = 'guihuibaoV1'
        self.DB = settings.DATABASES['jkx_ghb']
        self.DB_DW = settings.DATABASES['default']
        self.version = '贵州惠民保-二期'
        self.previous_version = '贵州惠民保-一期'
        self.INSURE_TARGET = 1930000  # 销售目标，数据不等于线上+线下，所以单独留着
        self.INSURE_TARGET_AMOUNT = 0  # 销售目标金额，数据不等于线上+线下，所以单独留着——这里用不到，忽略
        self.type = 'insure'  # 统计大类
        self.sale_start_time = '2023-12-27 00:00:00'  # 销售起始时间（含预售）
        self.sale_start_date = datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S').date()
        # self.end_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
        self.end_time = '2024-06-18 00:00:00'
        self.today = datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S')
        self.yesterday = datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
            days=1)
        # self.publish_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.publish_time = '2024-06-18 00:00:00'
        self.group_count_statistic = 1  # 1：根据数据表计算   2：根据手动数据统计  3:两者都计算
        self.complete_statistic = 'count'  # count：根据单量计算   amount：根据金额计算
        self.seller_list = ['安诚财险', '华贵保险', '太平财险', '中国人寿', '新华保险', '太平洋人寿', '平安财险',
                            '中国人保财险', '大家财险', '太平洋产险', '大地保险', '国寿财险']
        self.channel_list = ['安诚财险', '华贵保险', '太平财险', '中国人寿', '新华保险', '太平洋人寿', '平安财险',
                             '中国人保财险', '大家财险', '太平洋产险', '大地保险', '国寿财险', '线上']

    def get_connection_dw(self):
        """
        获取dw数据库连接
        """
        self.conn = pymysql.connect(host=idna.encode(self.DB_DW["HOST"]).decode('utf-8'),
                                    port=int(self.DB_DW["PORT"]),
                                    user=self.DB_DW["USER"],
                                    password=self.DB_DW["PASSWORD"], database=self.DB_DW["NAME"])
        return self.conn

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection_dw() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            print(query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code))
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]
            print(max_publish_time_df)

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def get_connection(self):
        """
        获取数据库连接
        """
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def get_full_range_date(self, df):
        """
        获取完整的日期范围
        """
        # 获取最小日期
        min_date = df['date'].min()
        # 如果 min_date 是空的，使用 sale_start_date 作为默认值
        min_date = min_date if pd.notnull(min_date) else self.sale_start_date
        # 如果 min_date 是 pandas.Timestamp 类型，转换为 datetime.date
        if isinstance(min_date, pd.Timestamp):
            min_date = min_date.to_pydatetime().date()
        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = pd.date_range(start=min(min_date, self.sale_start_date),
                                        end=self.today.date())
        return full_date_range

    def get_daily_sale(self):
        """
        获取健康险日度销售数据，包括日期、医保类型、是否线上、是否个人、支付方式、区域编码、代理人、渠道来源、销售金额、销售件数、产品名称
        """
        try:
            df_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_DAILY_SALE_CITY').format(product_set_code=self.product_set_code,
                                                            end_datetime=self.publish_time),
                self.get_connection())
            return df_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_daily_sale error:{e}')

    def cache_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        df_daily_sale = self.get_daily_sale()
        # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
        try:
            self.update_cache('get_daily_sale', df_daily_sale)
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_daily_sale error:{e}')
        return df_daily_sale

    def get_main_daily_sale(self):
        """
        获取健康险日度主要销售数据，主要是金额、数量数据
        """
        try:
            df_main_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_MAIN_DATA_CITY').format(product_set_code=self.product_set_code,
                                                           end_datetime=self.publish_time),
                self.get_connection())
            return df_main_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_main_daily_sale error:{e}')

    def cache_main_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        df_main_daily_sale = self.get_main_daily_sale()
        # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
        try:
            self.update_cache('get_main_daily_sale', df_main_daily_sale)
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_main_daily_sale error:{e}')
        return df_main_daily_sale

    def get_area_mapping(self):
        """
        获取地区映射表
        """
        # 获取编码与城市的对应关系
        area_mapping = PublicMapping.objects.filter(type='area')
        df_mapping = pd.DataFrame(list(area_mapping.values()))[['name', 'keywords']]
        # 城市统计中不含省本级
        df_mapping = df_mapping[df_mapping['name'] != '省本级']
        return df_mapping

    def get_total_count(self):
        """
        获取总销量，这边只计算数据表的数据，如果加团单，在datav脚本处理，保持数据的真实性
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_group = data.groupby(['medicare_area_code_substr']).agg({'count': 'sum'}).reset_index()
        df_group = pd.merge(df_mapping, df_group, left_on='keywords', right_on='medicare_area_code_substr', how='left')
        df_group['count'].fillna(0, inplace=True)
        df_group['indic_name'] = df_group['name'].apply(lambda x: self.version + '-销量-' + x + '-累计值')
        df_group = query_indicator_code_v1(df_group)
        # 删除code为空的行
        df_group.dropna(subset=['code'], inplace=True)
        df_group.fillna(0, inplace=True)
        df_group.reset_index(drop=True, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_group.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_count error:{e}')
        return df_group

    # def get_target_ratio(self):
    #     """
    #     获取完成率
    #     """
    #     data = self.get_from_cache('get_daily_sale')
    #     data = data[data['main'] == 1]
    #     if self.group_count_statistic == 1:
    #         if self.complete_statistic == 'count':
    #             # 看是否是要统计上传团单数量，如果不要，直接统计销量，其实后期如果保司上传团单，这边也是会获取到的
    #             target_ratio = round(data['count'].sum() / self.INSURE_TARGET * 100, 4)
    #         else:
    #             print(12)
    #             target_ratio = round(data['amount'].sum() / self.INSURE_TARGET_AMOUNT * 100, 4)
    #     else:
    #         if self.complete_statistic == 'count':
    #             # 看是否需要统计上传团单数量，需要统计，加上手动数据，但是后期会重复，应该需要调整手动团单数据，或者取消限制
    #             df_offline_seller_group = self.get_seller_group_report_data()
    #             group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
    #             target_ratio = round((data['count'].sum() + group_seller['value'].sum()) / self.INSURE_TARGET * 100,
    #                                  4)
    #         # todo:看需要再添加功能，根据上传的金额统计完成率
    #     indic_name = self.version + '-完成率-当期值'
    #     code = PublicIndicatorMain.objects.get(name=indic_name).code
    #     try:
    #         with transaction.atomic():
    #             custom_update_or_create(PublicIndicatorData,
    #                                     code=code, end_time=self.end_time,
    #                                     defaults={'publish_time': self.publish_time,
    #                                               'value': Decimal(str(target_ratio))},
    #                                     exclude_fields=['publish_time'])
    #     except Exception as e:
    #         logger.error(f'{type(self).__name__}:get_target_ratio error:{e}')
    #     return target_ratio

    def get_total_amount(self):
        """
        获取总销售额
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_group = data.groupby(['medicare_area_code_substr']).agg({'amount': 'sum'}).reset_index()
        df_group = pd.merge(df_mapping, df_group, left_on='keywords', right_on='medicare_area_code_substr', how='left')
        df_group['amount'].fillna(0, inplace=True)

        df_group['indic_name'] = df_group['name'].apply(lambda x: self.version + '-销售额-' + x + '-累计值')
        query_indicator_code(df_group)
        # 删除code为空的行
        df_group.dropna(subset=['code'], inplace=True)
        df_group.fillna(0, inplace=True)
        df_group.reset_index(drop=True, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_group.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_amount error:{e}')
        return df_group

    def get_daily_count(self):
        """
        获取日度销量，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_count = data.groupby(['medicare_area_code_substr', 'date']).agg({'count': 'sum'}).reset_index()
        df_daily_count = pd.merge(df_daily_count, df_mapping, left_on='medicare_area_code_substr', right_on='keywords',
                                  how='left')
        df_daily_count = df_daily_count[['date', 'name', 'count']].rename(columns={'name': 'area_name'})
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_count)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_count['date'] = df_daily_count['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_count = df_full_combinations.merge(df_daily_count, how='left', on=['date', 'area_name'])
        df_daily_count.fillna(0, inplace=True)
        df_daily_count['indic_name'] = df_daily_count['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-当期值')
        df_daily_count = query_indicator_code_v1(df_daily_count)
        # 删除code为空的行
        df_daily_count.dropna(subset=['code'], inplace=True)
        df_daily_count.fillna(0, inplace=True)
        df_daily_count.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_count error:{e}')
        return df_daily_count

    def get_daily_cumsum(self):
        """
        获取日度累计销量，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_cumsum = data.groupby(['medicare_area_code_substr', 'date']).agg({'count': 'sum'}).reset_index()
        df_daily_cumsum = pd.merge(df_daily_cumsum, df_mapping, left_on='medicare_area_code_substr',
                                   right_on='keywords',
                                   how='left')
        df_daily_cumsum = df_daily_cumsum[['date', 'name', 'count']].rename(columns={'name': 'area_name'})

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_cumsum)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_cumsum['date'] = df_daily_cumsum['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_cumsum = df_full_combinations.merge(df_daily_cumsum, how='left', on=['date', 'area_name'])
        df_daily_cumsum.fillna(0, inplace=True)

        # 按照 'date' 排序
        df_daily_cumsum = df_daily_cumsum.sort_values(by='date')
        df_daily_cumsum['cumulative_count'] = df_daily_cumsum.groupby(['area_name'])['count'].cumsum()
        # 定义指标名称
        df_daily_cumsum['indic_name'] = df_daily_cumsum['area_name'].apply(
            lambda x: self.version + '-销量-' + x + '-累计值')

        # 获取code
        df_daily_cumsum = query_indicator_code_v1(df_daily_cumsum)
        # 删除code为空的行
        df_daily_cumsum.dropna(subset=['code'], inplace=True)
        df_daily_cumsum.fillna(0, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_cumsum error:{e}')
        return df_daily_cumsum

    def get_daily_online_count(self):
        """
        获取日度线上销量，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_online_count = data.query('is_online == 1').groupby(['medicare_area_code_substr', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_daily_online_count = pd.merge(df_daily_online_count, df_mapping, left_on='medicare_area_code_substr',
                                         right_on='keywords',
                                         how='left')
        df_daily_online_count = df_daily_online_count[['date', 'name', 'count']].rename(columns={'name': 'area_name'})

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_online_count)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_online_count['date'] = df_daily_online_count['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_online_count = df_full_combinations.merge(df_daily_online_count, how='left', on=['date', 'area_name'])
        df_daily_online_count.fillna(0, inplace=True)
        df_daily_online_count['indic_name'] = df_daily_online_count['area_name'].apply(
            lambda x: self.version + '-销量-线上-' + x + '-当期值')
        df_daily_online_count = query_indicator_code_v1(df_daily_online_count)
        # 删除code为空的行
        df_daily_online_count.dropna(subset=['code'], inplace=True)
        df_daily_online_count.fillna(0, inplace=True)
        df_daily_online_count.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_online_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_online_count error:{e}')
        return df_daily_online_count

    def get_daily_online_cumsum(self):
        """
        获取日度线上累计销量，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_online_cumsum = data.query('is_online == 1').groupby(['medicare_area_code_substr', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_daily_online_cumsum = pd.merge(df_daily_online_cumsum, df_mapping, left_on='medicare_area_code_substr',
                                          right_on='keywords',
                                          how='left')
        df_daily_online_cumsum = df_daily_online_cumsum[['date', 'name', 'count']].rename(columns={'name': 'area_name'})

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_online_cumsum)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_online_cumsum['date'] = df_daily_online_cumsum['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_online_cumsum = df_full_combinations.merge(df_daily_online_cumsum, how='left',
                                                            on=['date', 'area_name'])
        df_daily_online_cumsum.fillna(0, inplace=True)

        # 按照 'date' 排序
        df_daily_online_cumsum = df_daily_online_cumsum.sort_values(by='date')
        df_daily_online_cumsum['cumulative_count'] = df_daily_online_cumsum.groupby(['area_name'])['count'].cumsum()
        # 定义指标名称
        df_daily_online_cumsum['indic_name'] = df_daily_online_cumsum['area_name'].apply(
            lambda x: self.version + '-销量-线上-' + x + '-累计值')
        # 获取code
        df_daily_online_cumsum = query_indicator_code_v1(df_daily_online_cumsum)
        # 删除code为空的行
        df_daily_online_cumsum.dropna(subset=['code'], inplace=True)
        df_daily_online_cumsum.fillna(0, inplace=True)
        df_daily_online_cumsum.reset_index(drop=True, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_daily_online_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_online_cumsum error:{e}')
        return df_daily_online_cumsum

    def get_daily_offline_count(self):
        """
        获取日度线下销量，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_offline_count = data.query('is_online == 0').groupby(['medicare_area_code_substr', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_daily_offline_count = pd.merge(df_daily_offline_count, df_mapping, left_on='medicare_area_code_substr',
                                          right_on='keywords',
                                          how='left')
        df_daily_offline_count = df_daily_offline_count[['date', 'name', 'count']].rename(columns={'name': 'area_name'})

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_offline_count)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_offline_count['date'] = df_daily_offline_count['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_offline_count = df_full_combinations.merge(df_daily_offline_count, how='left',
                                                            on=['date', 'area_name'])
        df_daily_offline_count.fillna(0, inplace=True)
        df_daily_offline_count['indic_name'] = df_daily_offline_count['area_name'].apply(
            lambda x: self.version + '-销量-线下-' + x + '-当期值')
        df_daily_offline_count = query_indicator_code_v1(df_daily_offline_count)
        # 删除code为空的行
        df_daily_offline_count.dropna(subset=['code'], inplace=True)
        df_daily_offline_count.fillna(0, inplace=True)
        df_daily_offline_count.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_count error:{e}')
        return df_daily_offline_count

    def get_daily_offline_cumsum(self):
        """
        获取日度线下累计销量，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_offline_cumsum = data.query('is_online == 0').groupby(['medicare_area_code_substr', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_daily_offline_cumsum = pd.merge(df_daily_offline_cumsum, df_mapping, left_on='medicare_area_code_substr',
                                           right_on='keywords',
                                           how='left')
        df_daily_offline_cumsum = df_daily_offline_cumsum[['date', 'name', 'count']].rename(
            columns={'name': 'area_name'})

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_offline_cumsum)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_offline_cumsum['date'] = df_daily_offline_cumsum['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_offline_cumsum = df_full_combinations.merge(df_daily_offline_cumsum, how='left',
                                                             on=['date', 'area_name'])
        df_daily_offline_cumsum.fillna(0, inplace=True)

        # 按照 'date' 排序
        df_daily_offline_cumsum = df_daily_offline_cumsum.sort_values(by='date')
        df_daily_offline_cumsum['cumulative_count'] = df_daily_offline_cumsum.groupby(['area_name'])['count'].cumsum()
        # 定义指标名称
        df_daily_offline_cumsum['indic_name'] = df_daily_offline_cumsum['area_name'].apply(
            lambda x: self.version + '-销量-线下-' + x + '-累计值')
        # 获取code
        df_daily_offline_cumsum = query_indicator_code_v1(df_daily_offline_cumsum)
        # 删除code为空的行
        df_daily_offline_cumsum.dropna(subset=['code'], inplace=True)
        df_daily_offline_cumsum.fillna(0, inplace=True)
        df_daily_offline_cumsum.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_cumsum error:{e}')
        return df_daily_offline_cumsum

    def get_today_count(self):
        """
        获取当日销量
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        today_data = data[data['date'] == self.today.date()]

        df_group = today_data.groupby(['medicare_area_code_substr']).agg({'count': 'sum'}).reset_index()
        df_group = pd.merge(df_mapping, df_group, left_on='keywords', right_on='medicare_area_code_substr', how='left')
        df_group['count'].fillna(0, inplace=True)
        df_group['indic_name'] = df_group['name'].apply(lambda x: self.version + '-销量-' + x + '-当期值')
        query_indicator_code(df_group)
        # 删除code为空的行
        df_group.dropna(subset=['code'], inplace=True)
        df_group.fillna(0, inplace=True)
        df_group.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_group.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_today_count error:{e}')
        return df_group

    def get_daily_amount(self):
        """
        获取日度销售额，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_amount = data.groupby(['medicare_area_code_substr', 'date']).agg({'amount': 'sum'}).reset_index()

        df_daily_amount = pd.merge(df_daily_amount, df_mapping, left_on='medicare_area_code_substr',
                                   right_on='keywords',
                                   how='left')
        df_daily_amount = df_daily_amount[['date', 'name', 'amount']].rename(columns={'name': 'area_name'})
        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_amount)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_amount['date'] = df_daily_amount['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_amount = df_full_combinations.merge(df_daily_amount, how='left', on=['date', 'area_name'])
        df_daily_amount.fillna(0, inplace=True)
        df_daily_amount['indic_name'] = df_daily_amount['area_name'].apply(
            lambda x: self.version + '-销售额-' + x + '-当期值')
        df_daily_amount = query_indicator_code_v1(df_daily_amount)
        # 删除code为空的行
        df_daily_amount.dropna(subset=['code'], inplace=True)
        df_daily_amount.fillna(0, inplace=True)
        df_daily_amount.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_amount.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount error:{e}')
        return df_daily_amount

    def get_daily_amount_cumsum(self):
        """
        获取日度销售额累计值，时间序列，用于更新历史数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_amount_cumsum = data.groupby(['medicare_area_code_substr', 'date']).agg(
            {'amount': 'sum'}).reset_index()
        df_daily_amount_cumsum = pd.merge(df_daily_amount_cumsum, df_mapping, left_on='medicare_area_code_substr',
                                          right_on='keywords', how='left')
        df_daily_amount_cumsum = df_daily_amount_cumsum[['date', 'name', 'amount']].rename(
            columns={'name': 'area_name'})

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_amount_cumsum)

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['date', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['date'] = df_full_combinations['date'].dt.strftime('%Y-%m-%d')
        df_daily_amount_cumsum['date'] = df_daily_amount_cumsum['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 填充缺失值
        df_daily_amount_cumsum = df_full_combinations.merge(df_daily_amount_cumsum, how='left',
                                                            on=['date', 'area_name'])
        df_daily_amount_cumsum.fillna(0, inplace=True)

        # 按照 'date' 排序
        df_daily_amount_cumsum = df_daily_amount_cumsum.sort_values(by='date')
        df_daily_amount_cumsum['cumulative_amount'] = df_daily_amount_cumsum.groupby(['area_name'])['amount'].cumsum()
        # 定义指标名称
        df_daily_amount_cumsum['indic_name'] = df_daily_amount_cumsum['area_name'].apply(
            lambda x: self.version + '-销售额-' + x + '-累计值')

        # 获取code
        df_daily_amount_cumsum = query_indicator_code_v1(df_daily_amount_cumsum)
        # 删除code为空的行
        df_daily_amount_cumsum.dropna(subset=['code'], inplace=True)
        df_daily_amount_cumsum.fillna(0, inplace=True)
        df_daily_amount_cumsum.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_daily_amount_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount_cumsum error:{e}')
        return df_daily_amount_cumsum

    def get_today_amount(self):
        """
        获取当日销售额
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        today_data = data[data['date'] == self.today.date()]

        df_group = today_data.groupby(['medicare_area_code_substr']).agg({'amount': 'sum'}).reset_index()
        df_group = pd.merge(df_mapping, df_group, left_on='keywords', right_on='medicare_area_code_substr', how='left')
        df_group['amount'].fillna(0, inplace=True)
        df_group['indic_name'] = df_group['name'].apply(lambda x: self.version + '-销售额-' + x + '-当期值')
        query_indicator_code(df_group)
        # 删除code为空的行
        df_group.dropna(subset=['code'], inplace=True)
        df_group.fillna(0, inplace=True)
        df_group.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_group.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_today_amount error:{e}')
        return df_group

    def get_yesterday_count(self):
        """
        获取昨日销量
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        data['date'] = pd.to_datetime(data['date'])
        yesterday_data = data[data['date'] == self.yesterday]

        df_group = yesterday_data.groupby(['medicare_area_code_substr']).agg({'count': 'sum'}).reset_index()
        df_group = pd.merge(df_mapping, df_group, left_on='keywords', right_on='medicare_area_code_substr', how='left')
        df_group['count'].fillna(0, inplace=True)
        df_group['indic_name'] = df_group['name'].apply(lambda x: self.version + '-销量-' + x + '-当期值')
        query_indicator_code(df_group)
        # 删除code为空的行
        df_group.dropna(subset=['code'], inplace=True)
        df_group.fillna(0, inplace=True)
        df_group.reset_index(drop=True, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_group.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.yesterday,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_yesterday_count error:{e}')
        return df_group

    def get_yesterday_amount(self):
        """
        获取昨日销售额
        """
        df_mapping = self.get_area_mapping()

        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        yesterday_data = data[data['date'] == self.yesterday.date()]

        df_group = yesterday_data.groupby(['medicare_area_code_substr']).agg({'amount': 'sum'}).reset_index()
        df_group = pd.merge(df_mapping, df_group, left_on='keywords', right_on='medicare_area_code_substr', how='left')
        df_group['amount'].fillna(0, inplace=True)
        df_group['indic_name'] = df_group['name'].apply(lambda x: self.version + '-销售额-' + x + '-当期值')
        query_indicator_code(df_group)
        # 删除code为空的行
        df_group.dropna(subset=['code'], inplace=True)
        df_group.fillna(0, inplace=True)
        df_group.reset_index(drop=True, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_group.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.yesterday,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_yesterday_amount error:{e}')
        return df_group

    def get_person_group_count(self):
        """
        获取团单、个单数量
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_person_group = data.groupby(['medicare_area_code_substr', 'is_personal']).agg({'count': 'sum'}).reset_index()
        # 忽略其他类型，如果有空为底层数据问题，这边不做处理
        df_person_group['is_personal'] = df_person_group['is_personal'].map({0: '团单', 1: '个单'})
        df_person_group = pd.merge(df_person_group, df_mapping, left_on='medicare_area_code_substr',
                                   right_on='keywords', how='left')
        df_combinations = pd.MultiIndex.from_product([['团单', '个单'], df_mapping['name'].unique()],
                                                     names=['is_personal', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_person_group = pd.merge(df_full_combinations, df_person_group, left_on=['is_personal', 'area_name'],
                                   right_on=['is_personal', 'name'], how='left')
        df_person_group.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_person_group['count'].fillna(0, inplace=True)
        # 查询数据库中所有数据
        db_person_group = PublicStatistics.objects.filter(type=self.type, statistical_type='personal',
                                                          product_set_code=self.product_set_code)
        df_db_person_group = pd.DataFrame(list(db_person_group.values()))
        if df_db_person_group.empty:
            df_db_person_group = pd.DataFrame(columns=['id', 'key', 'additional_info'])
        else:
            df_db_person_group = df_db_person_group[['id', 'key', 'additional_info']]
            # 只取有区域的数据，避免删除总的数据
            df_db_person_group = df_db_person_group[df_db_person_group['additional_info'].notnull()]
        delete_df = sqldf(
            "select a.id from df_db_person_group a left join df_person_group b on a.key = b.is_personal and a.additional_info = b.additional_info where b.is_personal is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()
        try:
            with transaction.atomic():
                for index, row in df_person_group.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='personal',
                                            product_set_code=self.product_set_code, key=row['is_personal'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_person_group_count error:{e}')
        return df_person_group

    def get_online_offline_count(self):
        """
        获取线上、线下销售统计数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_online_offline_count = data.groupby(['medicare_area_code_substr', 'is_online']).agg(
            {'count': 'sum'}).reset_index()
        df_online_offline_count['is_online'] = df_online_offline_count['is_online'].map({0: '线下', 1: '线上'})
        df_online_offline_count = pd.merge(df_online_offline_count, df_mapping, left_on='medicare_area_code_substr',
                                           right_on='keywords', how='left')

        df_combinations = pd.MultiIndex.from_product([['线下', '线上'], df_mapping['name'].unique()],
                                                     names=['is_online', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_online_offline_count = pd.merge(df_full_combinations, df_online_offline_count,
                                           left_on=['is_online', 'area_name'],
                                           right_on=['is_online', 'name'], how='left')
        df_online_offline_count.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_online_offline_count['count'].fillna(0, inplace=True)

        # 查询数据库中所有数据
        db_online_offline = PublicStatistics.objects.filter(type=self.type, statistical_type='isonline',
                                                            product_set_code=self.product_set_code)
        df_db_online_offline = pd.DataFrame(list(db_online_offline.values()))
        if df_db_online_offline.empty:
            df_db_online_offline = pd.DataFrame(columns=['id', 'key', 'additional_info'])
        else:
            df_db_online_offline = df_db_online_offline[['id', 'key', 'additional_info']]
            # 只取有区域的数据，避免删除总的数据
            df_db_online_offline = df_db_online_offline[df_db_online_offline['additional_info'].notnull()]
        delete_df = sqldf(
            "select a.id from df_db_online_offline a left join df_online_offline_count b on a.key = b.is_online and a.additional_info = b.additional_info where b.is_online is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        # 数据写入统计表
        try:
            with transaction.atomic():
                for index, row in df_online_offline_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='isonline',
                                            product_set_code=self.product_set_code, key=row['is_online'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count PublicStatistics error:{e}')
        # 保证下期取数方便，线上线下累计值写入指标表
        online_count = df_online_offline_count[df_online_offline_count['is_online'] == '线上']
        online_count['indic_name'] = online_count['additional_info'].apply(
            lambda x: self.version + '-销量-线上-' + x + '-累计值')
        online_count = query_indicator_code_v1(online_count)
        try:
            with transaction.atomic():
                for index, row in online_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count online error:{e}')
        offline_count = df_online_offline_count[df_online_offline_count['is_online'] == '线下']
        offline_count['indic_name'] = offline_count['additional_info'].apply(
            lambda x: self.version + '-销量-线下-' + x + '-累计值')
        offline_count = query_indicator_code_v1(offline_count)
        try:
            with transaction.atomic():
                for index, row in offline_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count offline error:{e}')
        return df_online_offline_count

    def get_pay_type_count(self):
        """
        获取支付方式统计数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(data, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '个账'})
        df_pay_type_count = data.groupby(['medicare_area_code_substr', 'pay_type']).agg({'count': 'sum'}).reset_index()

        df_pay_type_count = pd.merge(df_pay_type_count, df_mapping, left_on='medicare_area_code_substr',
                                     right_on='keywords', how='left')
        df_combinations = pd.MultiIndex.from_product([['自费', '个账'], df_mapping['name'].unique()],
                                                     names=['pay_type', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_pay_type_count = pd.merge(df_full_combinations, df_pay_type_count, left_on=['pay_type', 'area_name'],
                                     right_on=['pay_type', 'name'], how='left')
        df_pay_type_count.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_pay_type_count['count'].fillna(0, inplace=True)
        # 查询数据库中所有数据
        db_pay_type_count = PublicStatistics.objects.filter(type=self.type, statistical_type='pay',
                                                            product_set_code=self.product_set_code)
        db_pay_type_count = pd.DataFrame(list(db_pay_type_count.values()))
        if db_pay_type_count.empty:
            db_pay_type_count = pd.DataFrame(columns=['id', 'key', 'additional_info'])
        else:
            db_pay_type_count = db_pay_type_count[['id', 'key', 'additional_info']]
            # 只取有区域的数据，避免删除总的数据
            db_pay_type_count = db_pay_type_count[db_pay_type_count['additional_info'].notnull()]
        delete_df = sqldf(
            "select a.id from db_pay_type_count a left join df_pay_type_count b on a.key = b.pay_type and a.additional_info = b.additional_info where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_count error:{e}')
        return df_pay_type_count

    def get_pay_type_amount(self):
        """
        获取支付方式统计金额数据，历史逻辑只取了主险，保持一致
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(data, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '个账'})
        df_pay_type_amount = data.groupby(['medicare_area_code_substr', 'pay_type']).agg(
            {'amount': 'sum'}).reset_index()

        df_pay_type_amount = pd.merge(df_pay_type_amount, df_mapping, left_on='medicare_area_code_substr',
                                      right_on='keywords', how='left')
        df_combinations = pd.MultiIndex.from_product([['自费', '个账'], df_mapping['name'].unique()],
                                                     names=['pay_type', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_pay_type_amount = pd.merge(df_full_combinations, df_pay_type_amount, left_on=['pay_type', 'area_name'],
                                      right_on=['pay_type', 'name'], how='left')
        df_pay_type_amount.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_pay_type_amount['amount'].fillna(0, inplace=True)
        # 查询数据库中所有数据
        db_pay_type_amount = PublicStatistics.objects.filter(type=self.type, statistical_type='pay_amount',
                                                             product_set_code=self.product_set_code)
        db_pay_type_amount = pd.DataFrame(list(db_pay_type_amount.values()))
        if db_pay_type_amount.empty:
            db_pay_type_amount = pd.DataFrame(columns=['id', 'key', 'additional_info'])
        else:
            db_pay_type_amount = db_pay_type_amount[['id', 'key', 'additional_info']]
            # 只取有区域的数据，避免删除总的数据
            db_pay_type_amount = db_pay_type_amount[db_pay_type_amount['additional_info'].notnull()]
        delete_df = sqldf(
            "select a.id from db_pay_type_amount a left join df_pay_type_amount b on a.key = b.pay_type and a.additional_info = b.additional_info where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_amount.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay_amount',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_amount error:{e}')
        return df_pay_type_amount

    def get_medicare_type_count(self):
        """
        获取医保类型统计数据
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # 筛选medicare_type为空的数据
        simplify_replace(data, 'medicare_type', {'EMPLOYEE': '职保', 'RESIDENT': '居保', 'UNKNOWN': '其他'}, '其他')

        df_medicare_type_count = data.groupby(['medicare_area_code_substr', 'medicare_type']).agg(
            {'count': 'sum'}).reset_index()

        df_medicare_type_count = pd.merge(df_medicare_type_count, df_mapping, left_on='medicare_area_code_substr',
                                          right_on='keywords', how='left')
        # 其他有可能是赋值的，也可能是空的，所以要重新处理
        df_medicare_type_count = df_medicare_type_count.groupby(['medicare_type', 'name']).agg(
            {'count': 'sum'}).reset_index()
        df_combinations = pd.MultiIndex.from_product([['职保', '居保', '其他'], df_mapping['name'].unique()],
                                                     names=['medicare_type', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_medicare_type_count = pd.merge(df_full_combinations, df_medicare_type_count,
                                          left_on=['medicare_type', 'area_name'],
                                          right_on=['medicare_type', 'name'], how='left')
        df_medicare_type_count.rename(columns={'area_name': 'additional_info'}, inplace=True)
        df_medicare_type_count['count'].fillna(0, inplace=True)
        # 删除类型为其他且count为0的行
        df_medicare_type_count = df_medicare_type_count[
            ~(df_medicare_type_count['medicare_type'] == '其他') | (df_medicare_type_count['count'] != 0)]

        # 查询数据库中所有数据
        db_medicare_type_count = PublicStatistics.objects.filter(type=self.type, statistical_type='medicare',
                                                                 product_set_code=self.product_set_code)
        db_medicare_type_count = pd.DataFrame(list(db_medicare_type_count.values()))
        if db_medicare_type_count.empty:
            db_medicare_type_count = pd.DataFrame(columns=['id', 'key', 'additional_info'])
        else:
            db_medicare_type_count = db_medicare_type_count[['id', 'key', 'additional_info']]
            # 只取有区域的数据，避免删除总的数据
            db_medicare_type_count = db_medicare_type_count[db_medicare_type_count['additional_info'].notnull()]
        delete_df = sqldf(
            "select a.id from db_medicare_type_count a left join df_medicare_type_count b on a.key = b.medicare_type and a.additional_info = b.additional_info where b.medicare_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_medicare_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='medicare',
                                            product_set_code=self.product_set_code, key=row['medicare_type'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_type_count error:{e}')
        return df_medicare_type_count

    def get_medicare_person_count(self):
        """
        获取医保人数统计数据，低频处理，便于后续理赔指标计算
        """
        df_mapping = self.get_area_mapping()
        with self.get_connection() as conn:
            df_medicare = pd.read_sql(query_sql('SQL_MEDICAL_TYPE_CITY').format(product_set_code=self.product_set_code,
                                                                                end_datetime=self.publish_time), conn)
        simplify_replace(df_medicare, 'medicare_type',
                         {'EMPLOYEE': '职工医保', 'RESIDENT': '居民医保', 'UNKNOWN': '其他医保'}, '其他医保')

        df_medicare = pd.merge(df_medicare, df_mapping, left_on='medicare_area_code_substr',
                               right_on='keywords', how='left')
        # 防止一个城市有多个其他
        df_medicare = df_medicare.groupby(['medicare_area_code_substr','keywords','medicare_type', 'name']).agg(
            {'person_count': 'sum', 'count': 'sum'}).reset_index()
        df_combinations = pd.MultiIndex.from_product(
            [['职工医保', '居民医保', '其他医保'], df_mapping['name'].unique()],
            names=['medicare_type', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_medicare = pd.merge(df_full_combinations, df_medicare,
                               left_on=['medicare_type', 'area_name'],
                               right_on=['medicare_type', 'name'], how='left')
        # 由于其他医保可能是赋值的，也可能是空的，所以要重新处理
        df_medicare = df_medicare.groupby(['medicare_type', 'area_name']).agg(
            {'person_count': 'sum', 'count': 'sum'}).reset_index()
        df_medicare['indic_name'] = self.version + '-人数-' + df_medicare['area_name'] + '-' + df_medicare[
            'medicare_type'] + '-当期值'

        df_medicare_city = df_medicare.groupby(['area_name']).agg({'person_count': 'sum', 'count': 'sum'}).reset_index()
        df_medicare_city['medicare_type'] = '合计'
        df_medicare_city['indic_name'] = self.version + '-参保人数-' + df_medicare_city['area_name'] + '-当期值'

        df_medicare = pd.concat([df_medicare, df_medicare_city], ignore_index=True)
        df_medicare.reset_index(drop=True, inplace=True)
        # 获取指标code
        df_medicare = query_indicator_code_v1(df_medicare)
        # # 删除code为空的行
        df_medicare.dropna(subset=['code'], inplace=True)
        df_medicare.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_medicare.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['person_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_person_count error:{e}')

        return df_medicare

    def get_last_24_hour_count(self):
        """
        获取过去24小时统计数据（统计每个城市的数据）
        """
        # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
        # start_datetime = max(
        #     datetime.datetime.strptime(self.publish_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
        #         hours=24), datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S'))
        start_datetime = datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S')  ## 所有历史
        # 统计每个城市的数据，只取医保编码前四位，与数据库关联获取地址
        with self.get_connection() as conn:
            df_last_24_hour_count = pd.read_sql(
                query_sql('SQL_LAST_24_HOUR_AREA_COUNT').format(product_set_code=self.product_set_code,
                                                                start_datetime=start_datetime,
                                                                end_datetime=self.publish_time), conn)
        # 获取编码与城市的对应关系
        df_mapping = self.get_area_mapping()

        # 获取过去24小时的完整日期范围，日期格式为'%Y-%m-%d %H:00:00'
        full_date_range = pd.date_range(start=start_datetime.replace(minute=0, second=0),
                                        end=self.publish_time[:13] + ':00:00', freq='H')

        df_combinations = pd.MultiIndex.from_product([full_date_range, df_mapping['name'].unique()],
                                                     names=['datetime', 'area_name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_full_combinations['datetime'] = df_full_combinations['datetime'].dt.strftime('%Y-%m-%d %H:00:00')
        # 拼接地区截取编码
        df_full_combinations = pd.merge(df_full_combinations, df_mapping, left_on='area_name', right_on='name',
                                        how='left')
        # 拼接最终的完整数据
        df_last_24_hour_count = sqldf(
            "select a.*,ifnull(b.count,0) as 'count' from df_full_combinations a left join df_last_24_hour_count b on a.datetime = b.datetime and a.keywords = b.medicare_area_code_substr")
        df_last_24_hour_count['indic_name'] = df_last_24_hour_count['area_name'].apply(
            lambda x: self.version + '-销量-' + str(x) + '-当期值(小时)')
        # 获取code
        df_last_24_hour_count = query_indicator_code_v1(df_last_24_hour_count)
        # 删除code为空的行
        df_last_24_hour_count.dropna(subset=['code'], inplace=True)
        df_last_24_hour_count.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_last_24_hour_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['datetime'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_last_24_hour_count error:{e}')
        return df_last_24_hour_count

    def get_online_source_info(self):
        """
        获取线上销售渠道的数据，只计算指标数据，并未计算线上渠道的整体表数据
        线上个单数量
        """
        df_mapping = self.get_area_mapping()
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_online_source_info = data.query("is_online==1 and is_personal==1").groupby(
            ['medicare_area_code_substr', 'source', 'date']).agg({'count': 'sum'}).reset_index()

        df_online_source_info = pd.merge(df_online_source_info, df_mapping, left_on='medicare_area_code_substr',
                                         right_on='keywords', how='left')

        df_online_source_info['date'] = df_online_source_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        # 暂定这种分类方法
        df_online_source_info['source'] = df_online_source_info['source'].apply(
            lambda x: '支付宝' if '支付宝' in x
            else '公众号')
        # 写入指标表
        full_date_range = pd.date_range(start=self.sale_start_date, end=self.today.date(), freq='D')
        # 拼接完整的日期、地区、产品组合
        df_combinations = pd.MultiIndex.from_product(
            [full_date_range, ['支付宝', '公众号'], df_mapping['name'].unique()],
            names=['date', 'source', 'name'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_group = df_online_source_info.groupby(['name', 'source', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_group['date'] = pd.to_datetime(df_group['date'])

        df_indicator = sqldf(
            "select a.*,ifnull(b.count,0) as `value` from df_full_combinations a left join df_group b on a.date=b.date and a.source=b.source and a.name=b.name")
        df_indicator['indic_name'] = self.version + '-销量-线上-' + df_indicator['source'] + '-' + df_indicator[
            'name'] + '-当期值'
        df_indicator = query_indicator_code_v1(df_indicator)
        # 删除code为空的行
        df_indicator.dropna(subset=['code'], inplace=True)
        df_indicator.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_indicator.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['value']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_source_info indicator error:{e}')
        return df_indicator

    # def get_offline_seller(self):
    #     """
    #     获取线下个单销售情况
    #     """
    #     data = self.get_from_cache('get_daily_sale')
    #     data = data[data['main'] == 1]
    #     df_group = data.query("is_online==0 and is_personal==1").groupby(['seller', 'date']).agg(
    #         {'count': 'sum'}).reset_index()
    #     df_total = data.query("is_online==0 and is_personal==1").groupby(['date']).agg(
    #         {'count': 'sum'}).reset_index()
    #     full_date_range = pd.date_range(start=self.sale_start_date, end=self.today.date(), freq='D')
    #     # 拼接完整的日期、地区、产品组合
    #     df_combinations = pd.MultiIndex.from_product([full_date_range, self.seller_list],
    #                                                  names=['date', 'seller'])
    #     df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
    #     df_group['date'] = pd.to_datetime(df_group['date'])
    #     df_indicator = sqldf(
    #         "select a.*,ifnull(b.count,0) as `value` from df_full_combinations a left join df_group b on a.date=b.date and a.seller=b.seller")
    #     df_indicator['indic_name'] = self.version + '-销量-线下-个单-' + df_indicator['seller'] + '-当期值'
    #
    #     full_date_range = self.get_full_range_date(df_total)
    #     df_total = (
    #         df_total.set_index('date')
    #         .reindex(full_date_range)
    #         .fillna(0)
    #         .reset_index()
    #         .rename(columns={'index': 'date'})
    #     )
    #     df_total['indic_name'] = self.version + '-销量-线下-个单-当期值'
    #     df_total.rename(columns={'count': 'value'}, inplace=True)
    #     df_indicator = pd.concat([df_indicator, df_total], axis=0).reset_index(drop=True)
    #     query_indicator_code(df_indicator)
    #     # 删除code为空的行
    #     df_indicator.dropna(subset=['code'], inplace=True)
    #     df_indicator.fillna(0, inplace=True)
    #     try:
    #         with transaction.atomic():
    #             for index, row in df_indicator.iterrows():
    #                 custom_update_or_create(PublicIndicatorData,
    #                                         code=row['code'], end_time=row['date'],
    #                                         defaults={'publish_time': self.publish_time,
    #                                                   'value': Decimal(str(row['value']))},
    #                                         exclude_fields=['publish_time'])
    #     except Exception as e:
    #         logger.error(f'{type(self).__name__}:get_offline_seller indicator error:{e}')
    #     return df_indicator

    def get_channel_info(self):
        """
        获取线下保司的数据、线上数据，包括排名、保司、占比、代理人数、人均出单、个单、团单、今日参保、昨日参保、目标、完成率
        """
        # 获取保司代理人数量
        df_mapping = self.get_area_mapping()
        with self.get_connection() as conn:
            df_seller_person = pd.read_sql(
                query_sql('SQL_SELLER_PERSON').format(product_set_code=self.product_set_code), conn)
        # 剔除线上渠道的数据
        df_seller_person = df_seller_person[~df_seller_person['seller_name'].str.contains('线上渠道')][
            ['seller_name', 'employee_num']].rename(columns={'seller_name': 'seller'})
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # 获取区域编码的对应城市名称
        data = pd.merge(data, df_mapping, left_on='medicare_area_code_substr',
                        right_on='keywords', how='left')
        # 线上单独处理，分城市
        df_online_info = data.query("is_online==1").groupby(['name', 'date', 'is_personal']).agg(
            {'count': 'sum'}).reset_index()
        df_online_info['seller'] = '线上'

        # 线下处理，贵州的seller的简称为空，所以要用seller_name，分城市
        df_offline_seller_info = data.query("is_online==0").groupby(['name', 'date', 'seller_name', 'is_personal']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'seller_name': 'seller'})
        # 剔除线上渠道数据
        df_offline_seller_info = df_offline_seller_info[~df_offline_seller_info['seller'].str.contains('线上渠道')]
        # 合并数据，线上线下
        df_offline_seller_info = pd.concat([df_offline_seller_info, df_online_info], axis=0).reset_index(drop=True)
        # 计算总量的数据指标列
        df_offline_seller = df_offline_seller_info.groupby(['name', 'seller']).agg({'count': 'sum'}).reset_index()
        # 分城市的没有具体的员工数量，用0填充
        df_offline_seller['employee_num'] = 0
        # df_offline_seller = pd.merge(df_offline_seller, df_seller_person, how='left', on=['name','seller'])

        # # 获取目标数据
        df_target = PublicTarget.objects.filter(
            Q(product_set_code=self.product_set_code) & (Q(type='agent') | Q(type='online'))
        )
        df_target = pd.DataFrame(list(df_target.values()))
        df_target = df_target[['name', 'short_name', 'target', 'type']].rename(
            columns={'name': 'seller'})
        # 个单数量
        df_offline_seller_personal = df_offline_seller_info.query("is_personal==1").groupby(['name', 'seller']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'personal_count'})
        # 团单数量
        if self.group_count_statistic == 1:
            # 数据库取值逻辑
            df_offline_seller_group = df_offline_seller_info.query("is_personal==0").groupby(['name', 'seller']).agg(
                {'count': 'sum'}).reset_index().rename(columns={'count': 'group_count'})
        else:
            # 手动取值逻辑
            df_offline_seller_group = self.get_seller_group_report_data()
            df_offline_seller_group = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
            df_offline_seller_group.rename(columns={'value': 'group_count'}, inplace=True)
            df_offline_seller_group = pd.merge(df_offline_seller_group, df_target, how='left', on=['short_name'])[
                ['seller', 'group_count']]
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_personal, how='left', on=['name', 'seller'])
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_group, how='left', on=['name', 'seller'])
        # 手动计算
        df_offline_seller.fillna(0, inplace=True)
        df_offline_seller['count'] = df_offline_seller['personal_count'] + df_offline_seller['group_count']

        # 人均值计算，与宁惠保只计算个单不一样，用于省份计算
        # df_offline_seller['average_count'] = df_offline_seller.apply(
        #     lambda x: round(x['count'] / x['employee_num'], 1) if x['employee_num'] != 0 else 0, axis=1)
        # 城市不统计人均单量
        df_offline_seller['average_count'] = 0

        df_offline_seller.fillna(0, inplace=True)
        # 按照name分组，计算insure_ratio占比数据
        # 使用 transform 而不是 apply 可以确保结果序列与原始 DataFrame 的索引对齐，从而避免了索引不匹配的问题。
        df_offline_seller['insure_ratio'] = df_offline_seller.groupby('name')['count'].transform(
            lambda x: round(x / x.sum() if x.sum() != 0 else 0, 3))

        df_target_offline = df_target[df_target['type'] == 'agent']
        df_target_online = df_target[df_target['type'] == 'online']
        # 如果线上的目标出现线上，则只取线上，否则所有数据求和
        if '线上' in df_target_online['seller'].tolist():
            df_target_online = df_target_online[df_target_online['seller'] == '线上']
        else:
            df_target_online = pd.DataFrame(
                {'seller': '线上', 'target': [df_target_online['target'].sum()], 'type': 'online'})
        df_target = pd.concat([df_target_offline, df_target_online], axis=0).reset_index(drop=True)[
            ['seller', 'target', 'short_name']]
        # 合并目标数据
        df_offline_seller = pd.merge(df_offline_seller, df_target, how='outer', on='seller')
        # # 计算参保率
        # df_offline_seller['target_ratio'] = df_offline_seller.apply(
        #     lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
        #     axis=1
        # )
        # 分城市的没有目标数据，用0填充，target_ratio也用0
        df_offline_seller['target'] = 0
        df_offline_seller['target_ratio'] = 0
        # # 按照target_ratio排序
        # # # 排序，将seller为线上放在一个，其他按照target_ratio排序
        # # df_offline_zgrb = df_offline_seller[df_offline_seller['seller'] == '线上']
        # # df_offline_other = df_offline_seller[df_offline_seller['seller'] != '线上']
        # # df_offline_other.sort_values(by=['target_ratio'], ascending=False, inplace=True)
        # # # 合并成完整数据
        # # df_offline_seller = pd.concat([df_offline_zgrb, df_offline_other], axis=0).reset_index(drop=True)
        # 历史按照销量排序
        df_offline_seller.sort_values(by=['name', 'count'], ascending=False, inplace=True)

        df_offline_seller_today = df_offline_seller_info[
            df_offline_seller_info['date'] == self.today.date()]
        df_offline_seller_yesterday = df_offline_seller_info[
            df_offline_seller_info['date'] == self.yesterday.date()]
        df_offline_seller_today = df_offline_seller_today.groupby(['name', 'seller']).agg(
            {'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})
        df_offline_seller_yesterday = df_offline_seller_yesterday.groupby(['name', 'seller']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_today, how='left', on=['name', 'seller'])
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_yesterday, how='left', on=['name', 'seller'])
        df_offline_seller.fillna(0, inplace=True)
        # 删除全称列，将简称重命名为seller
        df_offline_seller.drop(columns=['seller'], inplace=True)
        df_offline_seller.rename(columns={'short_name': 'seller'}, inplace=True)

        df_offline_seller.reset_index(drop=True, inplace=True)
        # 根据name分组，按照name分组，index排序，获取位置
        df_offline_seller['position'] = df_offline_seller.groupby('name')['count'].rank(method='first', ascending=False)

        number_sum = df_offline_seller.groupby('name').apply(lambda x: x.apply(sum_or_combine)).drop(
            columns=['name']).reset_index()
        number_sum['insure_ratio'] = 1
        number_sum['position'] = len(self.channel_list) + 1
        # # 人均值计算
        # number_sum['average_count'] = number_sum.apply(
        #     lambda x: round(x['personal_count'] / x['employee_num'], 1) if x['employee_num'] != 0 else 0, axis=1)
        # number_sum['insure_ratio'] = 1
        # number_sum['position'] = len(df_offline_seller) + 1
        # number_sum['insure_ratio'] = number_sum['insure_ratio'].apply(lambda x: 1 if x > 1 else x)
        df_offline_seller = pd.concat([df_offline_seller, number_sum], axis=0).reset_index(drop=True)
        df_offline_seller['product_set_code'] = self.product_set_code
        df_offline_seller['publish_time'] = self.publish_time
        df_offline_seller.rename(columns={'name': 'additional_info'}, inplace=True)
        # 如果目标还未确认，数据库赋值0，这边判定后直接赋值0
        # df_offline_seller['target_ratio'] = df_offline_seller.apply(
        #     lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
        #     axis=1
        # )
        #
        # 查询数据库中所有数据
        db_insure_agent = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        # 如果db_insure_agent有数据则进行下面处理
        if db_insure_agent.exists():
            df_db_insure_agent = pd.DataFrame(list(db_insure_agent.values()))[['id', 'name', 'additional_info']]
            df_db_insure_agent = df_db_insure_agent[df_db_insure_agent['additional_info'].notnull()]
            delete_df = sqldf(
                "select a.id from df_db_insure_agent a left join df_offline_seller b on a.name = b.seller and a.additional_info = b.additional_info where b.seller is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                InsureAgent.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_offline_seller.iterrows():
                    custom_update_or_create(InsureAgent,
                                            product_set_code=row['product_set_code'], name=row['seller'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'employee_count': row['employee_num'],
                                                      'average_count': row['average_count'],
                                                      'total_count': row['count'],
                                                      'personal_count': row['personal_count'],
                                                      'group_count': row['group_count'],
                                                      'insure_ratio': row['insure_ratio'],
                                                      'position': row['position'],
                                                      'target': row['target'],
                                                      'target_ratio': row['target_ratio'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_channel_info error:{e}')
        return df_offline_seller

    def get_area_info(self):
        """
        地区参保数据，包括排名、参保地、占比、总单数、今日参保、昨日参保、参保率
        """
        df_mapping = self.get_area_mapping()
        df_area = PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code)
        df_area = pd.DataFrame(list(df_area.values()))[['count', 'code', 'name']].rename(
            columns={'code': 'area_code', 'count': 'base_insure'})
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # data = data.query("is_personal==1")
        # code部分超过6位，取前6位
        data['area_code'] = data['area_code'].apply(lambda x: x[:6] if x is not None else x)
        # area_code为空的置为999999，area_name为空的置为'其他'
        data.fillna({'area_code': '999999', 'area_name': '其他'}, inplace=True)
        data.loc[data['area_name'] == '其他', 'area_code'] = '999999'
        data.loc[data['area_name'] == '其他', 'medicare_area_code_substr'] = '9999'

        df_area_info = data.groupby(['medicare_area_code_substr','area_code', 'area_name', 'date']).agg({'count': 'sum'}).reset_index()
        df_area_total = df_area_info.groupby(['medicare_area_code_substr','area_code', 'area_name']).agg({'count': 'sum'}).reset_index()
        df_area_total = pd.merge(df_area_total, df_area, how='outer', on='area_code')
        # # name如果为空，用area_name代替
        df_area_total['name'] = df_area_total['name'].fillna(df_area_total['area_name'])
        df_area_total['area_name'] = df_area_total['area_name'].fillna(df_area_total['name'])
        # 如果name与area_name不同，则用name代替
        df_area_total.loc[df_area_total['name'] != df_area_total['area_name'], 'area_name'] = df_area_total['name']

        # 剔除有基本医疗数据的行，这些是市级的，市级以下都没有，用于区分哪些是区域没有数据
        df_area_total = df_area_total[df_area_total['base_insure'] == 0]
        # 对于没有交易的地区，用0填充
        df_area_total['count'].fillna(0, inplace=True)

        # 如果medicare_area_code_substr为空，则用area_code前4位代替
        df_area_total['medicare_area_code_substr'].fillna(df_area_total['area_code'].apply(lambda x: x[:4]), inplace=True)
        # 根据dw中的地区名称重新分组统计
        df_area_total = df_area_total.groupby(['medicare_area_code_substr','area_code', 'name']).agg(
            {'count': 'sum', 'base_insure': 'first'}).reset_index()
        df_area_total.fillna(0, inplace=True)
        df_area_total['count'] = df_area_total['count'].astype(int)

        # 计算占比，使用 transform 而不是 apply 可以确保结果序列与原始 DataFrame 的索引对齐，从而避免了索引不匹配的问题。
        df_area_total['ratio'] = df_area_total.groupby(['medicare_area_code_substr'])['count'].transform(
            lambda x: round(x / x.sum() if x.sum() != 0 else 0, 3))

        # 分地区的没有参保率，因为没有参保数据
        df_area_total['insure_ratio'] = 0
        # df_area_total['insure_ratio'] = df_area_total.apply(
        #     lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
        #     axis=1
        # )
        df_area_total.sort_values(by=['medicare_area_code_substr', 'count'], ascending=False, inplace=True)
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total['position'] = df_area_total.groupby('medicare_area_code_substr')['count'].rank(method='first', ascending=False)

        # # name如果为其他，放到最后，调整索引
        # df_area_total.loc[df_area_total['name'] == '其他', 'position'] = df_area_total.shape[0] + 1
        # df_area_total.sort_values(by='position', inplace=True)
        # df_area_total.reset_index(drop=True, inplace=True)
        # df_area_total['position'] = df_area_total.index + 1
        df_area_today = df_area_info[
            df_area_info['date'] == self.today.date()].groupby(['medicare_area_code_substr','area_code']).agg(
            {'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})

        df_area_yesterday = df_area_info[
            df_area_info['date'] == self.yesterday.date()].groupby(['medicare_area_code_substr','area_code']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        if df_area_today.empty:
            df_area_total['today_count'] = 0
        else:
            df_area_total = pd.merge(df_area_total, df_area_today, how='left', on=['medicare_area_code_substr','area_code'])
            df_area_total['today_count'].fillna(0, inplace=True)
        if df_area_yesterday.empty:
            df_area_total['yesterday_count'] = 0
        else:
            df_area_total = pd.merge(df_area_total, df_area_yesterday, how='left', on=['medicare_area_code_substr','area_code'])
            df_area_total['yesterday_count'].fillna(0, inplace=True)

        number_sum = df_area_total.groupby('medicare_area_code_substr').apply(lambda x: x.apply(sum_or_combine)).drop(
            columns=['medicare_area_code_substr']).reset_index()

        number_sum['ratio'] = 1
        number_sum.drop(columns=['position'], inplace=True)
        # psoition 根据medicare_area_code_substr分组，计算条数并加+1
        max_position = df_area_total.groupby('medicare_area_code_substr').agg({'position': 'max'}).reset_index()
        max_position['position'] = max_position['position'] + 1
        number_sum = pd.merge(number_sum, max_position, how='left', on='medicare_area_code_substr')

        # 如果来不及订目标，则target_ratio为0，比率也都是0
        number_sum['insure_ratio'] = number_sum.apply(
            lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
            axis=1
        )
        df_area_total = pd.concat([df_area_total, number_sum], axis=0).reset_index(drop=True)
        df_area_total['product_set_code'] = self.product_set_code
        df_area_total['publish_time'] = self.publish_time


        # 获取区域编码的对应城市名称
        df_mapping.rename(columns={'name': 'additional_info'}, inplace=True)
        df_area_total = pd.merge(df_area_total, df_mapping, left_on='medicare_area_code_substr',
                        right_on='keywords', how='left')
        # 匹配不到的数据，直接删除，主要为省本级
        df_area_total = df_area_total[df_area_total['additional_info'].notnull()]
        df_area_total.drop(columns=['keywords'], inplace=True)
        # 查询数据库中所有数据
        db_insure_area = InsureArea.objects.filter(product_set_code=self.product_set_code)
        # 只要additional_info不为空的数据
        df_area_total = df_area_total[df_area_total['additional_info'].notnull()]
        if db_insure_area.exists():
            df_db_insure_area = pd.DataFrame(list(db_insure_area.values()))[['id', 'name','additional_info']]
            delete_df = sqldf(
                "select a.id from df_db_insure_area a left join df_area_total b on a.name = b.name and a.additional_info = b.additional_info where b.name is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                # print(delete_ids)
                InsureArea.objects.filter(id__in=delete_ids).delete()
        df_area_total.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_area_total.iterrows():
                    custom_update_or_create(InsureArea,
                                            product_set_code=row['product_set_code'], name=row['name'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time,
                                                      'total_count': row['count'],
                                                      'ratio': row['ratio'], 'insure_ratio': row['insure_ratio'],
                                                      'position': row['position'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_area_info error:{e}')
        return df_area_total

    def get_age_gender_count(self):
        """
        获取年龄性别数据
        """
        with self.get_connection() as conn:
            df_age_gender_count = pd.read_sql(
                query_sql('SQL_AGE_GENDER_CITY').format(product_set_code=self.product_set_code,
                                                        end_datetime=self.publish_time),
                conn)
        return df_age_gender_count

    def cache_age_gender_count(self):
        """
        缓存年龄性别数据，主动推送，保证数据的实时性
        """
        try:
            df_age_gender_count = self.get_age_gender_count()
            self.update_cache('get_age_gender_count', df_age_gender_count)
            return df_age_gender_count
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_age_gender_count error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_age_gender_count error:{e}')

    def get_age_gender(self):
        """
        年龄性别数据，包括年龄段、性别、参保数
        """
        # 编码与城市关联
        df_mapping = self.get_area_mapping()
        # 合并数据
        df_age_gender = self.get_from_cache('get_age_gender_count')
        df_age_gender = df_age_gender.merge(df_mapping, left_on='medicare_area_code_substr', right_on='keywords',
                                            how='left')

        age_bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, float('inf')]
        age_labels = ['0-10', '10-20', '20-30', '30-40', '40-50', '50-60', '60-70', '70-80', '80-90', '90及以上']
        age_group(df_age_gender, 'age', age_bins, age_labels)
        df_age_gender['gender'].replace({'FEMALE': '女', 'MALE': '男'}, inplace=True)
        df_age_gender_group = df_age_gender.groupby(['medicare_area_code_substr', 'age_group', 'gender']).agg(
            {'count': 'sum'}).reset_index()

        # 分区域分年龄性别总量范围
        area_age_gender_combinations = pd.MultiIndex.from_product(
            [df_mapping['name'].unique(), age_labels, ['男', '女']],
            names=['area_name', 'age_group', 'gender'])
        df_area_age_gender_combinations = pd.DataFrame(index=area_age_gender_combinations).reset_index()
        df_area_age_gender_combinations = pd.merge(df_area_age_gender_combinations, df_mapping, left_on='area_name',
                                                   right_on='name', how='left')
        df_age_gender_group = sqldf(
            "select a.*,ifnull(b.count,0) as 'count' from df_area_age_gender_combinations a left join df_age_gender_group b on a.age_group = b.age_group and a.gender = b.gender and a.keywords = b.medicare_area_code_substr")
        df_age_gender_group.rename(columns={'area_name': 'additional_info'}, inplace=True)

        df_age_gender_group.sort_values(by=['additional_info', 'age_group', 'gender'], inplace=True)
        df_age_gender_group.reset_index(drop=True, inplace=True)
        df_age_gender_group['product_set_code'] = self.product_set_code
        df_age_gender_group['publish_time'] = self.publish_time
        try:
            with transaction.atomic():
                for index, row in df_age_gender_group.iterrows():
                    custom_update_or_create(InsureAgeSex,
                                            product_set_code=row['product_set_code'], sex=row['gender'],
                                            age_distribution=row['age_group'], additional_info=row['additional_info'],
                                            defaults={'value': row['count'],
                                                      'publish_time': row['publish_time']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_gender error:{e}')
        return df_age_gender_group

    # 计算每个区域的平均年龄，避免零除错误
    def calculate_weighted_average(self, group):
        total_count = group['count'].sum()
        if total_count == 0:
            return pd.Series({'average_age': 0})
        average_age = (group['age'] * group['count']).sum() / total_count
        return pd.Series({'average_age': average_age})

    def get_age_range(self):
        """
        获取年龄范围统计数据、包括平均年龄、年龄中位数
        """
        # 编码与城市关联
        df_mapping = self.get_area_mapping()

        df_age_gender = self.get_from_cache('get_age_gender_count')
        df_age_gender = df_age_gender.merge(df_mapping, left_on='medicare_area_code_substr', right_on='keywords',
                                            how='left')

        age_bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, float('inf')]
        age_labels = ['0-10', '10-20', '20-30', '30-40', '40-50', '50-60', '60-70', '70-80', '80-90', '90及以上']
        age_group(df_age_gender, 'age', age_bins, age_labels)
        # 根据区域年龄段分组
        df_age_group = df_age_gender.groupby(['name', 'age_group']).agg({'count': 'sum'}).reset_index()
        # 如果组合在df_age_group中不存在，则增加一条，值为0，保证数据完整
        # 分区域分年龄性别总量范围
        area_age_gender_combinations = pd.MultiIndex.from_product([df_mapping['name'].unique(), age_labels],
                                                                  names=['name', 'age_group'])
        df_area_age_gender_combinations = pd.DataFrame(index=area_age_gender_combinations).reset_index()
        df_age_group = sqldf(
            "select a.*,ifnull(b.count,0) as 'count' from df_area_age_gender_combinations a left join df_age_group b on a.age_group = b.age_group and a.name = b.name")
        df_age_group.rename(columns={'name': 'additional_info'}, inplace=True)
        df_age_group['count'] = df_age_group['count'].astype(int)
        if df_age_group['count'].sum() == 0:
            df_age_group['ratio'] = 0
        else:
            df_age_group['ratio'] = df_age_group['count'] / df_age_group.groupby('additional_info')['count'].transform(
                'sum')
            df_age_group['ratio'] = df_age_group['ratio'].fillna(0)  # 处理分母为0的情况，即count总和为0
        # 查询数据库中所有数据
        db_age_group = PublicStatistics.objects.filter(type=self.type, statistical_type='age_ratio',
                                                       product_set_code=self.product_set_code)
        # 查询补充信息不为空的，因为省份单独计算
        if db_age_group.exists():
            df_db_age_group = pd.DataFrame(list(db_age_group.values()))[['id', 'key', 'additional_info']]
            df_db_age_group = df_db_age_group[df_db_age_group['additional_info'].notnull()]
            delete_df = sqldf(
                "select a.id from df_db_age_group a left join df_age_group b on a.key = b.age_group and a.additional_info = b.additional_info where b.age_group is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_age_group.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='age_ratio',
                                            product_set_code=self.product_set_code, key=row['age_group'],
                                            additional_info=row['additional_info'],
                                            defaults={'publish_time': self.publish_time, 'value': row['ratio']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range error:{e}')

        # 计算平均年龄
        df_age_gender['count'] = df_age_gender['count'].astype(int)
        # 计算每个区域的平均年龄
        if df_age_gender.empty:
            df_average_ages = pd.DataFrame(columns=['name', 'average_age'])
        else:
            df_average_ages = df_age_gender.groupby('name').apply(
                lambda x: (x['age'] * x['count']).sum() / x['count'].sum() if x['count'].sum() > 0 else 0).reset_index()
            df_average_ages.rename(columns={0: 'average_age'}, inplace=True)
        df_average_ages = pd.merge(df_mapping, df_average_ages, on='name', how='left')
        df_average_ages['average_age'].fillna(0, inplace=True)
        df_average_ages['indic_name'] = df_average_ages['name'].apply(
            lambda x: self.version + '-年龄-' + str(x) + '-平均值')

        # 获取code
        query_indicator_code(df_average_ages)
        # 删除code为空的行
        df_average_ages.dropna(subset=['code'], inplace=True)
        df_average_ages.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_average_ages.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['average_age']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range_average error:{e}')

        # 计算每个区域的中位数
        if df_age_gender.empty:
            df_median_ages = pd.DataFrame(columns=['name', 'median_age'])
        else:
            df_median_ages = df_age_gender.groupby('name').apply(
                lambda x: np.repeat(x['age'].values, x['count'].values)
            ).reset_index()
            df_median_ages.rename(columns={0: 'median_age'}, inplace=True)
            df_median_ages['median_age'] = df_median_ages['median_age'].apply(lambda x: np.median(x))

        # 合并中位数数据
        df_median_ages = pd.merge(df_mapping, df_median_ages, on='name', how='left')
        df_median_ages['median_age'].fillna(0, inplace=True)
        df_median_ages['indic_name'] = df_median_ages['name'].apply(
            lambda x: self.version + '-年龄-' + str(x) + '-中位值'
        )

        # 获取中位数的编码
        query_indicator_code(df_median_ages)
        # 删除编码为空的行
        df_median_ages.dropna(subset=['code'], inplace=True)
        df_median_ages.fillna(0, inplace=True)

        # 更新数据库中的中位数
        try:
            with transaction.atomic():
                for index, row in df_median_ages.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['median_age']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range_median error:{e}')
        return df_age_group, df_average_ages, df_median_ages

    def get_renewal_ratio(self):
        """
        获取续保占比数据（分城市）
        """
        # 获取编码与城市的对应关系
        df_mapping = self.get_area_mapping()
        # 获取前期的参保人数
        with self.get_connection_dw() as conn_dw:
            df_prev_person_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.previous_product_set_code,
                                                                 statistical_type='当期值', unit='人'), conn_dw)
        df_prev_person_count = df_prev_person_count[
            df_prev_person_count['name'].str.contains('参保人数') & ~df_prev_person_count['name'].str.contains('-参保人数-当期值')][['name','value']]
        df_prev_person_count['city_name'] = df_prev_person_count['name'].apply(lambda x: x.split('-')[3])
        with self.get_connection() as conn:
            df_renewal_ratio = pd.read_sql(
                query_sql('SQL_RENEWAL_RATIO_CITY').format(product_set_code=self.product_set_code,
                                                           previous_product_set_code=self.previous_product_set_code,
                                                           end_datetime=self.publish_time), conn)
        # 关联过去城市名称
        df_renewal_ratio = df_mapping.merge(df_renewal_ratio, left_on='keywords', right_on='medicare_area_code_substr',
                                            how='left')
        # 计算续保率的df
        df_renewal_precent = df_renewal_ratio.merge(df_prev_person_count[['city_name', 'value']], left_on='name', right_on='city_name', how='left')
        df_renewal_precent['renewal_precnet'] = round(df_renewal_precent['renewal_person'] / df_renewal_precent['value'],4).fillna(0)
        df_renewal_precent['indic_name'] = df_renewal_precent['name'].apply(
            lambda x: self.previous_version + '-续保率-' + str(x) + '-当期值')
        # 获取code
        query_indicator_code(df_renewal_precent)
        df_renewal_precent.dropna(subset=['code'], inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_renewal_precent.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['renewal_precnet'] * 100))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_renewal_ratio renewal_precnet error:{e}')


        # 如果除数为0特殊处理
        df_renewal_ratio['renewal_ratio'].fillna(0, inplace=True)
        df_renewal_ratio['indic_name'] = df_renewal_ratio['name'].apply(
            lambda x: self.version + '-续保占比-' + str(x) + '-当期值')
        # 删除name为空的数据
        df_renewal_ratio.dropna(subset=['name'], inplace=True)
        df_renewal_ratio.reset_index(drop=True, inplace=True)
        # 获取code
        query_indicator_code(df_renewal_ratio)
        # 删除code为空的行
        df_renewal_ratio.dropna(subset=['code'], inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_renewal_ratio.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['renewal_ratio'] * 100))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_renewal_ratio error:{e}')

        return df_renewal_ratio,df_renewal_precent


if __name__ == '__main__':
    source = GzGhbInsureCityV2()
    # cs = source.get_daily_sale()
    # print(cs)
    # source.cache_daily_sale()
    # source.cache_main_daily_sale()
    # df_daily_amount_cumsum = source.get_daily_amount_cumsum()
    # print(df_daily_amount_cumsum)
    # total_count = source.get_total_count()
    # print(total_count)
    # total_amount = source.get_total_amount()
    # print(total_amount)
    # person_group = source.get_person_group_count()
    # print(person_group)
    # online_offline_count = source.get_online_offline_count()
    # print(online_offline_count)
    # pay_type_count = source.get_pay_type_count()
    # print(pay_type_count)
    # pay_type_amount = source.get_pay_type_amount()
    # print(pay_type_amount)
    # df_last_24_hour_count = source.get_last_24_hour_count()
    # print(df_last_24_hour_count)
    # df_daily_count = source.get_daily_count()
    # print(df_daily_count)
    # today_count = source.get_today_count()
    # print(today_count)
    # df_daily_amount = source.get_daily_amount()
    # print(df_daily_amount)
    # today_amount = source.get_today_amount()
    # print(today_amount)
    # yesterday_count = source.get_yesterday_count()
    # print(yesterday_count)
    # yesterday_amount = source.get_yesterday_amount()
    # print(yesterday_amount)
    # df_online_source_info = source.get_online_source_info()
    # print(df_online_source_info)
    # df_offline_seller = source.get_channel_info()
    # print(df_offline_seller)
    # df_area_total = source.get_area_info()
    # print(df_area_total)
    # df_age_gender_group = source.get_age_gender()
    # print(df_age_gender_group)
    # source.cache_age_gender_count()
    df_age_group,df_average_ages,df_median_ages = source.get_age_range()
    print(df_age_group)
    print(df_average_ages)
    print(df_median_ages)
    # df_daily_online_count = source.get_daily_online_count()
    # print(df_daily_online_count)
    # df_daily_offline_count = source.get_daily_offline_count()
    # print(df_daily_offline_count)
    # df_daily_cumsum = source.get_daily_cumsum()
    # print(df_daily_cumsum)
    # df_daily_online_cumsum = source.get_daily_online_cumsum()
    # print(df_daily_online_cumsum)
    # df_daily_offline_cumsum = source.get_daily_offline_cumsum()
    # print(df_daily_offline_cumsum)
    # df_medicare_type_count = source.get_medicare_type_count()
    # print(df_medicare_type_count)
    # df_medicare_person_count = source.get_medicare_person_count()
    # print(df_medicare_person_count)
    # df_renewal_ratio,df_renewal_precent = source.get_renewal_ratio()
    # print(df_renewal_ratio)
    # print(df_renewal_precent)
