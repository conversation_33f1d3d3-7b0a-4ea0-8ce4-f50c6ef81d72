# Generated by Django 4.2.1 on 2025-06-11 09:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("medical", "0012_alter_medicalselfprepareddrugbase_each_dose"),
    ]

    operations = [
        migrations.AlterField(
            model_name="medicalchineseherbaldrugbase",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalchineseherbaldrugbase",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldesignatedproviders",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldesignatedproviders",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldrugbase",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldrugbase",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldrugentity",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldrugentity",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalfieldmapping",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalfieldmapping",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalmedicinediagnosis",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalmedicinediagnosis",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalnationalnegotiateddrug",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalnationalnegotiateddrug",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalnationalnegotiateddrugproviders",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalnationalnegotiateddrugproviders",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalselfprepareddrugbase",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalselfprepareddrugbase",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalservicebase",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalservicebase",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalserviceentity",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalserviceentity",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalsuppliesbase",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalsuppliesbase",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalsuppliesentity",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalsuppliesentity",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalsuppliesregistermessage",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="medicalsuppliesregistermessage",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
    ]
