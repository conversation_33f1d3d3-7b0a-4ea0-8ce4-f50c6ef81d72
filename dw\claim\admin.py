from django.contrib import admin

from claim.models import ClaimProtonHeavyIon, ClaimMonthlyPay, ClaimLiabilityPay, ClaimPreexistingCondition, \
    ClaimTopCaseInfo,ClaimAmountRangePay,ClaimAgeRangePay, ClaimAreaPay,ClaimProductPay, ClaimGroupPay, \
    ClaimSellerPay,ClaimPayType, ClaimGenderPay,ClaimPayOverview,ClaimAgeOverview,ClaimXuePingXian, \
    ClaimXuePingXianHN


@admin.register(ClaimProtonHeavyIon)
class ClaimProtonHeavyIonAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code', 'name', 'credential_number', 'pay_amount', 'total_amount', 'coordinated_amount',
        'self_burden_amount', 'past_symptom')
    list_filter = ('product_set_code', 'name', 'credential_number')


@admin.register(ClaimMonthlyPay)
class ClaimMonthlyPayAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code','is_provincial','city', 'pay_month', 'pay_number', 'pay_amount')
    list_filter = ('product_set_code', 'pay_month')


@admin.register(ClaimLiabilityPay)
class ClaimLiabilityPayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'liability_type', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_avg_amount',
        'pay_amount_rate')
    list_filter = ('product_set_code', 'liability_type')


@admin.register(ClaimPreexistingCondition)
class ClaimPreexistingConditionAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'past_symptom_type', 'pay_number', 'pay_person_number', 'pay_amount',
        'pay_avg_amount',
        'pay_amount_rate', 'premium_amount', 'claim_rate')
    list_filter = ('product_set_code', 'past_symptom_type')


@admin.register(ClaimTopCaseInfo)
class ClaimTopCaseInfoAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code', 'name', 'age', 'gender', 'disease', 'pay_amount','total_amount','fund_paid_amount',
        'self_burden_amount','reduction_rate','claim_type')
    list_filter = ('product_set_code', 'gender', 'claim_type')


@admin.register(ClaimAmountRangePay)
class ClaimAmountRangePayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'amount_range', 'type', 'pay_number', 'pay_number_rate', 'pay_amount',
        'pay_amount_rate')
    list_filter = ('product_set_code', 'type')


@admin.register(ClaimAgeRangePay)
class ClaimAgeRangePayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'age_range', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_avg_amount',
        'pay_amount_rate', 'insure_person_number', 'incidence_rate')
    list_filter = ('product_set_code', 'age_range')



@admin.register(ClaimAreaPay)
class ClaimAreaPayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code', 'area_name', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_avg_amount',
        'pay_avg_amount_per_case', 'pay_amount_rate', 'premium_amount', 'insured_person_number', 'claim_rate')
    list_filter = ('product_set_code', 'area_name')


@admin.register(ClaimProductPay)
class ClaimProductPayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'product_type', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_amount_rate',
        'premium_amount', 'claim_rate')
    list_filter = ('product_set_code', 'product_type')



@admin.register(ClaimGroupPay)
class ClaimGroupPayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'policy_type', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_number_rate',
        'pay_person_number_rate', 'pay_amount_rate', 'premium_amount', 'claim_rate')
    list_filter = ('product_set_code', 'policy_type')



@admin.register(ClaimSellerPay)
class ClaimSellerPayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'channel_name', 'pay_number', 'pay_number_rate', 'pay_person_number', 'pay_person_number_rate',
        'pay_amount', 'pay_amount_rate', 'premium_amount', 'claim_rate')
    list_filter = ('product_set_code', 'channel_name')


@admin.register(ClaimPayType)
class ClaimPayTypeAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'type', 'pay_number','pay_person_number', 'pay_number_rate', 'pay_amount', 'pay_amount_rate')
    list_filter = ('product_set_code', 'type')



@admin.register(ClaimGenderPay)
class ClaimGenderPayAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'gender', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_amount_rate','premium_amount')
    list_filter = ('product_set_code', 'gender')


@admin.register(ClaimPayOverview)
class ClaimPayOverviewAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'pay_number', 'pay_person_number', 'pay_amount', 'pay_avg_amount', 'pay_max_amount',
        'pay_max_person_amount')
    list_filter = ('product_set_code',)


@admin.register(ClaimAgeOverview)
class ClaimAgeOverviewAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code','is_provincial','city', 'avg_age', 'max_age', 'min_age')
    list_filter = ('product_set_code',)


@admin.register(ClaimXuePingXian)
class ClaimXuePingXianAdmin(admin.ModelAdmin):
    list_display = (
        'township','school_name','insured_name','insured_credential_number','age','parent_name',
        'parent_mobile','policy_number','claim_number','case_stage','overall_reimbursement_amount',
        'outside_amount','amount','accident_reason','accident_type','accident_category','issue_date',
        'accident_date','actual_paid_amount','remark'
    )


@admin.register(ClaimXuePingXianHN)
class ClaimXuePingXianHNAdmin(admin.ModelAdmin):
    list_display = (
        'claim_number','issue_remark','insured_name','insured_credential_number','age','gender',
        'mobile','accident_date','accident_type','pay_type','accident_reason','complete_date',
        'medical_reimbursement','amount','non_medical_amount','claim_amount',
        'actual_paid_amount','accident_location','school_type','insurance_company'
    )
    list_filter = ('gender', 'accident_type', 'pay_type', 'school_type', 'insurance_company')
    search_fields = ('claim_number', 'insured_name', 'insured_credential_number', 'mobile')