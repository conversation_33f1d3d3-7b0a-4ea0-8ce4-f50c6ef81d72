from django.contrib import admin

from medical.models import (
    MedicalChineseHerbalDrugBase,
    MedicalDesignatedProviders,
    MedicalDrugBase,
    MedicalDrugEntity,
    MedicalFieldMapping,
    MedicalMedicineDiagnosis,
    MedicalNationalNegotiatedDrug,
    MedicalNationalNegotiatedDrugProviders,
    MedicalSelfPreparedDrugBase,
    MedicalServiceBase,
    MedicalServiceEntity,
    MedicalSuppliesBase,
    MedicalSuppliesEntity,
    MedicalSuppliesRegisterMessage,
)


# Register your models here.


@admin.register(MedicalChineseHerbalDrugBase)
class MedicalChineseHerbalDrugBaseAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'type', 'category_source', 'category', 'dosage', 'medicinal_material_name',
                   'source_standard_document_name', 'province_code', 'province_name', 'efficacy_information',
                   'national_healthcare_policy', 'provincial_healthcare_policy', 'medicinal_part',
                   'properties_meridian_tropism', 'preparation_method', 'rid', 'create_time', 'update_time')
    list_filter = ('type', 'category', 'province_name', 'create_time')
    search_fields = ('code', 'name', 'medicinal_material_name')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalDesignatedProviders)
class MedicalDesignatedProvidersAdmin(admin.ModelAdmin):
    list_display = ('id', 'province_code', 'province_name', 'city_code', 'city_name', 'code', 'name', 'category',
                   'category_name', 'address', 'lat', 'lnt', 'type', 'type_name', 'level', 'level_name', 'uscc',
                   'mobile', 'business_scope', 'cross_regional_medical_status', 'inpatient_status', 'outpatient_status',
                   'outpatient_chronic_status', 'outpatient_chronic_disease', 'outpatient_chronic_disease_name',
                   'electronic_prescription_status', 'electronic_certificate_status', 'mobile_payment_status',
                   'insurance_wallet_status', 'electronic_billing_status', 'create_time', 'update_time')
    list_filter = ('type_name', 'level_name', 'province_name', 'city_name', 'create_time')
    search_fields = ('code', 'name', 'uscc', 'mobile')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalDrugBase)
class MedicalDrugBaseAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'type', 'type_name', 'category_name', 'market_status', 'registered_name',
                   'registered_dosage_form', 'registered_specifications', 'product_name', 'expiration', 'dosage_form',
                   'specifications', 'packaging_material', 'each_dose', 'efficacy_information', 'minimum_packaging_count',
                   'minimum_preparation_unit', 'minimum_packaging_unit', 'minimum_prescription_unit', 'otc_flag',
                   'listed_license_holder', 'production_company_name', 'approval_number', 'standard_code',
                   'categories_national', 'number_national', 'generic_name_national', 'dosage_form_national',
                   'remark_national', 'rid', 'create_time', 'update_time')
    list_filter = ('type', 'category_name', 'market_status', 'otc_flag', 'create_time')
    search_fields = ('code', 'registered_name', 'product_name', 'approval_number', 'standard_code')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalDrugEntity)
class MedicalDrugEntityAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'central_code', 'type', 'type_name', 'province_code', 'province_name',
                   'city_code', 'city_name', 'category_name', 'registered_name', 'registered_specifications', 'product_name',
                   'specifications', 'packaging_material', 'minimum_packaging_count', 'minimum_packaging_unit',
                   'minimum_prescription_unit', 'otc_flag', 'production_company_name', 'approval_number',
                   'categories_national', 'number_national', 'generic_name_national', 'dosage_form_national',
                   'procure_ceil_price', 'payment_upper_limit', 'person_type', 'initial_payment_ratio',
                   'pharmacy_sale_allowed', 'payment_restricted_scope', 'government_guided_price', 'begin_date',
                   'end_date', 'create_time', 'update_time')
    list_filter = ('category_name', 'type', 'province_name', 'city_name', 'pharmacy_sale_allowed', 'create_time')
    search_fields = ('code', 'central_code', 'registered_name', 'product_name', 'approval_number')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalFieldMapping)
class MedicalFieldMappingAdmin(admin.ModelAdmin):
    list_display = ('id', 'source_table', 'source_field', 'target_table', 'target_field', 'default', 'description',
                   'dict_table', 'dict_id', 'dict_code_field', 'dict_name_field', 'create_time', 'update_time')
    list_filter = ('source_table', 'target_table', 'dict_table', 'create_time')
    search_fields = ('source_table', 'source_field', 'target_table', 'target_field', 'description')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalMedicineDiagnosis)
class MedicalMedicineDiagnosisAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'code', 'type', 'type_name', 'chapter_code_scope', 'chapter', 'section_code_scope',
                   'section', 'category_code', 'category', 'subcategory_code', 'subcategory', 'detail_code', 'detail',
                   'rid', 'create_time', 'update_time')
    list_filter = ('type', 'type_name', 'create_time')
    search_fields = ('code', 'name', 'chapter', 'section', 'category')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalNationalNegotiatedDrug)
class MedicalNationalNegotiatedDrugAdmin(admin.ModelAdmin):
    list_display = ('id', 'drug_name', 'production_company_name', 'sale_flag', 'mobile', 'create_time', 'update_time')
    list_filter = ('sale_flag', 'create_time')
    search_fields = ('drug_name', 'production_company_name', 'mobile')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalNationalNegotiatedDrugProviders)
class MedicalNationalNegotiatedDrugProvidersAdmin(admin.ModelAdmin):
    list_display = ('id', 'drug_name', 'name', 'type_name', 'province_code', 'province_name', 'city_code', 'city_name',
                   'address', 'lat', 'lnt', 'production_company_name', 'rid', 'create_time', 'update_time')
    list_filter = ('type_name', 'province_name', 'city_name', 'create_time')
    search_fields = ('drug_name', 'name', 'production_company_name', 'address')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalSelfPreparedDrugBase)
class MedicalSelfPreparedDrugBaseAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'minimum_packaging_count', 'minimum_preparation_unit', 'minimum_packaging_unit',
                   'each_dose', 'dosage_form', 'specifications', 'efficacy_information', 'packaging_material',
                   'license_number', 'approval_number', 'approval_begin_date', 'elder_medication_precautions',
                   'child_medication_precautions', 'hospital_name', 'hospital_contact_person', 'hospital_address',
                   'hospital_regional_code', 'hospital_mobile', 'production_company_name', 'production_company_address',
                   'rid', 'create_time', 'update_time')
    list_filter = ('dosage_form', 'create_time')
    search_fields = ('code', 'name', 'hospital_name', 'production_company_name', 'approval_number')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalServiceBase)
class MedicalServiceBaseAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'charge_item_code', 'charge_item_name', 'admin_region_code', 'admin_region_name',
                   'treatment_item_content', 'treatment_item_description', 'treatment_excluded_content', 'pricing_unit',
                   'begin_date', 'end_date', 'remark', 'rid', 'create_time', 'update_time')
    list_filter = ('admin_region_name', 'pricing_unit', 'create_time')
    search_fields = ('code', 'name', 'charge_item_code', 'charge_item_name')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalServiceEntity)
class MedicalServiceEntityAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'charge_item_code', 'charge_item_name', 'central_code', 'level_type',
                   'province_code', 'province_name', 'city_code', 'city_name', 'treatment_item_content',
                   'treatment_item_description', 'treatment_excluded_content', 'pricing_unit', 'payment_type',
                   'payment_upper_limit', 'price', 'price_composition', 'service_output', 'initial_payment_ratio',
                   'limited_payment', 'begin_date', 'additional_info', 'create_time', 'update_time')
    list_filter = ('level_type', 'province_name', 'city_name', 'payment_type', 'create_time')
    search_fields = ('code', 'name', 'central_code', 'charge_item_code', 'charge_item_name')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalSuppliesBase)
class MedicalSuppliesBaseAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'general_name', 'first_type', 'second_type', 'third_type', 'type_path',
                   'material', 'specifications', 'production_company_name', 'rid', 'create_time', 'update_time')
    list_filter = ('first_type', 'second_type', 'third_type', 'material', 'create_time')
    search_fields = ('code', 'name', 'general_name', 'production_company_name')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalSuppliesEntity)
class MedicalSuppliesEntityAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'central_code', 'level_type','province_code', 'province_name',
                    'city_code', 'city_name', 'registered_name', 'registered_number', 'model',
                   'specifications', 'production_company_name', 'initial_payment_ratio', 'payment_upper_limit',
                   'tender_number', 'tender_price', 'packaging_unit', 'begin_date', 'create_time', 'update_time')
    list_filter = ('create_time',)
    search_fields = ('code', 'name', 'central_code', 'registered_name', 'registered_number', 'production_company_name')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')


@admin.register(MedicalSuppliesRegisterMessage)
class MedicalSuppliesRegisterMessageAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'registration_number', 'single_product_name', 'create_time', 'update_time')
    list_filter = ('create_time',)
    search_fields = ('code', 'registration_number', 'single_product_name')
    ordering = ['-create_time']
    readonly_fields = ('create_time', 'update_time')