#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量操作具体实现
包含删除、更新、插入的具体实现方法
"""

import logging
import time
import pandas as pd
from .database_utils import create_sqlalchemy_engine_with_retry

logger = logging.getLogger(__name__)


class BatchOperationImplementation:
    """
    批量操作具体实现类
    """

    def __init__(self, db_config=None):
        """
        初始化实现类

        Args:
            db_config (dict): 数据库配置
        """
        self.db_config = db_config

    def batch_delete_records(self, cursor, to_delete, target_table, unique_fields):
        """
        高性能批量删除记录

        优化策略：
        1. 优先使用id字段进行批量删除（最快）
        2. 单字段使用IN删除
        3. 多字段使用批量OR条件删除
        4. 避免逐条删除，提升性能

        Args:
            cursor: 数据库游标
            to_delete (list): 要删除的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表

        Returns:
            dict: 删除统计信息
        """
        stats = {'deleted': 0, 'errors': 0}

        if not to_delete:
            return stats

        start_time = time.time()
        logger.info(f"开始高性能批量删除 {len(to_delete)} 条记录...")
        print(f"  [DELETE] 开始删除 {len(to_delete)} 条记录...")

        try:
            # 策略1: 优先检查是否有id字段（最高效）
            if self._try_delete_by_id(cursor, to_delete, target_table, stats):
                elapsed_time = time.time() - start_time
                logger.info(f"使用id字段批量删除完成: 删除 {stats['deleted']} 条, 耗时 {elapsed_time:.2f}秒")
                print(f"  [PERFORMANCE] ID字段删除耗时: {elapsed_time:.2f}秒")
                return stats

            # 策略2: 单字段情况，使用IN删除
            if len(unique_fields) == 1:
                method_start = time.time()
                self._delete_by_single_field(cursor, to_delete, target_table, unique_fields[0], stats)
                method_time = time.time() - method_start
                print(f"  [PERFORMANCE] 单字段IN删除耗时: {method_time:.2f}秒")
            else:
                # 策略3: 多字段情况，使用批量OR条件删除
                method_start = time.time()
                self._delete_by_multiple_fields_optimized(cursor, to_delete, target_table, unique_fields, stats)
                method_time = time.time() - method_start
                print(f"  [PERFORMANCE] 多字段OR删除耗时: {method_time:.2f}秒")

        except Exception as e:
            logger.error(f"批量删除失败: {str(e)}")
            stats['errors'] = len(to_delete)
            elapsed_time = time.time() - start_time
            print(f"  [ERROR] 删除失败，耗时: {elapsed_time:.2f}秒")

        elapsed_time = time.time() - start_time
        logger.info(f"批量删除完成: 删除 {stats['deleted']} 条, 错误 {stats['errors']} 条, 耗时 {elapsed_time:.2f}秒")
        print(f"  [TIMING] 删除操作总耗时: {elapsed_time:.2f}秒")
        return stats

    def _try_delete_by_id(self, cursor, to_delete, target_table, stats):
        """
        尝试使用id字段进行批量删除（最高效的方式）

        Args:
            cursor: 数据库游标
            to_delete (list): 要删除的数据列表
            target_table (str): 目标表名
            stats (dict): 统计信息

        Returns:
            bool: 是否成功使用id删除
        """
        # 检查是否有id字段
        id_fields = ['id', 'table_id', f'{target_table}_id']

        for id_field in id_fields:
            if hasattr(to_delete[0], id_field) or (isinstance(to_delete[0], dict) and id_field in to_delete[0]):
                try:
                    # 收集所有id值
                    id_values = []
                    for row in to_delete:
                        if isinstance(row, dict):
                            id_val = row.get(id_field)
                        else:
                            id_val = getattr(row, id_field, None) if hasattr(row, id_field) else row.get(id_field)

                        if id_val is not None and pd.notna(id_val):
                            id_values.append(id_val)

                    if id_values:
                        logger.info(f"使用 {id_field} 字段进行批量删除，共 {len(id_values)} 个ID")

                        # 分批删除，避免SQL语句过长
                        batch_size = 10000  # 每批10000个ID
                        total_deleted = 0

                        for i in range(0, len(id_values), batch_size):
                            batch_ids = id_values[i:i + batch_size]
                            placeholders = ','.join(['%s'] * len(batch_ids))
                            sql = f"DELETE FROM `{target_table}` WHERE `{id_field}` IN ({placeholders})"

                            cursor.execute(sql, batch_ids)
                            batch_deleted = cursor.rowcount
                            total_deleted += batch_deleted

                            logger.info(f"ID批量删除批次 {i//batch_size + 1}: 删除 {batch_deleted} 条")

                        stats['deleted'] = total_deleted
                        return True

                except Exception as e:
                    logger.warning(f"使用 {id_field} 删除失败，尝试其他方式: {e}")
                    continue

        return False

    def _delete_by_single_field(self, cursor, to_delete, target_table, field, stats):
        """
        单字段批量删除

        Args:
            cursor: 数据库游标
            to_delete (list): 要删除的数据列表
            target_table (str): 目标表名
            field (str): 字段名
            stats (dict): 统计信息
        """
        values = []
        for row in to_delete:
            if isinstance(row, dict):
                val = row.get(field)
            else:
                val = getattr(row, field, None) if hasattr(row, field) else row.get(field)

            if val is not None and pd.notna(val):
                values.append(val)

        if values:
            # 分批删除，避免SQL语句过长
            batch_size = 10000
            total_deleted = 0

            for i in range(0, len(values), batch_size):
                batch_values = values[i:i + batch_size]
                placeholders = ','.join(['%s'] * len(batch_values))
                sql = f"DELETE FROM `{target_table}` WHERE `{field}` IN ({placeholders})"

                cursor.execute(sql, batch_values)
                batch_deleted = cursor.rowcount
                total_deleted += batch_deleted

                logger.info(f"单字段删除批次 {i//batch_size + 1}: 删除 {batch_deleted} 条")

            stats['deleted'] = total_deleted

    def _delete_by_multiple_fields_optimized(self, cursor, to_delete, target_table, unique_fields, stats):
        """
        多字段优化批量删除 - 使用批量OR条件而非逐条删除

        Args:
            cursor: 数据库游标
            to_delete (list): 要删除的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表
            stats (dict): 统计信息
        """
        # 构建批量OR条件删除
        batch_size = 1000  # 每批1000条记录的OR条件
        total_deleted = 0

        for i in range(0, len(to_delete), batch_size):
            batch_data = to_delete[i:i + batch_size]

            # 构建OR条件
            or_conditions = []
            params = []

            for row in batch_data:
                and_conditions = []
                for field in unique_fields:
                    if isinstance(row, dict):
                        val = row.get(field)
                    else:
                        val = getattr(row, field, None) if hasattr(row, field) else row.get(field)

                    if val is not None and pd.notna(val):
                        and_conditions.append(f"`{field}` = %s")
                        params.append(val)
                    else:
                        and_conditions.append(f"`{field}` IS NULL")

                if and_conditions:
                    or_conditions.append(f"({' AND '.join(and_conditions)})")

            if or_conditions:
                where_clause = " OR ".join(or_conditions)
                sql = f"DELETE FROM `{target_table}` WHERE {where_clause}"

                cursor.execute(sql, params)
                batch_deleted = cursor.rowcount
                total_deleted += batch_deleted

                logger.info(f"多字段删除批次 {i//batch_size + 1}: 删除 {batch_deleted} 条")

        stats['deleted'] = total_deleted

    def batch_update_records_optimized(self, cursor, to_update, target_table, unique_fields):
        """
        优化的批量更新记录方法

        特点：
        1. 使用 cursor.executemany() 实现真正的批量SQL执行
        2. 按字段签名分组，相同更新字段的记录一起处理
        3. 支持大批次处理，提高性能
        4. 完整的错误处理和性能监控

        Args:
            cursor: 数据库游标
            to_update (list): 要更新的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表

        Returns:
            dict: 更新统计信息
        """
        stats = {'updated': 0, 'errors': 0, 'batches': 0, 'groups': 0}
        if not to_update:
            return stats

        start_time = time.time()

        try:
            # 按字段签名分组
            update_groups = {}
            logger.info(f"开始对 {len(to_update)} 条更新记录进行字段签名分组...")

            for row in to_update:
                # 修复：允许None值字段参与更新，不再过滤掉None值
                # 这样可以将目标表中的有值字段更新为None
                update_fields = [col for col in row.index if col not in unique_fields]
                signature = tuple(sorted(update_fields))
                if signature not in update_groups:
                    update_groups[signature] = []
                update_groups[signature].append(row)

            stats['groups'] = len(update_groups)
            logger.info(f"分组完成: 共 {stats['groups']} 个字段签名组")

            for group_idx, (fields_sig, rows) in enumerate(update_groups.items()):
                if not fields_sig:
                    logger.warning(f"跳过空字段签名组")
                    continue

                logger.info(f"处理第 {group_idx + 1}/{stats['groups']} 组: 字段 {list(fields_sig)}, {len(rows)} 条记录")

                # 根据数据量动态调整批处理大小（提高性能）
                total_rows = len(rows)
                if total_rows > 30000:
                    batch_size = 15000  # 大数据量：每批15000条记录
                elif total_rows > 10000:
                    batch_size = 12000  # 中等数据量：每批12000条记录
                else:
                    batch_size = 10000  # 小数据量：每批10000条记录

                batches = [rows[i:i + batch_size] for i in range(0, total_rows, batch_size)]
                logger.info(f"  使用批处理大小: {batch_size}, 共分 {len(batches)} 批处理")

                for idx, batch_rows in enumerate(batches):
                    batch_start_time = time.time()
                    logger.info(f"  批次 {idx + 1}/{len(batches)}: 处理 {len(batch_rows)} 条记录")

                    try:
                        # 构建批量更新SQL
                        set_clause = ", ".join([f"`{field}` = %s" for field in fields_sig])
                        where_clause = " AND ".join([f"`{field}` = %s" for field in unique_fields])
                        sql = f"UPDATE `{target_table}` SET {set_clause} WHERE {where_clause}"

                        # 准备批量参数
                        params_list = []
                        for row in batch_rows:
                            params = []
                            # SET子句参数
                            for field in fields_sig:
                                params.append(row[field] if pd.notna(row[field]) else None)
                            # WHERE子句参数
                            for field in unique_fields:
                                params.append(row[field] if pd.notna(row[field]) else None)
                            params_list.append(tuple(params))

                        # 执行批量更新 - 这是真正的批量操作，不是逐条执行
                        cursor.executemany(sql, params_list)
                        batch_updated = cursor.rowcount
                        stats['updated'] += batch_updated
                        stats['batches'] += 1

                        batch_time = time.time() - batch_start_time
                        logger.info(f"  批次 {idx + 1} 完成: 更新 {batch_updated} 条, 耗时 {batch_time:.2f}秒")

                    except Exception as e:
                        logger.error(f"  批次 {idx + 1} 更新失败: {str(e)}")
                        stats['errors'] += len(batch_rows)

        except Exception as e:
            logger.error(f"批量更新失败: {str(e)}")
            stats['errors'] = len(to_update)

        total_time = time.time() - start_time
        logger.info(f"批量更新完成: 处理 {stats['groups']} 组, {stats['batches']} 批次, "
                   f"更新 {stats['updated']} 条, 错误 {stats['errors']} 条, "
                   f"总耗时 {total_time:.2f}秒")

        return stats

    def batch_update_records_smart(self, cursor, to_update, target_table, unique_fields):
        """
        智能批量更新记录方法 - 根据数据特征自动选择最优方法

        选择策略：
        1. 单一唯一字段 + 数据量≤50000 + 字段数≤20 → CASE WHEN方法（最快）
        2. 单一唯一字段 + 数据量≤100000 → CASE WHEN方法
        3. 其他情况 → executemany方法（稳定可靠）

        Args:
            cursor: 数据库游标
            to_update (list): 要更新的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表

        Returns:
            dict: 更新统计信息
        """
        stats = {'updated': 0, 'errors': 0, 'batches': 0, 'groups': 0, 'method': 'unknown'}
        if not to_update:
            return stats

        start_time = time.time()
        data_count = len(to_update)
        unique_field_count = len(unique_fields)

        # 分析数据特征
        field_count = len(to_update[0].index) if hasattr(to_update[0], 'index') else len(to_update[0])

        logger.info(f"开始智能批量更新分析: {data_count} 条记录, {unique_field_count} 个唯一字段, {field_count} 个总字段")

        # 智能选择更新方法
        use_case_when = False
        reason = ""

        if unique_field_count == 1:
            if data_count <= 50000 and field_count <= 20:
                use_case_when = True
                reason = f"最优条件: 单一唯一字段, {data_count}条数据≤50000, {field_count}个字段≤20"
            elif data_count <= 100000:
                use_case_when = True
                reason = f"良好条件: 单一唯一字段, {data_count}条数据≤100000"
            else:
                reason = f"数据量过大: {data_count}条 > 100000，使用executemany方法"
        else:
            reason = f"多唯一字段({unique_field_count}个)，使用executemany方法"

        logger.info(f"智能选择结果: {'CASE WHEN' if use_case_when else 'executemany'} 方法 - {reason}")
        print(f"  [STRATEGY] 选择 {'CASE WHEN' if use_case_when else 'executemany'} 更新方法: {reason}")

        try:
            if use_case_when:
                # 使用CASE WHEN超高性能方法
                stats['method'] = 'case_when'
                if self._try_case_when_batch_update(cursor, to_update, target_table, unique_fields[0], stats):
                    total_time = time.time() - start_time
                    logger.info(f"CASE WHEN方法完成: 更新 {stats['updated']} 条, 总耗时 {total_time:.2f}秒")
                    print(f"  [PERFORMANCE] CASE WHEN方法耗时: {total_time:.2f}秒")
                    return stats
                else:
                    # CASE WHEN失败，回退到executemany
                    logger.info("CASE WHEN方法失败，回退到executemany方法")
                    print(f"  [FALLBACK] CASE WHEN失败，回退到executemany方法")
                    stats['method'] = 'executemany_fallback'
                    return self.batch_update_records_optimized(cursor, to_update, target_table, unique_fields)
            else:
                # 直接使用executemany方法
                stats['method'] = 'executemany'
                return self.batch_update_records_optimized(cursor, to_update, target_table, unique_fields)

        except Exception as e:
            logger.error(f"智能批量更新失败: {str(e)}")
            stats['errors'] = len(to_update)
            total_time = time.time() - start_time
            print(f"  [ERROR] 更新失败，耗时: {total_time:.2f}秒")
            return stats

    def batch_update_records_ultra_fast(self, cursor, to_update, target_table, unique_fields):
        """
        超高性能批量更新记录方法 - 使用CASE WHEN语句

        注意：这是直接调用CASE WHEN方法，建议使用batch_update_records_smart进行智能选择

        Args:
            cursor: 数据库游标
            to_update (list): 要更新的数据列表
            target_table (str): 目标表名
            unique_fields (list): 唯一性字段列表

        Returns:
            dict: 更新统计信息
        """
        stats = {'updated': 0, 'errors': 0, 'batches': 0, 'groups': 0, 'method': 'case_when_direct'}
        if not to_update:
            return stats

        start_time = time.time()
        logger.info(f"开始CASE WHEN超高性能批量更新 {len(to_update)} 条记录...")

        try:
            # 直接使用CASE WHEN批量更新（适用于单一唯一字段的情况）
            if len(unique_fields) == 1 and len(to_update) <= 100000:
                if self._try_case_when_batch_update(cursor, to_update, target_table, unique_fields[0], stats):
                    total_time = time.time() - start_time
                    logger.info(f"CASE WHEN超高性能更新完成: 更新 {stats['updated']} 条, 总耗时 {total_time:.2f}秒")
                    return stats

            # 如果不满足条件，回退到executemany方法
            logger.info("不满足CASE WHEN条件，回退到executemany批量更新方法")
            stats['method'] = 'executemany_fallback'
            return self.batch_update_records_optimized(cursor, to_update, target_table, unique_fields)

        except Exception as e:
            logger.error(f"CASE WHEN批量更新失败: {str(e)}")
            stats['errors'] = len(to_update)
            return stats

    def _try_case_when_batch_update(self, cursor, to_update, target_table, unique_field, stats):
        """
        尝试使用CASE WHEN进行批量更新（最高效的方法）

        Returns:
            bool: 是否成功
        """
        try:
            # 按字段签名分组
            update_groups = {}
            for row in to_update:
                # 获取需要更新的字段（排除唯一性字段）
                update_fields = [col for col in row.index if col != unique_field]
                fields_sig = tuple(sorted(update_fields))
                if fields_sig not in update_groups:
                    update_groups[fields_sig] = []
                update_groups[fields_sig].append(row)

            stats['groups'] = len(update_groups)
            logger.info(f"CASE WHEN更新: 数据按字段签名分为 {stats['groups']} 组")

            for group_idx, (fields_sig, rows) in enumerate(update_groups.items()):
                if not fields_sig:
                    continue

                logger.info(f"CASE WHEN更新第 {group_idx + 1}/{stats['groups']} 组: 字段 {list(fields_sig)}, {len(rows)} 条记录")

                # 分批处理，每批最多3000条（CASE WHEN的最佳批次大小）
                batch_size = 3000
                for i in range(0, len(rows), batch_size):
                    batch_rows = rows[i:i + batch_size]
                    batch_start_time = time.time()

                    # 构建CASE WHEN SQL
                    unique_values = []
                    set_clauses = []

                    # 收集所有唯一值
                    for row in batch_rows:
                        unique_values.append(row[unique_field])

                    # 为每个字段构建CASE WHEN子句
                    for field in fields_sig:
                        case_when_parts = []
                        for row in batch_rows:
                            value = row[field] if pd.notna(row[field]) else None
                            # 转义唯一字段的值
                            escaped_unique_value = str(row[unique_field]).replace("'", "''")
                            if value is None:
                                case_when_parts.append(f"WHEN `{unique_field}` = '{escaped_unique_value}' THEN NULL")
                            else:
                                # 转义单引号
                                escaped_value = str(value).replace("'", "''")
                                case_when_parts.append(f"WHEN `{unique_field}` = '{escaped_unique_value}' THEN '{escaped_value}'")

                        case_when_sql = f"`{field}` = CASE " + " ".join(case_when_parts) + f" ELSE `{field}` END"
                        set_clauses.append(case_when_sql)

                    # 构建IN子句（转义所有值）
                    escaped_unique_values = [str(v).replace("'", "''") for v in unique_values]
                    unique_values_str = "', '".join(escaped_unique_values)

                    # 构建完整的UPDATE SQL
                    sql = f"UPDATE `{target_table}` SET {', '.join(set_clauses)} WHERE `{unique_field}` IN ('{unique_values_str}')"

                    # 执行更新
                    cursor.execute(sql)
                    batch_updated = cursor.rowcount
                    stats['updated'] += batch_updated
                    stats['batches'] += 1

                    batch_time = time.time() - batch_start_time
                    logger.info(f"  CASE WHEN批次 {i//batch_size + 1} 完成: 更新 {batch_updated} 条, 耗时 {batch_time:.2f}秒")

            return True

        except Exception as e:
            logger.warning(f"CASE WHEN批量更新失败，将回退到executemany: {str(e)}")
            return False

    def batch_insert_with_tosql(self, to_insert, target_table):
        """
        使用to_sql方法批量插入新记录，增加连接池配置和重试机制
        如果to_sql失败，自动回退到传统INSERT方法

        Args:
            to_insert (list): 要插入的数据列表
            target_table (str): 目标表名

        Returns:
            dict: 操作统计信息
        """
        stats = {'inserted': 0, 'errors': 0, 'method': 'unknown'}

        if not to_insert:
            return stats

        start_time = time.time()
        logger.info(f"开始批量插入 {len(to_insert)} 条记录...")
        print(f"  [INSERT] 开始插入 {len(to_insert)} 条记录...")

        # 首先尝试to_sql方法
        tosql_start = time.time()
        if self._try_tosql_insert(to_insert, target_table, stats):
            stats['method'] = 'to_sql'
            total_time = time.time() - start_time
            logger.info(f"to_sql方法插入完成: 插入 {stats['inserted']} 条, 耗时 {total_time:.2f}秒")
            print(f"  [PERFORMANCE] to_sql方法耗时: {total_time:.2f}秒")
            return stats

        tosql_time = time.time() - tosql_start
        print(f"  [FALLBACK] to_sql失败(耗时{tosql_time:.2f}秒)，回退到传统INSERT方法")

        # 如果to_sql失败，回退到传统INSERT方法
        logger.info("to_sql方法失败，回退到传统INSERT方法")
        fallback_start = time.time()
        result = self._fallback_insert_method(to_insert, target_table, stats)
        fallback_time = time.time() - fallback_start

        result['method'] = 'executemany_fallback'
        total_time = time.time() - start_time
        print(f"  [PERFORMANCE] 传统INSERT耗时: {fallback_time:.2f}秒, 总耗时: {total_time:.2f}秒")

        return result

    def _try_tosql_insert(self, to_insert, target_table, stats):
        """
        尝试使用to_sql方法插入

        Returns:
            bool: 是否成功
        """
        max_retries = 2  # 减少重试次数
        retry_delay = 1

        for attempt in range(max_retries):
            try:
                # 转换为DataFrame
                if isinstance(to_insert[0], pd.Series):
                    df = pd.DataFrame(to_insert)
                else:
                    df = pd.DataFrame(to_insert)

                # 确保没有NaN值
                df = df.where(pd.notnull(df), None)

                # 使用简单SQLAlchemy引擎批量插入
                try:
                    from .database_utils import create_simple_sqlalchemy_engine
                    engine = create_simple_sqlalchemy_engine(self.db_config)
                except ImportError:
                    # 回退到原方法
                    from .database_utils import create_sqlalchemy_engine_with_retry
                    engine = create_sqlalchemy_engine_with_retry(self.db_config)

                # 批量插入数据
                df.to_sql(target_table, engine, if_exists='append', index=False)

                stats['inserted'] = len(df)
                logger.info(f"使用to_sql方法成功插入 {stats['inserted']} 条记录")

                # 关闭引擎
                engine.dispose()
                return True

            except Exception as e:
                logger.warning(f"to_sql批量插入尝试 {attempt + 1}/{max_retries} 失败: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    logger.warning(f"to_sql方法最终失败，将使用传统INSERT方法")
                    return False

        return False

    def _fallback_insert_method(self, to_insert, target_table, stats):
        """
        传统INSERT方法作为备用方案

        Args:
            to_insert (list): 要插入的数据列表
            target_table (str): 目标表名
            stats (dict): 统计信息

        Returns:
            dict: 操作统计信息
        """
        try:
            from .database_utils import DatabaseConnectionManager

            # 获取数据库连接
            conn_manager = DatabaseConnectionManager(self.db_config)
            conn = conn_manager.get_connection()
            cursor = conn.cursor()

            # 获取第一条记录的字段
            if not to_insert:
                return stats

            first_record = to_insert[0]
            if isinstance(first_record, pd.Series):
                columns = list(first_record.index)
            else:
                columns = list(first_record.keys())

            # 构建INSERT SQL
            placeholders = ', '.join(['%s'] * len(columns))
            columns_str = ', '.join([f'`{col}`' for col in columns])
            sql = f"INSERT INTO `{target_table}` ({columns_str}) VALUES ({placeholders})"

            # 准备批量数据
            batch_data = []
            for record in to_insert:
                if isinstance(record, pd.Series):
                    values = [record[col] if pd.notna(record[col]) else None for col in columns]
                else:
                    values = [record.get(col) if record.get(col) is not None and pd.notna(record.get(col)) else None for col in columns]
                batch_data.append(tuple(values))

            # 分批插入
            batch_size = 5000  # 传统方法使用较小批次
            total_inserted = 0

            for i in range(0, len(batch_data), batch_size):
                batch = batch_data[i:i + batch_size]
                cursor.executemany(sql, batch)
                batch_inserted = cursor.rowcount
                total_inserted += batch_inserted
                logger.info(f"传统INSERT批次 {i//batch_size + 1}: 插入 {batch_inserted} 条")

            stats['inserted'] = total_inserted
            logger.info(f"传统INSERT方法成功插入 {total_inserted} 条记录")

            # 关闭连接
            cursor.close()
            conn.close()

        except Exception as e:
            logger.error(f"传统INSERT方法也失败: {str(e)}")
            stats['errors'] = len(to_insert)

        return stats


# 为BatchOperationManager添加具体实现方法
def add_implementation_methods(manager_class):
    """
    为BatchOperationManager类添加具体实现方法

    Args:
        manager_class: BatchOperationManager类
    """
    impl = BatchOperationImplementation()

    # 添加方法到管理器类
    manager_class._batch_delete_records = impl.batch_delete_records
    manager_class._batch_update_records_optimized = impl.batch_update_records_smart  # 使用智能选择方法
    manager_class._batch_insert_with_tosql = impl.batch_insert_with_tosql

    # 同时提供直接访问各种更新方法的接口
    manager_class._batch_update_records_executemany = impl.batch_update_records_optimized
    manager_class._batch_update_records_case_when = impl.batch_update_records_ultra_fast
