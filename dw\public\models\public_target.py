from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class PublicTarget(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    type = models.CharField(max_length=32, blank=True, null=True, verbose_name='类型')
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='名称')
    short_name = models.CharField(max_length=64, blank=True, null=True, verbose_name='简称')
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='编码')
    target = models.IntegerField(blank=True, null=True, verbose_name='销售目标')

    class Meta:
        db_table = 'public_target'
        verbose_name = '销售目标表'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code','type', 'name', 'short_name'],
                name='unique_public_target_combination'
            ),
        ]
