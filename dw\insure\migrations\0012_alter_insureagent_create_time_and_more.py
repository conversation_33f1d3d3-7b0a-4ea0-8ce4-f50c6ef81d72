# Generated by Django 4.2.1 on 2025-05-26 09:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0011_insurepvuv_unique_insure_pvuv_combination"),
    ]

    operations = [
        migrations.AlterField(
            model_name="insureagent",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insureagent",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="insureagesex",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insureagesex",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="insurearea",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insurearea",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="insuregroup",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insuregroup",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="insuremobile",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insuremobile",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="insureonline",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insureonline",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True, db_comment="创建时间", verbose_name="创建时间"
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True, db_comment="更新时间", verbose_name="更新时间"
            ),
        ),
    ]
