import json
import warnings
from pathlib import Path
import idna
import pandas as pd
import pymysql

from dw import settings

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

DB = settings.DATABASES['default']
DB_UMAMI = settings.DATABASES['umami']


def get_connection():
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn


def get_connection_umami():
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB_UMAMI["HOST"]).decode('utf-8'), port=int(DB_UMAMI["PORT"]),
                           user=DB_UMAMI["USER"],
                           password=DB_UMAMI["PASSWORD"], database=DB_UMAMI["NAME"])
    return conn


def get_person_count(website_id,start_time):
    """
    获取访客量
    :param website_id:
    :return:
    """
    sql = """
    SELECT count(DISTINCT session_id ) as person_count
    FROM
    website_event 
    WHERE
        website_id = '{website_id}'
        AND event_type = '1'
        AND CONVERT_TZ( created_at, '+00:00', '+08:00' ) >= '{start_time}';
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id,start_time=start_time), conn)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['person_count']


def get_daily_uv(website_id):
    """
    获取日度访客量
    :param website_id:
    :return:
    """
    sql = """
    SELECT date(CONVERT_TZ( created_at, '+00:00', '+08:00' )) date,
	count(distinct session_id) as person_count
    FROM
    website_event 
    WHERE
        website_id = '{website_id}'
        AND event_type = '1'
    group by date(CONVERT_TZ( created_at, '+00:00', '+08:00' ));
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id), conn)
    df.rename(columns={'date': '日期', 'person_count': '访客量'}, inplace=True)
    return df


def get_view_count(website_id, start_time):
    """
    获取浏览量数据
    :param website_id:
    :return:
    """
    sql = """
    select count(*) view_num from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    and CONVERT_TZ( created_at, '+00:00', '+08:00' ) >='{start_time}'
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id, start_time=start_time), conn)

    if df.empty:
        return 0
    else:
        return df.iloc[0]['view_num']


def get_daily_pv(website_id):
    """
    获取日度浏览量数据
    :param website_id:
    :return:
    """
    sql = """
    select date(CONVERT_TZ( created_at, '+00:00', '+08:00' )) date,count(*) view_num from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    group by date(CONVERT_TZ( created_at, '+00:00', '+08:00' ))
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id), conn)
    df.rename(columns={'date': '日期', 'view_num': '浏览量'}, inplace=True)
    return df


def get_visit_count(website_id):
    """
    获取访问次数据
    """
    sql = """
        select count(distinct visit_id) as visit_count from website_event
        where website_id = '{website_id}'
        and event_type = 1;
        """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id), conn)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['visit_count']


def get_counce_rate(website_id):
    """
    获取跳出率数据
    """
    sql = """
    # 只访问了单个页面，除总访问次数，计算跳出率
    select sum(one_page_count) one_page_count from (
    select visit_id,count(*) one_page_count  from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    group by visit_id
    HAVING count(*) =1) a
    """
    with get_connection_umami() as conn:
        one_page_count = pd.read_sql(sql.format(website_id=website_id), conn)
    if one_page_count.empty:
        one_page_count = 0
    else:
        one_page_count = one_page_count.iloc[0]['one_page_count']

    visit_count = get_visit_count(website_id)
    if visit_count == 0:
        return '0%'
    else:
        return str(int(round(one_page_count / visit_count * 100, 0))) + '%'


def convert_seconds_to_hms(seconds):
    """
    将秒数转换为小时、分钟和秒的格式。

    参数:
    seconds (int): 总秒数

    返回:
    tuple: (小时, 分钟, 秒)
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return hours, minutes, seconds


def get_visit_duration(website_id):
    """
    获取平均访问时长数据
    """
    sql = """
    select avg(seconds) avg_seconds from 
    (select visit_id,TIMESTAMPDIFF(SECOND,min(created_at),max(created_at)) seconds  from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    group by visit_id) a
            """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id), conn)
    if df.empty:
        avg_seconds = 0
    else:
        avg_seconds = df.iloc[0]['avg_seconds']

    hours, minutes, seconds = convert_seconds_to_hms(avg_seconds)
    if hours > 0:
        return f"{hours}小时{minutes}钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分{seconds}秒"
    else:
        return f"{seconds}秒"


def get_beacon_data(website_id):
    """
    获取埋点数据
    """
    # 根据访客数量统计，避免一个人多次访问的问题
    sql = """
    select event_name,count(distinct session_id) num from website_event
    where website_id = '{website_id}'
    and event_type = '2'
    and CONVERT_TZ( created_at, '+00:00', '+08:00' ) >='2024-09-14 15:00:00'
    group by event_name
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id), conn)
    sql_mapping = """
    select `key` as name,label as event_name from system_dict_value
    where dict_id = '14'
    and status = '1'
    """
    with get_connection() as conn:
        df_mapping = pd.read_sql(sql_mapping, conn)

    df = pd.merge(df, df_mapping, on='event_name', how='left')
    df.rename(columns={'name': '事件名称', 'num': '事件数量'}, inplace=True)
    print(df)
    # 访客数，因为部分埋点是14号之后才有，所以添加限制条件
    view_count = int(get_person_count(website_id, '2024-09-14 15:00:00'))
    vist_count = int(df[df['事件名称'] == '进入产品详情页']['事件数量'].sum())
    # add_person_count = int(df[df['事件名称'] == '添加参保人信息']['事件数量'].sum())
    buy_product = int(df[df['事件名称'] == '购买产品']['事件数量'].sum())
    pay_count = int(df[(df['事件名称'] == '医保个账支付') | (df['事件名称'] == '自费支付')]['事件数量'].sum())
    pay_success_count = int(df[df['事件名称'] == '进入支付成功页']['事件数量'].sum())
    beacon_list = [view_count, vist_count,buy_product, pay_count, pay_success_count]
    print(view_count, vist_count, buy_product, pay_count, pay_success_count)
    beacon_ratio_list = [100, round(vist_count / view_count * 100, 2), round(buy_product / view_count * 100, 2),
                         round(pay_count / view_count * 100, 2), round(pay_success_count / view_count * 100, 2)]
    return beacon_list, beacon_ratio_list


def content():
    website_id = 'c73e45a2-073b-402c-8e83-4caa1f8a60ca'
    data = {}

    person_count = get_person_count(website_id, '2000-01-01 00:00:00')
    view_count = get_view_count(website_id, '2000-01-01 00:00:00')
    visit_count = get_visit_count(website_id)
    visit_duration = get_visit_duration(website_id)
    counce_rate = get_counce_rate(website_id)
    data['浏览量'] = int(view_count)
    data['访问次数'] = int(visit_count)
    data['访客数'] = int(person_count)
    data['跳出率'] = counce_rate
    data['平均访问时间'] = visit_duration

    df_daily_pv = get_daily_pv(website_id)
    df_daily_uv = get_daily_uv(website_id)

    # 转换为 DataFrame
    sub_data_df = pd.merge(df_daily_pv, df_daily_uv, on='日期', how='outer')
    sub_data_df = sub_data_df.fillna(0)
    sub_data_df.sort_values(by='日期', ascending=True, inplace=True)
    sub_data_df['日期'] = sub_data_df['日期'].apply(lambda x: x.strftime('%Y-%m-%d'))
    sub_data_df.reset_index(drop=True, inplace=True)
    data['日度走势'] = sub_data_df.to_dict('records')
    y_data, beacon_ratio_list = get_beacon_data(website_id)
    data['埋点数据'] = beacon_ratio_list

    # data结果存入到nhb_beacon_insure_v4json文件中
    # file_name = Path.cwd().joinpath('temp_files').joinpath("nhb_beacon_insure_v4.json")
    #调度用这个地址，本地调试用上面
    file_name = Path.cwd().joinpath('transfrom').joinpath('tasks').joinpath('insure').joinpath('temp_files').joinpath("nhb_beacon_insure_v4.json")
    with open(file_name, 'w') as f:
        json.dump(data, f)
    return data


# if __name__ == '__main__':
#     data = content()
#     print(data)
