{% extends "public/data_dictionary/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<!-- 当前页面已经通过模板中的条件判断自动高亮 -->
<style>
/* 页面布局优化 */
.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.database-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

.database-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.database-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.database-name {
    font-size: 1.5em;
    font-weight: bold;
    color: #333;
    text-decoration: none;
}

.database-name:hover {
    color: #007bff;
    text-decoration: none;
}

.database-type {
    background: #007bff;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9em;
}

.database-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.9em;
    color: #666;
}

.database-description {
    color: #666;
    margin-top: 10px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 4em;
    margin-bottom: 20px;
    color: #ddd;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>{{ title }}</h1>
                <div>
                    <a href="{% url 'data_dictionary:global_search' %}" class="btn btn-outline-primary">
                        <i class="fas fa-search"></i> 全局搜索
                    </a>
                    <a href="{% url 'data_dictionary:statistics' %}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> 统计信息
                    </a>
                </div>
            </div>

            {% if databases %}
                <div class="row">
                    {% for database in databases %}
                    <div class="col-lg-6 col-xl-4">
                        <div class="database-card">
                            <div class="database-header">
                                <a href="{% url 'data_dictionary:database_detail' database.id %}" class="database-name">
                                    {{ database.name }}
                                </a>
                                <span class="database-type">{{ database.type }}</span>
                            </div>
                            
                            <div class="database-stats">
                                <div class="stat-item">
                                    <div class="stat-number">{{ database.table_count }}</div>
                                    <div class="stat-label">总表数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">{{ database.active_table_count }}</div>
                                    <div class="stat-label">活跃表</div>
                                </div>
                            </div>
                            
                            {% if database.description %}
                            <div class="database-description">
                                {{ database.description|truncatechars:100 }}
                            </div>
                            {% endif %}
                            
                            {% if database.data_source %}
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-database"></i> 数据来源: {{ database.data_source }}
                                </small>
                            </div>
                            {% endif %}
                            
                            {% if database.update_frequency %}
                            <div class="mt-1">
                                <small class="text-muted">
                                    <i class="fas fa-clock"></i> 更新频率: {{ database.update_frequency }}
                                </small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-database"></i>
                    <h3>暂无数据库信息</h3>
                    <p>请先使用管理命令同步数据库元数据信息</p>
                    <div class="mt-3">
                        <code>python manage.py sync_data_dictionary</code>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 添加卡片点击效果
    $('.database-card').click(function(e) {
        if (!$(e.target).is('a')) {
            var link = $(this).find('.database-name').attr('href');
            if (link) {
                window.location.href = link;
            }
        }
    });
    
    // 添加键盘快捷键支持
    $(document).keydown(function(e) {
        // Ctrl+K 打开全局搜索
        if (e.ctrlKey && e.keyCode === 75) {
            e.preventDefault();
            window.location.href = "{% url 'data_dictionary:global_search' %}";
        }
    });
});
</script>
{% endblock %}
