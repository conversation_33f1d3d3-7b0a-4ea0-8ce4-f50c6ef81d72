#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETL表配置文件
定义各种表的ETL处理配置
"""

# 🏥 ETL表配置字典
TABLE_CONFIGS = {
    "医保定点机构": {
        "source_table": "spider_fuwu_fixed_hospital",
        "target_table": "medical_designated_providers",
        "unique_fields": ['province_code', 'city_code', 'code'],
        "description": "医保定点医疗机构数据",
        "icon": "[医院]",
        "target_where_clause": "type_name='定点医疗机构'"  # 目标表数据过滤条件
    },

    "医保定点药店": {
        "source_table": "spider_fuwu_retail_pharmacy",
        "target_table": "medical_designated_providers",
        "unique_fields": ['province_code', 'city_code', 'code'],
        "description": "医保定点药店数据",
        "icon": "[药店]",
        "target_where_clause": "type_name='定点零售药店'"  # 目标表数据过滤条件
    },

    "医疗服务项目": {
        "source_table": "spider_fuwu_service_facilities",
        "target_table": "medical_service_base",
        "unique_fields": ['code', 'charge_item_code', 'admin_region_code'],
        "description": "医疗服务项目分类基础数据",
        "icon": "[服务]",
        "target_where_clause": None  # 无特殊过滤条件
    },

    "西药信息": {
        "source_table": "spider_fuwu_western_medicine",
        "target_table": "medical_drug_base",
        "unique_fields": ['code'],
        "description": "西药基础信息数据",
        "icon": "[西药]",
        "target_where_clause": "type_name = '西药'"  # 无特殊过滤条件
    },

    "中成药信息": {
        "source_table": "spider_fuwu_chinese_prescription_medicine",
        "target_table": "medical_drug_base",
        "unique_fields": ['code'],
        "description": "中成药基础信息数据",
        "icon": "[中药]",
        "target_where_clause": "type_name = '中成药'"  # 无特殊过滤条件
    },

    "西医疾病诊断": {
        "source_table": "spider_fuwu_western_disease",
        "target_table": "medical_medicine_diagnosis",
        "unique_fields": ['rid'],
        "description": "西医疾病诊断数据",
        "icon": "[诊断]",
        "target_where_clause": "type_name = '西医疾病诊断'"  # 目标表数据过滤条件
    },

    "手术操作分类": {
        "source_table": "spider_fuwu_surgical_catalog",
        "target_table": "medical_medicine_diagnosis",
        "unique_fields": ['code'],
        "description": "手术操作分类数据",
        "icon": "[手术]",
        "target_where_clause": "type_name = '手术操作分类'"  # 目标表数据过滤条件
    },

    "中医疾病分类": {
        "source_table": "spider_fuwu_tcm_disease",
        "target_table": "medical_medicine_diagnosis",
        "unique_fields": ['code'],
        "description": "中医疾病分类数据",
        "icon": "[中医疾病]",
        "target_where_clause": "type_name = '中医疾病分类'"  # 目标表数据过滤条件
    },

    "医用耗材": {
        "source_table": "spider_fuwu_medical_supplies",
        "target_table": "medical_supplies_base",
        "unique_fields": ['code'],
        "description": "医用耗材分类基础数据",
        "icon": "[耗材]",
        "target_where_clause": None  # 无特殊过滤条件
    },

    "医用耗材注册信息": {
        "source_table": "spider_fuwu_medical_supplies",
        "target_table": "medical_supplies_register_message",
        "unique_fields": ['code', 'registration_number', 'single_product_name'],
        "description": "医用耗材注册信息数据（一对多映射）",
        "icon": "[注册]",
        "target_where_clause": None  # 无特殊过滤条件
    },

    "自制药信息": {
        "source_table": "spider_fuwu_selfprep_medicine",
        "target_table": "medical_self_prepared_drug_base",
        "unique_fields": ['code'],
        "description": "自制药基础信息数据",
        "icon": "[自制药]",
        "target_where_clause": None  # 无特殊过滤条件
    },

    "中草药信息": {
        "source_table": "spider_fuwu_tcm_herb",
        "target_table": "medical_chinese_herbal_drug_base",
        "unique_fields": ['code'],
        "description": "中草药基础信息数据",
        "icon": "[草药]",
        "target_where_clause": None  # 无特殊过滤条件
    },

    "国谈药目录清单": {
        "source_table": "spider_fuwu_national_drug_catalog",
        "target_table": "medical_national_negotiated_drug",
        "unique_fields": ['drug_name'],
        "description": "国谈药目录清单数据",
        "icon": "[国谈药]",
        "target_where_clause": None  # 无特殊过滤条件
    },

    "国谈药定点零售药店": {
        "source_table": "spider_fuwu_national_drug_retail_pharmacy",
        "target_table": "medical_national_negotiated_drug_providers",
        "unique_fields": ['rid'],  # 使用目标表字段名
        "description": "国谈药定点零售药店数据",
        "icon": "[国谈药店]",
        "target_where_clause": "type = '定点零售药店'"  # 无特殊过滤条件
    },

    "国谈药定点医疗机构": {
        "source_table": "spider_fuwu_national_drug_hospital",
        "target_table": "medical_national_negotiated_drug_providers",
        "unique_fields": ['rid'],  # 使用目标表字段名
        "description": "国谈药定点医疗机构数据",
        "icon": "[国谈药医疗机构]",
        "target_where_clause": "type = '定点医疗机构'"  # 无特殊过滤条件
    },

}

# 🎯 处理模式配置
PROCESSING_MODES = {
    "1": {
        "name": "测试模式",
        "description": "处理1000条数据",
        "icon": "[测试]",
        "limit": 1000
    },
    "2": {
        "name": "全量处理",
        "description": "处理所有数据",
        "icon": "[快速]",
        "limit": None
    },
    "3": {
        "name": "全量处理",
        "description": "处理所有数据（显示总量）",
        "icon": "[统计]",
        "limit": None
    },
    "4": {
        "name": "自定义数量",
        "description": "用户指定处理数量",
        "icon": "[自定义]",
        "limit": "custom"
    }
}

# 🔧 操作模式配置
OPERATION_MODES = {
    "1": {
        "name": "Upsert模式",
        "description": "推荐，支持增删改",
        "icon": "[推荐]",
        "mode": "upsert"
    },
    "2": {
        "name": "传统模式",
        "description": "仅插入新数据",
        "icon": "[传统]",
        "mode": "traditional"
    }
}

def get_table_config(table_name):
    """
    获取指定表的配置

    Args:
        table_name (str): 表名

    Returns:
        dict: 表配置信息，如果不存在返回None
    """
    return TABLE_CONFIGS.get(table_name)

def get_all_table_names():
    """
    获取所有可用的表名列表

    Returns:
        list: 表名列表
    """
    return list(TABLE_CONFIGS.keys())

def get_processing_mode_config(mode_choice):
    """
    获取处理模式配置

    Args:
        mode_choice (str): 模式选择

    Returns:
        dict: 处理模式配置
    """
    return PROCESSING_MODES.get(mode_choice)

def get_operation_mode_config(operation_choice):
    """
    获取操作模式配置

    Args:
        operation_choice (str): 操作选择

    Returns:
        dict: 操作模式配置
    """
    return OPERATION_MODES.get(operation_choice)

def print_table_menu():
    """
    打印表选择菜单
    """
    print("\n[表选择] 请选择要处理的表:")
    for i, (table_name, config) in enumerate(TABLE_CONFIGS.items(), 1):
        icon = config.get('icon', '[*]')
        print(f"{i}. {icon} {table_name} - {config['description']}")

def print_processing_mode_menu():
    """
    打印处理模式菜单
    """
    print("\n[处理模式] 请选择处理模式:")
    for choice, config in PROCESSING_MODES.items():
        icon = config.get('icon', '[*]')
        print(f"{choice}. {icon} {config['name']} ({config['description']})")

def print_operation_mode_menu():
    """
    打印操作模式菜单
    """
    print("\n[操作模式] 请选择操作模式:")
    for choice, config in OPERATION_MODES.items():
        icon = config.get('icon', '[*]')
        print(f"{choice}. {icon} {config['name']} ({config['description']})")

def validate_table_choice(choice, max_choice):
    """
    验证表选择是否有效

    Args:
        choice (str): 用户选择
        max_choice (int): 最大选择数

    Returns:
        tuple: (是否有效, 选择索引)
    """
    try:
        choice_index = int(choice) - 1
        if 0 <= choice_index < max_choice:
            return True, choice_index
        else:
            return False, 0
    except (ValueError, IndexError):
        return False, 0

# 🎨 默认配置
DEFAULT_BATCH_SIZE = 10000
DEFAULT_TABLE = "医保定点机构"
