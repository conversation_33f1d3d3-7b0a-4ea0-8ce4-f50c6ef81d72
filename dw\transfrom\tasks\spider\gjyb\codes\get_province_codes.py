from bs4 import BeautifulSoup
import pandas as pd

def get_area_codes(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    area_codes = {}
    
    # 查找所有城市节点
    city_nodes = soup.find_all('li', class_='el-cascader-node')
    
    for node in city_nodes:
        # 获取城市编码
        input_tag = node.find('input', class_='el-radio__original')
        if input_tag and 'value' in input_tag.attrs:
            code = input_tag['value']
            
            # 获取城市名称
            city_span = node.find('span', {'data-v-173cce12': ''})
            if city_span:
                city_name = city_span.text.strip()
                area_codes[code] = city_name
    
    return area_codes

# 从文件读取HTML内容
try:
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    web_txt_path = os.path.join(current_dir, 'web.txt')
    
    with open(web_txt_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
except FileNotFoundError:
    print(f'错误：找不到web.txt文件，请确保文件位于：{web_txt_path}')
    exit(1)
except Exception as e:
    print(f'读取文件时发生错误：{str(e)}')
    exit(1)

# 获取城市编码映射
area_codes = get_area_codes(html_content)

# 将结果转换为DataFrame
df = pd.DataFrame(list(area_codes.items()), columns=['code', 'city'])

df.to_excel(os.path.join(current_dir, 'area_codes.xlsx'), index=False)