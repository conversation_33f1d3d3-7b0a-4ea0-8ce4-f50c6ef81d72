#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保险产品埋点统计数据模型
"""

import django
from django.db import models
from common.models import BaseModel
from django.db.models import constraints
from utils.column_name import db_comment_kwarg


class InsureBeacon(BaseModel):
    """
    保险产品埋点统计数据表
    存储用户行为埋点的统计数据，如转化率等
    """
    
    # 基本标识字段
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码', **db_comment_kwarg('产品集编码'))
    statistics_date = models.DateField(blank=True, null=True, verbose_name='统计日期', **db_comment_kwarg('统计日期'))
    data_source = models.CharField(max_length=32, default='umami', verbose_name='数据来源', **db_comment_kwarg('数据来源'))
    
    # 埋点指标字段
    step_name = models.CharField(max_length=50, blank=True, null=True, verbose_name='步骤名称', **db_comment_kwarg('步骤名称'))
    step_order = models.IntegerField(blank=True, null=True, verbose_name='步骤顺序', **db_comment_kwarg('步骤顺序'))
    conversion_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='转化率', **db_comment_kwarg('转化率'))
    absolute_count = models.BigIntegerField(blank=True, null=True, verbose_name='绝对数量', **db_comment_kwarg('绝对数量'))
    
    # 备注信息
    remarks = models.TextField(blank=True, null=True, verbose_name='备注', **db_comment_kwarg('备注'))

    class Meta:
        db_table = 'insure_beacon'
        verbose_name = '销售埋点统计表'
        verbose_name_plural = verbose_name

        # Django 4.2+会自动识别这个属性，低版本会忽略
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

        # 唯一性约束
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'statistics_date', 'data_source', 'step_order'],
                name='unique_insure_beacon'
            ),
        ]
        
        # 索引优化
        indexes = [
            models.Index(fields=['product_set_code', 'statistics_date']),
            models.Index(fields=['step_order']),
            models.Index(fields=['product_set_code', 'step_order']),
        ]

    def __str__(self):
        return f"{self.product_set_code} - {self.step_name} - {self.conversion_rate}%"
