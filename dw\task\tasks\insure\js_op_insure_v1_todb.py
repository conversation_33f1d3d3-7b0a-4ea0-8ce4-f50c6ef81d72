import logging
import time

from transfrom.tasks.insure.js_op_insure_v1 import JsOpInsureV1
from transfrom.utils.utils import send_feishu_message

logger = logging.getLogger(__name__)



# 用去区分任务的名称
name ='js_op_insure_v1_todb'

def normal_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = JsOpInsureV1()
    for method in [
        'get_online_source_info',
        'get_channel_info',
        'get_offline_seller',
        'get_daily_online_count',
        'get_daily_offline_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'{name} normal_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'{name} normal_to_db: {method} cost:{_cost} ms')


def fast_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = JsOpInsureV1()
    for method in [
        'get_last_24_hour_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'{name} fast_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'{name} fast_to_db: {method} cost:{_cost} ms')


def main_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = JsOpInsureV1()
    for method in [
        'get_total_count',
        'get_today_count',
        'get_yesterday_count',
        'get_total_amount',
        'get_today_amount',
        'get_yesterday_amount',
        'get_person_group_count',
        'get_online_offline_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'{name} main_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'{name} main_to_db: {method} cost:{_cost} ms')


def slow_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = JsOpInsureV1()
    for method in [
        'get_age_gender',
        'get_age_range'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'{name} slow_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'{name} slow_to_db: {method} cost:{_cost} ms')


def history_to_db():
    """
    补充历史数据，并保证历史数据的准确性，图表数据由于调度，不一定可以获取准确的当日数据
    """
    source = JsOpInsureV1()
    for method in [
        'get_daily_count',
        'get_daily_cumsum',
        'get_daily_online_cumsum',
        'get_daily_offline_cumsum',
        'get_daily_amount',
        'get_daily_amount_cumsum'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'{name} history_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'{name} history_to_db: {method} cost:{_cost} ms')
