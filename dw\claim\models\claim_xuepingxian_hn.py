from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class ClaimXuePingXianHN(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码', **_db_comment_kwarg('产品集编码'))
    claim_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='报案号', **_db_comment_kwarg('报案号'))
    issue_remark = models.CharField(max_length=512, blank=True, null=True, verbose_name='出单备注', **_db_comment_kwarg('出单备注'))
    insured_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='被保险人', **_db_comment_kwarg('被保险人'))
    insured_credential_number = models.CharField(max_length=128, blank=True, null=True, verbose_name='身份证号', **_db_comment_kwarg('身份证号'))
    age = models.IntegerField(blank=True, null=True, verbose_name='年龄', **_db_comment_kwarg('年龄'))
    gender = models.CharField(max_length=32, blank=True, null=True, verbose_name='性别', **_db_comment_kwarg('性别'))
    mobile = models.CharField(max_length=128, blank=True, null=True, verbose_name='联系电话', **_db_comment_kwarg('联系电话'))
    accident_date = models.DateField(blank=True, null=True, verbose_name='出险日期', **_db_comment_kwarg('出险日期'))
    accident_type = models.CharField(max_length=128, blank=True, null=True, verbose_name='类型', **_db_comment_kwarg('类型'))
    pay_type = models.CharField(max_length=128, blank=True, null=True, verbose_name='赔付类型', **_db_comment_kwarg('赔付类型'))
    accident_reason = models.CharField(max_length=128, blank=True, null=True, verbose_name='事故原因', **_db_comment_kwarg('事故原因'))
    complete_date = models.DateField(blank=True, null=True, verbose_name='结案日期', **_db_comment_kwarg('结案日期'))
    medical_reimbursement = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='医保报销', **_db_comment_kwarg('医保报销'))
    amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='发票金额', **_db_comment_kwarg('发票金额'))
    non_medical_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='非医保', **_db_comment_kwarg('非医保'))
    claim_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='索赔金额', **_db_comment_kwarg('索赔金额'))
    actual_paid_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='结案支付金额', **_db_comment_kwarg('结案支付金额'))
    accident_location = models.CharField(max_length=128, blank=True, null=True, verbose_name='出险地点', **_db_comment_kwarg('出险地点'))
    school_type = models.CharField(max_length=128, blank=True, null=True, verbose_name='公办', **_db_comment_kwarg('学校类型'))
    insurance_company = models.CharField(max_length=128, blank=True, null=True, verbose_name='保司', **_db_comment_kwarg('保司'))

    class Meta:
        db_table = 'claim_xuepingxian_hn'
        verbose_name = '理赔-湖南学平险理赔明细'
        verbose_name_plural = verbose_name


# 动态为 Meta 添加 db_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    ClaimXuePingXianHN.Meta.db_comment = '理赔-湖南学平险理赔明细'
