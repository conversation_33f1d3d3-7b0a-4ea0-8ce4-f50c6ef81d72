import datetime
import logging
import warnings
from decimal import Decimal

import idna
import numpy as np
import pandas as pd
import pymysql
from django.db import transaction
from django.db.models import Q
from pandasql import sqldf

from dw import settings
from insure.models import InsureOnline, InsureAgent, InsureArea, InsureAgeSex
from public.models import PublicIndicatorData, PublicIndicatorMain, PublicStatistics, PublicAreaBaseInsure, PublicTarget
from task.utils.cache_manager import CacheManager
from transfrom.utils.utils import simplify_replace, query_sql, sum_or_combine, age_group, custom_update_or_create, \
    send_feishu_message, query_indicator_code

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class NXBInsureV4(CacheManager):
    """
    日照暖心保-四期数据处理
    指标名称数据需要手动添加，不自动生成
    关于所有的参保，只统计主险
    团单：经沟通，会由保司的人进行上传，不会与宁惠保一样，口头上报
    """

    def __init__(self):
        super().__init__()
        self.product_set_code = 'rizhao_nxbV4'
        self.DB = settings.DATABASES['jkx']
        self.DB_DW = settings.DATABASES['default']
        self.version = '日照暖心保-四期'
        self.INSURE_TARGET = 500000  # 销售目标，数据不等于线上+线下，所以单独留着 todo:未知
        self.INSURE_TARGET_AMOUNT = 3500000
        self.type = 'insure'  # 统计大类
        self.sale_start_time = '2024-11-04 12:00:00'  # 销售起始时间（含预售）
        self.sale_start_date = datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S').date()
        self.end_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
        self.today = datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S')
        self.yesterday = datetime.datetime.strptime(self.end_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
            days=1)
        self.publish_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.group_count_statistic = 1  # 1：根据数据表计算   2：根据手动数据统计
        self.complete_statistic = 'count'  # count：根据单量计算   amount：根据金额计算
        self.seller_list = ['人保财险', '太平财险', '国寿财险', '阳光财险', '中国人寿', '太平洋财险', '平安财险',
                            '大地财险', '渤海财险']

    def get_connection_dw(self):
        """
        获取dw数据库连接
        """
        self.conn = pymysql.connect(host=idna.encode(self.DB_DW["HOST"]).decode('utf-8'), port=int(self.DB_DW["PORT"]),
                                    user=self.DB_DW["USER"],
                                    password=self.DB_DW["PASSWORD"], database=self.DB_DW["NAME"])
        return self.conn

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection_dw() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            print(query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code))
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]
            print(max_publish_time_df)

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def get_connection(self):
        """
        获取数据库连接
        """
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def get_full_range_date(self, df):
        """
        获取完整的日期范围
        """
        # 获取最小日期
        min_date = df['date'].min()
        # 如果 min_date 是空的，使用 sale_start_date 作为默认值
        min_date = min_date if pd.notnull(min_date) else self.sale_start_date
        # 如果 min_date 是 pandas.Timestamp 类型，转换为 datetime.date
        if isinstance(min_date, pd.Timestamp):
            min_date = min_date.to_pydatetime().date()
        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = pd.date_range(start=min(min_date, self.sale_start_date),
                                        end=self.today.date())
        return full_date_range

    def get_daily_sale(self):
        """
        获取健康险日度销售数据，包括日期、医保类型、是否线上、是否个人、支付方式、区域编码、代理人、渠道来源、销售金额、销售件数、产品名称
        """
        try:
            df_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_DAILY_SALE').format(product_set_code=self.product_set_code,
                                                       end_datetime=self.publish_time),
                self.get_connection())
            return df_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_daily_sale error:{e}')

    def cache_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        df_daily_sale = self.get_daily_sale()
        # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
        try:
            self.update_cache('get_daily_sale', df_daily_sale)
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_daily_sale error:{e}')
        return df_daily_sale

    def get_main_daily_sale(self):
        """
        获取健康险日度主要销售数据，主要是金额、数量数据
        """
        try:
            df_main_daily_sale = pd.read_sql(
                query_sql('SQL_JKX_MAIN_DATA').format(product_set_code=self.product_set_code,
                                                      end_datetime=self.publish_time),
                self.get_connection())
            return df_main_daily_sale
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:get_main_daily_sale error:{e}')

    def cache_main_daily_sale(self):
        """
        从数据库表中获取日销售数据，如果不存在，则从接口获取数据并写入数据库,保证数据库有数据
        """
        df_main_daily_sale = self.get_main_daily_sale()
        # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
        try:
            self.update_cache('get_main_daily_sale', df_main_daily_sale)
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_main_daily_sale error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_main_daily_sale error:{e}')
        return df_main_daily_sale

    def get_total_count(self):
        """
        获取总销量，这边只计算数据表的数据，如果加团单，在datav脚本处理，保持数据的真实性
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        total_count = data['count'].sum()
        indic_name = self.version + '-销量-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_count))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_count error:{e}')
        return total_count


    def get_total_count_add_insure(self):
        """
        获取总销量(附加险)
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 0]
        total_count = data['count'].sum()
        indic_name = self.version + '-销量-附加险-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_count))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_count_add_insure error:{e}')
        return total_count



    def get_daily_cumsum_add_insure(self):
        """
        获取日度累计销量（附加险），时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 0]
        df_daily_cumsum = data.groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum['cumulative_count'] = df_daily_cumsum['count'].cumsum()
        df_daily_cumsum.drop(columns=['count'], inplace=True)

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_cumsum)
        # 填充缺失值
        df_daily_cumsum = (
            df_daily_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-附加险-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_cumsum_add_insure error:{e}')
        return df_daily_cumsum


    def get_total_amount_add_insure(self):
        """
        获取总销售额（附加险）
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 0]
        total_amount = data['amount'].sum()
        indic_name = self.version + '-销售额-附加险-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_amount))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_amount_add_insure error:{e}')
        return total_amount


    def get_daily_amount_cumsum_add_insure(self):
        """
        获取日度销售额累计值（附加险），时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 0]
        df_daily_amount_cumsum = data.groupby(['date']).agg({'amount': 'sum'}).reset_index()

        df_daily_amount_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_amount_cumsum.reset_index(drop=True, inplace=True)
        df_daily_amount_cumsum['cumulative_amount'] = df_daily_amount_cumsum['amount'].cumsum()
        df_daily_amount_cumsum.drop(columns=['amount'], inplace=True)
        full_date_range = self.get_full_range_date(df_daily_amount_cumsum)
        df_daily_amount_cumsum = (
            df_daily_amount_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_amount_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销售额-附加险-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_amount_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount_cumsum_add_insure error:{e}')
        return df_daily_amount_cumsum


    def get_target_ratio(self):
        """
        获取完成率
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        if self.group_count_statistic == 1:
            if self.complete_statistic == 'count':
                # 看是否是要统计上传团单数量，如果不要，直接统计销量，其实后期如果保司上传团单，这边也是会获取到的
                target_ratio = round(data['count'].sum() / self.INSURE_TARGET * 100, 4)
            else:
                print(12)
                target_ratio = round(data['amount'].sum() / self.INSURE_TARGET_AMOUNT * 100, 4)
        else:
            if self.complete_statistic == 'count':
                # 看是否需要统计上传团单数量，需要统计，加上手动数据，但是后期会重复，应该需要调整手动团单数据，或者取消限制
                df_offline_seller_group = self.get_seller_group_report_data()
                group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
                target_ratio = round((data['count'].sum() + group_seller['value'].sum()) / self.INSURE_TARGET * 100, 4)
            # todo:看需要再添加功能，根据上传的金额统计完成率
        indic_name = self.version + '-完成率-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(target_ratio))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_target_ratio error:{e}')
        return target_ratio

    def get_total_amount(self):
        """
        获取总销售额
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        total_amount = data['amount'].sum()
        indic_name = self.version + '-销售额-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(total_amount))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_total_amount error:{e}')
        return total_amount

    def get_person_group_count(self):
        """
        获取团单、个单数量
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_person_group = data.groupby(['is_personal']).agg({'count': 'sum'}).reset_index()
        # 忽略其他类型，如果有空为底层数据问题，这边不做处理
        df_person_group['is_personal'] = df_person_group['is_personal'].map({0: '团单', 1: '个单'})
        if self.group_count_statistic != 1:
            # 如果不是用系统表数据，这边统计需要统计手动上传的团单数据，剔除系统团单数据
            # 手动上传的团单数据
            df_offline_seller_group = self.get_seller_group_report_data()
            group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
            group_seller.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
            # group_seller = pd.DataFrame(self.SELLERS)
            group_seller_sum = group_seller['group_count'].sum()
            df_group = pd.DataFrame({'is_personal': '团单', 'count': [group_seller_sum]})
            # 只取个单数据，剔除了团单数据
            df_person_group = df_person_group[df_person_group['is_personal'] == '个单']
            df_person_group = pd.concat([df_group, df_person_group], ignore_index=True)
        df_person_group.reset_index(drop=True, inplace=True)
        # 查询数据库中所有数据
        db_person_group = PublicStatistics.objects.filter(type=self.type, statistical_type='personal',
                                                          product_set_code=self.product_set_code)
        df_db_person_group = pd.DataFrame(list(db_person_group.values()))
        if df_db_person_group.empty:
            df_db_person_group = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_person_group = df_db_person_group[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_person_group a left join df_person_group b on a.key = b.is_personal where b.is_personal is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()
        try:
            with transaction.atomic():
                for index, row in df_person_group.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='personal',
                                            product_set_code=self.product_set_code, key=row['is_personal'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_person_group_count error:{e}')
        return df_person_group

    def get_online_offline_count(self):
        """
        获取线上、线下销售统计数据
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        df_online_offline_count = data.groupby(['is_online']).agg({'count': 'sum'}).reset_index()
        df_online_offline_count['is_online'] = df_online_offline_count['is_online'].map({0: '线下', 1: '线上'})

        # 查询数据库中所有数据
        db_online_offline_count = PublicStatistics.objects.filter(type=self.type, statistical_type='isonline',
                                                                  product_set_code=self.product_set_code)
        df_db_online_offline_count = pd.DataFrame(list(db_online_offline_count.values()))
        if df_db_online_offline_count.empty:
            df_db_online_offline_count = pd.DataFrame(columns=['id', 'key'])
        else:
            df_db_online_offline_count = df_db_online_offline_count[['id', 'key']]
        delete_df = sqldf(
            "select a.id from df_db_online_offline_count a left join df_online_offline_count b on a.key = b.is_online where b.is_online is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        # 数据写入统计表
        try:
            with transaction.atomic():
                for index, row in df_online_offline_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='isonline',
                                            product_set_code=self.product_set_code, key=row['is_online'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count PublicStatistics error:{e}')
        # 保证下期取数方便，线上线下累计值写入指标表
        online_count = df_online_offline_count[df_online_offline_count['is_online'] == '线上'].get('count',
                                                                                                   0).values.tolist()
        online_count = online_count[0] if online_count else 0
        indic_name_online = self.version + '-销量-线上-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name_online).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(online_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count online error:{e}')
        offline_count = df_online_offline_count[df_online_offline_count['is_online'] == '线下'].get('count',
                                                                                                    0).values.tolist()
        offline_count = offline_count[0] if offline_count else 0
        indic_name_offline = self.version + '-销量-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name_offline).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(offline_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_offline_count offline error:{e}')
        return df_online_offline_count

    def get_pay_type_count(self):
        """
        获取支付方式统计数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(data, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '个账'})
        df_pay_type_count = data.groupby(['pay_type']).agg({'count': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='pay',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_pay_type_count b on a.key = b.pay_type where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_count error:{e}')
        return df_pay_type_count

    def get_pay_type_amount(self):
        """
        获取支付方式统计金额数据，历史逻辑只取了主险，保持一致
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # NORMAL:自费、MEDICARE:个账，剩余为其他
        simplify_replace(data, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '个账'})
        df_pay_type_amount = data.groupby(['pay_type']).agg({'amount': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='pay_amount',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_pay_type_amount b on a.key = b.pay_type where b.pay_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_pay_type_amount.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='pay_amount',
                                            product_set_code=self.product_set_code, key=row['pay_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_pay_type_amount error:{e}')
        return df_pay_type_amount

    def get_medicare_type_count(self):
        """
        获取医保类型统计数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # 筛选medicare_type为空的数据
        simplify_replace(data, 'medicare_type', {'EMPLOYEE': '职保', 'RESIDENT': '居保'}, '其他')

        df_medicare_type_count = data.groupby(['medicare_type']).agg({'count': 'sum'}).reset_index()

        # 查询数据库中所有数据
        db_df = PublicStatistics.objects.filter(type=self.type, statistical_type='medicare',
                                                product_set_code=self.product_set_code)
        db_df = pd.DataFrame(list(db_df.values()))
        if db_df.empty:
            db_df = pd.DataFrame(columns=['id', 'key'])
        else:
            db_df = db_df[['id', 'key']]
        delete_df = sqldf(
            "select a.id from db_df a left join df_medicare_type_count b on a.key = b.medicare_type where b.medicare_type is null")
        # 如果有多余数据，先删除多余数据
        if delete_df.shape[0] > 0:
            delete_ids = delete_df['id'].tolist()
            PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_medicare_type_count.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='medicare',
                                            product_set_code=self.product_set_code, key=row['medicare_type'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_type_count error:{e}')
        return df_medicare_type_count

    def get_medicare_person_count(self):
        """
        获取医保人数统计数据，低频处理，便于后续理赔指标计算
        """
        with self.get_connection() as conn:
            df_medicare = pd.read_sql(query_sql('SQL_MEDICAL_TYPE').format(product_set_code=self.product_set_code,
                                                                           end_datetime=self.publish_time), conn)
            df_total_person = pd.read_sql(
                query_sql('SQL_INSURE_PERSON_MAIN').format(product_set_code=self.product_set_code,
                                                           end_datetime=self.publish_time), conn)
        simplify_replace(df_medicare, 'medicare_type', {'EMPLOYEE': '职工医保', 'RESIDENT': '居民医保'}, '其他医保')
        df_medicare = df_medicare.groupby(['medicare_type']).agg({'person_count': 'sum', 'count': 'sum'}).reset_index()
        df_medicare['indic_name'] = df_medicare['medicare_type'].apply(
            lambda x: self.version + '-人数-' + x + '-当期值')
        df_total = pd.DataFrame({'indic_name': [self.version + '-参保人数-当期值'], 'medicare_type': ['合计'],
                                 'person_count': [df_total_person['person_num'].sum()],
                                 'count': [df_medicare['count'].sum()]})
        df_medicare = pd.concat([df_medicare, df_total], ignore_index=True)
        df_medicare.reset_index(drop=True, inplace=True)
        # 获取指标code
        query_indicator_code(df_medicare)
        # 删除code为空的行
        df_medicare.dropna(subset=['code'], inplace=True)
        df_medicare.fillna(0, inplace=True)

        try:
            with transaction.atomic():
                for index, row in df_medicare.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=self.end_time,
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['person_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_medicare_person_count error:{e}')

        return df_medicare

    def get_last_24_hour_count(self):
        """
        获取过去24小时统计数据
        """
        # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
        start_datetime = max(datetime.datetime.strptime(self.publish_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
            hours=24), datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S'))
        with self.get_connection() as conn:
            df_last_24_hour_count = pd.read_sql(
                query_sql('SQL_LAST_24_HOUR_COUNT').format(product_set_code=self.product_set_code,
                                                           start_datetime=start_datetime,
                                                           end_datetime=self.publish_time), conn)
        df_last_24_hour_count['datetime'] = df_last_24_hour_count['datetime'].apply(
            lambda x: datetime.datetime.strptime(x, '%Y-%m-%d %H:%M:%S'))
        # 获取过去24小时的完整日期范围，日期格式为'%Y-%m-%d %H:00:00'
        full_date_range = pd.date_range(start=start_datetime.replace(minute=0, second=0),
                                        end=self.publish_time[:13] + ':00:00', freq='H')
        # 合并数据，并填充缺失值
        df_last_24_hour_count = (
            df_last_24_hour_count.set_index('datetime')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'datetime'})
        )
        indic_name = self.version + '-销量-当期值(小时)'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_last_24_hour_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['datetime'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_last_24_hour_count error:{e}')
        return df_last_24_hour_count

    def get_daily_count(self):
        """
        获取日度销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_count = data.groupby(['date']).agg({'count': 'sum'}).reset_index()

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_count)
        # 填充缺失值
        df_daily_count = (
            df_daily_count.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        indic_name = self.version + '-销量-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_count error:{e}')
        return df_daily_count

    def get_daily_cumsum(self):
        """
        获取日度累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_cumsum = data.groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_cumsum.reset_index(drop=True, inplace=True)
        df_daily_cumsum['cumulative_count'] = df_daily_cumsum['count'].cumsum()
        df_daily_cumsum.drop(columns=['count'], inplace=True)

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_cumsum)
        # 填充缺失值
        df_daily_cumsum = (
            df_daily_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_cumsum error:{e}')
        return df_daily_cumsum

    def get_daily_online_count(self):
        """
        获取日度线上销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_online_count = data.query('is_online == 1').groupby(['date']).agg({'count': 'sum'}).reset_index()

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_online_count)
        # 填充缺失值
        df_daily_online_count = (
            df_daily_online_count.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )

        indic_name = self.version + '-销量-线上-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_online_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_online_count error:{e}')
        return df_daily_online_count

    def get_daily_online_cumsum(self):
        """
        获取日度线上累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_online_cumsum = data.query('is_online == 1').groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_online_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_online_cumsum.reset_index(drop=True, inplace=True)
        df_daily_online_cumsum['cumulative_count'] = df_daily_online_cumsum['count'].cumsum()
        df_daily_online_cumsum.drop(columns=['count'], inplace=True)

        full_date_range = self.get_full_range_date(df_daily_online_cumsum)
        df_daily_online_cumsum = (
            df_daily_online_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_online_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-线上-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_online_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_online_cumsum error:{e}')
        return df_daily_online_cumsum

    def get_daily_offline_count(self):
        """
        获取日度线下销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_offline_count = data.query('is_online == 0').groupby(['date']).agg({'count': 'sum'}).reset_index()

        # 获取实际数据与销售起始日中最小的作为开始日期
        full_date_range = self.get_full_range_date(df_daily_offline_count)
        # 填充缺失值
        df_daily_offline_count = (
            df_daily_offline_count.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )

        indic_name = self.version + '-销量-线下-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_count.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_count error:{e}')
        return df_daily_offline_count

    def get_daily_offline_cumsum(self):
        """
        获取日度线下累计销量，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_offline_cumsum = data.query('is_online == 0').groupby(['date']).agg({'count': 'sum'}).reset_index()

        df_daily_offline_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_offline_cumsum.reset_index(drop=True, inplace=True)
        df_daily_offline_cumsum['cumulative_count'] = df_daily_offline_cumsum['count'].cumsum()
        df_daily_offline_cumsum.drop(columns=['count'], inplace=True)

        full_date_range = self.get_full_range_date(df_daily_offline_cumsum)
        df_daily_offline_cumsum = (
            df_daily_offline_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_offline_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销量-线下-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_offline_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_count']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_offline_cumsum error:{e}')
        return df_daily_offline_cumsum

    def get_today_count(self):
        """
        获取当日销量
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        today_count = data[data['date'] == self.today.date()][
            'count'].sum()
        indic_name = self.version + '-销量-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(today_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_today_count error:{e}')
        return today_count

    def get_daily_amount(self):
        """
        获取日度销售额，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_amount = data.groupby(['date']).agg({'amount': 'sum'}).reset_index()

        # 获取完整的日期范围
        full_date_range = self.get_full_range_date(df_daily_amount)
        df_daily_amount = (
            df_daily_amount.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        indic_name = self.version + '-销售额-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_amount.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount error:{e}')
        return df_daily_amount

    def get_daily_amount_cumsum(self):
        """
        获取日度销售额累计值，时间序列，用于更新历史数据
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_daily_amount_cumsum = data.groupby(['date']).agg({'amount': 'sum'}).reset_index()

        df_daily_amount_cumsum.sort_values(by='date', ascending=True, inplace=True)
        df_daily_amount_cumsum.reset_index(drop=True, inplace=True)
        df_daily_amount_cumsum['cumulative_amount'] = df_daily_amount_cumsum['amount'].cumsum()
        df_daily_amount_cumsum.drop(columns=['amount'], inplace=True)
        full_date_range = self.get_full_range_date(df_daily_amount_cumsum)
        df_daily_amount_cumsum = (
            df_daily_amount_cumsum.set_index('date')
            .reindex(full_date_range)
            .fillna(method='ffill')
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_daily_amount_cumsum.fillna(0, inplace=True)
        indic_name = self.version + '-销售额-累计值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                for index, row in df_daily_amount_cumsum.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=code, end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['cumulative_amount']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_daily_amount_cumsum error:{e}')
        return df_daily_amount_cumsum

    def get_today_amount(self):
        """
        获取当日销售额
        """
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        today_amount = data[data['date'] == self.today.date()][
            'amount'].sum()
        indic_name = self.version + '-销售额-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(today_amount))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_today_amount error:{e}')
        return today_amount

    def get_yesterday_count(self):
        """
        获取昨日销量
        """
        indic_name = self.version + '-销量-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        data['date'] = pd.to_datetime(data['date'])
        yesterday_count = data[data['date'] == self.yesterday]['count'].sum()
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.yesterday,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(yesterday_count))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_yesterday_count error:{e}')
        return yesterday_count

    def get_yesterday_amount(self):
        """
        获取昨日销售额
        """
        indic_name = self.version + '-销售额-当期值'
        code = PublicIndicatorMain.objects.get(name=indic_name).code
        data = self.get_from_cache('get_main_daily_sale')
        data = data[data['main'] == 1]
        yesterday_amount = data[data['date'] == self.yesterday.date()]['amount'].sum()
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.yesterday,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(yesterday_amount))})
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_yesterday_amount error:{e}')
        return yesterday_amount

    def get_online_source_info(self):
        """
        获取线上销售渠道的数据，包括排名、渠道、占比、总单数、今日参保、昨日参保、目标、完成率
        线上个单数量
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_online_source_info = data.query("is_online==1 and is_personal==1").groupby(['source', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_online_source_info['date'] = df_online_source_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        df_online_source_info['source'] = df_online_source_info['source'].apply(
            lambda x: '支付宝' if '支付宝' in x
            else '公众号')

        # 写入指标表
        full_date_range = pd.date_range(start=self.sale_start_date, end=self.today.date(), freq='D')
        # 拼接完整的日期、地区、产品组合
        df_combinations = pd.MultiIndex.from_product(
            [full_date_range, ['支付宝', '公众号']],
            names=['date', 'source'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_group = df_online_source_info.groupby(['source', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_group['date'] = pd.to_datetime(df_group['date'])
        df_indicator = sqldf(
            "select a.*,ifnull(b.count,0) as `value` from df_full_combinations a left join df_group b on a.date=b.date and a.source=b.source")
        df_indicator['indic_name'] = self.version + '-销量-线上-' + df_indicator['source'] + '-当期值'
        query_indicator_code(df_indicator)

        # 删除code为空的行
        df_indicator.dropna(subset=['code'], inplace=True)
        df_indicator.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_indicator.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['value']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_online_source_info indicator error:{e}')
        return df_indicator

    def get_offline_seller(self):
        """
        获取线下个单销售情况
        """
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        df_group = data.query("is_online==0 and is_personal==1").groupby(['seller', 'date']).agg(
            {'count': 'sum'}).reset_index()
        df_total = data.query("is_online==0 and is_personal==1").groupby(['date']).agg({'count': 'sum'}).reset_index()
        full_date_range = pd.date_range(start=self.sale_start_date, end=self.today.date(), freq='D')
        # 拼接完整的日期、地区、产品组合
        df_combinations = pd.MultiIndex.from_product([full_date_range, self.seller_list],
                                                     names=['date', 'seller'])
        df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
        df_group['date'] = pd.to_datetime(df_group['date'])
        df_indicator = sqldf(
            "select a.*,ifnull(b.count,0) as `value` from df_full_combinations a left join df_group b on a.date=b.date and a.seller=b.seller")
        df_indicator['indic_name'] = self.version + '-销量-线下-个单-' + df_indicator['seller'] + '-当期值'

        full_date_range = self.get_full_range_date(df_total)
        df_total = (
            df_total.set_index('date')
            .reindex(full_date_range)
            .fillna(0)
            .reset_index()
            .rename(columns={'index': 'date'})
        )
        df_total['indic_name'] = self.version + '-销量-线下-个单-当期值'
        df_total.rename(columns={'count': 'value'}, inplace=True)
        df_indicator = pd.concat([df_indicator, df_total], axis=0).reset_index(drop=True)
        query_indicator_code(df_indicator)
        # 删除code为空的行
        df_indicator.dropna(subset=['code'], inplace=True)
        df_indicator.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_indicator.iterrows():
                    custom_update_or_create(PublicIndicatorData,
                                            code=row['code'], end_time=row['date'],
                                            defaults={'publish_time': self.publish_time,
                                                      'value': Decimal(str(row['value']))},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_offline_seller indicator error:{e}')
        return df_indicator

    def get_channel_info(self):
        """
        获取线下保司的数据、线上数据，包括排名、保司、占比、代理人数、人均出单、个单、团单、今日参保、昨日参保、目标、完成率
        """
        # 获取保司代理人数量
        with self.get_connection() as conn:
            df_seller_person = pd.read_sql(
                query_sql('SQL_SELLER_PERSON_FIRST').format(product_set_code=self.product_set_code), conn)
            # 剔除线上的数据
            df_seller_person = df_seller_person[~df_seller_person['seller'].str.contains('线上渠道')]
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # 线上单独处理
        df_online_info = data.query("is_online==1").groupby(['date', 'is_personal']).agg(
            {'count': 'sum'}).reset_index()
        df_online_info['seller'] = '线上'

        # 线下处理
        df_offline_seller_info = data.query("is_online==0").groupby(['seller', 'date', 'is_personal']).agg(
            {'count': 'sum'}).reset_index()
        # 合并数据，线上线下
        df_offline_seller_info = pd.concat([df_offline_seller_info, df_online_info], axis=0).reset_index(drop=True)

        df_offline_seller = df_offline_seller_info.groupby(['seller']).agg({'count': 'sum'}).reset_index()
        df_offline_seller = pd.merge(df_offline_seller, df_seller_person, how='outer', on='seller')

        # 个单数量
        df_offline_seller_personal = df_offline_seller_info.query("is_personal==1").groupby(['seller']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'personal_count'})
        # 团单数量
        if self.group_count_statistic == 1:
            # 数据库取值逻辑
            df_offline_seller_group = df_offline_seller_info.query("is_personal==0").groupby(['seller']).agg(
                {'count': 'sum'}).reset_index().rename(columns={'count': 'group_count'})
        else:
            # 手动取值逻辑
            df_offline_seller_group = self.get_seller_group_report_data()
            df_offline_seller_group = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
            df_offline_seller_group.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
            df_offline_seller_group = df_offline_seller_group[['seller', 'group_count']]
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_personal, how='left', on='seller')
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_group, how='left', on='seller')
        # 手动计算
        df_offline_seller['personal_count'] = df_offline_seller['personal_count'].fillna(0).astype(int)
        df_offline_seller['group_count'] = df_offline_seller['group_count'].fillna(0).astype(int)
        df_offline_seller['count'] = df_offline_seller['personal_count'] + df_offline_seller['group_count']

        # 人均值只计算个单，20240921
        df_offline_seller['average_count'] = df_offline_seller.apply(
            lambda x: round(x['personal_count'] / x['employee_num'], 1) if x['employee_num'] != 0 else 0, axis=1)
        # print(df_offline_seller)

        df_offline_seller.fillna(0, inplace=True)
        df_offline_seller['insure_ratio'] = round(df_offline_seller['count'] / df_offline_seller['count'].sum(), 3)
        # 获取目标数据
        df_target = PublicTarget.objects.filter(
            Q(product_set_code=self.product_set_code) & (Q(type='agent') | Q(type='online'))
        )
        df_target = pd.DataFrame(list(df_target.values()))
        df_target = df_target[['short_name', 'target', 'type']].rename(
            columns={'short_name': 'seller'})
        df_target_offline = df_target[df_target['type'] == 'agent']
        df_target_online = df_target[df_target['type'] == 'online']
        # 如果线上的目标出现线上，则只取线上，否则所有数据求和
        if '线上' in df_target_online['seller'].tolist():
            df_target_online = df_target_online[df_target_online['seller'] == '线上']
        else:
            df_target_online = pd.DataFrame(
                {'seller': '线上', 'target': [df_target_online['target'].sum()], 'type': 'online'})
        df_target = pd.concat([df_target_offline, df_target_online], axis=0).reset_index(drop=True)[
            ['seller', 'target']]
        # 合并目标数据
        df_offline_seller = pd.merge(df_offline_seller, df_target, how='outer', on='seller')
        df_offline_seller['target_ratio'] = df_offline_seller.apply(
            lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
            axis=1
        )

        # 排序，将seller为线上放在一个，其他按照target_ratio排序
        df_offline_zgrb = df_offline_seller[df_offline_seller['seller'] == '线上']
        df_offline_other = df_offline_seller[df_offline_seller['seller'] != '线上']
        df_offline_other.sort_values(by=['target_ratio'], ascending=False, inplace=True)
        # df_offline_zgrb = df_offline_seller[df_offline_seller['seller'] == '人保财险']
        # df_offline_other = df_offline_seller[df_offline_seller['seller'] != '人保财险']
        # df_offline_other.sort_values(by=['target_ratio'], ascending=False, inplace=True)
        # 合并成完整数据
        df_offline_seller = pd.concat([df_offline_zgrb, df_offline_other], axis=0).reset_index(drop=True)

        # df_offline_seller_info['date'] = df_offline_seller_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        df_offline_seller_today = df_offline_seller_info[
            df_offline_seller_info['date'] == self.today.date()]
        df_offline_seller_yesterday = df_offline_seller_info[
            df_offline_seller_info['date'] == self.yesterday.date()]
        df_offline_seller_today = df_offline_seller_today.groupby(['seller']).agg(
            {'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})
        df_offline_seller_yesterday = df_offline_seller_yesterday.groupby(['seller']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_today, how='left', on='seller')
        df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_yesterday, how='left', on='seller')
        df_offline_seller.fillna(0, inplace=True)

        df_offline_seller.reset_index(drop=True, inplace=True)
        df_offline_seller['position'] = df_offline_seller.index + 1
        number_sum = df_offline_seller.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        # 人均值计算
        number_sum['average_count'] = number_sum.apply(
            lambda x: round(x['personal_count'] / x['employee_num'], 1) if x['employee_num'] != 0 else 0, axis=1)
        number_sum['insure_ratio'] = 1
        number_sum['position'] = len(df_offline_seller) + 1
        number_sum['insure_ratio'] = number_sum['insure_ratio'].apply(lambda x: 1 if x > 1 else x)
        df_offline_seller = pd.concat([df_offline_seller, number_sum], axis=0).reset_index(drop=True)
        df_offline_seller['product_set_code'] = self.product_set_code
        df_offline_seller['publish_time'] = self.publish_time
        # 如果目标还未确认，数据库赋值0，这边判定后直接赋值0
        df_offline_seller['target_ratio'] = df_offline_seller.apply(
            lambda row: round(row['count'] / row['target'], 3) if row['target'] != 0 else 0,
            axis=1
        )

        # 查询数据库中所有数据
        db_insure_agent = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        # 如果db_insure_agent有数据则进行下面处理
        if db_insure_agent.exists():
            df_db_insure_agent = pd.DataFrame(list(db_insure_agent.values()))[['id', 'name']]
            delete_df = sqldf(
                "select a.id from df_db_insure_agent a left join df_offline_seller b on a.name = b.seller where b.seller is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                InsureAgent.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_offline_seller.iterrows():
                    custom_update_or_create(InsureAgent,
                                            product_set_code=row['product_set_code'], name=row['seller'],
                                            defaults={'publish_time': self.publish_time,
                                                      'employee_count': row['employee_num'],
                                                      'average_count': row['average_count'],
                                                      'total_count': row['count'],
                                                      'personal_count': row['personal_count'],
                                                      'group_count': row['group_count'],
                                                      'insure_ratio': row['insure_ratio'], 'position': row['position'],
                                                      'target': row['target'],
                                                      'target_ratio': row['target_ratio'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_channel_info error:{e}')
        return df_offline_seller

    def get_area_info(self):
        """
        地区参保数据，包括排名、参保地、占比、总单数、今日参保、昨日参保、参保率
        """
        df_area = PublicAreaBaseInsure.objects.filter(product_set_code=self.product_set_code)
        df_area = pd.DataFrame(list(df_area.values()))[['count', 'code', 'name']].rename(
            columns={'code': 'area_code', 'count': 'base_insure'})
        data = self.get_from_cache('get_daily_sale')
        data = data[data['main'] == 1]
        # 不统计团单，团单无法区分地区
        data = data.query("is_personal==1")
        # code部分超过6位，取前6位
        data['area_code'] = data['area_code'].apply(lambda x: x[:6] if x is not None else x)
        # area_code为空的置为999999，area_name为空的置为'其他'
        data.fillna({'area_code': '000000', 'area_name': '其他'}, inplace=True)
        # # 地区合并，历史旧编码导致
        # data.loc[data['area_code'] == '320108', 'area_name'] = '江北新区'
        # data.loc[data['area_name'] == '江北新区', 'area_code'] = '320140'

        df_area_info = data.groupby(['area_code', 'area_name', 'date']).agg({'count': 'sum'}).reset_index()
        df_area_info = pd.merge(df_area_info, df_area, how='outer', on='area_code')
        # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
        df_area_info['base_insure'] = df_area_info['base_insure'].fillna(0)
        mask = (df_area_info['base_insure'] == 0) & (df_area_info['area_name'] != '其他')
        df_area_info.loc[mask, 'area_name'] = '其他'
        # name如果为空，用area_name代替
        df_area_info['name'] = df_area_info['name'].fillna(df_area_info['area_name'])
        # 如果name为其他，area_code置为999999
        df_area_info.loc[df_area_info['name'] == '其他', 'area_code'] = '000000'

        df_area_total = df_area_info.groupby(['area_code', 'area_name']).agg({'count': 'sum'}).reset_index()
        df_area_total = pd.merge(df_area_total, df_area, how='outer', on='area_code')
        # name如果为空，用area_name代替
        df_area_total['name'] = df_area_total['name'].fillna(df_area_total['area_name'])
        # 如果name为其他，area_code置为999999
        df_area_total.loc[df_area_total['name'] == '其他', 'area_code'] = '000000'

        df_area_total = df_area_total.groupby(['area_code', 'name']).agg(
            {'count': 'sum', 'base_insure': 'first'}).reset_index()
        df_area_total.fillna(0, inplace=True)

        df_area_total['count'] = df_area_total['count'].astype(int)
        # 计算占比
        if df_area_total['count'].sum() == 0:
            df_area_total['ratio'] = 0
        else:
            df_area_total['ratio'] = round(df_area_total['count'] / df_area_total['count'].sum(), 3)

        df_area_total['insure_ratio'] = df_area_total.apply(
            lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
            axis=1
        )
        df_area_total.sort_values(by=['insure_ratio', 'count'], ascending=False, inplace=True)
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total['position'] = df_area_total.index + 1
        # name如果为其他，放到最后，调整索引
        df_area_total.loc[df_area_total['name'] == '其他', 'position'] = df_area_total.shape[0] + 1
        df_area_total.sort_values(by='position', inplace=True)
        df_area_total.reset_index(drop=True, inplace=True)
        df_area_total['position'] = df_area_total.index + 1
        # df_area_info['date'] = df_area_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
        df_area_today = df_area_info[
            df_area_info['date'] == self.today.date()].groupby(['area_code']).agg(
            {'count': 'sum'}).reset_index().rename(
            columns={'count': 'today_count'})

        df_area_yesterday = df_area_info[
            df_area_info['date'] == self.yesterday.date()].groupby(['area_code']).agg(
            {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
        if df_area_today.empty:
            df_area_total['today_count'] = 0
        else:
            df_area_total = pd.merge(df_area_total, df_area_today, how='left', on='area_code')
            df_area_total['today_count'].fillna(0, inplace=True)
        if df_area_yesterday.empty:
            df_area_total['yesterday_count'] = 0
        else:
            df_area_total = pd.merge(df_area_total, df_area_yesterday, how='left', on='area_code')
            df_area_total['yesterday_count'].fillna(0, inplace=True)
        number_sum = df_area_total.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
        number_sum['ratio'] = number_sum['ratio'].apply(lambda x: 1 if x > 1 else x)
        number_sum['position'] = len(df_area_total) + 1
        # 如果来不及订目标，则target_ratio为0，比率也都是0
        number_sum['insure_ratio'] = number_sum.apply(
            lambda row: round(row['count'] / row['base_insure'], 3) if row['base_insure'] != 0 else 0,
            axis=1
        )
        df_area_total = pd.concat([df_area_total, number_sum], axis=0).reset_index(drop=True)
        df_area_total['product_set_code'] = self.product_set_code
        df_area_total['publish_time'] = self.publish_time
        # 查询数据库中所有数据
        db_insure_area = InsureArea.objects.filter(product_set_code=self.product_set_code)
        if db_insure_area.exists():
            df_db_insure_area = pd.DataFrame(list(db_insure_area.values()))[['id', 'name']]
            delete_df = sqldf(
                "select a.id from df_db_insure_area a left join df_area_total b on a.name = b.name where b.name is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                # print(delete_ids)
                InsureArea.objects.filter(id__in=delete_ids).delete()
        df_area_total.fillna(0, inplace=True)
        try:
            with transaction.atomic():
                for index, row in df_area_total.iterrows():
                    custom_update_or_create(InsureArea,
                                            product_set_code=row['product_set_code'], name=row['name'],
                                            defaults={'publish_time': self.publish_time, 'total_count': row['count'],
                                                      'ratio': row['ratio'], 'insure_ratio': row['insure_ratio'],
                                                      'position': row['position'],
                                                      'today_count': row['today_count'],
                                                      'yesterday_count': row['yesterday_count']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_area_info error:{e}')
        return df_area_total

    def get_age_gender_count(self):
        """
        获取年龄性别数据
        """
        with self.get_connection() as conn:
            df_age_gender_count = pd.read_sql(
                query_sql('SQL_AGE_GENDER').format(product_set_code=self.product_set_code,
                                                   end_datetime=self.publish_time),
                conn)
        return df_age_gender_count

    def cache_age_gender_count(self):
        """
        缓存年龄性别数据，主动推送，保证数据的实时性
        """
        try:
            df_age_gender_count = self.get_age_gender_count()
            self.update_cache('get_age_gender_count', df_age_gender_count)
            return df_age_gender_count
        except Exception as e:
            logger.error(f'{type(self).__name__}:cache_age_gender_count error:{e}')
            send_feishu_message(f'{type(self).__name__}:cache_age_gender_count error:{e}')

    def get_age_gender(self):
        """
        年龄性别数据，包括年龄段、性别、参保数
        """
        df_age_gender = self.get_from_cache('get_age_gender_count')
        age_bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, float('inf')]
        age_labels = ['0-10', '10-20', '20-30', '30-40', '40-50', '50-60', '60-70', '70-80', '80-90', '90及以上']
        age_group(df_age_gender, 'age', age_bins, age_labels)
        df_age_gender['gender'].replace({'FEMALE': '女', 'MALE': '男'}, inplace=True)
        df_age_gender_group = df_age_gender.groupby(['age_group', 'gender']).agg({'count': 'sum'}).reset_index()

        # 如果组合在df_age_gender_group中不存在，则增加一条，值为0，保证数据完整
        for x in age_labels:
            for s in ['男', '女']:
                if len(df_age_gender_group[
                           (df_age_gender_group['age_group'] == x) & (df_age_gender_group['gender'] == s)]) == 0:
                    df_age_gender_group = df_age_gender_group._append({'age_group': x, 'gender': s, 'count': 0},
                                                                      ignore_index=True)
        df_age_gender_group.sort_values(by=['age_group', 'gender'], inplace=True)
        df_age_gender_group.reset_index(drop=True, inplace=True)
        df_age_gender_group['product_set_code'] = self.product_set_code
        df_age_gender_group['publish_time'] = self.publish_time
        try:
            with transaction.atomic():
                for index, row in df_age_gender_group.iterrows():
                    custom_update_or_create(InsureAgeSex,
                                            product_set_code=row['product_set_code'], sex=row['gender'],
                                            age_distribution=row['age_group'],
                                            defaults={'value': row['count'], 'publish_time': row['publish_time']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_gender error:{e}')
        return df_age_gender_group

    def get_age_range(self):
        """
        获取年龄范围统计数据、包括平均年龄、年龄中位数
        """
        df_age_gender = self.get_from_cache('get_age_gender_count')
        age_bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, float('inf')]
        age_labels = ['0-10', '10-20', '20-30', '30-40', '40-50', '50-60', '60-70', '70-80', '80-90', '90及以上']
        age_group(df_age_gender, 'age', age_bins, age_labels)
        df_age_group = df_age_gender.groupby(['age_group']).agg({'count': 'sum'}).reset_index()
        # 如果组合在df_age_group中不存在，则增加一条，值为0，保证数据完整
        for x in age_labels:
            if len(df_age_group[(df_age_group['age_group'] == x)]) == 0:
                df_age_group = df_age_group._append({'age_group': x, 'count': 0},
                                                    ignore_index=True)
        df_age_group['count'] = df_age_group['count'].astype(int)
        if df_age_group['count'].sum() == 0:
            df_age_group['ratio'] = 0
        else:
            df_age_group['ratio'] = round(df_age_group['count'] / df_age_group['count'].sum(), 4)

        # 查询数据库中所有数据
        db_age_group = PublicStatistics.objects.filter(type=self.type, statistical_type='age_ratio',
                                                       product_set_code=self.product_set_code)
        if db_age_group.exists():
            df_db_age_group = pd.DataFrame(list(db_age_group.values()))[['id', 'key']]
            delete_df = sqldf(
                "select a.id from df_db_age_group a left join df_age_group b on a.key = b.age_group where b.age_group is null")
            # 如果有多余数据，先删除多余数据
            if delete_df.shape[0] > 0:
                delete_ids = delete_df['id'].tolist()
                PublicStatistics.objects.filter(id__in=delete_ids).delete()

        try:
            with transaction.atomic():
                for index, row in df_age_group.iterrows():
                    custom_update_or_create(PublicStatistics, type=self.type, statistical_type='age_ratio',
                                            product_set_code=self.product_set_code, key=row['age_group'],
                                            defaults={'publish_time': self.publish_time, 'value': row['ratio']},
                                            exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range error:{e}')
        # 计算平均年龄
        df_age_gender['count'] = df_age_gender['count'].astype(int)
        total_people = df_age_gender['count'].sum()
        weighted_age_sum = (df_age_gender['age'] * df_age_gender['count']).sum()
        if total_people == 0:
            average_age = 0
        else:
            average_age = round(weighted_age_sum / total_people, 0)

        indic_name_average = self.version + '-年龄-平均值'
        code = PublicIndicatorMain.objects.get(name=indic_name_average).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time,
                                                  'value': Decimal(str(average_age))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range_average error:{e}')

        # 计算中位数
        all_ages = []
        for index, row in df_age_gender.iterrows():
            all_ages.extend([row['age']] * row['count'])
        median_age = pd.Series(all_ages).median()
        median_age = 0 if pd.isnull(median_age) else median_age
        indic_name_median = self.version + '-年龄-中位值'
        code = PublicIndicatorMain.objects.get(name=indic_name_median).code
        try:
            with transaction.atomic():
                custom_update_or_create(PublicIndicatorData,
                                        code=code, end_time=self.end_time,
                                        defaults={'publish_time': self.publish_time, 'value': Decimal(str(median_age))},
                                        exclude_fields=['publish_time'])
        except Exception as e:
            logger.error(f'{type(self).__name__}:get_age_range_median error:{e}')
        return df_age_group, average_age, median_age


if __name__ == '__main__':
    nxb_sale_v4 = NXBInsureV4()
    # cs = nxb_sale_v4.get_daily_sale()
    # print(cs)
    # nxb_sale_v4.cache_daily_sale()
    # nxb_sale_v4.cache_main_daily_sale()
    # df_daily_amount_cumsum = nxb_sale_v4.get_daily_amount_cumsum()
    # print(df_daily_amount_cumsum)
    # total_count = nxb_sale_v4.get_total_count()
    # print(total_count)
    # total_amount = nxb_sale_v4.get_total_amount()
    # print(total_amount)
    # person_group = nxb_sale_v4.get_person_group_count()
    # print(person_group)
    # online_offline_count = nxb_sale_v4.get_online_offline_count()
    # print(online_offline_count)
    # pay_type_count = nxb_sale_v4.get_pay_type_count()
    # print(pay_type_count)
    # pay_type_amount = nxb_sale_v4.get_pay_type_amount()
    # print(pay_type_amount)
    # df_last_24_hour_count = nxb_sale_v4.get_last_24_hour_count()
    # print(df_last_24_hour_count)
    # df_daily_count = nxb_sale_v4.get_daily_count()
    # print(df_daily_count)
    # today_count = nxb_sale_v4.get_today_count()
    # print(today_count)
    # df_daily_amount = nxb_sale_v4.get_daily_amount()
    # print(df_daily_amount)
    # today_amount = nxb_sale_v4.get_today_amount()
    # print(today_amount)
    # yesterday_count = nxb_sale_v4.get_yesterday_count()
    # print(yesterday_count)
    # yesterday_amount = nxb_sale_v4.get_yesterday_amount()
    # print(yesterday_amount)
    # df_online_source_info = nxb_sale_v4.get_online_source_info()
    # print(df_online_source_info)
    # df_offline_seller = nxb_sale_v4.get_channel_info()
    # print(df_offline_seller)
    # df_offline = nxb_sale_v4.get_offline_seller()
    # print(df_offline)
    # df_area_total = nxb_sale_v4.get_area_info()
    # print(df_area_total)
    # df_age_gender_group = nxb_sale_v4.get_age_gender()
    # print(df_age_gender_group)
    # nxb_sale_v4.cache_age_gender_count()
    # df_age_group, average_age, median_age = nxb_sale_v4.get_age_range()
    # print(df_age_group)
    # print(average_age)
    # print(median_age)
    # df_daily_online_count = nxb_sale_v4.get_daily_online_count()
    # print(df_daily_online_count)
    # df_daily_offline_count = nxb_sale_v4.get_daily_offline_count()
    # print(df_daily_offline_count)
    # df_daily_cumsum = nxb_sale_v4.get_daily_cumsum()
    # print(df_daily_cumsum)
    # df_daily_online_cumsum = nxb_sale_v4.get_daily_online_cumsum()
    # print(df_daily_online_cumsum)
    # df_daily_offline_cumsum = nxb_sale_v4.get_daily_offline_cumsum()
    # print(df_daily_offline_cumsum)
    # df_target_ratio =nxb_sale_v4.get_target_ratio()
    # print(df_target_ratio)
    # df_medicare_type_count = nxb_sale_v4.get_medicare_type_count()
    # print(df_medicare_type_count)
    # df_medicare_person_count = nxb_sale_v4.get_medicare_person_count()
    # print(df_medicare_person_count)
    print(nxb_sale_v4.get_daily_cumsum_add_insure())
    print(nxb_sale_v4.get_total_count_add_insure())
    print(nxb_sale_v4.get_daily_amount_cumsum_add_insure())
    print(nxb_sale_v4.get_total_amount_add_insure())
