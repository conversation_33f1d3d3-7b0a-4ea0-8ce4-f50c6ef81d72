import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
from decimal import Decimal
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message
from utils.st import query_sql, text_write, sub_text_write, empty_line

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
        SELECT
        name product_set_name,
        code product_set_code,
        prev_code prev_product_set_code
    FROM
        product_set
     where left(name, 2) in ('南京')
     and  code not in ('ninghuibaoV1')
    order by code desc 
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_product_info():
    """
    获取产品集代码、名称、产品名称、产品代码
    :return:
    """
    SQL_PRODUCT_INFO = '''
    SELECT
        ps.name product_set_name,
        ps.code product_set_code,
        p.name product_name,
        p.code product_code
    FROM
        product_set ps join product p 
        on ps.id = p.product_set_id
    where p.delete_time is null
    and p.main=1
    order by ps.name desc 
        '''

    df_product_info = CONNECTOR_JKX.query(SQL_PRODUCT_INFO, show_spinner='查询中...', ttl=600)
    return df_product_info


@st.cache_resource(ttl=600)
def get_department_info(conn=CONNECTOR_JKX, sql=query_sql('SQL_DEPARTMENT_INFO')):
    """
    获取产品销售机构及部门信息
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_department_info = conn.query(sql, show_spinner='查询中...', ttl=0)
    return df_department_info


def get_invitee_info(conn=CONNECTOR_JKX, sql=query_sql('SQL_INVITEE_INFO')):
    """
    获取非一级代理人信息（用于层级拼接用）
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_invitee_info = conn.query(sql, show_spinner='查询中...', ttl=0)
    return df_invitee_info


def update_full_name(row):
    """
    将代理人信息拼接成完整姓名
    """
    # 将所有可能的非字符串值转换为字符串
    first_inviter_full_name = str(row['first_inviter_full_name'])
    second_inviter_full_name = str(row['second_inviter_full_name'])
    third_inviter_full_name = str(row['third_inviter_full_name'])

    if pd.isnull(row['first_inviter_id']):

        return row['full_name'] + '/' + row['employee_full_name']
    elif pd.isnull(row['second_inviter_id']):
        return row['full_name'] + '/' + first_inviter_full_name + '/' + row['employee_full_name']
    elif pd.isnull(row['third_inviter_id']):
        return row[
            'full_name'] + '/' + first_inviter_full_name + '/' + second_inviter_full_name + '/' + row[
            'employee_full_name']
    else:
        return row[
            'full_name'] + '/' + first_inviter_full_name + '/' + second_inviter_full_name + '/' + third_inviter_full_name + '/' + \
            row['employee_full_name']


def create_cascader_structure(df):
    """
    根据df创建级联结构，用于城市级联控件选择
    """
    # 创建一个空的级联结构
    cascader_items = []

    # 遍历省份
    for province in df['province_name'].unique():
        province_item = sac.CasItem(province, children=[])
        # 遍历城市
        cities = df[df['province_name'] == province]['city_name'].unique()
        for city in cities:
            city_item = sac.CasItem(city, children=[])
            # 遍历区县
            districts = df[(df['province_name'] == province) & (df['city_name'] == city)]['district_name'].unique()
            for district in districts:
                city_item.children.append(sac.CasItem(district))
            province_item.children.append(city_item)
        cascader_items.append(province_item)
    return cascader_items


def level_transformer(row):
    # 将层级转成中文
    row = int(row)
    chinese_level = {1: '一级', 2: '二级', 3: '三级', 4: '四级', 5: '五级', 6: '六级', 7: '七级', 8: '八级', 9: '九级',
                     10: '十级'}
    return chinese_level.get(row, '未知')


def merge_and_aggregate(df, level_columns):
    """
    根据提供的列名合并数据，并对指定列进行聚合。

    :param df: 输入的 DataFrame
    :param level_columns: 包含各级邀请人信息的列名列表
    :return: 处理后的 DataFrame
    """
    # 初始化一个空的 DataFrame 来存储合并后的结果
    merged_df = pd.DataFrame()

    # 遍历每一级邀请人信息
    for cols in level_columns:
        temp_df = df[cols].rename(columns={
            cols[0]: 'employee_id',
            cols[1]: 'employee_full_name',
            cols[2]: 'number'
        })
        merged_df = pd.concat([merged_df, temp_df])

    # 删除 NaN 值
    merged_df.dropna(subset=['employee_id'], inplace=True)

    return merged_df


def create_full_name(parts):
    """
    将给定的部分列表转换为完整名称。
    """
    return '/'.join(parts)


def get_seller_renewal_info(product_set_code,product_set_name, prev_product_set_code, organization_id, seller_name, first_name,
                            second_name, start_date,
                            end_date, conn=CONNECTOR_JKX, sql=query_sql('SQL_EMPLOYEE_RENEWAL')):
    """
    获取机构续保汇总信息，返回保司、机构、部门（一级、二级）、上期出单量、已续保件数、代续保件数、其他渠道已续保件数、续保率
    如果新的机构有名称、但是部门没有，说明部门被删除了
    :param product_set_code: 产品集代码
    :param product_set_name: 产品集名称
    :param start_date: 销售开始日期
    :param end_date: 销售结束日期
    :param organization_id: 销售机构ID
    :param department_name: 销售部门名称
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    # 如果没有传入日期，赋予默认值
    if start_date is None:
        start_date = '1900-01-01'
    if end_date is None:
        end_date = datetime.date.today()
    # 数据并未按照层级进行销量拼接、status是否删除不特殊处理，如果需要，后端会物理删除
    df = conn.query(
        sql.format(product_set_code=product_set_code, prev_product_set_code=prev_product_set_code,
                   start_date=start_date, end_date=end_date,
                   organization_id=organization_id),
        show_spinner='查询中...', ttl=0)
    # 今年全部的部门有哪些
    df_department = get_department_info()
    df_department = df_department[
        (df_department['product_name'] == product_set_name) & (df_department['organization_id'] == organization_id)]

    if df.empty:
        return df
    if first_name:
        df = df[(df['organization_name'] == seller_name) & (df['department_first_name'] == first_name)]
        df_department = df_department[df_department['first_name']==first_name]
    if second_name:
        df = df[(df['organization_name'] == seller_name) & (df['department_first_name'] == first_name) & (
                df['department_name'] == second_name)]
        df_department = df_department[df_department['name']==second_name]
    df_department = df_department[['organization_name', 'first_name', 'name']].rename(
        columns={'first_name': 'department_first_name', 'name': 'department_name'})

    # 一、二级机构是否匹配
    df['department_first_map'] = df.apply(
        lambda x: '1' if x['org_map'] == '1' and x['department_first_name'] == x['new_department_first_name'] else '0',
        axis=1)
    df['department_map'] = df.apply(
        lambda x: '1' if x['org_map'] == '1' and x['department_first_name'] == x['new_department_first_name'] and x[
            'department_name'] == x['new_department_name'] else '0', axis=1)

    # 历史部门出单情况
    df_history = df.groupby(['organization_name', 'department_first_name', 'department_name']).agg(
        {'client_id': 'nunique'}).reset_index()
    df_history.rename(columns={'client_id': 'history_order_num'}, inplace=True)
    # 已经续保情况(是在原来的部门)
    df_renewal_record = df[df['map_client_id'].notna()]
    df_renewal_self = df_renewal_record[
        (df_renewal_record['org_map'] == '1') & (df_renewal_record['department_first_map'] == '1') & (
                    df_renewal_record['department_map'] == '1')]
    df_renewal_departemnt = df_renewal_self.groupby(
        ['organization_name', 'department_first_name', 'department_name']).agg(
        {'map_client_id': 'nunique'}).reset_index()
    df_renewal_departemnt.rename(columns={'map_client_id': 'renewal_num'}, inplace=True)
    # 已经续保情况(是在其他地方)
    df_renewal_other = df_renewal_record[
        (df_renewal_record['org_map'] == '0') | (df_renewal_record['department_first_map'] == '0') | (
                    df_renewal_record['department_map'] == '0')]
    df_renewal_other = df_renewal_other.groupby(['organization_name', 'department_first_name', 'department_name']).agg(
        {'map_client_id': 'nunique'}).reset_index()
    df_renewal_other.rename(columns={'map_client_id': 'other_channel_renewal_num'}, inplace=True)
    # 拼接数据
    df_renewal = pd.merge(df_history, df_renewal_departemnt,
                          on=['organization_name', 'department_first_name', 'department_name'], how='outer')
    df_renewal = pd.merge(df_renewal, df_renewal_other,
                          on=['organization_name', 'department_first_name', 'department_name'], how='outer')
    df_renewal.fillna(0, inplace=True)
    # 还有多少单没有续保
    df_renewal['no_renewal_num'] = df_renewal['history_order_num'] - df_renewal['renewal_num'] - df_renewal[
        'other_channel_renewal_num']

    # 计算续保率
    df_renewal['renewal_rate'] = df_renewal.apply(
        lambda x: round(x['renewal_num'] / x['history_order_num'], 4) if x['history_order_num'] > 0 else 0,axis=1)
    # 计算其他渠道续保率
    df_renewal['other_channel_renewal_rate'] = df_renewal.apply(
        lambda x: round(x['other_channel_renewal_num'] / x['history_order_num'], 4) if x['history_order_num'] > 0 else 0,
        axis=1)

    df_renewal_all = pd.merge(df_department, df_renewal, on=['organization_name', 'department_first_name', 'department_name'], how='left')
    df_renewal_all.fillna(0, inplace=True)
    # 百分比字段格式调整
    df_renewal_all.sort_values(by=['organization_name', 'department_first_name', 'history_order_num','renewal_rate'],ascending=False, inplace=True)
    df_renewal_all.reset_index(drop=True, inplace=True)
    df_renewal_all['renewal_rate'] = df_renewal_all['renewal_rate'].apply(lambda x: f'{x * 100:.2f}%')
    df_renewal_all.drop(columns=['other_channel_renewal_rate'],inplace=True)
    # df_renewal_all['other_channel_renewal_rate'] = df_renewal_all['other_channel_renewal_rate'].apply(lambda x: f'{x * 100:.2f}%')
    df_renewal_all.rename(columns={'organization_name': '保司', 'department_first_name': '机构', 'department_name':'部门',
                                    'history_order_num': '上期出单量', 'no_renewal_num': '未续保件数','renewal_num':'已续保件数',
                                    'other_channel_renewal_num': '其他渠道投保件数','renewal_rate': '续保率'}, inplace=True)
    return df_renewal_all


@st.fragment
def download_excel(df,name):
# 下载数据
    excel_name = Path.cwd().joinpath('temp_files').joinpath(name)
    df.to_excel(excel_name, index=False)
    empty_line(1)
    st.download_button(
        label='下载数据',
        data=open(excel_name, 'rb').read(),
        file_name=name,
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

def main():
    # 权限检查
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("机构续保汇总")
    product_info = get_product_code()
    df_department = get_department_info()
    # 选择日期
    sale_from = datetime.date(2021, 1, 7)
    sale_until = datetime.date.today()
    # send_feishu_message(product_set_code_iframe)
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[
                        0]
                prev_product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['prev_product_set_code'].values[
                        0]
            else:
                product_set_code = None
                prev_product_set_code = None
        cols = st.columns([0.35, 0.15, 0.2])

        with cols[0]:
            select_department = df_department[df_department['product_name'] == column_name]
            # 如果是管理端来的，查看保司的权限有哪些
            if is_iframe == 1 and seller_name is not None:
                seller_name = st.selectbox("请选择保司",
                                           options=[seller_name],
                                           placeholder="请选择保司", disabled=disable)
                with cols[1]:
                    first_name = st.selectbox("请选择一级部门", options=
                    df_department[df_department['organization_name'] == seller_name]['first_name'].unique(),
                                              placeholder="请选择一级部门", index=None)
                with cols[2]:
                    second_name = st.selectbox("请选择二级部门", options=
                    df_department[(df_department['organization_name'] == seller_name) & (
                            df_department['first_name'] == first_name)]['name'].unique(),
                                               placeholder="请选择二级部门", index=None)
            else:
                seller_name = st.selectbox("请选择保司", index=None,
                                           options=select_department['organization_name'].unique(),
                                           placeholder="请选择保司")
                with cols[1]:
                    first_name = st.selectbox("请选择机构", options=
                    df_department[df_department['organization_name'] == seller_name]['first_name'].unique(),
                                              placeholder="请选择机构", index=None)
                with cols[2]:
                    second_name = st.selectbox("请选择部门", options=
                    df_department[(df_department['organization_name'] == seller_name) & (
                            df_department['first_name'] == first_name)]['name'].unique(),
                                               placeholder="请选择部门", index=None)

            if seller_name is not None:
                department_id = \
                    select_department[select_department['organization_name'] == seller_name][
                        'organization_id'].values[0]
            else:
                department_id = None
        cols = st.columns([0.2, 0.2, 0.4])

        with cols[0]:
            start_date = st.date_input('开始日期', min_value=sale_from, max_value=sale_until, value=None,
                                       key='start_date')
        with cols[1]:
            end_date = st.date_input('结束日期', min_value=sale_from, max_value=sale_until, value=None,
                                     key='end_date')
        st.divider()
        if st.button('查询'):
            if start_date and end_date and start_date > end_date:
                st.error('开始日期不能大于结束日期')
            elif column_name is None:
                st.warning('请选择产品')
            elif department_id is None:
                st.warning('请选择保司')
            else:
                with st.spinner('查询中...'):
                    df = get_seller_renewal_info(product_set_code,column_name, prev_product_set_code, department_id, seller_name,
                                                 first_name, second_name, start_date, end_date)
                    download_excel(df, "机构续保汇总.xlsx")
                    st.dataframe(df, hide_index=True, use_container_width=True)


    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
