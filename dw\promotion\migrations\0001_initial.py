# Generated by Django 3.2.12 on 2024-09-04 15:57

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PromotionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('name', models.CharField(blank=True, max_length=500, null=True, verbose_name='活动名称')),
                ('person_name', models.CharField(blank=True, max_length=64, null=True, verbose_name='责任人')),
                ('promotion_type', models.Char<PERSON><PERSON>(blank=True, max_length=64, null=True, verbose_name='营销活动大类')),
                ('type', models.CharField(blank=True, max_length=64, null=True, verbose_name='活动类型')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('additional_info', models.CharField(blank=True, max_length=1000, null=True, verbose_name='补充信息')),
            ],
            options={
                'verbose_name': '营销活动计划表',
                'verbose_name_plural': '营销活动计划表',
                'db_table': 'promotion_plan',
            },
        ),
    ]
