import datetime
import logging
import re
import warnings
from pprint import pprint

import idna
import numpy as np
import pandas as pd
import pymysql

from dw import settings
from insure.models import InsureArea, InsureAgeSex, InsureOnline, InsureAgent
from public.models import PublicStatistics, PublicAreaBaseInsure
from transfrom.utils.utils import query_sql
from task.utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


DB = settings.DATABASES['default']  # dw数据数据库

def get_connection():
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                                user=DB["USER"],
                                password=DB["PASSWORD"], database=DB["NAME"])
    return conn




def get_timeout_task():
    with get_connection() as conn:
        df = pd.read_sql(query_sql('SQL_CELERY'), conn)
        # 筛选时间超过6分钟的任务，需要根据任务实际调度来，任务调度都要小于6分钟
        df_timeout_task =df[df['difftime']>660]
        return df_timeout_task
