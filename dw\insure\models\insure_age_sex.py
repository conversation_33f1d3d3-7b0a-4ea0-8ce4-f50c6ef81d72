from django.db import models
from common.models import BaseModel
from django.db.models import constraints


class InsureAgeSex(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    publish_time = models.DateTimeField(blank=True, null=True, verbose_name='发布时间')
    sex = models.CharField(max_length=32, blank=True, null=True, verbose_name='性别')
    age_distribution = models.CharField(max_length=50, blank=True, null=True, verbose_name='年龄分布')
    value = models.IntegerField(blank=True, null=True, verbose_name='结果值')
    # 由于统计可能细化，例如分地区、产品、保司等，会导致表格过大，因此增加一个字段作为补充信息
    additional_info = models.CharField(max_length=128, blank=True, null=True,  verbose_name='补充信息')

    class Meta:
        db_table = 'insure_age_sex'
        verbose_name = '健康险年龄性别分布表'
        verbose_name_plural = verbose_name
        # 唯一性联合索引
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'publish_time', 'sex', 'age_distribution','additional_info'],
                name='unique_insure_age_sex_combination'
            ),
        ]
