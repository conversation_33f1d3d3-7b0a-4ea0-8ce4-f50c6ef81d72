import os
import sys
import pandas as pd
import logging
import numpy as np
from sqlalchemy import create_engine, inspect, text

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 向上回溯6层目录到项目根目录
project_root = os.path.abspath(os.path.join(current_dir, '../../../../../../'))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def data_to_db(excel_name,db_name):
    """
    读取定点零售药店.xlsx文件，使用pandas的to_sql方法将数据导入到spider_fuwu_fixed_hospital表中
    """
    try:
        # 获取pipeline目录路径
        pipeline_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取与pipeline同级的download目录路径
        parent_dir = os.path.dirname(pipeline_dir)
        download_dir = os.path.join(parent_dir, 'download')
        excel_path = os.path.join(download_dir, excel_name)
        
        logger.info(f"开始读取文件: {excel_path}")
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        # 显示数据基本信息
        logger.info(f"读取到 {len(df)} 条记录")
        logger.info(f"数据列: {df.columns.tolist()}")
        
        # 添加数据来源字段
        df['data_source'] = excel_name
        
        # 从Django设置中获取数据库配置
        from django.conf import settings
        db_settings = settings.DATABASES['local_default']
        
        if db_settings['ENGINE'] == 'django.db.backends.postgresql':
            db_url = f"postgresql://{db_settings['USER']}:{db_settings['PASSWORD']}@{db_settings['HOST']}:{db_settings['PORT']}/{db_settings['NAME']}"
        elif db_settings['ENGINE'] == 'django.db.backends.mysql':
            db_url = f"mysql+pymysql://{db_settings['USER']}:{db_settings['PASSWORD']}@{db_settings['HOST']}:{db_settings['PORT']}/{db_settings['NAME']}"
        elif db_settings['ENGINE'] == 'django.db.backends.sqlite3':
            db_url = f"sqlite:///{db_settings['NAME']}"
        else:
            raise ValueError(f"不支持的数据库引擎: {db_settings['ENGINE']}")
        
        engine = create_engine(db_url)
        
        logger.info("开始导入数据到数据库...")
        # 重名
        df.rename(columns = {'省份':'province','城市':'city'},inplace=True)
        # 先查看数据表是否存在，如果存在，则清空数据表
        if inspect(engine).has_table(db_name):
            logger.info(f"数据表 {db_name} 已存在，开始清空数据...")
            with engine.connect() as connection:
                sql = text(f"DELETE FROM {db_name} WHERE data_source = :source")
                connection.execute(sql, {"source": excel_name})
                connection.commit()
            logger.info(f"数据表 {db_name} 中的 {excel_name} 数据已清空")
        # 1. 确保数据类型一致
        for col in df.columns:
            if df[col].dtype == 'object':  # 字符串类型
                df[col] = df[col].astype(str).str.strip()  # 去除空格
                # 只替换完全等于"nan"的字符串
                df[col] = df[col].apply(lambda x: np.nan if x == 'nan' else x)
        # 去重
        df.drop_duplicates(inplace=True)
        # 使用to_sql方法将数据写入数据库
        # 将数据写入数据库
        df.to_sql(
            name=db_name,
            con=engine,
            if_exists='append',  # 追加模式
            index=False,
            chunksize=10000  # 分批写入，每批1000条
        )
        
        logger.info(f"{excel_name}数据导入完成，共导入{db_name} {len(df)} 条记录")
    
    except Exception as e:
        logger.error(f"导入数据时发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dw.settings')
    
    # 导入Django设置
    import django
    django.setup()
    excel_db_map = {
    # '定点医疗机构.xlsx':'spider_fuwu_fixed_hospital',
    # '定点零售药店.xlsx':'spider_fuwu_retail_pharmacy',
    # '国谈药定点零售药店.xlsx':'spider_fuwu_national_drug_retail_pharmacy',
    # '国谈药定点医疗机构.xlsx':'spider_fuwu_national_drug_hospital',
    # '国谈药目录清单.xlsx':'spider_fuwu_national_drug_catalog',
    # '手术操作分类.xlsx':'spider_fuwu_surgical_catalog',
    # '西药.xlsx':'spider_fuwu_western_medicine',
    # '中成药.xlsx':'spider_fuwu_chinese_prescription_medicine',
    '西医疾病诊断.xlsx':'spider_fuwu_western_disease',
    # '医疗服务项目.xlsx':'spider_fuwu_service_facilities',
    # '医用耗材.xlsx':'spider_fuwu_medical_supplies',
    # '中草药.xlsx':'spider_fuwu_tcm_herb',
    # '中医疾病分类.xlsx':'spider_fuwu_tcm_disease',
    # '自制药.xlsx':'spider_fuwu_selfprep_medicine'
    }

    # 写入数据库
    for excel_name,db_name in excel_db_map.items():
        data_to_db(excel_name,db_name)
