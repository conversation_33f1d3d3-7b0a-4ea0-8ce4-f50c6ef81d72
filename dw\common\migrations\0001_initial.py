# Generated by Django

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DatabaseMigrationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('migration_name', models.CharField(max_length=255, verbose_name='迁移名称')),
                ('database_type', models.CharField(max_length=50, verbose_name='数据库类型')),
                ('sql_executed', models.TextField(verbose_name='执行的SQL')),
                ('status', models.CharField(choices=[('success', '成功'), ('failed', '失败'), ('rollback', '已回滚')], max_length=20, verbose_name='状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('executed_at', models.DateTimeField(auto_now_add=True, verbose_name='执行时间')),
            ],
            options={
                'verbose_name': '数据库迁移日志',
                'verbose_name_plural': '数据库迁移日志',
                'db_table': 'database_migration_log',
                'ordering': ['-executed_at'],
            },
        ),
    ]
