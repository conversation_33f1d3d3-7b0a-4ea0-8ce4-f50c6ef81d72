import datetime

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from pathlib import Path
from plotly.subplots import make_subplots
from utils.st import query_sql, text_write
from pyecharts import options as opts
from pyecharts.charts import Sankey
from streamlit_echarts import st_pyecharts
import warnings
import plotly.figure_factory as ff

import sys

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        and ps.code not in ('ninghuibaoV1','ninghuibaoV2','ninghuibaoV3')
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)
    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code


def get_marketing_activities(product_set_code, conn=CONNECTOR_JKX, sql=query_sql('SQL_MARKETING_PLAN')):
    """
    获取产品销售机构及部门信息
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_marketing_activities = conn.query(sql.format(product_set_code=product_set_code), show_spinner='查询中...', ttl=0)
    df_marketing_activities.rename(columns={'label': '活动类型'}, inplace=True)
    if df_marketing_activities.empty:
        return pd.DataFrame(columns=['活动类型', '开始日期', '结束日期', 'keyword'])
    # 如果keyword为空，则拼接remark和location和coordinate和way
    df_marketing_activities['keyword'] = df_marketing_activities.apply(
        lambda x: x['remark'] + ' ' + x['location'] + ' ' + x['coordinate'] + ' ' + x['way'] if x[
                                                                                                    'keyword'] is None else
        x['keyword'], axis=1)
    df_marketing_activities.loc[df_marketing_activities['活动类型'] == 'AI语音电话', 'keyword'] = \
        df_marketing_activities['keyword'] + ' 发送:' + df_marketing_activities['click_num'].astype(str) + ' 送达:' + \
        df_marketing_activities['reading_num'].astype(str)
    # 如果开始日期等于结束日期 或者结束日期为空，结束日期为开始日期加一天
    df_marketing_activities['end_time'].fillna(df_marketing_activities['start_time'], inplace=True)
    df_marketing_activities['end_time'] = df_marketing_activities.apply(
        lambda x: x['start_time'] + datetime.timedelta(days=1) if x['start_time'] == x['end_time'] else x['end_time'],
        axis=1)
    # 根据活动类型、start_time、end_time进行分组，并添加排序列
    df_marketing_activities_group = df_marketing_activities.groupby(
        ['活动类型', 'start_time', 'end_time']).cumcount() + 1
    # 将排序列作为新列添加到原始DataFrame中
    df_marketing_activities['排序'] = df_marketing_activities_group
    # 如果排序的值不等于1，则end_time加上相应的分钟数，防止重叠
    df_marketing_activities.loc[df_marketing_activities['排序'] > 1, 'end_time'] = df_marketing_activities.apply(
        lambda x: x['end_time'] + datetime.timedelta(minutes=x['排序'] * 5), axis=1)
    df_marketing_activities.rename(columns={'start_time': '开始日期', 'end_time': '结束日期'}, inplace=True)
    return df_marketing_activities


def get_marketing_plan(product_set_code, conn=CONNECTOR_DW):
    """
    获取产品销售机构及部门信息
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    sql = """ 
    SELECT
        pp.*,
        sdv.`key` promotion_name
    FROM
        promotion_plan pp
        JOIN system_dict_value sdv ON pp.promotion_type = sdv.label 
    WHERE
        product_set_code = '{product_set_code}'
    """
    df_marketing_activities = conn.query(sql.format(product_set_code=product_set_code), show_spinner='查询中...', ttl=0)
    if df_marketing_activities.empty:
        df = pd.DataFrame(
            columns=[ '开始日期', '结束日期', '活动类型', '活动详情', '活动名称','活动明细','排序'])
        df_marketing_activities = pd.DataFrame(
            columns=['开始日期', '结束日期', '活动类型', '活动详情', '活动名称','活动明细','排序'])
        return df_marketing_activities,df
    df_marketing_activities.rename(columns={'promotion_name': '活动类型'}, inplace=True)
    if df_marketing_activities.empty:
        return pd.DataFrame(columns=['活动类型', '开始日期', '结束日期', '活动详情']), pd.DataFrame(
            columns=['活动类型', '开始日期', '结束日期', '活动详情'])
    df_marketing_activities['start_time'] = pd.to_datetime(df_marketing_activities['start_time'])
    df_marketing_activities['end_time'] = pd.to_datetime(df_marketing_activities['end_time'])
    # 如果keyword为空，则拼接remark和location和coordinate和way
    df_marketing_activities['活动详情'] = df_marketing_activities.apply(
        lambda x: x['name'] + '——' + x['additional_info'] if x['additional_info'] is not None else
        x['name'], axis=1)

    # 根据活动名称分类（小类）
    df = df_marketing_activities.copy()
    # 获取活动名称
    df['活动名称'] = df['additional_info']
    df['活动明细'] = df['name']
    df_group = df.groupby(
        ['活动明细', 'start_time', 'end_time']).cumcount() + 1
    # 将排序列作为新列添加到原始DataFrame中
    df['排序'] = df_group
    df['排序'].fillna(0, inplace=True)
    # 如果排序的值不等于1，则end_time加上相应的分钟数，防止重叠
    df.loc[df['排序'] > 1, 'end_time'] = df.apply(
        lambda x: x['end_time'] - datetime.timedelta(minutes=x['排序'] * 60), axis=1)
    df.sort_values(by=['id'], ascending=False, inplace=True)

    # 根据活动类型、start_time、end_time进行分组，并添加排序列
    df_marketing_activities_group = df_marketing_activities.groupby(
        ['活动类型', 'start_time', 'end_time']).cumcount() + 1
    # 将排序列作为新列添加到原始DataFrame中
    df_marketing_activities['排序'] = df_marketing_activities_group
    df_marketing_activities['排序'].fillna(0, inplace=True)

    # 如果排序的值不等于1，则end_time加上相应的分钟数，防止重叠
    df_marketing_activities.loc[df_marketing_activities['排序'] > 1, 'end_time'] = df_marketing_activities.apply(
        lambda x: x['end_time'] - datetime.timedelta(minutes=x['排序'] * 60), axis=1)

    df_marketing_activities.rename(columns={'start_time': '开始日期', 'end_time': '结束日期'}, inplace=True)
    df.rename(columns={'start_time': '开始日期', 'end_time': '结束日期'}, inplace=True)
    return df_marketing_activities, df


def marketing_plan(product_set_code):
    text_write("营销活动计划")
    st.text('1.营销活动计划类型')
    df, df_detail = get_marketing_plan(product_set_code)
    fig = px.timeline(df, x_start="开始日期", x_end="结束日期", y="活动类型", color='活动类型',
                      hover_name='活动详情')
    fig.update_yaxes(autorange="reversed")  # otherwise tasks are listed from the bottom up
    fig.update_xaxes(tickformat="%Y-%m-%d")
    fig.update_layout(height=400, xaxis_title=None,
                      yaxis_title=None, showlegend=False)

    st.plotly_chart(fig, use_container_width=True)
    st.text('2.营销活动计划')
    fig = px.timeline(df_detail, x_start="开始日期", x_end="结束日期", y="活动明细", color='活动明细',
                      hover_name='活动详情')
    fig.update_yaxes(autorange="reversed")  # otherwise tasks are listed from the bottom up
    fig.update_xaxes(tickformat="%Y-%m-%d")
    fig.update_layout(height=800, xaxis_title=None,
                      yaxis_title=None, showlegend=False)
    st.plotly_chart(fig, use_container_width=True)


def marketing_activities(product_set_code):
    text_write("营销活动")
    df = get_marketing_activities(product_set_code)
    fig = px.timeline(df, x_start="开始日期", x_end="结束日期", y="活动类型", color='活动类型',
                      hover_name='keyword')
    fig.update_yaxes(autorange="reversed")  # otherwise tasks are listed from the bottom up
    fig.update_xaxes(tickformat="%Y-%m-%d")
    fig.update_layout(height=400,
                      xaxis_title=None,
                      yaxis_title=None,
                      showlegend=False)
    st.plotly_chart(fig, use_container_width=True)


def wechat_push(product_set_code):
    text_write('推文阅读量')
    df = get_marketing_activities(product_set_code)
    df = df[df['活动类型'] == '发微信推文']
    df['name'] = df['keyword'].apply(lambda x: x.split(',')[0])
    df['name'] = df['name'] + df['开始日期'].apply(lambda x: x.strftime('%m-%d %H'))
    df.sort_values(by='开始日期', inplace=True, ascending=True)
    if df.empty:
        df = pd.DataFrame(columns=['name', 'reading_num'])
    fig = px.bar(df, y='name', x='reading_num', barmode='group')
    # 更新布局以隐藏坐标轴标题
    fig.update_layout(
        height=1000,
        xaxis_title=None,
        yaxis_title=None
    )
    st.plotly_chart(fig, use_container_width=True)


# @st.cache_data
def db_get_online(produc_set_code):
    # 如果是渠道码，需要去其他表获取新的名称
    sql = '''
SELECT
	t.CODE AS code,
	mc.channel_name, #用于判定那些需要处理
CASE
		
		WHEN LEFT ( mc.channel_name, 3 ) = '渠道码' THEN
		mcps.remark ELSE mc.channel_name 
	END AS name,
	t.count AS count 
FROM
	(
	SELECT
		mc.channel_code AS CODE,
		count( 1 ) AS count 
	FROM
		`order` o
		JOIN order_item oi ON oi.order_id = o.id
		JOIN order_item_client oic ON oic.order_item_id = oi.id 
		AND oic.is_return = 0
		JOIN product p ON p.id = oi.product_id  AND p.main = 1
		JOIN product_set ps ON ps.id = p.product_set_id 
		AND ps.CODE = '{produc_set_code}'
		JOIN marketing_channel mc ON mc.channel_id = o.source_id 
	WHERE
		o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
		AND o.is_online = 1 
	GROUP BY
		mc.channel_code 
	) t
	JOIN marketing_channel mc ON mc.channel_code = t.
	CODE LEFT JOIN marketing_channel_product_set mcps ON mc.channel_code = mcps.channel_code
    '''
    return CONNECTOR_JKX.query(sql.format(produc_set_code=produc_set_code), show_spinner='查询中...', ttl=0)


def channels(produc_set_code):
    df = db_get_online(produc_set_code)
    df['channel'] = df.apply(lambda x: x['name'].split('-')[0] if '渠道码' in x['channel_name'] else None, axis=1)
    df_t = df.copy()

    # 过滤
    df_t = df_t[~df_t['code'].isin(['agent', 'alipay', 'wesure'])]
    df_t = df_t[~df_t['code'].str.startswith('njapp')]
    df_t = df_t[~df_t['code'].str.startswith('wesure')]
    df_t.loc[df_t['code'] == 'source=online_push_2', 'code'] = 'online_push_2'
    # print(df_t)

    codes = []
    dfs = {}

    df_bus = df_t[
        df_t['code'].str.contains('subway') | df_t['code'].str.contains('bus') | df_t['channel'].str.contains('地铁') |
        df_t['channel'].str.contains('公交')]
    dfs['公交地铁'] = df_bus
    codes.extend(df_bus['code'].tolist())

    df_gzh = df_t[df_t['code'].str.startswith('online_gzh') | df_t['code'].str.contains('online_njws') | df_t[
        'name'].str.contains('推文')]
    dfs['推文'] = df_gzh
    codes.extend(df_gzh['code'].tolist())

    df_hospital_gzh = df_t[
        (df_t['channel_name'].str.contains('医院', na=False) | df_t['name'].str.contains('医院', na=False))
        & ~df_t['name'].str.contains('药店', na=False)
        & ~df_t['code'].str.contains('药店', na=False)
    ]
    dfs['医院微信公众号'] = df_hospital_gzh
    codes.extend(df_hospital_gzh['code'].tolist())

    df_zw_gzh = df_t[df_t['channel_name'].str.contains('政务') | df_t['name'].str.contains('政务')]
    dfs['政务'] = df_zw_gzh
    codes.extend(df_zw_gzh['code'].tolist())

    df_news_gzh = df_t[
        df_t['channel_name'].str.contains('新闻媒体微信公众号') | df_t['name'].str.contains('新闻媒体微信公众号')]
    dfs['新闻媒体微信公众号'] = df_news_gzh
    codes.extend(df_news_gzh['code'].tolist())

    df_push = df_t[df_t['code'].str.startswith('online_push') | df_t['name'].str.contains('模板消息')]
    dfs['模板消息'] = df_push
    codes.extend(df_push['code'].tolist())

    df_sms = df_t[df_t['channel_name'].str.contains('短信') | df_t['name'].str.contains('短信')]
    dfs['短信'] = df_sms
    codes.extend(df_sms['code'].tolist())

    df_ybj = df_t[df_t['code'].str.contains('ybj') | df_t['name'].str.contains('医保局')]
    dfs['医保局'] = df_ybj
    codes.extend(df_ybj['code'].tolist())

    df_company_wx = df_t[df_t['name'].str.contains('企业微信')]
    dfs['企业微信'] = df_company_wx
    codes.extend(df_company_wx['code'].tolist())

    df_other = df_t[~df_t['code'].isin(codes)]
    dfs['其他'] = df_other
    text_write('渠道销量分解')
    cols = st.columns(2)
    with cols[0]:

        def _map_code(code):
            if code.startswith('njapp'):
                return '我的南京'
            elif code.startswith('wesure'):
                return '微保'
            elif code.startswith('alipay'):
                return '支付宝'
            else:
                return '线上其他'

        df_m = df.copy()
        df_m['type'] = df_m['code'].apply(_map_code)
        fig = go.Figure(data=[go.Pie(labels=df_m['type'].unique().tolist(),
                                     values=[df_m[df_m['type'] == t]['count'].sum() for t in
                                             df_m['type'].unique().tolist()])])
        fig.update_traces(textposition='inside', textinfo='percent+label', automargin=True)
        # 添加标题
        fig.update_layout(title_text='线上分解')
        st.plotly_chart(fig, use_container_width=True)

    with cols[1]:
        fig = go.Figure(data=[go.Pie(labels=list(dfs.keys()), values=[df['count'].sum() for df in dfs.values()])])
        fig.update_traces(textposition='inside', textinfo='percent+label')
        fig.update_layout(title_text='其他渠道分解')
        st.plotly_chart(fig, use_container_width=True)

    text_write('渠道销量明细')
    keys = list(dfs.keys())
    for i in range(0, len(keys), 2):
        key_1 = keys[i]
        key_2 = keys[i + 1] if i + 1 < len(keys) else None

        cols = st.columns(2)
        with cols[0]:
            df = dfs[key_1]
            df = df.sort_values(by='count', ascending=False)
            fig = px.bar(df, y='count', x='name', title=key_1, labels={'name': '渠道', 'count': '销量'})
            # 更新 y 轴格式为整数形式
            fig.update_yaxes(tickformat=".0f")
            fig.update_layout(height=600, xaxis_title=None, yaxis_title=None, showlegend=False)
            st.plotly_chart(fig)

        if key_2:
            df_2 = dfs[key_2]
            df_2 = df_2.sort_values(by='count', ascending=False)
            with cols[1]:
                fig = px.bar(df_2, y='count', x='name', title=key_2, labels={'name': '渠道', 'count': '销量'})
                # 更新 y 轴格式为整数形式
                fig.update_yaxes(tickformat=".0f")
                fig.update_layout(height=600, xaxis_title=None, yaxis_title=None, showlegend=False)
                st.plotly_chart(fig)


def db_get_poster(product_set_code):
    sql = '''
SELECT
	base.material_name AS name,
	sum(
	IFNULL( detail_b.point_number, 0 )) AS point_number,
	sum(
	IFNULL( detail_c.buy_number, 0 )) AS buy_number 
FROM
	(
	SELECT
		ml.id AS material_id,
		ml.`name` AS material_name,
		ml.create_time AS create_time 
	FROM
		material ml 
	WHERE
		ml.product_set = '{product_set_code}' 
		AND ml.type = 'POSTER' 
	) base
	LEFT JOIN (
	SELECT
		a.id material_id,
		a.NAME material_name,
		count(*) AS point_number 
	FROM
		material_operating_trajectory m
		JOIN material a ON m.material_id = a.id 
	WHERE
		a.product_set = '{product_set_code}' 
		AND m.type = 'PLAY' 
	GROUP BY
		m.type,
		a.id 
	) detail_b ON base.material_id = detail_b.material_id
	LEFT JOIN (
	SELECT
		agent.poster_id AS material_id,
		count( 1 ) AS buy_number 
	FROM
		`order` o
		JOIN `order_agent` agent ON agent.order_id = o.id
		JOIN order_item oi ON oi.order_id = o.id
		JOIN order_item_client oic ON oic.order_item_id = oi.id 
		AND oic.is_return = 0
		JOIN product p ON p.id = oi.product_id 
		AND p.main = 1
		JOIN product_set ps ON ps.id = p.product_set_id 
	WHERE
		o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
		AND ps.CODE = '{product_set_code}' 
		AND agent.poster_id != 0 
	GROUP BY
		material_id 
	) detail_c ON base.material_id = detail_c.material_id 
GROUP BY
NAME 
ORDER BY
	buy_number DESC
    '''
    return CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code), ttl=0)


def poster(product_set_code):
    text_write("线下海报转换情况")

    # 截取并格式化 x 轴标签
    def format_name(name, max_length=20):
        if len(name) > max_length:
            return name[:max_length - 3] + '...'
        else:
            return name

    # 添加悬停文本
    def add_hover_text(df):
        df['name_hover'] = df['name'].apply(lambda x: x)
        df['name'] = df['name'].apply(format_name)
        return df

    # 获取数据
    df = db_get_poster(product_set_code)
    df['转化率'] = df['buy_number'] / df['point_number']

    # 格式化 x 轴标签并添加悬停文本
    df = add_hover_text(df)

    # 使用plotly绘制bar图
    fig = make_subplots(specs=[[{"secondary_y": True}]])

    # 添加柱状图
    fig.add_trace(
        go.Bar(x=df['name'], y=df['point_number'], name='点击量', hovertext=df['name_hover'],
               hovertemplate='%{x}<br>点击量: %{y}'),
        secondary_y=False
    )
    fig.add_trace(
        go.Bar(x=df['name'], y=df['buy_number'], name='购买量', hovertext=df['name_hover'],
               hovertemplate='%{x}<br>购买量: %{y}'),
        secondary_y=False
    )

    # 添加折线图
    fig.add_trace(
        go.Scatter(x=df['name'], y=df['转化率'], name='转化率', mode='lines+markers', hovertext=df['name_hover'],
                   hovertemplate='%{x}<br>转化率: %{y:.0%}'),
        secondary_y=True
    )

    # 更新布局
    fig.update_layout(
        barmode='overlay',
        height=700,
        xaxis=dict(tickangle=45),
        legend=dict(
            orientation="h",  # 水平方向
            yanchor="bottom",  # y轴锚点为底部
            y=1,  # y位置为1.02（顶部）
            xanchor="center",  # x轴锚点为中心
            x=0.5  # x位置为0.5（居中）
        )
    )
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    # 更新 y 轴
    fig.update_yaxes(secondary_y=True, tickformat=".0%")
    fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
    fig.update_yaxes(rangemode="tozero", secondary_y=True)
    fig.update_yaxes(rangemode="tozero", secondary_y=False)

    # 显示图表
    st.plotly_chart(fig, use_container_width=True)


def ai_phone_sales(product_set_code):
    """
    AI手机营销转换率
    接通情况分析（拨打号码数、接通数、通话时间分布）
    购买与电销 添加时间衰减最小单位、衰减系数，避免间隔时间过长也算是电销带来的效果
    :return:
    """
    sql = """
    SELECT DISTINCT
tst.name task_name,
tst.id task_id,
	o.id,
	o.pay_time,
	m.back_time,
	TIMESTAMPDIFF(SECOND, m.back_time, o.pay_time) diff_seconds,
	m.bill_sec AS batch,
	m.phone,
	buyer.credential_number
FROM
	`order` o
	JOIN order_item oi ON oi.order_id = o.id
	JOIN order_item_client oic ON oic.order_item_id = oi.id
	JOIN product_set ps ON ps.id = o.product_set_id
	JOIN product p ON p.id = oi.product_id
	JOIN USER buyer ON o.buyer_id = buyer.id
	JOIN USER client ON oic.client_id = client.id
	JOIN jkx_tele_sales.{tel_table_name} m ON m.phone = buyer.mobile 
	AND m.back_status = 'SUCCESS' 
	join jkx_tele_sales.tele_sales_task tst on tst.id = m.task_id and tst.delete_time is null
	join jkx_tele_sales.tele_sales_template tstt on tstt.id = tst.template_id and tstt.business_type='CALL' and tstt.product_set_code='ninghuibaoV5'
WHERE
	ps.CODE = 'ninghuibaoV5' 
	AND o.order_status = 'PAID_SUCCESS' 
	AND p.main = 1 
	AND o.pay_time >= m.back_time
    """
    sql_tele = """
    	select task_id,count(*) num from jkx_tele_sales.{tel_table_name}
    	where back_status = 'SUCCESS' 
	group by task_id
    """
    if product_set_code == 'ninghuibaoV5':
        tel_table_name = 'tele_sales_item_2024'
    else:
        tel_table_name = 'tele_sales_item'
    df = CONNECTOR_JKX.query(sql.format(tel_table_name=tel_table_name), show_spinner='查询中...', ttl=0)
    df_tele = CONNECTOR_JKX.query(sql_tele.format(tel_table_name=tel_table_name), show_spinner='查询中...', ttl=0)
    df = df.merge(df_tele, on='task_id', how='left')
    if df.empty:
        return pd.DataFrame(
            columns=['任务ID', '任务名称', '转换率', '类型'])
    df_24 = df[df['diff_seconds'] <= 86400].reset_index(drop=True)
    df_48 = df[df['diff_seconds'] <= 86400 * 2].reset_index(drop=True)
    df_group = df.groupby(['task_id', 'task_name']).agg({'id': 'count', 'num': 'first'}).reset_index()
    if df_group.empty:
        return pd.DataFrame(
            columns=['任务ID', '任务名称', '转换率', '类型'])
    df_group['转换率'] = df_group['id'] / df_group['num']
    df_group['类型'] = '全部'
    df_24_group = df_24.groupby(['task_id', 'task_name']).agg({'id': 'count', 'num': 'first'}).reset_index()
    df_24_group['转换率'] = df_24_group['id'] / df_24_group['num']
    df_24_group['类型'] = '24小时内'
    df_48_group = df_48.groupby(['task_id', 'task_name']).agg({'id': 'count', 'num': 'first'}).reset_index()
    df_48_group['转换率'] = df_48_group['id'] / df_48_group['num']
    df_48_group['类型'] = '48小时内'
    df_group = pd.concat([df_group, df_24_group, df_48_group])
    # 根据类型不同，将对应的转换率变成列名
    df_pivot = df_group.pivot(index=['task_id', 'task_name'], columns='类型', values='转换率')
    df_pivot = df_pivot.reset_index()
    df_pivot.rename(columns={'task_id': '任务ID', 'task_name': '任务名称'}, inplace=True)
    return df_pivot


def ai_phone_list():
    """
    AI手机营销列表
    """
    sql = """
    select t.id,t.name from jkx_tele_sales.tele_sales_template tst join jkx_tele_sales.tele_sales_task t 
    on tst.id = t.template_id
    where t.delete_time is  null 
    and tst.business_type='CALL'
    and tst.product_set_code='ninghuibaoV5'
    """
    df = CONNECTOR_JKX.query(sql, show_spinner='查询中...', ttl=0)
    return df


def sms_sales(product_set_code):
    """
    短信营销转换率
    接通情况分析（拨打号码数、接通数、通话时间分布）
    购买与电销 添加时间衰减最小单位、衰减系数，避免间隔时间过长也算是电销带来的效果
    :return:
    """
    sql = """
    SELECT DISTINCT
tst.name task_name,
tst.id task_id,
	o.id,
	o.pay_time,
	m.back_time,
	TIMESTAMPDIFF(SECOND, m.back_time, o.pay_time) diff_seconds,
	m.bill_sec AS batch,
	m.phone,
	buyer.credential_number
FROM
	`order` o
	JOIN order_item oi ON oi.order_id = o.id
	JOIN order_item_client oic ON oic.order_item_id = oi.id
	JOIN product_set ps ON ps.id = o.product_set_id
	JOIN product p ON p.id = oi.product_id
	JOIN USER buyer ON o.buyer_id = buyer.id
	JOIN USER client ON oic.client_id = client.id
	JOIN jkx_tele_sales.{tel_table_name} m ON m.phone = buyer.mobile 
	AND m.back_status = 'SUCCESS' 
	join jkx_tele_sales.tele_sales_task tst on tst.id = m.task_id and tst.delete_time is null
	join jkx_tele_sales.tele_sales_template tstt on tstt.id = tst.template_id and tstt.business_type='SMS' and tstt.product_set_code='ninghuibaoV5'
WHERE
	ps.CODE = 'ninghuibaoV5' 
	AND o.order_status = 'PAID_SUCCESS' 
	AND p.main = 1 
	AND o.pay_time >= m.back_time
    """
    sql_tele = """
    	select task_id,count(*) num from jkx_tele_sales.{tel_table_name}
    	where back_status = 'SUCCESS' 
	group by task_id
    """
    if product_set_code == 'ninghuibaoV5':
        tel_table_name = 'tele_sales_item_2024'
    else:
        tel_table_name = 'tele_sales_item'
    df = CONNECTOR_JKX.query(sql.format(tel_table_name=tel_table_name), show_spinner='查询中...', ttl=0)
    df_tele = CONNECTOR_JKX.query(sql_tele.format(tel_table_name=tel_table_name), show_spinner='查询中...', ttl=0)
    df = df.merge(df_tele, on='task_id', how='left')
    if df.empty:
        return pd.DataFrame(
            columns=['任务ID', '任务名称', '转换率', '类型'])
    df_24 = df[df['diff_seconds'] <= 86400].reset_index(drop=True)
    df_48 = df[df['diff_seconds'] <= 86400 * 2].reset_index(drop=True)
    df_group = df.groupby(['task_id', 'task_name']).agg({'id': 'count', 'num': 'first'}).reset_index()
    df_group['转换率'] = df_group['id'] / df_group['num']
    df_group['类型'] = '全部'
    df_24_group = df_24.groupby(['task_id', 'task_name']).agg({'id': 'count', 'num': 'first'}).reset_index()
    df_24_group['转换率'] = df_24_group['id'] / df_24_group['num']
    df_24_group['类型'] = '24小时内'
    df_48_group = df_48.groupby(['task_id', 'task_name']).agg({'id': 'count', 'num': 'first'}).reset_index()
    df_48_group['转换率'] = df_48_group['id'] / df_48_group['num']
    df_48_group['类型'] = '48小时内'
    df_group = pd.concat([df_group, df_24_group, df_48_group])
    # 根据类型不同，将对应的转换率变成列名
    df_pivot = df_group.pivot(index=['task_id', 'task_name'], columns='类型', values='转换率')
    df_pivot = df_pivot.reset_index()
    df_pivot.rename(columns={'task_id': '任务ID', 'task_name': '任务名称'}, inplace=True)
    return df_pivot


def sms_list():
    """
    短信营销列表
    """
    sql = """
    select t.id,t.name from jkx_tele_sales.tele_sales_template tst join jkx_tele_sales.tele_sales_task t 
    on tst.id = t.template_id
    where t.delete_time is  null 
    and tst.business_type='SMS'
    and tst.product_set_code='ninghuibaoV5'
    """
    df = CONNECTOR_JKX.query(sql, show_spinner='查询中...', ttl=0)
    return df


def ai_phone(product_set_code):
    """
    AI手机营销
    """
    text_write("AI电话营销转换率")
    df = ai_phone_sales(product_set_code)
    if df.empty:
        st.warning("暂无数据")
    else:
        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # 添加柱状图系列到主y轴
        fig.add_trace(
            go.Bar(x=df['任务名称'], y=df['全部'], name='转换率', hovertemplate='名称: %{x}<br>转换率: %{y:.2%}'),
            secondary_y=False
        )
        fig.add_trace(
            go.Bar(x=df['任务名称'], y=df['48小时内'], name='48小时转换率',
                   hovertemplate='名称: %{x}<br>48小时转换率: %{y:.2%}'),
            secondary_y=False
        )
        fig.add_trace(
            go.Bar(x=df['任务名称'], y=df['24小时内'], name='24小时转换率',
                   hovertemplate='名称: %{x}<br>24小时转换率: %{y:.2%}'),
            secondary_y=False
        )

        fig.update_xaxes(tickformat="%m-%d")
        # 更新 y 轴格式为整数形式
        fig.update_yaxes(tickformat=".0%")
        # 移除副y轴的网格线
        fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
        # 设置y轴和y次轴的范围模式为从0开始
        fig.update_yaxes(rangemode="tozero", secondary_y=True)
        fig.update_yaxes(rangemode="tozero", secondary_y=False)
        fig.update_traces(
        )
        fig.update_layout(yaxis_title="",
                          height=600,
                          plot_bgcolor='rgba(0, 0, 0, 0)',
                          paper_bgcolor='rgba(0, 0, 0, 0)',
                          legend=dict(
                              orientation="h",  # 水平方向
                              yanchor="bottom",  # y轴锚点为底部
                              y=1,  # y位置为1.02（顶部）
                              xanchor="center",  # x轴锚点为中心
                              x=0.5  # x位置为0.5（居中）
                          ),
                          title=dict(
                              text='',
                              x=0.5,  # 居中对齐
                              xanchor="center",
                              y=0.95,
                              font=dict(size=14)
                          ),
                          yaxis=dict(
                              fixedrange=True,  # 禁止y轴的缩放和平移
                              title_font=dict(size=12)  # 设置 yaxis_title 字体大小
                          ),
                          yaxis2=dict(
                              overlaying='y',
                              side='right',
                              title_font=dict(size=12)  # 设置 yaxis2_title 字体大小
                          ),
                          dragmode=False,  # 禁用拖动模式
                          xaxis=dict(
                              fixedrange=True  # 禁止x轴的缩放和平移
                          ))
        # 隐藏工具栏
        config = {'displayModeBar': False}
        st.plotly_chart(fig, config=config)
        download_excel(df.drop(columns=['任务ID']), 'AI电话营销转换率.xlsx')


def sms(product_set_code):
    """
    短信营销
    """
    text_write("短信营销转换率")
    df = sms_sales(product_set_code)
    if df.empty:
        st.warning("暂无数据")
    else:

        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # 添加柱状图系列到主y轴
        fig.add_trace(
            go.Bar(x=df['任务名称'], y=df['全部'], name='转换率', hovertemplate='名称: %{x}<br>转换率: %{y:.2%}'),
            secondary_y=False
        )
        fig.add_trace(
            go.Bar(x=df['任务名称'], y=df['48小时内'], name='48小时转换率',
                   hovertemplate='名称: %{x}<br>48小时转换率: %{y:.2%}'),
            secondary_y=False
        )
        fig.add_trace(
            go.Bar(x=df['任务名称'], y=df['24小时内'], name='24小时转换率',
                   hovertemplate='名称: %{x}<br>24小时转换率: %{y:.2%}'),
            secondary_y=False
        )

        fig.update_xaxes(tickformat="%m-%d")
        # 更新 y 轴格式为整数形式
        fig.update_yaxes(tickformat=".0%")
        # 移除副y轴的网格线
        fig.update_yaxes(showgrid=False, gridwidth=0, gridcolor='rgba(0,0,0,0)', secondary_y=True)
        # 设置y轴和y次轴的范围模式为从0开始
        fig.update_yaxes(rangemode="tozero", secondary_y=True)
        fig.update_yaxes(rangemode="tozero", secondary_y=False)
        fig.update_traces(
        )
        fig.update_layout(yaxis_title="",
                          height=600,
                          plot_bgcolor='rgba(0, 0, 0, 0)',
                          paper_bgcolor='rgba(0, 0, 0, 0)',
                          legend=dict(
                              orientation="h",  # 水平方向
                              yanchor="bottom",  # y轴锚点为底部
                              y=1,  # y位置为1.02（顶部）
                              xanchor="center",  # x轴锚点为中心
                              x=0.5  # x位置为0.5（居中）
                          ),
                          title=dict(
                              text='',
                              x=0.5,  # 居中对齐
                              xanchor="center",
                              y=0.95,
                              font=dict(size=14)
                          ),
                          yaxis=dict(
                              fixedrange=True,  # 禁止y轴的缩放和平移
                              title_font=dict(size=12)  # 设置 yaxis_title 字体大小
                          ),
                          yaxis2=dict(
                              overlaying='y',
                              side='right',
                              title_font=dict(size=12)  # 设置 yaxis2_title 字体大小
                          ),
                          dragmode=False,  # 禁用拖动模式
                          xaxis=dict(
                              fixedrange=True  # 禁止x轴的缩放和平移
                          ))
        # 隐藏工具栏
        config = {'displayModeBar': False}
        st.plotly_chart(fig, config=config)
        download_excel(df.drop(columns=['任务ID']), '短信营销转换率.xlsx')


@st.fragment
def download_excel(df, name):
    # 下载数据
    excel_name = Path.cwd().joinpath('temp_files').joinpath(name)
    df.to_excel(excel_name, index=False)
    st.download_button(
        label='下载数据',
        data=open(excel_name, 'rb').read(),
        file_name=name,
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


def main():
    st.subheader("营销")
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=product_info['product_set_name'],
                                                  placeholder="请选择产品")
    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[0]
    tabs = st.tabs(['总览', '线上渠道', 'AI电话营销', '短信营销', '线下海报'])
    st.divider()
    with tabs[0]:
        marketing_plan(product_set_code)
        marketing_activities(product_set_code=product_set_code)
    with tabs[1]:
        channels(product_set_code)
    # with tabs[2]:
    #     wechat_push(product_set_code)
    with tabs[2]:
        ai_phone(product_set_code)
    with tabs[3]:
        sms(product_set_code)
    with tabs[4]:
        poster(product_set_code)


if __name__ == '__main__':
    main()
