from docx import Document
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT, WD_ALIGN_VERTICAL
from docx.shared import RGBColor, Cm
from docx.oxml.ns import qn
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT



def add_paragraph(document, text, indent=1, size=16, font_name=u'楷体', align='left', line_spacing=1.0):
    """
    在Word文档中添加一个段落，并设置文本样式和对齐方式

    :param document (docx.document.Document): 要添加段落的文档对象
    :param text (str): 要添加的文本内容
    :param indent (int): 首行缩进（单位：厘米）
    :param size (int): 字体大小
    :param font_name (str): 字体名称
    :param align (str): 对齐方式，可选值为'left'（左对齐）或'center'（居中）
    :param line_spacing (float): 行距（单位：磅）
    """
    p = document.add_paragraph()
    p.paragraph_format.first_line_indent = Cm(indent)
    run = p.add_run(text)
    run.font.size = Pt(size)
    run.font.name = font_name
    run.element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
    p.paragraph_format.line_spacing = line_spacing

    if align == 'left':
        p.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT
    elif align == 'center':
        p.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    elif align == 'justify':
        p.alignment = WD_PARAGRAPH_ALIGNMENT.JUSTIFY

    if '中文' in text:  # 如果文本中包含中文，则设置中文字体样式
        run.element.rPr.rFonts.set(qn('w:eastAsia'), font_name)


def write_dataframe_to_word(document, dataframe, table_name, font_name=u'宋体'):
    """
    将数据框写入到Word文档中
    :param document: 要添加段落的文档对象
    :param dataframe: 数据框
    :param table_name: 表名称
    :param font_name: 字体格式
    :return:
    """
    p = document.add_paragraph()
    p.paragraph_format.first_line_indent = Cm(1)
    run = p.add_run(table_name)
    run.font.size = Pt(16)
    run.font.name = font_name
    run.element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
    p.paragraph_format.line_spacing = 1

    # 创建一个表格，并设置行数和列数
    table = document.add_table(rows=dataframe.shape[0] + 1, cols=dataframe.shape[1])
    table.style = 'Table Grid'

    # 填充表头内容
    for i in range(dataframe.shape[1]):
        cell = table.cell(0, i)
        r = cell.paragraphs[0].add_run(dataframe.columns[i])
        r.font.name = font_name
        r._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
        cell.paragraphs[0].style.font.size = Pt(11)
        cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

        r._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)

    # 填充数据内容
    for i in range(dataframe.shape[0]):
        for j in range(dataframe.shape[1]):
            cell = table.cell(i + 1, j)
            cell.text = str(dataframe.iloc[i, j])
            cell.paragraphs[0].style.font.size = Pt(11)
            cell.paragraphs[0].style.font.name = font_name
            cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER


def set_cell_text_style(table, row, col, text, font_name=u'宋体', bold=True, align='center'):
    """
    设置单元格的文本样式
    :param table: 表名称
    :param row: 行
    :param col: 列
    :param text: 单元格名称
    :param font_name: 字体
    :param bold: 是否加粗
    :param align: 格式居中
    :return:
    """
    cell = table.cell(row, col)
    cell.text = ''

    r = cell.paragraphs[0].add_run(text)
    r.font.name = font_name
    r._element.rPr.rFonts.set(qn('w:eastAsia'), font_name)
    r.font.bold = bold

    if align == 'center':
        cell.paragraphs[0].alignment = WD_PARAGRAPH_ALIGNMENT.CENTER

    table.cell(row, col).vertical_alignment = WD_ALIGN_VERTICAL.CENTER