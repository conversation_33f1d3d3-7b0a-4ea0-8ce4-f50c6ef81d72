# Generated by Django 3.2.12 on 2025-04-23 15:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0013_otherproduct_person_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherAuditPersonTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_serial_code', models.CharField(blank=True, max_length=200, null=True, unique=True, verbose_name='产品系列编码')),
                ('name', models.CharField(blank=True, max_length=512, null=True, verbose_name='审核人员姓名')),
                ('workload_target', models.FloatField(blank=True, null=True, verbose_name='工作量指标')),
            ],
            options={
                'verbose_name': '理赔-审核人员产品工作量指标',
                'verbose_name_plural': '理赔-审核人员产品工作量指标',
                'db_table': 'other_audit_person_target',
            },
        ),
    ]
