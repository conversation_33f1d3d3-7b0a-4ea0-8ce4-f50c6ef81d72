import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, sum_or_combine, simplify_replace
from utils.st import query_sql,text_write,empty_line

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        ps.name AS product_set_name,
        ps.code AS product_set_code,
        DATE_FORMAT(MIN(p.sale_from), '%Y-%m-%d') AS sale_from,
        DATE_FORMAT(MIN(p.pre_sale_from), '%Y-%m-%d') AS pre_sale_from
    FROM
        product_set ps
    JOIN
        product p ON p.product_set_id = ps.id AND p.main = 1
    WHERE
        LEFT(ps.code, 10) in ('rizhao_nxb','dezhou_hmb','binzhou_yh')
    GROUP BY
        ps.name
    ORDER BY
        ps.name DESC;

        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_pay_channel(product_set_code, end_datetime,sql = query_sql('SQL_JKX_MAIN_SALE')):
    """
    获取保司物料的传播情况，包括阅读和转发
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售开始时间
    :return:
    """
    data = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code, end_datetime=end_datetime),
                               ttl=0)
    # 不统计团单
    data = data.query("is_personal==1")
    # code部分超过6位，取前6位
    data['area_code'] = data['area_code'].apply(lambda x: x[:6] if x is not None else x)
    data['pay_channel'].fillna('其他支付', inplace=True)
    # area_code为空的置为999999，area_name为空的置为'其他'
    data.fillna({'area_code': '000000', 'area_name': '其他'}, inplace=True)
    # # 地区合并，地区、医保编码不一致，强制调平
    data.loc[data['area_name'] == '天衢新区', 'area_code'] = '378403'
    data.loc[data['area_name'] == '德城区', 'area_code'] = '378402'
    data.loc[data['area_name'] == '陵城区', 'area_code'] = '378421'
    data.loc[data['area_name'] == '宁津县', 'area_code'] = '378422'
    data.loc[data['area_name'] == '庆云县', 'area_code'] = '378423'
    data.loc[data['area_name'] == '临邑县', 'area_code'] = '378424'
    data.loc[data['area_name'] == '齐河县', 'area_code'] = '378425'
    data.loc[data['area_name'] == '平原县', 'area_code'] = '378426'
    data.loc[data['area_name'] == '夏津县', 'area_code'] = '378427'
    data.loc[data['area_name'] == '武城县', 'area_code'] = '378428'
    data.loc[data['area_name'] == '乐陵市', 'area_code'] = '378481'
    data.loc[data['area_name'] == '禹城市', 'area_code'] = '378482'

    sql_dw = """
    select * from public_area_base_insure where product_set_code = '{product_set_code}' 
    """
    df_area = CONNECTOR_DW.query(sql_dw.format(product_set_code=product_set_code), ttl=0)
    df_area = df_area[['count', 'code', 'name']].rename(
        columns={'code': 'area_code', 'count': 'base_insure'})
    data_offline = data.query("is_online==0")
    data_online = data.query("is_online==1")
    # 线上销售
    df_area_online = data_online.groupby(['area_code', 'area_name', 'pay_channel']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index()
    df_area_online['seller'] = '线上'
    df_area_online = pd.merge(df_area_online, df_area, how='outer', on='area_code')
    # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
    df_area_online['base_insure'] = df_area_online['base_insure'].fillna(0)
    mask = (df_area_online['base_insure'] == 0) & (df_area_online['area_name'] != '其他')
    df_area_online.loc[mask, 'area_name'] = '其他'

    #线下销售
    df_area_total = data_offline.groupby(['seller', 'area_code', 'area_name', 'pay_channel']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index()
    df_area_total = pd.merge(df_area_total, df_area, how='outer', on='area_code')
    # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
    df_area_total['base_insure'] = df_area_total['base_insure'].fillna(0)
    mask = (df_area_total['base_insure'] == 0) & (df_area_total['area_name'] != '其他')
    df_area_total.loc[mask, 'area_name'] = '其他'
    # 删选有效列
    df_area_total = pd.concat([df_area_online, df_area_total], ignore_index=True)
    df_area_total = df_area_total[['seller', 'area_name', 'pay_channel', 'count', 'amount']]

    simplify_replace(df_area_total, 'pay_channel',
                     {'WECHAT_PAY': '微信支付', 'MEDICARE': '个账支付', 'UNION_PAY': '银联支付', 'ALIPAY': '支付宝支付',
                      'DIGITAL': '数字货币'},'其他支付')
    df_area_total.rename(columns={'seller': '保司', 'area_name': '地区', 'count': '人数', 'amount': '保费'},
                         inplace=True)
    # 使用pivot_table方法将数据透视
    df_pivot = df_area_total.pivot_table(index=['保司', '地区'],columns='pay_channel',values=['人数', '保费'],aggfunc='sum')
    # 重置索引
    df_pivot = df_pivot.reset_index()

    # 重命名列
    df_pivot.columns = [f'{col[1]}-{col[0]}' if col[1] else col[0] for col in df_pivot.columns.values]
    df_pivot.fillna(0, inplace=True)
    df_pivot.sort_values(by=['保司', '地区'], inplace=True)
    return df_pivot


def get_medicare_info(product_set_code, end_datetime,sql = query_sql('SQL_JKX_MAIN_SALE')):
    """
    获取保司物料的传播情况，包括阅读和转发
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售开始时间
    :return:
    """
    data = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code, end_datetime=end_datetime),
                               ttl=0)
    # 不统计团单
    data = data.query("is_personal==1")
    # code部分超过6位，取前6位
    data['area_code'] = data['area_code'].apply(lambda x: x[:6] if x is not None else x)
    data['medicare_type'].fillna('其他', inplace=True)
    # area_code为空的置为999999，area_name为空的置为'其他'
    data.fillna({'area_code': '000000', 'area_name': '其他'}, inplace=True)
    # # 地区合并，地区、医保编码不一致，强制调平
    data.loc[data['area_name'] == '天衢新区', 'area_code'] = '378403'
    data.loc[data['area_name'] == '德城区', 'area_code'] = '378402'
    data.loc[data['area_name'] == '陵城区', 'area_code'] = '378421'
    data.loc[data['area_name'] == '宁津县', 'area_code'] = '378422'
    data.loc[data['area_name'] == '庆云县', 'area_code'] = '378423'
    data.loc[data['area_name'] == '临邑县', 'area_code'] = '378424'
    data.loc[data['area_name'] == '齐河县', 'area_code'] = '378425'
    data.loc[data['area_name'] == '平原县', 'area_code'] = '378426'
    data.loc[data['area_name'] == '夏津县', 'area_code'] = '378427'
    data.loc[data['area_name'] == '武城县', 'area_code'] = '378428'
    data.loc[data['area_name'] == '乐陵市', 'area_code'] = '378481'
    data.loc[data['area_name'] == '禹城市', 'area_code'] = '378482'

    sql_dw = """
        select * from public_area_base_insure where product_set_code = '{product_set_code}' 
        """
    df_area = CONNECTOR_DW.query(sql_dw.format(product_set_code=product_set_code), ttl=0)
    df_area = df_area[['count', 'code', 'name']].rename(
        columns={'code': 'area_code', 'count': 'base_insure'})

    data_offline = data.query("is_online==0")
    data_online = data.query("is_online==1")
    # 线上销售
    df_area_online = data_online.groupby(['area_code', 'area_name', 'medicare_type']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index()
    df_area_online['seller'] = '线上'
    df_area_online = pd.merge(df_area_online, df_area, how='outer', on='area_code')
    # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
    df_area_online['base_insure'] = df_area_online['base_insure'].fillna(0)
    mask = (df_area_online['base_insure'] == 0) & (df_area_online['area_name'] != '其他')
    df_area_online.loc[mask, 'area_name'] = '其他'

    df_area_total = data_offline.groupby(['seller', 'area_code', 'area_name', 'medicare_type']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index()
    df_area_total = pd.merge(df_area_total, df_area, how='outer', on='area_code')
    # 特殊处理，如果没有基本医疗人数，且name不是其他，则name改成其他
    df_area_total['base_insure'] = df_area_total['base_insure'].fillna(0)
    mask = (df_area_total['base_insure'] == 0) & (df_area_total['area_name'] != '其他')
    df_area_total.loc[mask, 'area_name'] = '其他'
    # 删选有效列
    df_area_total=pd.concat([df_area_online, df_area_total], ignore_index=True)
    df_area_total = df_area_total[['seller', 'area_name', 'medicare_type', 'count', 'amount']]

    simplify_replace(df_area_total, 'medicare_type',{'EMPLOYEE': '职工医保', 'RESIDENT': '居民医保'})
    df_area_total.rename(columns={'seller': '保司', 'area_name': '地区', 'count': '人数', 'amount': '保费'},
                         inplace=True)
    # 使用pivot_table方法将数据透视
    df_pivot = df_area_total.pivot_table(index=['保司', '地区'], columns='medicare_type', values=['人数', '保费'],
                                         aggfunc='sum')
    # 重置索引
    df_pivot = df_pivot.reset_index()

    # 重命名列
    df_pivot.columns = [f'{col[1]}-{col[0]}' if col[1] else col[0] for col in df_pivot.columns.values]
    df_pivot.fillna(0, inplace=True)
    df_pivot.sort_values(by=['保司', '地区'], inplace=True)
    return df_pivot

def main():
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("分保司分地区统计报表")
    st.text("只统计个单数据")
    product_info = get_product_code()
    # 选择日期
    sale_from = datetime.date(2021, 1, 7)
    sale_until = datetime.date.today()
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[0]
                sale_date = min(product_info[product_info['product_set_name'] == column_name]['sale_from'].values[0],
                                product_info[product_info['product_set_name'] == column_name]['pre_sale_from'].values[
                                    0])
            else:
                product_set_code = None
                sale_date = '2000-01-01'
        with cols[1]:
            end_date = st.date_input('截止日期', min_value=sale_from, max_value=sale_until, value=sale_until,
                                     key='end_date')

        st.divider()
        if st.button('查询'):
            with st.spinner('查询中...'):
                text_write("分支付方式统计报表")
                empty_line(1)
                df = get_pay_channel(product_set_code, end_date.strftime('%Y-%m-%d') + ' 23:59:59')
                st.dataframe(df, hide_index=True, use_container_width=True)

                text_write("分医保类型统计报表")
                df_medicare = get_medicare_info(product_set_code, end_date.strftime('%Y-%m-%d') + ' 23:59:59')
                st.dataframe(df_medicare, hide_index=True, use_container_width=True)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
