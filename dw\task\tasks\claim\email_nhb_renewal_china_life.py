import logging
from datetime import datetime

import pandas as pd
import os
import pymysql
import idna
import warnings
from dw import settings
from django.core.mail import EmailMessage
from transfrom.utils.utils import  query_sql

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


TO = ['<EMAIL>']
CC= ['<EMAIL>']

DB = settings.DATABASES['jkx']

def get_connection():
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                                user=DB["USER"],
                                password=DB["PASSWORD"], database=DB["NAME"])
    return conn


def get_nhb_renewal_china_life():
    """
    获取健康险日度销售数据，包括日期、医保类型、是否线上、是否个人、支付方式、区域编码、代理人、渠道来源、销售金额、销售件数、产品名称
    """
    df_nhb_renewal_china_life = pd.read_sql(query_sql('SQL_RENEWAL_CHINA_LIFE'),get_connection())
    return df_nhb_renewal_china_life


def email_nhb_renewal_china_life():
    date = datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'人寿续保签约情况_{date}.xlsx')
    df_nhb_renewal_china_life = get_nhb_renewal_china_life()
    df_nhb_renewal_china_life.to_excel(path, index=False)

    mail = EmailMessage(
        subject=f'每日统计_人寿续保签约情况_{date}',
        body='见附件',
        to=TO,
        cc=CC
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)

