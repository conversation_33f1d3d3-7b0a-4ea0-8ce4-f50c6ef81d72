from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    """
    为字段添加数据库注释的辅助函数
    只在Django 4.2+版本中添加db_comment参数
    """
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class PublicTableInfo(BaseModel):
    """
    表信息表，存储各个表的基本信息和元数据
    """
    database_id = models.IntegerField(
        blank=True,
        null=True,
        verbose_name='所属数据库ID',
        help_text='所属数据库ID，用于关联public_database_info.id',
        **_db_comment_kwarg('所属数据库ID，用于关联public_database_info.id')
    )
    database_name = models.CharField(
        max_length=64,
        default='default',
        verbose_name='所属数据库名称',
        help_text='所属数据库名称（冗余字段）',
        **_db_comment_kwarg('所属数据库名称（冗余字段）')
    )
    name = models.CharField(
        max_length=128,
        verbose_name='表名',
        help_text='表名',
        **_db_comment_kwarg('表名')
    )
    comment = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        verbose_name='表注释',
        help_text='表中文名或注释',
        **_db_comment_kwarg('表中文名或注释')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='表描述',
        help_text='表详细描述',
        **_db_comment_kwarg('表详细描述')
    )
    data_source = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name='数据来源',
        help_text='数据来源，表示数据的实际来源',
        **_db_comment_kwarg('数据来源，表示数据的实际来源')
    )
    update_frequency = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name='更新频率',
        help_text='更新频率',
        **_db_comment_kwarg('更新频率')
    )
    type = models.CharField(
        max_length=32,
        default='BASE_TABLE',
        verbose_name='表类型',
        help_text='表类型，如BASE_TABLE、VIEW等',
        **_db_comment_kwarg('表类型，如BASE_TABLE、VIEW等')
    )
    is_deprecated = models.BooleanField(
        default=False,
        verbose_name='是否废弃',
        help_text='是否不再使用，TRUE表示已废弃',
        **_db_comment_kwarg('是否不再使用，TRUE表示已废弃')
    )
    unique_key_fields = models.TextField(
        blank=True,
        null=True,
        verbose_name='唯一索引字段',
        help_text='唯一索引字段组合，多个组合用分号分隔',
        **_db_comment_kwarg('唯一索引字段组合，多个组合用分号分隔')
    )
    engine = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        verbose_name='存储引擎',
        help_text='存储引擎，如InnoDB、MyISAM等',
        **_db_comment_kwarg('存储引擎，如InnoDB、MyISAM等')
    )
    charset = models.CharField(
        max_length=32,
        blank=True,
        null=True,
        verbose_name='字符集',
        help_text='字符集',
        **_db_comment_kwarg('字符集')
    )
    collation = models.CharField(
        max_length=64,
        blank=True,
        null=True,
        verbose_name='排序规则',
        help_text='排序规则',
        **_db_comment_kwarg('排序规则')
    )

    class Meta:
        db_table = 'public_table_info'
        verbose_name = '表信息表'
        verbose_name_plural = verbose_name
        unique_together = [['database_id', 'name'], ['database_name', 'name']]
        indexes = [
            models.Index(fields=['database_id'], name='idx_pub_tbl_database_id'),
            models.Index(fields=['database_name'], name='idx_pub_tbl_database_name'),
            models.Index(fields=['data_source'], name='idx_pub_tbl_data_source'),
            models.Index(fields=['is_deprecated'], name='idx_pub_tbl_deprecated'),
            models.Index(fields=['name'], name='idx_pub_tbl_name'),
            models.Index(fields=['type'], name='idx_pub_tbl_type'),
        ]

    def __str__(self):
        return f"{self.database_name}.{self.name}"


# 动态为 Meta 添加 db_table_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    PublicTableInfo.Meta.db_table_comment = '表信息表，存储各个表的基本信息和元数据'
