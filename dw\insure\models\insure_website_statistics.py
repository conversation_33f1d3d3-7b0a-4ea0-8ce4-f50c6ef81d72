#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保险产品网站统计数据模型
"""

import django
from django.db import models
from common.models import BaseModel
from django.db.models import constraints
from utils.column_name import db_comment_kwarg


class InsureWebsiteStatistics(BaseModel):
    """
    保险产品网站统计数据表
    存储网站的总体统计指标，如总浏览量、访问次数、访客数等
    """
    
    # 基本标识字段
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码', **db_comment_kwarg('产品集编码'))
    statistics_date = models.DateField(blank=True, null=True, verbose_name='统计日期', **db_comment_kwarg('统计日期'))
    data_source = models.CharField(max_length=32, default='umami', verbose_name='数据来源', **db_comment_kwarg('数据来源'))
    
    # 核心统计指标
    total_page_views = models.BigIntegerField(blank=True, null=True, verbose_name='总浏览量', **db_comment_kwarg('总浏览量'))
    total_visits = models.BigIntegerField(blank=True, null=True, verbose_name='总访问次数', **db_comment_kwarg('总访问次数'))
    total_visitors = models.BigIntegerField(blank=True, null=True, verbose_name='总访客数', **db_comment_kwarg('总访客数'))
    bounce_rate = models.DecimalField(max_digits=5, decimal_places=2, blank=True, null=True, verbose_name='跳出率', **db_comment_kwarg('跳出率'))
    avg_visit_duration = models.DecimalField(max_digits=20, decimal_places=2, blank=True, null=True, verbose_name='平均访问时长(秒)', **db_comment_kwarg('平均访问时长(秒)'))
    
    # 备注信息
    remarks = models.TextField(blank=True, null=True, verbose_name='备注', **db_comment_kwarg('备注'))

    class Meta:
        db_table = 'insure_website_statistics'
        verbose_name = '销售网站埋点统计'
        verbose_name_plural = verbose_name

        # Django 4.2+会自动识别这个属性，低版本会忽略
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name

        # 唯一性约束：同一产品、同一日期、同一数据源只能有一条记录
        constraints = [
            constraints.UniqueConstraint(
                fields=['product_set_code', 'statistics_date', 'data_source'],
                name='unique_insure_website_statistics'
            ),
        ]
        
        # 索引优化
        indexes = [
            models.Index(fields=['product_set_code', 'statistics_date']),
            models.Index(fields=['statistics_date']),
            models.Index(fields=['product_set_code']),
        ]

    def __str__(self):
        return f"{self.product_set_code} - {self.statistics_date} - PV:{self.total_page_views}"

    @staticmethod
    def format_duration_from_seconds(total_seconds):
        """
        将秒数转换为可读的时长格式

        :param total_seconds: 总秒数
        :return: 格式化的时长字符串
        """
        if total_seconds is None or total_seconds < 0:
            return "0秒"

        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60

        if hours > 0:
            return f"{hours}小时{minutes}分{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分{seconds}秒"
        else:
            return f"{seconds}秒"


