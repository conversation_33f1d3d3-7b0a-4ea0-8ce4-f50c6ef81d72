from django.db import models
from common.models import BaseModel


class SystemDict(BaseModel):
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='字典名称')
    type = models.CharField(max_length=128, blank=True, null=True, verbose_name='字典类型')
    status = models.IntegerField(default=1,blank=True, null=True, verbose_name='状态（0正常 1停用）')
    description = models.TextField(blank=True, null=True, verbose_name='描述说明')

    class Meta:
        db_table = 'system_dict'
        verbose_name = '数据字典表'
        verbose_name_plural = verbose_name
