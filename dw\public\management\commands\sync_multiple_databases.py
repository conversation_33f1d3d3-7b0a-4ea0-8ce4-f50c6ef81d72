"""
多数据库同步管理命令
根据指定的业务规则同步多个数据库的元数据信息
"""
import logging
from django.core.management.base import BaseCommand
from django.db import connections, transaction
from django.conf import settings
from django.utils import timezone
from public.models import PublicDatabaseInfo, PublicTableInfo, PublicColumnInfo, PublicIndexInfo
from utils.field_translation import FieldTranslationService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = '同步多个数据库的元数据信息到数据字典表'

    def add_arguments(self, parser):
        parser.add_argument(
            '--databases',
            type=str,
            nargs='*',
            default=['jkx', 'nhb', 'dw', 'umami', 'tracker'],
            help='指定要同步的数据库连接名称列表 (默认: jkx nhb dw umami tracker)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅预览操作，不实际执行数据库更新'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制更新已存在的记录'
        )
        parser.add_argument(
            '--enable-ai-translation',
            action='store_true',
            help='启用AI智能翻译功能，为没有中文注释的字段生成AI建议'
        )

    def get_default_column_comment(self, column_name, table_name=None, data_type=None, enable_ai=False):
        """
        根据字段名称返回默认的中文注释
        如果启用AI翻译且没有预定义注释，则使用AI生成建议
        """
        # 常见字段名称的中文映射
        default_comments = {
            'create_time': '创建时间',
            'created_time': '创建时间',
            'created_at': '创建时间',
            'create_date': '创建日期',
            'created_date': '创建日期',
            'update_time': '更新时间',
            'updated_time': '更新时间',
            'updated_at': '更新时间',
            'update_date': '更新日期',
            'updated_date': '更新日期',
            'delete_time': '删除时间',
            'deleted_time': '删除时间',
            'deleted_at': '删除时间',
            'delete_date': '删除日期',
            'deleted_date': '删除日期',
            'modify_time': '修改时间',
            'modified_time': '修改时间',
            'modified_at': '修改时间',
            'modify_date': '修改日期',
            'modified_date': '修改日期',
            'id': 'ID',
            'user_id': '用户ID',
            'status': '状态',
            'is_deleted': '是否删除',
            'is_active': '是否激活',
            'is_enabled': '是否启用',
            'sort': '排序',
            'sort_order': '排序',
            'order_num': '排序号',
            'remark': '备注',
            'remarks': '备注',
            'note': '备注',
            'notes': '备注',
            'description': '描述',
            'desc': '描述',
        }

        # 转换为小写进行匹配
        column_name_lower = column_name.lower()
        predefined_comment = default_comments.get(column_name_lower, '')

        # 如果有预定义注释，直接返回
        if predefined_comment:
            return predefined_comment, None, None, None

        # 如果启用AI翻译且没有预定义注释，使用AI生成
        if enable_ai and not predefined_comment:
            try:
                translation_service = FieldTranslationService()
                from utils.field_translation import FieldInfo

                field_info = FieldInfo(
                    field_name=column_name,
                    table_name=table_name or '',
                    data_type=data_type or '',
                    database_name=''
                )

                result = translation_service.translate_field(field_info)
                if result.success:
                    return '', result.chinese_name, result.confidence, timezone.now()
                else:
                    self.stdout.write(
                        self.style.WARNING(f"AI翻译失败 {table_name}.{column_name}: {result.error_message}")
                    )
                    return '', None, None, None

            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f"AI翻译异常 {table_name}.{column_name}: {str(e)}")
                )
                return '', None, None, None

        return '', None, None, None

    def handle(self, *args, **options):
        databases = options['databases']
        dry_run = options['dry_run']
        force_update = options['force']
        enable_ai_translation = options['enable_ai_translation']

        self.stdout.write(f"开始同步数据库: {', '.join(databases)}")

        if dry_run:
            self.stdout.write(self.style.WARNING("预览模式：不会实际修改数据库"))

        if enable_ai_translation:
            self.stdout.write(self.style.SUCCESS("AI智能翻译功能已启用"))

        try:
            with transaction.atomic():
                for database_name in databases:
                    self.stdout.write(f"\n=== 同步数据库: {database_name} ===")
                    self.sync_database(database_name, dry_run, force_update, enable_ai_translation)

                if not dry_run:
                    self.stdout.write(
                        self.style.SUCCESS(f"\n成功同步所有数据库: {', '.join(databases)}")
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"同步过程中发生错误: {str(e)}")
            )
            logger.exception("多数据库同步失败")

    def sync_database(self, database_name, dry_run=False, force_update=False, enable_ai_translation=False):
        """同步单个数据库"""
        try:
            # 检查数据库连接是否存在
            if database_name not in connections.databases:
                self.stdout.write(
                    self.style.ERROR(f"数据库连接 '{database_name}' 不存在于配置中")
                )
                return

            # 获取数据库信息（不更新）
            try:
                db_info = PublicDatabaseInfo.objects.get(name=database_name)
                self.stdout.write(f"找到已存在的数据库记录: {database_name}")
            except PublicDatabaseInfo.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f"数据库 '{database_name}' 在数据字典中不存在，请先手动创建")
                )
                return

            if not dry_run:
                # 同步表信息
                self.sync_tables(db_info, database_name, force_update, enable_ai_translation)
            else:
                self.preview_sync(database_name)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"同步数据库 '{database_name}' 时发生错误: {str(e)}")
            )
            logger.exception(f"同步数据库 {database_name} 失败")

    def should_skip_table(self, table_name, database_name):
        """判断是否应该跳过表"""
        # dw数据库跳过系统保留表
        if database_name == 'dw':
            skip_prefixes = ['auth_', 'database_', 'django_']
            for prefix in skip_prefixes:
                if table_name.startswith(prefix):
                    return True

        # 跳过temp_开头的临时表
        if table_name.startswith('temp_') or table_name.startswith('tmp_') or table_name in ['temp', 'tmp']:
            return True

        return False

    def sync_tables(self, db_info, database_name, force_update, enable_ai_translation=False):
        """同步表信息"""
        connection = connections[database_name]
        
        with connection.cursor() as cursor:
            # 获取数据库中的所有表
            db_tables = self.get_tables_info(cursor)
            db_table_names = set()
            
            for table_info in db_tables:
                table_name = table_info['table_name']
                
                # 检查是否应该跳过
                if self.should_skip_table(table_name, database_name):
                    continue
                    
                db_table_names.add(table_name)
                
                # 根据唯一键查找表记录
                try:
                    table_obj = PublicTableInfo.objects.get(
                        database_id=db_info.id,
                        name=table_name
                    )
                    # 表已存在，根据规则更新（不能修改表名称和中文名）
                    if force_update:
                        # 不更新 name（表名称）和 comment（表的中文名称），保持原有值
                        # 只更新技术属性
                        table_obj.type = table_info.get('table_type', 'BASE_TABLE')
                        table_obj.engine = table_info.get('engine', '')
                        table_obj.charset = table_info.get('charset', '')
                        table_obj.collation = table_info.get('collation', '')
                        table_obj.save()
                        self.stdout.write(f"更新表记录: {table_name} (保持原有表名和中文名)")
                    else:
                        self.stdout.write(f"表已存在: {table_name}")
                        
                except PublicTableInfo.DoesNotExist:
                    # 表不存在，新增（使用数据库中的注释作为初始中文名）
                    table_obj = PublicTableInfo.objects.create(
                        database_id=db_info.id,
                        database_name=db_info.name,
                        name=table_name,
                        comment=table_info.get('table_comment', ''),  # 新增时使用数据库注释
                        type=table_info.get('table_type', 'BASE_TABLE'),
                        engine=table_info.get('engine', ''),
                        charset=table_info.get('charset', ''),
                        collation=table_info.get('collation', ''),
                    )
                    self.stdout.write(f"新增表记录: {table_name} (使用数据库注释: '{table_info.get('table_comment', '')}')")
                
                # 同步字段信息
                self.sync_columns(cursor, table_obj, table_name, force_update, enable_ai_translation)
                
                # 同步索引信息
                self.sync_indexes(cursor, table_obj, table_name, force_update)
            
            # 处理需要删除的表（数据库中不存在但数据字典中存在的表）
            existing_tables = PublicTableInfo.objects.filter(database_id=db_info.id)
            for table_obj in existing_tables:
                if table_obj.name not in db_table_names and not self.should_skip_table(table_obj.name, database_name):
                    # 标记为废弃而不是删除
                    table_obj.is_deprecated = True
                    table_obj.save()
                    self.stdout.write(f"标记表为废弃: {table_obj.name}")

    def sync_columns(self, cursor, table_obj, table_name, force_update, enable_ai_translation=False):
        """同步字段信息"""
        # 获取数据库中的字段信息
        db_columns = self.get_columns_info(cursor, table_name)
        db_column_names = set()
        
        for col_info in db_columns:
            column_name = col_info['column_name']
            db_column_names.add(column_name)
            
            # 处理字段属性
            is_primary_key = col_info['column_key'] == 'PRI'
            is_unique = col_info['column_key'] == 'UNI'
            is_auto_increment = 'auto_increment' in (col_info['extra'] or '').lower()
            is_nullable = col_info['is_nullable'] == 'YES'
            
            try:
                column_obj = PublicColumnInfo.objects.get(
                    table_id=table_obj.id,
                    name=column_name
                )
                # 字段已存在，根据特殊规则更新
                if force_update:
                    updated = False

                    # 检查字段名是否不同（这种情况理论上不应该发生，因为是按name查找的）
                    if column_obj.name != column_name:
                        column_obj.name = column_name
                        updated = True
                        self.stdout.write(f"  字段名称变更: {table_name}.{column_obj.name} -> {column_name}")

                    # 注释更新规则：如果字段表中有注释但数据库没有，不进行更新
                    db_comment = col_info.get('column_comment', '')
                    if column_obj.comment and not db_comment:
                        # 保留原有注释，不更新
                        self.stdout.write(f"  保留字段注释: {table_name}.{column_name} (数据库无注释)")
                    else:
                        # 如果数据库和字段表都没有注释，尝试使用默认注释或AI翻译
                        final_comment = db_comment
                        if not final_comment and not column_obj.comment:
                            default_comment, ai_suggested_name, ai_confidence_score, ai_translation_time = self.get_default_column_comment(
                                column_name, table_name, col_info['data_type'], enable_ai_translation
                            )
                            if default_comment:
                                final_comment = default_comment
                                self.stdout.write(f"  应用默认注释: {table_name}.{column_name} -> '{default_comment}'")
                            elif ai_suggested_name:
                                # 设置AI翻译字段
                                column_obj.ai_suggested_name = ai_suggested_name
                                column_obj.ai_confidence_score = ai_confidence_score
                                column_obj.ai_translation_time = ai_translation_time
                                column_obj.ai_translation_status = 'translated'
                                updated = True
                                self.stdout.write(f"  AI翻译建议: {table_name}.{column_name} -> '{ai_suggested_name}' (置信度: {ai_confidence_score:.2f})")

                        if column_obj.comment != final_comment:
                            column_obj.comment = final_comment
                            updated = True

                    # 其他字段属性正常更新
                    if (column_obj.data_type != col_info['data_type'] or
                        column_obj.type != col_info['column_type'] or
                        column_obj.max_length != col_info.get('character_maximum_length') or
                        column_obj.numeric_precision != col_info.get('numeric_precision') or
                        column_obj.numeric_scale != col_info.get('numeric_scale') or
                        column_obj.is_nullable != is_nullable or
                        column_obj.default != col_info.get('column_default') or
                        column_obj.is_auto_increment != is_auto_increment or
                        column_obj.is_primary_key != is_primary_key or
                        column_obj.is_unique != is_unique or
                        column_obj.ordinal_position != col_info['ordinal_position']):

                        column_obj.data_type = col_info['data_type']
                        column_obj.type = col_info['column_type']
                        column_obj.max_length = col_info.get('character_maximum_length')
                        column_obj.numeric_precision = col_info.get('numeric_precision')
                        column_obj.numeric_scale = col_info.get('numeric_scale')
                        column_obj.is_nullable = is_nullable
                        column_obj.default = col_info.get('column_default')
                        column_obj.is_auto_increment = is_auto_increment
                        column_obj.is_primary_key = is_primary_key
                        column_obj.is_unique = is_unique
                        column_obj.ordinal_position = col_info['ordinal_position']
                        updated = True

                    if updated:
                        column_obj.save()
                        self.stdout.write(f"  更新字段记录: {table_name}.{column_name}")
                    else:
                        self.stdout.write(f"  字段无变化: {table_name}.{column_name}")
                else:
                    self.stdout.write(f"  字段已存在: {table_name}.{column_name}")
                    
            except PublicColumnInfo.DoesNotExist:
                # 字段不存在，新增
                # 确定字段注释：优先使用数据库注释，如果没有则尝试使用默认注释或AI翻译
                db_comment = col_info.get('column_comment', '')
                final_comment = db_comment
                ai_suggested_name = None
                ai_confidence_score = None
                ai_translation_time = None
                ai_translation_status = 'pending'

                if not final_comment:
                    default_comment, ai_suggested_name, ai_confidence_score, ai_translation_time = self.get_default_column_comment(
                        column_name, table_name, col_info['data_type'], enable_ai_translation
                    )
                    if default_comment:
                        final_comment = default_comment
                        self.stdout.write(f"  新增字段使用默认注释: {table_name}.{column_name} -> '{default_comment}'")
                    elif ai_suggested_name:
                        ai_translation_status = 'translated'
                        self.stdout.write(f"  新增字段AI翻译建议: {table_name}.{column_name} -> '{ai_suggested_name}' (置信度: {ai_confidence_score:.2f})")

                column_obj = PublicColumnInfo.objects.create(
                    table_id=table_obj.id,
                    table_name=table_obj.name,
                    name=column_name,
                    comment=final_comment,
                    data_type=col_info['data_type'],
                    type=col_info['column_type'],
                    max_length=col_info.get('character_maximum_length'),
                    numeric_precision=col_info.get('numeric_precision'),
                    numeric_scale=col_info.get('numeric_scale'),
                    is_nullable=is_nullable,
                    default=col_info.get('column_default'),
                    is_auto_increment=is_auto_increment,
                    is_primary_key=is_primary_key,
                    is_unique=is_unique,
                    ordinal_position=col_info['ordinal_position'],
                    # AI翻译相关字段
                    ai_suggested_name=ai_suggested_name,
                    ai_translation_status=ai_translation_status,
                    ai_translation_time=ai_translation_time,
                    ai_confidence_score=ai_confidence_score,
                )
                self.stdout.write(f"  新增字段记录: {table_name}.{column_name}")
        
        # 删除数据库中不存在的字段
        existing_columns = PublicColumnInfo.objects.filter(table_id=table_obj.id)
        for column_obj in existing_columns:
            if column_obj.name not in db_column_names:
                column_obj.delete()
                self.stdout.write(f"  删除字段记录: {table_name}.{column_obj.name}")

    def sync_indexes(self, cursor, table_obj, table_name, force_update):
        """同步索引信息（正常处理）"""
        # 获取数据库中的索引信息
        db_indexes = self.get_indexes_info(cursor, table_name)
        db_index_names = set()
        
        for index_info in db_indexes.values():
            index_name = index_info['name']
            db_index_names.add(index_name)
            column_names = ','.join(index_info['columns'])
            
            try:
                index_obj = PublicIndexInfo.objects.get(
                    table_id=table_obj.id,
                    name=index_name
                )
                # 索引已存在，更新
                if force_update:
                    index_obj.type = index_info['type']
                    index_obj.is_unique = index_info['is_unique']
                    index_obj.is_primary = index_info['is_primary']
                    index_obj.column_names = column_names
                    index_obj.comment = index_info['comment']
                    index_obj.save()
                    self.stdout.write(f"  更新索引记录: {table_name}.{index_name}")
                else:
                    self.stdout.write(f"  索引已存在: {table_name}.{index_name}")
                    
            except PublicIndexInfo.DoesNotExist:
                # 索引不存在，新增
                index_obj = PublicIndexInfo.objects.create(
                    table_id=table_obj.id,
                    table_name=table_obj.name,
                    name=index_name,
                    type=index_info['type'],
                    is_unique=index_info['is_unique'],
                    is_primary=index_info['is_primary'],
                    column_names=column_names,
                    comment=index_info['comment'],
                )
                self.stdout.write(f"  新增索引记录: {table_name}.{index_name}")
        
        # 删除数据库中不存在的索引
        existing_indexes = PublicIndexInfo.objects.filter(table_id=table_obj.id)
        for index_obj in existing_indexes:
            if index_obj.name not in db_index_names:
                index_obj.delete()
                self.stdout.write(f"  删除索引记录: {table_name}.{index_obj.name}")

    def get_tables_info(self, cursor):
        """获取表信息"""
        sql = """
        SELECT
            table_name,
            COALESCE(table_comment, '') as table_comment,
            COALESCE(table_type, 'BASE TABLE') as table_type,
            COALESCE(engine, '') as engine,
            COALESCE(table_collation, '') as collation
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        ORDER BY table_name
        """

        cursor.execute(sql)
        columns = [col[0] for col in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]

        # 确保每个记录都有必要的字段
        for table_info in result:
            if 'table_name' not in table_info:
                table_info['table_name'] = table_info.get('TABLE_NAME', '')
            if 'table_comment' not in table_info:
                table_info['table_comment'] = table_info.get('TABLE_COMMENT', '')
            if 'table_type' not in table_info:
                table_info['table_type'] = table_info.get('TABLE_TYPE', 'BASE TABLE')
            if 'engine' not in table_info:
                table_info['engine'] = table_info.get('ENGINE', '')
            if 'collation' not in table_info:
                table_info['collation'] = table_info.get('TABLE_COLLATION', '')

        return result

    def get_columns_info(self, cursor, table_name):
        """获取字段信息"""
        sql = """
        SELECT
            column_name,
            COALESCE(column_comment, '') as column_comment,
            data_type,
            COALESCE(column_type, data_type) as column_type,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            is_nullable,
            column_default,
            COALESCE(extra, '') as extra,
            COALESCE(column_key, '') as column_key,
            ordinal_position
        FROM information_schema.columns
        WHERE table_schema = DATABASE() AND table_name = %s
        ORDER BY ordinal_position
        """

        cursor.execute(sql, [table_name])
        columns = [col[0] for col in cursor.description]
        result = [dict(zip(columns, row)) for row in cursor.fetchall()]

        # 确保每个记录都有必要的字段
        for col_info in result:
            if 'column_name' not in col_info:
                col_info['column_name'] = col_info.get('COLUMN_NAME', '')
            if 'column_comment' not in col_info:
                col_info['column_comment'] = col_info.get('COLUMN_COMMENT', '')
            if 'data_type' not in col_info:
                col_info['data_type'] = col_info.get('DATA_TYPE', 'varchar')
            if 'column_type' not in col_info:
                col_info['column_type'] = col_info.get('COLUMN_TYPE', col_info.get('data_type', 'varchar'))
            if 'character_maximum_length' not in col_info:
                col_info['character_maximum_length'] = col_info.get('CHARACTER_MAXIMUM_LENGTH')
            if 'numeric_precision' not in col_info:
                col_info['numeric_precision'] = col_info.get('NUMERIC_PRECISION')
            if 'numeric_scale' not in col_info:
                col_info['numeric_scale'] = col_info.get('NUMERIC_SCALE')
            if 'is_nullable' not in col_info:
                col_info['is_nullable'] = col_info.get('IS_NULLABLE', 'YES')
            if 'column_default' not in col_info:
                col_info['column_default'] = col_info.get('COLUMN_DEFAULT')
            if 'extra' not in col_info:
                col_info['extra'] = col_info.get('EXTRA', '')
            if 'column_key' not in col_info:
                col_info['column_key'] = col_info.get('COLUMN_KEY', '')
            if 'ordinal_position' not in col_info:
                col_info['ordinal_position'] = col_info.get('ORDINAL_POSITION', 1)

        return result

    def get_indexes_info(self, cursor, table_name):
        """获取索引信息"""
        sql = """
        SELECT 
            index_name,
            non_unique,
            column_name,
            seq_in_index,
            index_type,
            index_comment
        FROM information_schema.statistics 
        WHERE table_schema = DATABASE() AND table_name = %s
        ORDER BY index_name, seq_in_index
        """
        
        cursor.execute(sql, [table_name])
        index_data = cursor.fetchall()
        
        # 按索引名分组
        indexes = {}
        for row in index_data:
            index_name, non_unique, column_name, seq_in_index, index_type, index_comment = row
            
            if index_name not in indexes:
                indexes[index_name] = {
                    'name': index_name,
                    'is_unique': non_unique == 0,
                    'is_primary': index_name == 'PRIMARY',
                    'type': index_type or 'BTREE',
                    'comment': index_comment or '',
                    'columns': []
                }
            
            indexes[index_name]['columns'].append(column_name)
        
        return indexes

    def preview_sync(self, database_name):
        """预览同步操作"""
        connection = connections[database_name]
        
        with connection.cursor() as cursor:
            tables_info = self.get_tables_info(cursor)
            
            # 过滤掉需要跳过的表
            filtered_tables = []
            for table_info in tables_info:
                table_name = table_info['table_name']
                if not self.should_skip_table(table_name, database_name):
                    filtered_tables.append(table_info)
            
            self.stdout.write(f"[预览] 将同步 {len(filtered_tables)} 个表:")
            for table_info in filtered_tables:
                table_name = table_info['table_name']
                self.stdout.write(f"  - {table_name}")
                
                # 预览字段数量
                cursor.execute(
                    "SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = %s",
                    [table_name]
                )
                column_count = cursor.fetchone()[0]
                
                # 预览索引数量
                cursor.execute(
                    "SELECT COUNT(DISTINCT index_name) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = %s",
                    [table_name]
                )
                index_count = cursor.fetchone()[0]
                
                self.stdout.write(f"    字段数: {column_count}, 索引数: {index_count}")
            
            if database_name == 'dw':
                # 显示跳过的系统表
                skipped_tables = []
                for table_info in tables_info:
                    table_name = table_info['table_name']
                    if self.should_skip_table(table_name, database_name):
                        skipped_tables.append(table_name)
                
                if skipped_tables:
                    self.stdout.write(f"[预览] 将跳过 {len(skipped_tables)} 个系统表:")
                    for table_name in skipped_tables:
                        self.stdout.write(f"  - {table_name}")
