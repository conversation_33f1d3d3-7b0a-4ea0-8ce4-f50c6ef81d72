// 适配器文件，用于在Node.js环境中模拟浏览器环境中的window.__o函数

// 确保window对象有location属性
if (typeof window !== 'undefined' && !window.location) {
    window.location = {
        href: 'http://localhost',
        protocol: 'http:',
        host: 'localhost'
    };
}

// 使用node-fetch代替axios，避免浏览器环境依赖
const fetch = require('node-fetch');

// 模拟window.__o函数，根据模块ID返回相应的模块
function mockWindowO(moduleId) {
    // 如果模块ID是"7d92"，返回request和processResponse函数
    if (moduleId === "7d92") {
        return {
            // 实现request函数，使用node-fetch发送请求
            request: async function(options) {
                try {
                    console.log(`正在发送请求到: ${options.url}`);

                    // 准备fetch选项
                    const fetchOptions = {
                        method: options.method || 'GET',
                        headers: options.headers || {},
                        timeout: options.timeout || 30000
                    };

                    // 如果有请求体数据，添加到选项中
                    if (options.data) {
                        fetchOptions.body = JSON.stringify(options.data);
                    }

                    // 发送请求
                    const response = await fetch(options.url, fetchOptions);

                    // 解析响应
                    const data = await response.json();

                    // 返回与axios兼容的响应格式
                    return {
                        data: data,
                        status: response.status,
                        statusText: response.statusText,
                        headers: response.headers
                    };
                } catch (error) {
                    console.error('请求出错:', error.message);
                    throw error;
                }
            },

            // 实现processResponse函数，处理响应数据
            processResponse: function(response) {
                // 如果响应中包含data字段，直接返回
                if (response && response.data) {
                    return response.data;
                }
                return response;
            }
        };
    }

    // 如果请求的是其他模块ID，返回空对象
    console.warn(`未知的模块ID: ${moduleId}`);
    return {};
}

// 导出模拟的window.__o函数
module.exports = mockWindowO;
