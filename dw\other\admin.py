from django.contrib import admin

from other.models import OtherYbStatisticNhb, OtherProduct, OtherProductCode, OtherProductProton, OtherProductResponse, \
    OtherProductInsureType, OtherProductAuditPerson,OtherProductMedicalType,OtherManagementStaff,OtherSentryReplayFiles,\
    OtherStreamlitKeyMapping,OtherAuditPersonTarget


@admin.register(OtherYbStatisticNhb)
class OtherYbStatisticNhbAdmin(admin.ModelAdmin):
    list_display = ['id', 'product_code', 'type', 'is_post', 'create_time',
                    'update_time']

@admin.register(OtherManagementStaff)
class OtherManagementStaffAdmin(admin.ModelAdmin):
    list_display = ['product_set_code', 'short_name', 'agent_id','name','type','additional_info']

@admin.register(OtherProduct)
class OtherProductAdmin(admin.ModelAdmin):
    list_display = ['product_name', 'start_date', 'amount','person_number']


@admin.register(OtherProductCode)
class OtherProductCodeAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'conn', 'type']


@admin.register(OtherProductProton)
class OtherProductProtonAdmin(admin.ModelAdmin):
    list_display = ['product_name', 'total_amount', 'payout_ratio', 'apply_num', 'close_num', 'pay_num',
                    'past_symptom_pay_amount','past_symptom_pay_person', 'avg_pay_amount',
                    'max_pay_amount', 'avg_burden_reduce_ratio', 'max_reduce_ratio', 'avg_reduce_ratio',
                    'total_claim_amount', 'social_pay_amount', 'burden_amount']


@admin.register(OtherProductInsureType)
class OtherProductInsureTypeAdmin(admin.ModelAdmin):
    list_display = ['product_name', 'type', 'person_num']


@admin.register(OtherProductResponse)
class OtherProductResponseAdmin(admin.ModelAdmin):
    list_display = ['product_name', 'type', 'amount']


@admin.register(OtherProductAuditPerson)
class OtherProductAuditPersonAdmin(admin.ModelAdmin):
    list_display = ['name', 'code']


@admin.register(OtherProductMedicalType)
class OtherProductMedicalTypeAdmin(admin.ModelAdmin):
    list_display = ['product_name', 'medicare_type', 'person_num']


@admin.register(OtherSentryReplayFiles)
class OtherSentryReplayFilesAdmin(admin.ModelAdmin):
    list_display = ['id', 'file_name', 'product_set_code', 'type', 'order_list','description']
    list_filter = ('product_set_code',)


@admin.register(OtherStreamlitKeyMapping)
class OtherStreamlitKeyMappingAdmin(admin.ModelAdmin):
    list_display = ['product_serial_code', 'type', 'client_id','client_secret','url']
    list_filter = ('product_serial_code',)

@admin.register(OtherAuditPersonTarget)
class OtherAuditPersonTargetAdmin(admin.ModelAdmin):
    list_display = ['product_serial_code','name','is_review','workload_target']
    list_filter = ('product_serial_code',)