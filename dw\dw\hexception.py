def check_database_version_supported(self):
    """
    在这里修改 check_database_version_supported 方法以适配 MySQL 5.7
    """
    if (
        self.features.minimum_database_version is not None
        and self.get_database_version() < self.features.minimum_database_version
    ):
        db_version = ".".join(map(str, self.get_database_version()))
        min_db_version = ".".join(map(str, self.features.minimum_database_version))
