from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    """
    为字段添加数据库注释的辅助函数
    只在Django 4.2+版本中添加db_comment参数
    """
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class PublicDatabaseInfo(BaseModel):
    """
    数据库信息表，存储各个数据库的基本信息
    """
    name = models.CharField(
        max_length=64, 
        unique=True, 
        verbose_name='数据库名称', 
        help_text='数据库名称，唯一标识',
        **_db_comment_kwarg('数据库名称，唯一标识')
    )
    type = models.CharField(
        max_length=32, 
        default='MySQL', 
        verbose_name='数据库类型', 
        help_text='数据库类型，如MySQL、PostgreSQL等',
        **_db_comment_kwarg('数据库类型，如MySQL、PostgreSQL等')
    )
    description = models.TextField(
        blank=True, 
        null=True, 
        verbose_name='数据库描述', 
        help_text='数据库描述信息',
        **_db_comment_kwarg('数据库描述信息')
    )
    data_source = models.CharField(
        max_length=128, 
        blank=True, 
        null=True, 
        verbose_name='数据来源', 
        help_text='数据来源说明',
        **_db_comment_kwarg('数据来源说明')
    )
    update_frequency = models.CharField(
        max_length=64, 
        blank=True, 
        null=True, 
        verbose_name='更新频率', 
        help_text='更新频率，如实时更新、每日更新等',
        **_db_comment_kwarg('更新频率，如实时更新、每日更新等')
    )

    class Meta:
        db_table = 'public_database_info'
        verbose_name = '数据库信息表'
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['name'], name='idx_pub_db_name'),
            models.Index(fields=['type'], name='idx_pub_db_type'),
            models.Index(fields=['data_source'], name='idx_pub_db_data_source'),
        ]

    def __str__(self):
        return f"{self.name} ({self.type})"


# 动态为 Meta 添加 db_table_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    PublicDatabaseInfo.Meta.db_table_comment = '数据库信息表，存储各个数据库的基本信息'
