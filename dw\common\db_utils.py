"""
数据库兼容性工具类
支持MySQL、PostgreSQL、SQLite等数据库的跨平台迁移
"""

from django.db import connection
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class DatabaseCompatibilityUtils:
    """数据库兼容性工具类"""

    # 数据库类型映射
    DB_TYPE_MAPPING = {
        'django.db.backends.mysql': 'mysql',
        'django.db.backends.postgresql': 'postgresql',
        'django.db.backends.sqlite3': 'sqlite',
        'django.db.backends.oracle': 'oracle',
    }

    # 默认值语法映射
    DEFAULT_VALUE_SYNTAX = {
        'mysql': {
            'current_timestamp': 'CURRENT_TIMESTAMP',
            'current_timestamp_on_update': 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            'datetime_type': 'DATETIME',
            'text_type': 'TEXT',
            'varchar_type': 'VARCHAR',
            'int_type': 'INT',
            'bigint_type': 'BIGINT',
        },
        'postgresql': {
            'current_timestamp': 'CURRENT_TIMESTAMP',
            'current_timestamp_on_update': 'CURRENT_TIMESTAMP',  # PostgreSQL使用触发器实现
            'datetime_type': 'TIMESTAMP',
            'text_type': 'TEXT',
            'varchar_type': 'VARCHAR',
            'int_type': 'INTEGER',
            'bigint_type': 'BIGINT',
        },
        'sqlite': {
            'current_timestamp': 'CURRENT_TIMESTAMP',
            'current_timestamp_on_update': 'CURRENT_TIMESTAMP',  # SQLite使用触发器实现
            'datetime_type': 'DATETIME',
            'text_type': 'TEXT',
            'varchar_type': 'VARCHAR',
            'int_type': 'INTEGER',
            'bigint_type': 'INTEGER',
        },
        'oracle': {
            'current_timestamp': 'CURRENT_TIMESTAMP',
            'current_timestamp_on_update': 'CURRENT_TIMESTAMP',
            'datetime_type': 'TIMESTAMP',
            'text_type': 'CLOB',
            'varchar_type': 'VARCHAR2',
            'int_type': 'NUMBER',
            'bigint_type': 'NUMBER',
        }
    }

    @classmethod
    def get_database_type(cls, database_alias='default'):
        """获取数据库类型"""
        try:
            db_config = settings.DATABASES.get(database_alias, {})
            engine = db_config.get('ENGINE', '')
            return cls.DB_TYPE_MAPPING.get(engine, 'unknown')
        except Exception as e:
            logger.error(f"获取数据库类型失败: {e}")
            return 'unknown'

    @classmethod
    def get_database_version(cls, database_alias='default'):
        """获取数据库版本"""
        try:
            with connection.cursor() as cursor:
                db_type = cls.get_database_type(database_alias)
                if db_type == 'mysql':
                    cursor.execute("SELECT VERSION()")
                elif db_type == 'postgresql':
                    cursor.execute("SELECT version()")
                elif db_type == 'sqlite':
                    cursor.execute("SELECT sqlite_version()")
                elif db_type == 'oracle':
                    cursor.execute("SELECT * FROM v$version WHERE banner LIKE 'Oracle%'")
                else:
                    return 'unknown'

                result = cursor.fetchone()
                return result[0] if result else 'unknown'
        except Exception as e:
            logger.error(f"获取数据库版本失败: {e}")
            return 'unknown'

    @classmethod
    def get_syntax(cls, syntax_key, database_alias='default'):
        """获取特定数据库的语法"""
        db_type = cls.get_database_type(database_alias)
        syntax_map = cls.DEFAULT_VALUE_SYNTAX.get(db_type, cls.DEFAULT_VALUE_SYNTAX['mysql'])
        return syntax_map.get(syntax_key, '')

    @classmethod
    def generate_alter_table_sql(cls, table_name, column_name, column_type, default_value=None,
                                comment=None, database_alias='default'):
        """生成ALTER TABLE语句"""
        db_type = cls.get_database_type(database_alias)

        if db_type == 'mysql':
            return cls._generate_mysql_alter_sql(table_name, column_name, column_type,
                                                default_value, comment)
        elif db_type == 'postgresql':
            return cls._generate_postgresql_alter_sql(table_name, column_name, column_type,
                                                    default_value, comment)
        elif db_type == 'sqlite':
            return cls._generate_sqlite_alter_sql(table_name, column_name, column_type,
                                                 default_value, comment)
        elif db_type == 'oracle':
            return cls._generate_oracle_alter_sql(table_name, column_name, column_type,
                                                 default_value, comment)
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")

    @classmethod
    def _generate_mysql_alter_sql(cls, table_name, column_name, column_type, default_value, comment):
        """生成MySQL的ALTER TABLE语句"""
        sql_parts = [f"ALTER TABLE {table_name} MODIFY COLUMN {column_name} {column_type}"]

        if default_value:
            sql_parts.append(f"DEFAULT {default_value}")

        if comment:
            sql_parts.append(f"COMMENT '{comment}'")

        return " ".join(sql_parts)

    @classmethod
    def _generate_postgresql_alter_sql(cls, table_name, column_name, column_type, default_value, comment):
        """生成PostgreSQL的ALTER TABLE语句"""
        sqls = []

        # PostgreSQL需要分别设置类型和默认值
        sqls.append(f"ALTER TABLE {table_name} ALTER COLUMN {column_name} TYPE {column_type}")

        if default_value:
            sqls.append(f"ALTER TABLE {table_name} ALTER COLUMN {column_name} SET DEFAULT {default_value}")

        if comment:
            sqls.append(f"COMMENT ON COLUMN {table_name}.{column_name} IS '{comment}'")

        return sqls

    @classmethod
    def _generate_sqlite_alter_sql(cls, table_name, column_name, column_type, default_value, comment):
        """生成SQLite的ALTER TABLE语句"""
        # SQLite的ALTER TABLE功能有限，可能需要重建表
        logger.warning("SQLite的ALTER TABLE功能有限，建议使用Django的migration系统")
        return f"-- SQLite不支持直接修改列类型，请使用Django migration"

    @classmethod
    def _generate_oracle_alter_sql(cls, table_name, column_name, column_type, default_value, comment):
        """生成Oracle的ALTER TABLE语句"""
        sqls = []

        sqls.append(f"ALTER TABLE {table_name} MODIFY {column_name} {column_type}")

        if default_value:
            sqls.append(f"ALTER TABLE {table_name} MODIFY {column_name} DEFAULT {default_value}")

        if comment:
            sqls.append(f"COMMENT ON COLUMN {table_name}.{column_name} IS '{comment}'")

        return sqls

    @classmethod
    def generate_timestamp_columns_sql(cls, table_name, database_alias='default'):
        """生成时间戳列的SQL语句"""
        db_type = cls.get_database_type(database_alias)
        datetime_type = cls.get_syntax('datetime_type', database_alias)
        current_ts = cls.get_syntax('current_timestamp', database_alias)
        current_ts_update = cls.get_syntax('current_timestamp_on_update', database_alias)

        if db_type == 'mysql':
            return [
                f"ALTER TABLE {table_name} MODIFY COLUMN create_time {datetime_type} DEFAULT {current_ts}",
                f"ALTER TABLE {table_name} MODIFY COLUMN update_time {datetime_type} DEFAULT {current_ts} ON UPDATE {current_ts}"
            ]
        elif db_type == 'postgresql':
            # PostgreSQL需要使用触发器来实现ON UPDATE功能
            return [
                f"ALTER TABLE {table_name} ALTER COLUMN create_time SET DEFAULT {current_ts}",
                f"ALTER TABLE {table_name} ALTER COLUMN update_time SET DEFAULT {current_ts}",
                cls._generate_postgresql_update_trigger(table_name)
            ]
        elif db_type == 'sqlite':
            # SQLite需要使用触发器实现自动更新时间戳
            return [
                f"-- SQLite为create_time设置默认值",
                f"-- SQLite为update_time设置默认值并创建更新触发器",
                cls._generate_sqlite_update_trigger(table_name)
            ]
        else:
            return [f"-- 不支持的数据库类型: {db_type}"]

    @classmethod
    def _generate_postgresql_update_trigger(cls, table_name):
        """生成PostgreSQL的更新触发器"""
        return f"""
        CREATE OR REPLACE FUNCTION update_{table_name}_timestamp()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.update_time = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';

        DROP TRIGGER IF EXISTS trigger_update_{table_name}_timestamp ON {table_name};
        CREATE TRIGGER trigger_update_{table_name}_timestamp
            BEFORE UPDATE ON {table_name}
            FOR EACH ROW
            EXECUTE FUNCTION update_{table_name}_timestamp();
        """

    @classmethod
    def _generate_sqlite_update_trigger(cls, table_name):
        """生成SQLite的更新触发器"""
        return f"""
        -- 为SQLite创建更新时间戳的触发器
        CREATE TRIGGER IF NOT EXISTS trigger_update_{table_name}_timestamp
        AFTER UPDATE ON {table_name}
        FOR EACH ROW
        WHEN NEW.update_time = OLD.update_time  -- 只有当update_time没有被手动修改时才自动更新
        BEGIN
            UPDATE {table_name} SET update_time = CURRENT_TIMESTAMP WHERE rowid = NEW.rowid;
        END;

        -- 为SQLite设置create_time和update_time的默认值（如果支持）
        -- 注意：SQLite的ALTER TABLE功能有限，这些语句可能需要在表创建时设置
        """
