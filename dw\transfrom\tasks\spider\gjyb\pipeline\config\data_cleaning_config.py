#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家医保局(gjyb)数据清洗配置
"""

import logging
import pandas as pd
from typing import Dict, Callable
from transfrom.utils.data_cleaning_config import BaseDataCleaningConfig, CommonCleaningFunctions

logger = logging.getLogger(__name__)


class GjybDataCleaningConfig(BaseDataCleaningConfig):
    """
    国家医保局数据清洗配置
    """

    def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
        """
        获取表级别的清洗规则配置

        表级别清洗适用于跨字段的复杂清洗逻辑
        注意：避免与字段级别清洗重复配置

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {规则配置}}
        """
        return {
            'spider_fuwu_fixed_hospital': {
                'type_validation': {
                    'enabled': True,
                    'dict_id': 24,
                    'type_field': 'medinsType',
                    'type_name_field': 'medinsTypeName'
                }
            },
            'spider_fuwu_retail_pharmacy': {
                'type_validation': {
                    'enabled': True,
                    'dict_id': 24,
                    'type_field': 'medinsType',
                    'type_name_field': 'medinsTypeName'
                },
                'custom_function': 'clean_fuwu_pharmacy_data'   # 使用自定义函数
            },
            'spider_fuwu_service_facilities': {
                'custom_function': 'clean_fuwu_service_facilities_data'   # 使用自定义函数处理复杂业务逻辑
            },
            'spider_fuwu_western_medicine': {
                'custom_function': 'clean_fuwu_western_medicine_data'   # 使用自定义函数处理西药数据清洗
            },
            'spider_fuwu_western_disease': {
                # 西医疾病诊断数据清洗 - 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
            },
            'spider_fuwu_surgical_catalog': {
                # 手术操作分类数据清洗 - 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
            },
            'spider_fuwu_tcm_disease': {
                # 中医疾病分类数据清洗 - 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
            },
            'spider_fuwu_medical_supplies': {
                'custom_function_by_target': {
                    'medical_supplies_base': 'clean_fuwu_medical_supplies_data',  # 基础数据清洗
                    'medical_supplies_register_message': 'clean_fuwu_medical_supplies_register_data'  # 注册信息清洗（一对多）
                }
            },
            'spider_fuwu_chinese_prescription_medicine': {
                'custom_function': 'clean_fuwu_chinese_medicine_data'   # 使用自定义函数处理中成药数据清洗
            },
            'spider_fuwu_tcm_herb': {
                'custom_function': 'clean_fuwu_tcm_herb_data'   # 使用自定义函数处理中草药数据清洗和admdvs一对多映射
            },
            'spider_fuwu_selfprep_medicine': {
                'custom_function': 'clean_fuwu_selfprep_medicine_data'   # 使用自定义函数处理自制药数据清洗
            },
            'spider_fuwu_national_drug_catalog': {
                # 国谈药目录清单数据清洗 - 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
            },
            'spider_fuwu_national_drug_retail_pharmacy': {
                # 国谈药定点零售药店数据清洗 - 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
                # 特别注意经纬度字段的数值处理
            },
            'spider_fuwu_national_drug_hospital': {
                # 国谈药定点医疗机构数据清洗 - 使用标准清洗策略
                # 主要处理空字符串转NULL，确保数据质量
                # 特别注意经纬度字段的数值处理
            },
        }

    def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
        """
        获取字段级别的清洗规则配置

        Returns:
            Dict: 清洗规则配置，格式为 {表名: {字段名: {规则配置}}}
        """
        return {
            'spider_fuwu_fixed_hospital': {
                'addr': {
                    'fullwidth_to_halfwidth': {
                    'enabled': True
                },
                    'address_cleaning': {
                        'enabled': True,
                        'min_length': 200
                    }
                }
            },
            'spider_fuwu_retail_pharmacy': {
                'addr': {
                    'fullwidth_to_halfwidth': {
                    'enabled': True
                },
                    'address_cleaning': {
                        'enabled': True,
                        'min_length': 200
                    }
                },
                'tel': {
                    'phone_cleaning_advanced': {
                        'enabled': True
                    }
                }
            },
            'spider_fuwu_service_facilities': {
            },
            'spider_fuwu_selfprep_medicine': {
                'medinsConerTel': {
                    'phone_cleaning_advanced': {
                        'enabled': True
                    }
                }
            },
            'spider_fuwu_national_drug_retail_pharmacy': {
                'addr': {
                    'fullwidth_to_halfwidth': {
                    'enabled': True
                },
                    'address_cleaning': {
                        'enabled': True,
                        'min_length': 200
                    }
                }
            },
            'spider_fuwu_national_drug_hospital': {
                'addr': {
                    'fullwidth_to_halfwidth': {
                    'enabled': True
                },
                    'address_cleaning': {
                        'enabled': True,
                        'min_length': 200
                    }
                }
            }
        }

    def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
        """
        获取自定义清洗函数

        Returns:
            Dict[str, Callable]: 自定义清洗函数字典
        """
        return {
            'clean_fuwu_hospital_data': self._clean_fuwu_hospital_data,
            'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data,
            'clean_fuwu_service_facilities_data': self._clean_fuwu_service_facilities_data,
            'clean_fuwu_western_medicine_data': self._clean_fuwu_western_medicine_data,
            'clean_fuwu_chinese_medicine_data': self._clean_fuwu_chinese_medicine_data,
            'clean_fuwu_tcm_herb_data': self._clean_fuwu_tcm_herb_data,
            'clean_fuwu_selfprep_medicine_data': self._clean_fuwu_selfprep_medicine_data,
            'clean_fuwu_medical_supplies_data': self.clean_fuwu_medical_supplies_data,
            'clean_fuwu_medical_supplies_register_data': self.clean_fuwu_medical_supplies_register_data,
            # 字段级处理函数
            'process_medListCodg_for_charge_code': self._process_medListCodg_for_charge_code,
            'process_admdvs_for_region_name': self._process_admdvs_for_region_name,
            'convert_timestamp_to_date': self._convert_timestamp_to_date,
        }

    def _clean_fuwu_hospital_data(self, df):
        """
        清洗医保定点机构数据的自定义函数

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 对addr字段进行增强清洗（处理不规范地址数据）

        return df

    def _clean_fuwu_pharmacy_data(self, df):
        """
        清洗医保定点药店数据的自定义函数

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 对addr字段进行增强清洗（处理不规范地址数据）
        if 'businessLvOutMedOtp' in df.columns:
            # 全角转半角处理
            df['businessLvOutMedOtp'] = df['businessLvOutMedOtp'].apply(
                lambda x: 3 if int(x)==2 else x
            )
            logger.info(f"businessLvOutMedOtp字段转换完成")

        return df

    def _clean_fuwu_service_facilities_data(self, df):
        """
        清洗医疗服务项目数据的自定义函数

        主要处理：
        1. medListCodg字段：按第一个"-"分割，前部分作为code，后部分作为charge_item_code
        2. servitemName字段：只处理admdvs!=100000的数据，并关联admdvs=100000的数据获取charge_item_name
        3. 过滤数据：只保留admdvs!=100000的数据用于写入目标表
        4. admdvs字段：关联字典获取名称

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame（只包含admdvs!=100000的数据）
        """
        import pandas as pd

        logger.info(f"开始清洗医疗服务项目数据，共 {len(df)} 条记录")

        # 首先获取admdvs=100000的数据用于关联（国家标准数据）
        national_data = df[df['admdvs'].astype(str) == '100000'].copy()
        national_mapping_for_name = {}  # 用于name关联

        logger.info(f"输入数据中admdvs=100000的记录数: {len(national_data)}")

        if not national_data.empty:
            # 创建国家标准映射：medListCodg -> servitemName (用于name，按code关联)
            national_mapping_for_name = national_data.set_index('medListCodg')['servitemName'].to_dict()
            logger.info(f"获取到 {len(national_mapping_for_name)} 条国家标准数据用于name关联")
        else:
            logger.warning("输入数据中没有国家标准数据(admdvs=100000)，name字段关联将失败")

        # 过滤数据：只保留admdvs!=100000的数据
        df_filtered = df[df['admdvs'].astype(str) != '100000'].copy()
        logger.info(f"过滤后保留 {len(df_filtered)} 条记录（admdvs!=100000）")

        if df_filtered.empty:
            logger.warning("过滤后没有数据，返回空DataFrame")
            return df_filtered

        # 1. 处理medListCodg字段 - 保持原值，让字段映射阶段处理分割逻辑
        if 'medListCodg' in df_filtered.columns:
            # 创建临时字段用于charge_item_name关联
            def extract_charge_code_for_name_mapping(x):
                """提取charge_item_code部分用于name关联"""
                if pd.notna(x) and str(x).strip():
                    x_str = str(x).strip()
                    if '-' in x_str:
                        parts = x_str.split('-', 1)  # 只分割一次
                        if len(parts) > 1:
                            return parts[1]  # 返回第一个'-'后面的所有内容
                        else:
                            return ''
                    else:
                        return ''
                return ''

            # 保存处理后的值用于charge_item_name关联
            df_filtered['_charge_item_code_processed'] = df_filtered['medListCodg'].apply(extract_charge_code_for_name_mapping)

            logger.info(f"medListCodg字段处理完成: 保持原值，字段映射阶段将处理code和charge_item_code的分割")

        # 2. 处理servitemName字段 - 直接创建目标字段
        if 'servitemName' in df_filtered.columns:


            # 直接创建目标字段 - 性能优化版本
            # 使用向量化操作替代apply，提升性能
            def vectorized_get_name_by_code(codes_series):
                """向量化获取名称

                逻辑说明：
                1. 从medListCodg提取code部分（第一个'-'之前的部分）
                2. 使用这个code在国家标准数据中查找对应的servitemName作为name
                """
                result = []
                for code in codes_series:
                    if pd.notna(code) and str(code).strip():
                        code_str = str(code).strip()
                        if '-' in code_str:
                            parts = code_str.split('-', 1)
                            # 修复：取第一个'-'之前的部分作为code，用于name关联
                            extracted_code = parts[0]  # 取第一个'-'之前的部分
                            if extracted_code and extracted_code in national_mapping_for_name:
                                result.append(national_mapping_for_name[extracted_code])
                                continue
                        else:
                            # 如果没有'-'，直接使用整个值作为code
                            extracted_code = code_str
                            if extracted_code and extracted_code in national_mapping_for_name:
                                result.append(national_mapping_for_name[extracted_code])
                                continue
                    result.append('')
                return pd.Series(result, index=codes_series.index)

            df_filtered['name'] = vectorized_get_name_by_code(df_filtered['medListCodg'])
            df_filtered['charge_item_name'] = df_filtered['servitemName'].fillna('')

            # 统计关联成功率 - 使用向量化操作
            name_successful_mappings = (df_filtered['name'] != '').sum()
            charge_item_name_successful_mappings = (df_filtered['charge_item_name'] != '').sum()
            total_records = len(df_filtered)
            name_success_rate = (name_successful_mappings / total_records * 100) if total_records > 0 else 0
            charge_item_name_success_rate = (charge_item_name_successful_mappings / total_records * 100) if total_records > 0 else 0

            logger.info(f"servitemName字段处理完成: 直接创建name和charge_item_name字段")
            logger.info(f"name关联成功率: {name_success_rate:.1f}% ({name_successful_mappings}/{total_records})")
            logger.info(f"charge_item_name设置成功率: {charge_item_name_success_rate:.1f}% ({charge_item_name_successful_mappings}/{total_records})")

        # 3. 处理admdvs字段 - 不在这里处理，让字段映射阶段处理
        # 因为字段映射配置中已经有了字典映射配置，我们只需要确保源字段值正确
        if 'admdvs' in df_filtered.columns:
            # 确保admdvs字段是字符串类型，便于后续字段映射处理
            df_filtered['admdvs'] = df_filtered['admdvs'].astype(str)

            # 添加调试信息：显示源数据样本
            unique_admdvs = df_filtered['admdvs'].unique()[:10]  # 显示前10个唯一值
            logger.info(f"admdvs字段样本值: {unique_admdvs.tolist()}")
            logger.info(f"admdvs字段将通过字段映射阶段处理，映射到admin_region_code和admin_region_name")

        # 4. 处理日期字段 - 将时间戳转换为日期（直接覆盖源字段，配合字段映射使用）
        date_fields = ['begndate', 'enddate']
        for date_field in date_fields:
            if date_field in df_filtered.columns:
                # 使用通用的时间戳转换函数，直接覆盖源字段
                from transfrom.utils.date import batch_convert_timestamp_to_date
                df_filtered[date_field] = batch_convert_timestamp_to_date(df_filtered[date_field])
                logger.info(f"{date_field}字段转换完成: 时间戳 -> 日期（源字段已更新，将通过字段映射复制到目标字段）")

        # 5. 处理其他字段映射
        field_mappings = {
            'trtItemDscr': 'treatment_item_description',
            'trtItemCont': 'treatment_item_content',
            'trtExctCont': 'treatment_excluded_content',
            'prcunt': 'pricing_unit',
            'memo': 'remark',
            'rid': 'rid'
        }

        for source_field, target_field in field_mappings.items():
            if source_field in df_filtered.columns:
                df_filtered[target_field] = df_filtered[source_field]

        logger.info(f"医疗服务项目数据清洗完成，最终输出 {len(df_filtered)} 条记录")
        return df_filtered

    def _clean_fuwu_western_medicine_data(self, df):
        """
        清洗西药数据的自定义函数

        主要处理：
        1. prodentpName、lstdLicHolder字段：如果值为 --，则替换成Null；如果包含中英文逗号、分号，则都替换成英文逗号
        2. natHiDruglistChrgitmLv、natDrugNo、drugGenname、natHiDruglistDosform、natHiDruglistMemo字段：如果值为 --，则替换成Null

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        import pandas as pd
        import re

        logger.info(f"开始清洗西药数据，共 {len(df)} 条记录")

        # 1. 处理 prodentpName、lstdLicHolder 字段
        company_fields = ['prodentpName', 'lstdLicHolder']
        for field in company_fields:
            if field in df.columns:
                # 记录清洗前的统计
                before_count = df[field].notna().sum()
                dash_count = (df[field] == '--').sum()



                # 应用清洗函数 - 使用向量化操作优化性能
                # 先处理'--'值
                mask_dash = df[field] == '--'
                df.loc[mask_dash, field] = None

                # 处理非空值的逗号分号替换
                mask_not_null = df[field].notna()
                if mask_not_null.any():
                    df.loc[mask_not_null, field] = df.loc[mask_not_null, field].astype(str).str.replace(r'[，,；;]', ',', regex=True)

                # 记录清洗后的统计
                after_count = df[field].notna().sum()
                logger.info(f"{field}字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，处理了 {dash_count} 条'--'值")

        # 2. 处理 natHiDruglistChrgitmLv、natDrugNo、drugGenname、natHiDruglistDosform、natHiDruglistMemo 字段
        null_dash_fields = ['natHiDruglistChrgitmLv', 'natDrugNo', 'drugGenname', 'natHiDruglistDosform', 'natHiDruglistMemo']
        for field in null_dash_fields:
            if field in df.columns:
                # 记录清洗前的统计
                before_count = df[field].notna().sum()
                dash_count = (df[field] == '--').sum()

                # 应用清洗函数 - 使用向量化操作优化性能
                # 直接处理'--'值
                mask_dash = df[field] == '--'
                df.loc[mask_dash, field] = None

                # 记录清洗后的统计
                after_count = df[field].notna().sum()
                logger.info(f"{field}字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，处理了 {dash_count} 条'--'值")

        # 3. 处理 each_dose 字段 - 清理无用的问号，提取有效信息 - 性能优化版本
        if 'eachDos' in df.columns:
            # 记录清洗前的统计
            before_count = df['eachDos'].notna().sum()

            # 使用向量化操作统计问号数量
            question_mark_count = df['eachDos'].fillna('').astype(str).str.count(r'\?').sum()

            # 向量化清洗逻辑：清理问号并提取有效信息
            def vectorized_clean_each_dose(series):
                """向量化清洗eachDos字段"""
                result = []
                for value in series:
                    if pd.isna(value) or value is None:
                        result.append(None)
                        continue

                    value_str = str(value).strip()

                    # 如果值为 --，则替换成 None
                    if value_str == '--':
                        result.append(None)
                        continue

                    # 如果字符串为空或只包含问号，返回 None
                    if not value_str or value_str.replace('?', '').strip() == '':
                        result.append(None)
                        continue

                    # 清理问号并提取有效信息
                    cleaned_value = self._clean_each_dose_content(value_str)
                    result.append(cleaned_value if cleaned_value else None)

                return pd.Series(result, index=series.index)

            # 应用向量化清洗函数
            df['eachDos'] = vectorized_clean_each_dose(df['eachDos'])

            # 使用向量化操作处理'无'值
            mask_wu = df['eachDos'] == '无'
            df.loc[mask_wu, 'eachDos'] = None

            # 记录清洗后的统计
            after_count = df['eachDos'].notna().sum()
            logger.info(f"eachDos字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，清理了 {question_mark_count} 个问号")

        # 4.处理drugProdname字段，字段值为 无，替换成空 - 向量化优化
        if 'drugProdname' in df.columns:
            mask_wu = df['drugProdname'] == '无'
            df.loc[mask_wu, 'drugProdname'] = None
            logger.info(f"drugProdname字段清洗完成")

        # 5.处理pacmatl字段，字段值为 无，替换成空 - 向量化优化
        if 'pacmatl' in df.columns:
            mask_wu = df['pacmatl'] == '无'
            df.loc[mask_wu, 'pacmatl'] = None
            logger.info(f"pacmatl字段清洗完成")
        logger.info(f"西药数据清洗完成，最终输出 {len(df)} 条记录")
        return df

    def _clean_fuwu_chinese_medicine_data(self, df):
        """
        清洗中成药数据的自定义函数

        参考西药清洗规则，主要处理：
        1. prodentpName、lstdLicHolder字段：如果值为 --，则替换成Null；如果包含中英文逗号、分号，则都替换成英文逗号
        2. natHiDruglistChrgitmLv、natDrugNo、drugGenname、natHiDruglistDosform、natHiDruglistMemo字段：如果值为 --，则替换成Null
        3. eachDos字段：智能清洗问号并提取有效信息

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        import pandas as pd
        import re

        logger.info(f"开始清洗中成药数据，共 {len(df)} 条记录")

        # 1. 处理 prodentpName、lstdLicHolder 字段（与西药相同的逻辑）
        company_fields = ['prodentpName', 'lstdLicHolder']
        for field in company_fields:
            if field in df.columns:
                # 记录清洗前的统计
                before_count = df[field].notna().sum()
                dash_count = (df[field] == '--').sum()

                # 应用清洗函数 - 使用向量化操作优化性能
                # 先处理'--'值
                mask_dash = df[field] == '--'
                df.loc[mask_dash, field] = None

                # 处理非空值的逗号分号替换
                mask_not_null = df[field].notna()
                if mask_not_null.any():
                    df.loc[mask_not_null, field] = df.loc[mask_not_null, field].astype(str).str.replace(r'[，,；;]', ',', regex=True)

                # 记录清洗后的统计
                after_count = df[field].notna().sum()
                logger.info(f"{field}字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，处理了 {dash_count} 条'--'值")

        # 2. 处理 natHiDruglistChrgitmLv、natDrugNo、drugGenname、natHiDruglistDosform、natHiDruglistMemo 字段（与西药相同的逻辑）
        null_dash_fields = ['natHiDruglistChrgitmLv', 'natDrugNo', 'drugGenname', 'natHiDruglistDosform', 'natHiDruglistMemo']
        for field in null_dash_fields:
            if field in df.columns:
                # 记录清洗前的统计
                before_count = df[field].notna().sum()
                dash_count = (df[field] == '--').sum()

                # 应用清洗函数 - 使用向量化操作优化性能
                # 直接处理'--'值
                mask_dash = df[field] == '--'
                df.loc[mask_dash, field] = None

                # 记录清洗后的统计
                after_count = df[field].notna().sum()
                logger.info(f"{field}字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，处理了 {dash_count} 条'--'值")

        # 3. 处理 eachDos 字段 - 清理无用的问号，提取有效信息（与西药相同的逻辑）- 性能优化版本
        if 'eachDos' in df.columns:
            # 记录清洗前的统计
            before_count = df['eachDos'].notna().sum()

            # 使用向量化操作统计问号数量
            question_mark_count = df['eachDos'].fillna('').astype(str).str.count(r'\?').sum()

            # 向量化清洗逻辑：清理问号并提取有效信息
            def vectorized_clean_each_dose(series):
                """向量化清洗eachDos字段"""
                result = []
                for value in series:
                    if pd.isna(value) or value is None:
                        result.append(None)
                        continue

                    value_str = str(value).strip()

                    # 如果值为 --，则替换成 None
                    if value_str == '--':
                        result.append(None)
                        continue

                    # 如果字符串为空或只包含问号，返回 None
                    if not value_str or value_str.replace('?', '').strip() == '':
                        result.append(None)
                        continue

                    # 清理问号并提取有效信息
                    cleaned_value = self._clean_each_dose_content(value_str)
                    result.append(cleaned_value if cleaned_value else None)

                return pd.Series(result, index=series.index)

            # 应用向量化清洗函数
            df['eachDos'] = vectorized_clean_each_dose(df['eachDos'])

            # 使用向量化操作处理'无'值
            mask_wu = df['eachDos'] == '无'
            df.loc[mask_wu, 'eachDos'] = None

            # 记录清洗后的统计
            after_count = df['eachDos'].notna().sum()
            logger.info(f"eachDos字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，清理了 {question_mark_count} 个问号")

        # 4.处理drugProdname字段，字段值为 无，替换成空 - 向量化优化
        if 'drugProdname' in df.columns:
            mask_wu = df['drugProdname'] == '无'
            df.loc[mask_wu, 'drugProdname'] = None
            logger.info(f"drugProdname字段清洗完成")

        # 5.处理pacmatl字段，字段值为 无，替换成空 - 向量化优化
        if 'pacmatl' in df.columns:
            mask_wu = df['pacmatl'] == '无'
            df.loc[mask_wu, 'pacmatl'] = None
            logger.info(f"pacmatl字段清洗完成")
            
        logger.info(f"中成药数据清洗完成，最终输出 {len(df)} 条记录")
        return df

    def _clean_fuwu_tcm_herb_data(self, df):
        """
        清洗中草药数据的自定义函数

        主要处理：
        1. admdvs字段的一对多映射：直接映射到province_code，通过字典映射到province_name
        2. 基础数据清洗和验证

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        import pandas as pd

        logger.info(f"开始清洗中草药数据，共 {len(df)} 条记录")

        # 1. 处理admdvs字段的一对多映射
        if 'admdvs' in df.columns:
            # 确保admdvs字段是字符串类型，便于后续字段映射处理
            df['admdvs'] = df['admdvs'].astype(str)

            # 直接创建province_code字段（直接映射）
            df['province_code'] = df['admdvs']

            # 创建province_name字段（通过字典映射）- 性能优化版本
            try:
                from transfrom.utils.field_mapping import DictMappingManager
                dict_mapping_manager = DictMappingManager()
                region_mapping_df = dict_mapping_manager.get_dict_mapping(dict_id=19)

                if not region_mapping_df.empty:
                    # 一次性创建映射字典，避免重复查询
                    region_mapping_dict = dict(zip(region_mapping_df['dict_code'], region_mapping_df['dict_name']))
                    logger.info(f"成功加载省份字典映射: {len(region_mapping_dict)} 条记录")

                    # 使用向量化操作进行映射，性能比apply快很多
                    df['province_name'] = df['admdvs'].astype(str).map(region_mapping_dict).fillna('')
                else:
                    logger.warning("未获取到省份字典数据，province_name字段将为空")
                    df['province_name'] = ''
            except Exception as e:
                logger.warning(f"处理admdvs字段时出错: {e}")
                df['province_name'] = ''

            # 添加特殊标记，防止字段映射阶段覆盖我们的正确值
            df['_province_name_cleaned'] = True

            # 统计映射成功率 - 使用向量化操作优化性能
            province_code_successful_mappings = (df['province_code'] != '').sum()
            province_name_successful_mappings = (df['province_name'] != '').sum()
            total_records = len(df)
            province_code_success_rate = (province_code_successful_mappings / total_records * 100) if total_records > 0 else 0
            province_name_success_rate = (province_name_successful_mappings / total_records * 100) if total_records > 0 else 0

            logger.info(f"admdvs一对多映射处理完成: province_code和province_name字段已创建")
            logger.info(f"province_code设置成功率: {province_code_success_rate:.1f}% ({province_code_successful_mappings}/{total_records})")
            logger.info(f"province_name关联成功率: {province_name_success_rate:.1f}% ({province_name_successful_mappings}/{total_records})")

            # 添加调试信息：显示源数据样本
            unique_admdvs = df['admdvs'].unique()[:10]  # 显示前10个唯一值
            logger.info(f"admdvs字段样本值: {unique_admdvs.tolist()}")

        # 2. 处理其他字段的基础清洗
        # 确保关键字段不为空
        key_fields = ['medListCodg', 'tcmherbName']
        for field in key_fields:
            if field in df.columns:
                # 统计空值情况
                null_count = df[field].isnull().sum()
                empty_count = (df[field] == '').sum()
                if null_count > 0 or empty_count > 0:
                    logger.warning(f"{field}字段存在 {null_count} 个空值和 {empty_count} 个空字符串")

        logger.info(f"中草药数据清洗完成，最终输出 {len(df)} 条记录")
        return df

    def _clean_fuwu_selfprep_medicine_data(self, df):
        """
        清洗自制药数据的自定义函数

        主要处理：
        1. 批量清洗无效值字段：如果值为 '无'、'--' 或 '-'，则替换成NULL
           - prodentpName (生产企业名称)
           - prodentpAddr (生产企业地址)
           - pacmatl (包装材质)
           - aprvno (批准文号)
           - eldPatnMedc (老年患者用药注意事项)
           - chldMedc (儿童患者用药注意事项)
        2. eachDos字段：智能清洗问号并提取有效信息
        3. 时间戳转换：aprvnoBegndate字段转换为日期格式
        4. 直接创建目标字段映射，避免依赖medical_field_mapping表

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        import pandas as pd

        logger.info(f"开始清洗自制药数据，共 {len(df)} 条记录")

        # 1. 批量处理需要清洗无效值的字段：如果值为 '无'、'--' 或 '-'，则替换成Null
        invalid_value_fields = ['prodentpName', 'prodentpAddr', 'pacmatl', 'aprvno', 'eldPatnMedc', 'chldMedc']
        invalid_values = ['无', '--', '-','否','/']

        for field in invalid_value_fields:
            if field in df.columns:
                # 记录清洗前的统计
                before_count = df[field].notna().sum()

                # 统计各种无效值的数量
                value_counts = {}
                for invalid_val in invalid_values:
                    value_counts[invalid_val] = (df[field] == invalid_val).sum()

                # 应用清洗函数 - 使用向量化操作优化性能
                mask_invalid = df[field].isin(invalid_values)
                df.loc[mask_invalid, field] = None

                # 记录清洗后的统计
                after_count = df[field].notna().sum()
                processed_info = ', '.join([f"{count}条'{val}'值" for val, count in value_counts.items() if count > 0])
                logger.info(f"{field}字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，处理了 {processed_info}")

        # 2. 处理 eachDos 字段 - 清理无用的问号，提取有效信息（参考西药清洗逻辑）- 性能优化版本
        if 'eachDos' in df.columns:
            # 记录清洗前的统计
            before_count = df['eachDos'].notna().sum()

            # 使用向量化操作统计问号数量
            question_mark_count = df['eachDos'].fillna('').astype(str).str.count(r'\?').sum()

            # 向量化清洗逻辑：清理问号并提取有效信息
            def vectorized_clean_each_dose(series):
                """向量化清洗eachDos字段"""
                result = []
                for value in series:
                    if pd.isna(value) or value is None:
                        result.append(None)
                        continue

                    value_str = str(value).strip()

                    # 如果值为 '--'、'-' 或 '无'，则替换成 None
                    if value_str in ['--', '-', '无']:
                        result.append(None)
                        continue

                    # 如果字符串为空或只包含问号，返回 None
                    if not value_str or value_str.replace('?', '').strip() == '':
                        result.append(None)
                        continue

                    # 清理问号并提取有效信息
                    cleaned_value = self._clean_each_dose_content(value_str)
                    result.append(cleaned_value if cleaned_value else None)

                return pd.Series(result, index=series.index)

            # 应用向量化清洗函数
            df['eachDos'] = vectorized_clean_each_dose(df['eachDos'])

            # 记录清洗后的统计
            after_count = df['eachDos'].notna().sum()
            logger.info(f"eachDos字段清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录，清理了 {question_mark_count} 个问号")

        # 3. 处理日期字段 - 将时间戳转换为日期
        if 'aprvnoBegndate' in df.columns:
            # 使用通用的时间戳转换函数
            from transfrom.utils.date import batch_convert_timestamp_to_date
            df['aprvnoBegndate'] = batch_convert_timestamp_to_date(df['aprvnoBegndate'])
            logger.info(f"aprvnoBegndate字段转换完成: 时间戳 -> 日期")

        # 3.5 处理电话号码字段 - 应用高级电话清洗
        if 'medinsConerTel' in df.columns:
            # 记录清洗前的统计
            before_count = df['medinsConerTel'].notna().sum()

            # 应用高级电话清洗函数
            df['medinsConerTel'] = df['medinsConerTel'].apply(CommonCleaningFunctions.clean_phone_number_advanced)

            # 记录清洗后的统计
            after_count = df['medinsConerTel'].notna().sum()
            logger.info(f"medinsConerTel字段电话清洗完成: 清洗前 {before_count} 条有效记录，清洗后 {after_count} 条有效记录")

        # 4. 直接创建目标字段映射（避免依赖medical_field_mapping表）
        logger.info("开始创建目标字段映射")

        # 基础信息字段映射
        field_mappings = {
            'medListCodg': 'code',                                    # 制剂代码
            'drugProdname': 'name',                                   # 制剂名称
            'minPacCnt': 'minimum_packaging_count',                   # 最小包装数量
            'minPrepunt': 'minimum_preparation_unit',                 # 最小制剂单位
            'minPacunt': 'minimum_packaging_unit',                    # 最小包装单位
            'eachDos': 'each_dose',                                   # 每次剂量
            'dosform': 'dosage_form',                                 # 剂型
            'drugSpec': 'specifications',                             # 规格
            'efccAtd': 'efficacy_information',                        # 疗效说明
            'pacmatl': 'packaging_material',                          # 包装材质
            'selfprepPmtno': 'license_number',                        # 许可证号
            'aprvno': 'approval_number',                              # 批准文号
            'aprvnoBegndate': 'approval_begin_date',                  # 批准文号开始日期（已转换为日期格式）
            'eldPatnMedc': 'elder_medication_precautions',            # 老年患者用药注意事项
            'chldMedc': 'child_medication_precautions',               # 儿童患者用药注意事项
            'hospPrepAppyerEmpName': 'hospital_name',                 # 医疗机构名称
            'medinsConerName': 'hospital_contact_person',             # 医疗机构联系人
            'hospPrepAppyerEmpAddr': 'hospital_address',              # 医疗机构地址
            'regn': 'hospital_regional_code',                         # 医疗机构区域编码
            'medinsConerTel': 'hospital_mobile',                      # 医疗机构电话号码
            'prodentpName': 'production_company_name',                # 生产企业名称
            'prodentpAddr': 'production_company_address',             # 生产企业地址
            'rid': 'rid'                                              # 记录标识
        }

        # 应用字段映射
        mapped_count = 0
        for source_field, target_field in field_mappings.items():
            if source_field in df.columns:
                df[target_field] = df[source_field]
                mapped_count += 1
            else:
                # 如果源字段不存在，创建空字段
                df[target_field] = None
                logger.warning(f"源字段 '{source_field}' 不存在，目标字段 '{target_field}' 设置为None")

        logger.info(f"字段映射完成: 成功映射 {mapped_count}/{len(field_mappings)} 个字段")

        logger.info(f"自制药数据清洗完成，最终输出 {len(df)} 条记录")
        return df

    def _clean_each_dose_content(self, content):
        """
        清洗 each_dose 字段内容，只对包含大量问号的字段进行清洗

        Args:
            content (str): 原始内容

        Returns:
            str: 清洗后的内容
        """
        import re

        if not content or not isinstance(content, str):
            return ''

        # 1. 检查是否包含大量问号（3个或以上连续问号）
        # 如果没有大量问号，直接返回原内容
        if not re.search(r'\?{3,}', content):
            return content

        # 2. 统计问号数量，如果问号数量相对较少，也直接返回原内容
        question_count = content.count('?')
        total_length = len(content)
        if total_length > 0 and question_count / total_length < 0.3:  # 问号占比小于30%
            return content

        # 3. 对于包含大量问号的内容，进行清洗
        cleaned_content = content

        # 移除所有问号
        cleaned_content = re.sub(r'\?+', '', cleaned_content)

        # 清理多余的空格和标点
        cleaned_content = re.sub(r'\s+', ' ', cleaned_content)  # 多个空格变成单个空格
        cleaned_content = re.sub(r'[,，]{2,}', '，', cleaned_content)  # 多个逗号变成单个中文逗号
        cleaned_content = cleaned_content.strip()

        # 4. 如果清理后内容为空或太短，返回空
        if not cleaned_content or len(cleaned_content) < 3:
            return ''

        # 5. 提取有意义的信息片段
        meaningful_patterns = [
            # 完整的用药描述
            r'每次\d+(?:\.\d+)?(?:ml|g|mg|μg|片|粒|袋|瓶|支|盒|包)',
            r'每[日天]\d+次',
            # 数字+单位的组合
            r'\d+(?:\.\d+)?(?:kcal|ml|h|g|mg|μg|L|kg)(?:/\w+)?',
            # 数字范围（如100-125ml/h）
            r'\d+(?:\.\d+)?[-~]\d+(?:\.\d+)?(?:kcal|ml|h|g|mg|μg|L|kg|次|小时|分钟|天|日|周|月|年|片|粒|袋|瓶|支|盒|包)?(?:/\w+)?',
            # 括号内的有效信息（如(500ml×4)）
            r'\([^)]*(?:\d+(?:\.\d+)?(?:kcal|ml|h|g|mg|μg|L|kg|次|小时|分钟|天|日|周|月|年|片|粒|袋|瓶|支|盒|包))[^)]*\)',
            # 营养液相关的描述
            r'\d+(?:\.\d+)?kcal\s*\([^)]*\)',
        ]

        # 6. 提取所有有意义的片段
        meaningful_parts = []
        for pattern in meaningful_patterns:
            matches = re.findall(pattern, cleaned_content, re.IGNORECASE)
            meaningful_parts.extend(matches)

        # 7. 如果提取到有意义的片段，组合它们
        if meaningful_parts:
            # 去重并保持顺序
            seen = set()
            unique_parts = []
            for part in meaningful_parts:
                if part not in seen:
                    seen.add(part)
                    unique_parts.append(part)

            result = '，'.join(unique_parts)
            return result

        # 8. 如果没有提取到特定模式，但内容看起来有用，进行基本清理
        # 保留中文、数字、字母和基本标点
        basic_cleaned = re.sub(r'[^\u4e00-\u9fa5\w\d\s,，。；;：:\-\+\*\/\(\)\.]+', '', cleaned_content)
        basic_cleaned = re.sub(r'\s+', ' ', basic_cleaned).strip()

        # 如果基本清理后还有实质内容，返回它
        if basic_cleaned and len(basic_cleaned) >= 3:
            return basic_cleaned

        return ''

    def _process_medListCodg_for_charge_code(self, value):
        """
        字段级处理函数：从medListCodg中提取charge_item_code

        Args:
            value: medListCodg字段值

        Returns:
            str: 提取的charge_item_code
        """
        import pandas as pd

        if pd.notna(value) and str(value).strip():
            value_str = str(value).strip()
            if '-' in value_str:
                # 按第一个'-'分割，取后面的所有内容
                parts = value_str.split('-', 1)  # 只分割一次
                if len(parts) > 1:
                    return parts[1]  # 返回第一个'-'后面的所有内容
        return ''

    def _process_admdvs_for_region_name(self, value):
        """
        字段级处理函数：将admdvs代码映射为区域名称

        注意：此函数已优化，建议在数据清洗阶段使用向量化操作而不是字段级处理

        Args:
            value: admdvs字段值

        Returns:
            str: 区域名称
        """
        import pandas as pd

        if pd.notna(value) and str(value).strip():
            try:
                # 使用类级别缓存避免重复查询
                if not hasattr(self, '_region_mapping_cache'):
                    from transfrom.utils.field_mapping import DictMappingManager
                    dict_mapping_manager = DictMappingManager()
                    region_mapping_df = dict_mapping_manager.get_dict_mapping(dict_id=19)

                    if not region_mapping_df.empty:
                        self._region_mapping_cache = dict(zip(region_mapping_df['dict_code'], region_mapping_df['dict_name']))
                    else:
                        self._region_mapping_cache = {}

                return self._region_mapping_cache.get(str(value).strip(), '')
            except Exception as e:
                logger.warning(f"处理admdvs字段时出错: {e}")
        return ''

    def _process_medListCodg_by_target_field(self, value, target_field=None):
        """
        字段级处理函数：根据目标字段对medListCodg进行不同的处理

        Args:
            value: medListCodg字段值
            target_field: 目标字段名，用于决定处理方式

        Returns:
            str: 处理后的值
        """
        import pandas as pd

        if pd.isna(value) or not str(value).strip():
            return ''

        value_str = str(value).strip()

        # 根据目标字段决定处理方式
        if target_field == 'code':
            # 目标是code字段，取第一个'-'之前的部分
            if '-' in value_str:
                return value_str.split('-')[0]
            else:
                return value_str
        elif target_field == 'charge_item_code':
            # 目标是charge_item_code字段，取第一个'-'之后的部分
            if '-' in value_str:
                parts = value_str.split('-', 1)  # 只分割一次
                if len(parts) > 1:
                    return parts[1]  # 返回第一个'-'后面的所有内容
                else:
                    return ''
            else:
                return ''
        else:
            # 其他情况，返回原值
            return value_str

    def _convert_timestamp_to_date(self, value):
        """
        字段级处理函数：将时间戳转换为日期格式

        Args:
            value: 时间戳值

        Returns:
            str: 日期字符串 (YYYY-MM-DD格式)
        """
        import pandas as pd

        try:
            from transfrom.utils.date import batch_convert_timestamp_to_date

            # 检查值是否有效
            if pd.isna(value) or value == '' or value is None:
                return ''

            # 使用通用的时间戳转换函数
            result = batch_convert_timestamp_to_date(pd.Series([value]))
            converted_value = result.iloc[0] if len(result) > 0 else ''

            # 确保返回有效的日期字符串
            if pd.isna(converted_value) or converted_value == 'None':
                return ''

            return str(converted_value)
        except Exception as e:
            logger.warning(f"转换时间戳时出错: {e}, 值: {value}")
            return ''

    def _validate_medins_type(self, df):
        """
        验证和清洗机构类型字段

        Args:
            df (pd.DataFrame): 源数据DataFrame

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        if 'medinsType' not in df.columns:
            return df

        try:
            # 延迟导入避免循环导入
            from transfrom.utils.field_mapping import DictMappingManager
            dict_mapping_manager = DictMappingManager()
            institution_type = dict_mapping_manager.get_dict_mapping(dict_id=24)

            if not institution_type.empty:
                # 获取有效的type代码列表
                dict_code_list = institution_type['dict_code'].tolist()
                logger.info(f"获取到的机构类型字典: {dict_code_list}")

                # 记录清洗前的统计
                before_clean_count = df['medinsType'].notna().sum()

                # 清洗type字段：只保留在字典范围内的值
                invalid_types = df[~df['medinsType'].isin(dict_code_list) & df['medinsType'].notna()]['medinsType'].unique()
                if len(invalid_types) > 0:
                    logger.info(f"发现无效的medinsType值: {invalid_types.tolist()}")

                # 使用向量化操作替代apply，提升性能
                mask_valid = df['medinsType'].isin(dict_code_list)
                df.loc[~mask_valid, 'medinsType'] = None

                # 清洗type_name字段：如果对应的type为None，则type_name也设为None
                if 'medinsTypeName' in df.columns:
                    type_is_null = df['medinsType'].isna()
                    df.loc[type_is_null, 'medinsTypeName'] = None

                # 记录清洗后的统计
                after_clean_count = df['medinsType'].notna().sum()
                logger.info(f"type字段清洗完成: 类型范围为 {dict_code_list}")
                logger.info(f"type字段清洗统计: 清洗前 {before_clean_count} 条有效记录，清洗后 {after_clean_count} 条有效记录")
            else:
                logger.warning("未获取到dict_id=24的字典数据，跳过type字段清洗")
        except Exception as e:
            logger.error(f"type字段清洗失败: {e}")

        return df

    def clean_fuwu_medical_supplies_data(self, df):
        """
        清洗医用耗材数据

        Args:
            df (pd.DataFrame): 原始数据框

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        import pandas as pd

        logger.info(f"开始清洗医用耗材数据，共 {len(df)} 条记录")

        # 1. 基础数据过滤和清洗
        df_filtered = df.copy()

        # 2. 处理medListCodg字段 - 作为唯一标识
        if 'medListCodg' in df_filtered.columns:
            # 清理空值和无效值
            df_filtered = df_filtered[df_filtered['medListCodg'].notna()]
            df_filtered = df_filtered[df_filtered['medListCodg'].astype(str).str.strip() != '']
            logger.info(f"medListCodg字段清洗完成，保留 {len(df_filtered)} 条有效记录")

        # 3. 处理分类字段
        type_fields = ['firstMcsType', 'secondMcsType', 'thirdMcsType']
        for field in type_fields:
            if field in df_filtered.columns:
                # 清理分类字段的空值
                df_filtered[field] = df_filtered[field].fillna('')
                df_filtered[field] = df_filtered[field].astype(str).str.strip()
                logger.info(f"{field}字段清洗完成")

        # 4. 处理mcsType字段（分类明细）
        if 'mcsType' in df_filtered.columns:
            df_filtered['mcsType'] = df_filtered['mcsType'].fillna('')
            df_filtered['mcsType'] = df_filtered['mcsType'].astype(str).str.strip()
            logger.info("mcsType字段清洗完成")

        # 5. 处理名称字段
        name_fields = ['mcsName', 'hiGenname']
        for field in name_fields:
            if field in df_filtered.columns:
                # 清理名称字段
                df_filtered[field] = df_filtered[field].fillna('')
                df_filtered[field] = df_filtered[field].astype(str).str.strip()
                logger.info(f"{field}字段清洗完成")

        # 6. 处理规格和材质字段
        spec_fields = ['spec', 'mcsMatl']
        for field in spec_fields:
            if field in df_filtered.columns:
                df_filtered[field] = df_filtered[field].fillna('')
                df_filtered[field] = df_filtered[field].astype(str).str.strip()
                logger.info(f"{field}字段清洗完成")

        # 7. 处理生产企业名称
        if 'prodentpName' in df_filtered.columns:
            df_filtered['prodentpName'] = df_filtered['prodentpName'].fillna('')
            df_filtered['prodentpName'] = df_filtered['prodentpName'].astype(str).str.strip()
            logger.info("prodentpName字段清洗完成")

        # 8. 处理rid字段
        if 'rid' in df_filtered.columns:
            df_filtered['rid'] = df_filtered['rid'].fillna('')
            df_filtered['rid'] = df_filtered['rid'].astype(str).str.strip()
            logger.info("rid字段清洗完成")

        # 9. 数据质量检查
        # 检查必要字段是否存在
        required_fields = ['medListCodg', 'mcsName']
        missing_required = []
        for field in required_fields:
            if field not in df_filtered.columns:
                missing_required.append(field)

        if missing_required:
            logger.warning(f"缺少必要字段: {missing_required}")

        # 10. 去重处理（基于medListCodg）
        initial_count = len(df_filtered)
        df_filtered = df_filtered.drop_duplicates(subset=['medListCodg'], keep='first')
        final_count = len(df_filtered)

        if initial_count != final_count:
            logger.info(f"去重处理: 原始 {initial_count} 条，去重后 {final_count} 条，删除重复 {initial_count - final_count} 条")

        logger.info(f"医用耗材数据清洗完成，最终保留 {len(df_filtered)} 条记录")

        return df_filtered

    def clean_fuwu_medical_supplies_register_data(self, df):
        """
        清洗医用耗材注册信息数据（一对多映射）

        主要处理：
        1. 过滤只有 rregisterMessageDTOS 有值的记录
        2. 解析 rregisterMessageDTOS JSON字段
        3. 实现一对多映射：一个medListCodg对应多个注册信息
        4. 创建目标字段：code, registration_number, single_product_name

        Args:
            df (pd.DataFrame): 原始数据框

        Returns:
            pd.DataFrame: 清洗后的DataFrame（一对多展开后的数据）
        """
        import pandas as pd
        import json

        logger.info(f"开始清洗医用耗材注册信息数据，共 {len(df)} 条记录")

        # 1. 过滤只有 rregisterMessageDTOS 有值的记录
        if 'rregisterMessageDTOS' not in df.columns:
            logger.warning("源数据中没有 rregisterMessageDTOS 字段，返回空DataFrame")
            return pd.DataFrame()

        # 过滤条件：rregisterMessageDTOS 不为空且不为空字符串且不为空数组
        df_filtered = df[
            df['rregisterMessageDTOS'].notna() &
            (df['rregisterMessageDTOS'] != '') &
            (df['rregisterMessageDTOS'] != '[]')
        ].copy()

        logger.info(f"过滤后保留 {len(df_filtered)} 条有 rregisterMessageDTOS 数据的记录")

        if df_filtered.empty:
            logger.warning("过滤后没有有效数据，返回空DataFrame")
            return pd.DataFrame()

        # 2. 解析 rregisterMessageDTOS JSON字段并实现一对多映射
        expanded_records = []
        parse_errors = 0
        total_register_items = 0

        for index, row in df_filtered.iterrows():
            medListCodg = row.get('medListCodg', '')
            rregisterMessageDTOS = row.get('rregisterMessageDTOS', '')

            try:
                # 解析JSON数据
                if isinstance(rregisterMessageDTOS, str):
                    # 处理可能的格式问题
                    rregisterMessageDTOS = rregisterMessageDTOS.strip()

                    # 修复JSON格式问题：将单引号替换为双引号
                    rregisterMessageDTOS = rregisterMessageDTOS.replace("'", '"')

                    # 修复无效的转义字符问题
                    # 处理类似 \xad\xad 这样的无效转义序列
                    import re
                    # 移除无效的转义序列，保留有效的转义字符
                    rregisterMessageDTOS = re.sub(r'\\x[0-9a-fA-F]{2}', '', rregisterMessageDTOS)
                    # 移除其他可能的无效转义字符（除了常见的 \n, \t, \r, \", \\）
                    rregisterMessageDTOS = re.sub(r'\\(?![ntr"\\])', '', rregisterMessageDTOS)

                    if rregisterMessageDTOS.startswith('[') and rregisterMessageDTOS.endswith(']'):
                        # 标准JSON数组格式
                        register_items = json.loads(rregisterMessageDTOS)
                    else:
                        # 可能是多个JSON对象用逗号分隔的格式
                        # 例如：{"specMolNum": "3", "regFilNo": "-", "sinProdName": "锁定接骨板"},{"specMolNum": "3", "regFilNo": "-", "sinProdName": "锁定接骨板-胫骨"}
                        if rregisterMessageDTOS.startswith('{'):
                            # 添加数组括号
                            rregisterMessageDTOS = '[' + rregisterMessageDTOS + ']'
                            register_items = json.loads(rregisterMessageDTOS)
                        else:
                            logger.warning(f"无法识别的JSON格式: {rregisterMessageDTOS[:100]}...")
                            parse_errors += 1
                            continue
                else:
                    # 如果已经是列表或字典
                    register_items = rregisterMessageDTOS

                # 确保是列表格式
                if isinstance(register_items, dict):
                    register_items = [register_items]
                elif not isinstance(register_items, list):
                    logger.warning(f"rregisterMessageDTOS 数据格式不正确: {type(register_items)}")
                    parse_errors += 1
                    continue

                # 3. 为每个注册项创建一条记录
                for item in register_items:
                    if isinstance(item, dict):
                        # 提取注册信息
                        regFilNo = item.get('regFilNo', '')
                        sinProdName = item.get('sinProdName', '')

                        # 创建展开后的记录
                        expanded_record = {
                            # 目标字段（直接映射）
                            'code': medListCodg,  # 来源于 medListCodg
                            'registration_number': regFilNo,  # 来源于 regFilNo
                            'single_product_name': sinProdName,  # 来源于 sinProdName
                            # 保留原始字段以支持字段映射
                            'medListCodg': medListCodg,  # 保留原始字段
                            'rregisterMessageDTOS': str(rregisterMessageDTOS),  # 保留原始字段
                            # 保留其他可能需要的字段
                            'specMolNum': item.get('specMolNum', ''),
                            '_source_medListCodg': medListCodg,  # 保留原始字段用于调试
                            '_source_rregisterMessageDTOS': str(rregisterMessageDTOS)[:200]  # 保留原始数据片段用于调试
                        }

                        expanded_records.append(expanded_record)
                        total_register_items += 1
                    else:
                        logger.warning(f"注册项数据格式不正确: {type(item)}")
                        parse_errors += 1

            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败 (medListCodg: {medListCodg}): {str(e)}")
                parse_errors += 1
            except Exception as e:
                logger.warning(f"处理记录失败 (medListCodg: {medListCodg}): {str(e)}")
                parse_errors += 1

        # 4. 创建展开后的DataFrame
        if expanded_records:
            df_expanded = pd.DataFrame(expanded_records)

            # 5. 数据清洗和标准化
            # 清理空值和无效值
            for field in ['code', 'registration_number', 'single_product_name']:
                if field in df_expanded.columns:
                    # 清理空字符串和'-'值
                    df_expanded[field] = df_expanded[field].replace(['', '-', '--'], None)
                    df_expanded[field] = df_expanded[field].astype(str).str.strip()
                    df_expanded[field] = df_expanded[field].replace(['', 'None', 'nan'], None)

            # 6. 去重处理（基于复合唯一索引）
            initial_count = len(df_expanded)
            df_expanded = df_expanded.drop_duplicates(
                subset=['code', 'registration_number', 'single_product_name'],
                keep='first'
            )
            dedup_count = len(df_expanded)

            if initial_count != dedup_count:
                logger.info(f"去重处理: 原始 {initial_count} 条，去重后 {dedup_count} 条，删除重复 {initial_count - dedup_count} 条")

            # 7. 过滤无效记录（registration_number和single_product_name都为空的记录）
            # 只有code有值，但registration_number和single_product_name都为空的记录是无效的
            before_filter_count = len(df_expanded)
            df_expanded = df_expanded[
                df_expanded['single_product_name'].notna() |  # single_product_name有值
                df_expanded['registration_number'].notna()    # 或者registration_number有值
            ]
            after_filter_count = len(df_expanded)

            if before_filter_count != after_filter_count:
                filtered_count = before_filter_count - after_filter_count
                logger.info(f"无效记录过滤: 过滤前 {before_filter_count} 条，过滤后 {after_filter_count} 条，删除无效记录 {filtered_count} 条")

            # 8. 数据质量统计
            valid_code_count = df_expanded['code'].notna().sum()
            valid_registration_number_count = df_expanded['registration_number'].notna().sum()
            valid_single_product_name_count = df_expanded['single_product_name'].notna().sum()

            logger.info(f"医用耗材注册信息数据清洗完成:")
            logger.info(f"  - 输入记录数: {len(df_filtered)}")
            logger.info(f"  - 解析出注册项: {total_register_items}")
            logger.info(f"  - 解析错误数: {parse_errors}")
            logger.info(f"  - 去重后记录数: {dedup_count}")
            logger.info(f"  - 最终输出记录数: {after_filter_count}")
            logger.info(f"  - 有效code数: {valid_code_count}")
            logger.info(f"  - 有效registration_number数: {valid_registration_number_count}")
            logger.info(f"  - 有效single_product_name数: {valid_single_product_name_count}")

            return df_expanded
        else:
            logger.warning("没有成功解析出任何注册信息，返回空DataFrame")
            return pd.DataFrame()


# 创建国家医保局数据清洗配置实例
gjyb_data_cleaning_config = GjybDataCleaningConfig()


# 提供便捷的函数接口
def get_gjyb_table_cleaning_rules(table_name: str):
    """获取国家医保局表级别清洗规则"""
    return gjyb_data_cleaning_config.get_table_cleaning_rules().get(table_name, {})


def get_gjyb_field_cleaning_rules(table_name: str, field_name: str = None):
    """获取国家医保局字段级别清洗规则"""
    table_rules = gjyb_data_cleaning_config.get_field_cleaning_rules().get(table_name, {})
    if field_name:
        return table_rules.get(field_name, {})
    return table_rules


def get_gjyb_custom_cleaning_functions():
    """获取国家医保局自定义清洗函数"""
    return gjyb_data_cleaning_config.get_custom_cleaning_functions()


# 向后兼容的清洗函数
def clean_fuwu_hospital_data(df, province='gjyb'):
    """清洗医保定点机构数据（向后兼容）"""
    return gjyb_data_cleaning_config._clean_fuwu_hospital_data(df)


def clean_fuwu_pharmacy_data(df, province='gjyb'):
    """清洗医保定点药店数据（向后兼容）"""
    return gjyb_data_cleaning_config._clean_fuwu_pharmacy_data(df)


def clean_fuwu_western_medicine_data(df, province='gjyb'):
    """清洗西药数据（向后兼容）"""
    return gjyb_data_cleaning_config._clean_fuwu_western_medicine_data(df)


def clean_fuwu_chinese_medicine_data(df, province='gjyb'):
    """清洗中成药数据（向后兼容）"""
    return gjyb_data_cleaning_config._clean_fuwu_chinese_medicine_data(df)


def clean_fuwu_tcm_herb_data(df, province='gjyb'):
    """清洗中草药数据（向后兼容）"""
    return gjyb_data_cleaning_config._clean_fuwu_tcm_herb_data(df)


def clean_fuwu_selfprep_medicine_data(df, province='gjyb'):
    """清洗自制药数据（向后兼容）"""
    return gjyb_data_cleaning_config._clean_fuwu_selfprep_medicine_data(df)


def clean_fuwu_medical_supplies_data(df, province='gjyb'):
    """清洗医用耗材数据（向后兼容）"""
    return gjyb_data_cleaning_config.clean_fuwu_medical_supplies_data(df)


def clean_fuwu_medical_supplies_register_data(df, province='gjyb'):
    """清洗医用耗材注册信息数据（向后兼容）"""
    return gjyb_data_cleaning_config.clean_fuwu_medical_supplies_register_data(df)
