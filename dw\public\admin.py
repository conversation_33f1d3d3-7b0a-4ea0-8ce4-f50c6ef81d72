from django.contrib import admin

from public.models import PublicIndicatorData, PublicIndicatorMain, PublicSqlTemplate, SystemDict, SystemDictValue, \
    PublicSqlScope, PublicSqlType, PublicStatistics, PublicTarget, PublicAreaBaseInsure, \
    PublicMapping, PublicDatabaseInfo, PublicTableInfo, PublicColumnInfo, PublicIndexInfo, PublicHoliday

from public.forms import StatisticsForm, MappingForm, TargetForm


# Register your models here


@admin.register(PublicIndicatorData)
class PublicIndicatorDataAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'publish_time', 'end_time', 'value', 'description',
                    'create_time', 'update_time')
    list_filter = ('publish_time',)
    search_fields = ('code',)
    ordering = ['code', '-end_time']


@admin.register(PublicIndicatorMain)
class PublicIndicatorMainAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'name', 'area', 'currency', 'freq', 'unit', 'product_set_code',
                    'statistical_type', 'description')
    list_filter = ('product_set_code', 'statistical_type', 'freq', 'unit')
    search_fields = ('code', 'name')
    ordering = ['-id']

    actions = ['freq_add', 'copy_records']

    def freq_add(self, request, queryset):
        for obj in queryset:
            if not obj.freq:
                obj.freq = '日'
            obj.save()
            # 向用户显示成功消息
            self.message_user(request, '频度空值赋值成功')

    freq_add.short_description = '频度赋值'

    def copy_records(self, request, queryset):
        # 遍历所选的记录
        for obj in queryset:
            new_obj = PublicIndicatorMain(
                code=obj.code + '_copy',
                name=obj.name + '_copy',
                area=obj.area,
                currency=obj.currency,
                freq=obj.freq,
                unit=obj.unit,
                product_set_code=obj.product_set_code,
                statistical_type=obj.statistical_type,
                description=obj.description,
            )
            new_obj.save()
            self.message_user(request, '复制记录成功')

    copy_records.short_description = '复制记录'


@admin.register(PublicSqlTemplate)
class PublicSqlTemplateAdmin(admin.ModelAdmin):

    # 确保自定义方法被正确定义在类内部
    def get_sql_type_display(self, obj):
        return ", ".join([type_.value for type_ in obj.type.all()])

    get_sql_type_display.short_description = 'SQL分类'  # 指定中文列头

    def get_sql_scope_display(self, obj):
        return ", ".join([scope.value for scope in obj.scope.all()])

    get_sql_scope_display.short_description = '适用产品范围'  # 指定中文列头

    list_display = (
        'id', 'name', 'name_en', 'get_sql_type_display', 'get_sql_scope_display', 'description',
        'is_param',
        'param_names', 'param_values', 'param_description', 'is_complete')
    list_filter = ('name', 'name_en', 'scope', 'type')
    search_fields = ('template', 'name')
    ordering = ['-update_time']


@admin.register(SystemDict)
class SysDictAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'type', 'status', 'description', 'create_time', 'update_time')
    list_filter = ('type',)
    search_fields = ('name',)
    ordering = ['name', '-id']


@admin.register(SystemDictValue)
class SysDictValueAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'dict_id', 'position', 'label', 'key', 'status', 'description', 'create_time',
        'update_time')
    list_filter = ('label',)
    search_fields = ('dict_id',)
    ordering = ['dict_id', '-id']


@admin.register(PublicSqlScope)
class PublicSqlScopeAdmin(admin.ModelAdmin):
    list_display = ('id', 'scope', 'value', 'create_time', 'update_time')
    ordering = ['-id']


@admin.register(PublicSqlType)
class PublicSqlTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'type', 'value', 'create_time', 'update_time')
    ordering = ['-id']


@admin.register(PublicStatistics)
class PublicStatisticsAdmin(admin.ModelAdmin):
    form = StatisticsForm
    list_display = (
        'id', 'product_set_code', 'publish_time', 'get_type_display', 'get_statis_type_display', 'key', 'value',
        'additional_info',
        'create_time', 'update_time')
    list_filter = ('product_set_code', 'type', 'statistical_type', 'additional_info', 'publish_time')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', 'statistical_type', '-update_time']

    def get_statis_type_display(self, obj):
        try:
            sys_dict_entry = SystemDictValue.objects.get(label=obj.statistical_type, dict_id=1)
            return sys_dict_entry.key
        except SystemDictValue.DoesNotExist:
            return "未知"

    get_statis_type_display.short_description = '统计分类'

    def get_type_display(self, obj):
        try:
            sys_dict_entry = SystemDictValue.objects.get(label=obj.type, dict_id=2)
            return sys_dict_entry.key
        except SystemDictValue.DoesNotExist:
            return "未知"

    get_type_display.short_description = '分类'


@admin.register(PublicTarget)
class PublicTargetAdmin(admin.ModelAdmin):
    form = TargetForm
    list_display = (
        'id', 'product_set_code', 'publish_time', 'get_target_type_display', 'name', 'code', 'short_name', 'target',
        'create_time',
        'update_time')
    list_filter = ('product_set_code', 'type')
    search_fields = ('short_name', 'code')
    ordering = ['-id']

    def get_target_type_display(self, obj):
        try:
            sys_dict_entry = SystemDictValue.objects.get(label=obj.type, dict_id=7)
            return sys_dict_entry.key
        except SystemDictValue.DoesNotExist:
            return "未知"

    get_target_type_display.short_description = '分类'


@admin.register(PublicAreaBaseInsure)
class PublicAreaBaseInsureAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code', 'publish_time', 'name', 'code', 'count', 'employee_count', 'resident_count', 'target',
        'create_time','update_time')
    list_filter = ('product_set_code',)
    search_fields = ('name', 'code')
    ordering = ['-id']


@admin.register(PublicMapping)
class PublicMappingAdmin(admin.ModelAdmin):
    form = MappingForm
    list_display = (
        'id', 'get_type_display', 'name', 'keywords', 'medical_keywords', 'drug_keywords',
        'create_time', 'update_time')
    list_filter = ('name', 'type',)
    search_fields = ('type',)

    def get_type_display(self, obj):
        try:
            sys_dict_entry = SystemDictValue.objects.get(label=obj.type, dict_id=3)
            return sys_dict_entry.key
        except SystemDictValue.DoesNotExist:
            return "未知"

    get_type_display.short_description = '分类'


# 数据字典相关模型管理
@admin.register(PublicDatabaseInfo)
class PublicDatabaseInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'type', 'description', 'data_source', 'update_frequency', 'create_time', 'update_time')
    list_filter = ('type', 'data_source', 'update_frequency')
    search_fields = ('name', 'description')
    ordering = ['name']
    readonly_fields = ('create_time', 'update_time')


@admin.register(PublicTableInfo)
class PublicTableInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'database_id', 'database_name', 'name', 'comment', 'data_source', 'type', 'is_deprecated', 'create_time', 'update_time')
    list_filter = ('database_name', 'type', 'is_deprecated', 'data_source', 'update_frequency')
    search_fields = ('name', 'comment', 'description', 'database_name')
    ordering = ['database_name', 'name']
    readonly_fields = ('create_time', 'update_time')


@admin.register(PublicColumnInfo)
class PublicColumnInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'table_id', 'table_name', 'name', 'comment', 'data_type', 'type', 'is_nullable', 'is_primary_key', 'is_unique', 'ordinal_position')
    list_filter = ('table_name', 'data_type', 'is_nullable', 'is_primary_key', 'is_unique', 'is_indexed', 'is_auto_increment')
    search_fields = ('name', 'comment', 'description', 'table_name')
    ordering = ['table_name', 'ordinal_position']
    readonly_fields = ('create_time', 'update_time')


@admin.register(PublicIndexInfo)
class PublicIndexInfoAdmin(admin.ModelAdmin):
    list_display = ('id', 'table_id', 'table_name', 'name', 'type', 'is_unique', 'is_primary', 'column_names', 'comment')
    list_filter = ('table_name', 'type', 'is_unique', 'is_primary')
    search_fields = ('name', 'comment', 'column_names', 'table_name')
    ordering = ['table_name', 'name']
    readonly_fields = ('create_time', 'update_time')


@admin.register(PublicHoliday)
class PublicHolidayAdmin(admin.ModelAdmin):
    list_display = ('id', 'date', 'get_work_status_display', 'create_time', 'update_time')
    list_filter = ('is_work', 'create_time')
    search_fields = ('date',)
    ordering = ['-date']
    readonly_fields = ('create_time', 'update_time')

    def get_work_status_display(self, obj):
        """显示工作日状态的中文描述"""
        return "工作日" if obj.is_work == '1' else "节假日"

    get_work_status_display.short_description = '工作日状态'
    get_work_status_display.admin_order_field = 'is_work'  # 允许按此字段排序
