# GJYB 数据清洗配置优化建议

## 当前问题分析

通过对比 GJYB 和 NJYB 的清洗配置，发现以下问题：

### 1. 代码重复严重

**示例1：字段分割逻辑重复**
```python
# 在 gjyb 的 _clean_fuwu_service_facilities_data 中
def extract_charge_code_for_name_mapping(x):
    if pd.notna(x) and str(x).strip():
        x_str = str(x).strip()
        if '-' in x_str:
            parts = x_str.split('-', 1)
            if len(parts) > 1:
                return parts[1]
        return ''
    return ''

# 在 gjyb 的 _process_medListCodg_for_charge_code 中
def _process_medListCodg_for_charge_code(self, value):
    if pd.notna(value) and str(value).strip():
        value_str = str(value).strip()
        if '-' in value_str:
            parts = value_str.split('-', 1)
            if len(parts) > 1:
                return parts[1]
    return ''
```

**示例2：无效值清理逻辑重复**
```python
# 在西药清洗中
company_fields = ['prodentpName', 'lstdLicHolder']
for field in company_fields:
    mask_dash = df[field] == '--'
    df.loc[mask_dash, field] = None

# 在中成药清洗中（完全相同的逻辑）
company_fields = ['prodentpName', 'lstdLicHolder']
for field in company_fields:
    mask_dash = df[field] == '--'
    df.loc[mask_dash, field] = None
```

### 2. 字典验证逻辑重复

```python
# 在 gjyb 的 _validate_medins_type 中
# 在 njyb 的 _validate_medins_type 中
# 在 gjyb 的 _clean_fuwu_tcm_herb_data 中
# 都有类似的字典验证逻辑，代码基本相同
```

## 优化建议

### 1. 使用通用字段处理函数

**优化前（重复代码）**：
```python
# 在多个函数中重复出现
def extract_charge_code_for_name_mapping(x):
    if pd.notna(x) and str(x).strip():
        x_str = str(x).strip()
        if '-' in x_str:
            parts = x_str.split('-', 1)
            if len(parts) > 1:
                return parts[1]
        return ''
    return ''
```

**优化后（使用通用函数）**：
```python
# 在 gjyb 配置中导入通用函数
from transfrom.utils.common_field_processing import CommonFieldProcessing

# 直接调用通用函数
charge_code = CommonFieldProcessing.extract_code_from_composite_field(value, '-', 'after')
code = CommonFieldProcessing.extract_code_from_composite_field(value, '-', 'before')
```

### 2. 统一无效值清理

**优化前（重复代码）**：
```python
# 在西药清洗中
company_fields = ['prodentpName', 'lstdLicHolder']
for field in company_fields:
    before_count = df[field].notna().sum()
    dash_count = (df[field] == '--').sum()
    mask_dash = df[field] == '--'
    df.loc[mask_dash, field] = None
    after_count = df[field].notna().sum()
    logger.info(f"{field}字段清洗完成: 清洗前 {before_count} 条，清洗后 {after_count} 条")

# 在中成药清洗中（完全相同的逻辑）
# ... 重复的代码
```

**优化后（使用通用函数）**：
```python
# 统一处理多个字段
company_fields = ['prodentpName', 'lstdLicHolder']
for field in company_fields:
    df = CommonFieldProcessing.clean_invalid_values(df, field, ['--'])
    # 如果需要额外的逗号分号处理
    if field in df.columns and df[field].notna().any():
        df[field] = df[field].astype(str).str.replace(r'[，,；;]', ',', regex=True)
```

### 3. 统一字典验证

**优化前（重复代码）**：
```python
def _validate_medins_type(self, df):
    # 84行重复的字典验证逻辑
    if 'medinsType' not in df.columns:
        return df
    try:
        from transfrom.utils.field_mapping import DictMappingManager
        dict_mapping_manager = DictMappingManager()
        institution_type = dict_mapping_manager.get_dict_mapping(dict_id=24)
        # ... 更多重复代码
```

**优化后（使用通用函数）**：
```python
# 直接调用通用函数
df = CommonFieldProcessing.validate_field_against_dict(df, 'medinsType', 24, 'medinsTypeName')
```

### 4. 优化复合字段处理

**优化前（内联函数）**：
```python
def vectorized_get_name_by_code(codes_series):
    result = []
    for code in codes_series:
        if pd.notna(code) and str(code).strip():
            code_str = str(code).strip()
            if '-' in code_str:
                parts = code_str.split('-', 1)
                extracted_code = parts[0]
                # ... 更多处理逻辑
    return pd.Series(result, index=codes_series.index)
```

**优化后（使用通用函数）**：
```python
# 提取code部分
df['extracted_code'] = df['medListCodg'].apply(
    lambda x: CommonFieldProcessing.extract_code_from_composite_field(x, '-', 'before')
)

# 提取charge_item_code部分
df['extracted_charge_code'] = df['medListCodg'].apply(
    lambda x: CommonFieldProcessing.extract_code_from_composite_field(x, '-', 'after')
)
```

## 具体优化步骤

### 步骤1：导入通用函数模块

```python
# 在 gjyb/pipeline/config/data_cleaning_config.py 顶部添加
from transfrom.utils.common_field_processing import CommonFieldProcessing
```

### 步骤2：替换字段分割逻辑

将所有的字段分割逻辑替换为通用函数调用：

```python
# 替换 _process_medListCodg_for_charge_code 函数
def _process_medListCodg_for_charge_code(self, value):
    return CommonFieldProcessing.extract_code_from_composite_field(value, '-', 'after')

# 替换内联的分割函数
# 原来的 extract_charge_code_for_name_mapping 函数可以删除
```

### 步骤3：统一无效值清理

```python
# 在西药和中成药清洗函数中
def _clean_fuwu_western_medicine_data(self, df):
    logger.info(f"开始清洗西药数据，共 {len(df)} 条记录")
    
    # 1. 统一处理无效值字段
    company_fields = ['prodentpName', 'lstdLicHolder']
    for field in company_fields:
        df = CommonFieldProcessing.clean_invalid_values(df, field, ['--'])
        # 处理逗号分号替换
        if field in df.columns and df[field].notna().any():
            df[field] = df[field].astype(str).str.replace(r'[，,；;]', ',', regex=True)
    
    # 2. 处理其他无效值字段
    null_dash_fields = ['natHiDruglistChrgitmLv', 'natDrugNo', 'drugGenname', 'natHiDruglistDosform', 'natHiDruglistMemo']
    for field in null_dash_fields:
        df = CommonFieldProcessing.clean_invalid_values(df, field, ['--'])
    
    # 3. 处理特殊字段
    special_fields = ['drugProdname', 'pacmatl']
    for field in special_fields:
        df = CommonFieldProcessing.clean_invalid_values(df, field, ['无'])
    
    # 4. eachDos字段的特殊处理保持不变（因为逻辑比较复杂）
    # ...
```

### 步骤4：统一字典验证

```python
# 删除 _validate_medins_type 函数，直接在需要的地方调用
df = CommonFieldProcessing.validate_field_against_dict(df, 'medinsType', 24, 'medinsTypeName')
```

### 步骤5：优化省份映射处理

```python
# 在 _clean_fuwu_tcm_herb_data 中
def _clean_fuwu_tcm_herb_data(self, df):
    logger.info(f"开始清洗中草药数据，共 {len(df)} 条记录")
    
    # 使用通用函数处理省份映射
    if 'admdvs' in df.columns:
        df['admdvs'] = df['admdvs'].astype(str)
        df['province_code'] = df['admdvs']
        
        # 使用通用的字典映射功能
        # 这部分可以进一步抽取到通用函数中
        try:
            from transfrom.utils.field_mapping import DictMappingManager
            dict_mapping_manager = DictMappingManager()
            region_mapping_df = dict_mapping_manager.get_dict_mapping(dict_id=19)
            
            if not region_mapping_df.empty:
                region_mapping_dict = dict(zip(region_mapping_df['dict_code'], region_mapping_df['dict_name']))
                df['province_name'] = df['admdvs'].map(region_mapping_dict).fillna('')
                df['_province_name_cleaned'] = True
        except Exception as e:
            logger.warning(f"处理admdvs字段时出错: {e}")
            df['province_name'] = ''
    
    # 其他处理逻辑...
```

## 预期效果

### 1. 代码量减少
- **GJYB配置文件**：预计可减少约400-500行重复代码
- **维护成本降低**：统一的处理逻辑便于维护和调试

### 2. 一致性提升
- **处理标准统一**：所有省份使用相同的处理逻辑
- **bug修复效率**：修复通用函数中的问题，所有使用的地方都会受益

### 3. 扩展性增强
- **新省份开发**：可直接复用通用函数，减少开发工作量
- **功能增强**：在通用函数中增加新功能，所有使用的地方都会受益

## 实施建议

1. **分步实施**：建议分步骤实施，每次优化一个功能模块
2. **充分测试**：每次优化后进行充分测试，确保功能正常
3. **向后兼容**：保留原有函数作为备份，标记为废弃
4. **文档更新**：及时更新相关文档和注释

通过这些优化，GJYB的清洗配置将更加简洁、高效，并且与NJYB保持一致的设计理念。
