# ETL多表配置使用说明

## 📋 概述

现在的ETL系统支持多表处理，通过配置文件的方式管理不同表的ETL参数，使系统更加灵活和可扩展。

## 🗂️ 文件结构

```
pipeline/
├── fuwu_etl_mapping.py      # 主执行文件
├── config/
│   ├── __init__.py          # 配置模块初始化
│   ├── field_config.py      # 字段处理配置
│   └── table_configs.py     # 表配置文件
└── files/
    └── README_配置说明.md   # 本说明文件
```

## ⚙️ 如何添加新表配置

### 1. 编辑 `config/table_configs.py` 文件

在 `TABLE_CONFIGS` 字典中添加新的表配置：

```python
TABLE_CONFIGS = {
    # 现有配置...

    "新表名称": {
        "source_table": "源表名",
        "target_table": "目标表名",
        "unique_fields": ['唯一字段1', '唯一字段2'],
        "description": "表的描述信息",
        "icon": "🆕"  # 可选的图标
    }
}
```

### 2. 配置参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `source_table` | string | ✅ | 爬虫数据源表名 |
| `target_table` | string | ✅ | 目标数据表名 |
| `unique_fields` | list | ✅ | 用于判断记录唯一性的字段列表 |
| `description` | string | ✅ | 表的描述信息，显示在菜单中 |
| `icon` | string | ❌ | 显示图标，可选 |
| `target_where_clause` | string | ❌ | 目标表数据过滤条件，用于多表共享目标表的场景 |

### 3. 示例配置

#### 基本配置（一对一表映射）
```python
# 基本的一对一表映射
"药品信息": {
    "source_table": "spider_drug_info",
    "target_table": "medical_drugs",
    "unique_fields": ['drug_code', 'approval_number'],
    "description": "药品基础信息数据",
    "icon": "[药品]"
}
```

#### 高级配置（多表共享目标表）
```python
# 多个爬虫表写入同一个目标表的场景
"医保定点机构": {
    "source_table": "spider_fuwu_fixed_hospital",
    "target_table": "medical_designated_providers",
    "unique_fields": ['province_code', 'city_code', 'code'],
    "description": "医保定点医疗机构数据",
    "icon": "[医院]",
    "target_where_clause": "category_name='定点医疗机构'"  # 只处理医疗机构数据
},

"医保定点药店": {
    "source_table": "spider_fuwu_retail_pharmacy",
    "target_table": "medical_designated_providers",  # 同一个目标表
    "unique_fields": ['province_code', 'city_code', 'code'],
    "description": "医保定点药店数据",
    "icon": "[药店]",
    "target_where_clause": "category_name='定点零售药店'"  # 只处理药店数据
}
```

## 🎯 使用流程

### 1. 运行程序
```bash
python fuwu_etl_mapping.py
```

### 2. 选择表
程序会显示所有可用的表配置：
```
🎯 请选择要处理的表:
1. 🏥 医保定点机构 - 医保定点医疗机构数据
2. 💊 医保定点药店 - 医保定点药店数据
3. 👨‍⚕️ 医生信息 - 医生信息数据
4. 💉 药品信息 - 药品基础信息数据
5. 🩺 医疗服务项目 - 医疗服务项目数据
```

### 3. 选择处理模式
```
🎯 请选择处理模式:
1. 🧪 测试模式 (处理1000条数据)
2. ⚡ 全量处理 (高效模式，不查询总量)
3. 📊 全量处理 (先查询总量)
4. ⚙️ 自定义数量 (用户指定处理数量)
```

### 4. 选择操作模式
```
🔧 请选择操作模式:
1. 🚀 Upsert模式 (推荐，支持增删改)
2. 📝 传统模式 (仅插入新数据)
```

### 5. 确认执行
程序会显示执行摘要，确认后开始处理。

## 🔧 高级配置

### 1. 修改默认值

在 `config/table_configs.py` 中可以修改默认配置：

```python
# 默认批处理大小
DEFAULT_BATCH_SIZE = 10000

# 默认表（当选择无效时使用）
DEFAULT_TABLE = "医保定点机构"
```

### 2. 添加新的处理模式

可以在 `PROCESSING_MODES` 中添加新的处理模式：

```python
PROCESSING_MODES = {
    # 现有模式...

    "5": {
        "name": "增量模式",
        "description": "只处理最近更新的数据",
        "icon": "🔄",
        "limit": None,
        "process_all": False
    }
}
```

### 3. 自定义字段映射

如果需要特殊的字段映射规则，可以在配置中添加：

```python
"特殊表": {
    "source_table": "spider_special_table",
    "target_table": "target_special_table",
    "unique_fields": ['id'],
    "description": "特殊处理的表",
    "icon": "⭐",
    "custom_mapping": True,  # 标记需要特殊处理
    "mapping_rules": {       # 自定义映射规则
        "field1": "target_field1",
        "field2": "target_field2"
    }
}
```

## 🎨 图标建议

常用的表类型图标：

- 🏥 医院/医疗机构
- 💊 药店/药品
- 👨‍⚕️ 医生/医护人员
- 💉 药品/注射剂
- 🩺 医疗设备/服务
- 📋 基础数据/配置
- 🔬 检验/化验
- 🏢 机构/组织
- 📊 统计/报表
- 🆔 身份/认证

## 🚀 最佳实践

1. **命名规范**: 使用清晰、一致的表名和字段名
2. **唯一字段**: 确保唯一字段组合能够准确标识记录
3. **描述信息**: 提供清晰的表描述，便于用户理解
4. **测试优先**: 新配置先用测试模式验证
5. **备份数据**: 重要数据处理前先备份

## 🔍 故障排除

### 常见问题

1. **表不存在**: 检查源表和目标表名是否正确
2. **字段不匹配**: 确认唯一字段在源表中存在
3. **权限问题**: 确保数据库用户有相应表的读写权限
4. **数据类型**: 检查字段映射配置是否正确

### 调试技巧

1. 使用测试模式先处理少量数据
2. 检查日志输出了解详细错误信息
3. 验证字段映射配置的完整性
4. 确认数据库连接和权限设置

---

通过这种配置化的方式，您可以轻松地添加新的表处理，而无需修改主要的业务逻辑代码。
