#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据清洗工具模块
提供各种数据清洗功能
"""

import logging
import re
import pandas as pd
from .field_mapping import DictMappingManager
from .field_config import get_data_cleaning_config
from .data_cleaning_config import (
    get_table_cleaning_rules,
    get_field_cleaning_rules,
    get_custom_cleaning_functions,
    has_cleaning_rules,
    CommonCleaningFunctions
)

# 导入省份配置以触发自动注册
try:
    import transfrom.tasks.spider.gjyb.pipeline.config
    import transfrom.tasks.spider.jsyb.pipeline.config
    import transfrom.tasks.spider.njyb.pipeline.config
except ImportError as e:
    logging.warning(f"部分省份数据清洗配置导入失败: {e}")

logger = logging.getLogger(__name__)


class DataCleaner:
    """
    数据清洗器
    提供各种数据清洗功能，支持配置化的清洗规则
    """

    def __init__(self):
        """初始化数据清洗器"""
        # 注意：preserve_empty_string_fields配置现在从统一配置模块获取
        # 不再在这里硬编码配置
        # 传统清洗规则已迁移到配置化系统，不再需要硬编码规则

    def register_cleaning_rule(self, table_name, cleaning_func):
        """
        注册新的清洗规则（已废弃，请使用配置化清洗系统）

        Args:
            table_name (str): 表名
            cleaning_func (function): 清洗函数，接受DataFrame参数，返回清洗后的DataFrame
        """
        logger.warning(f"register_cleaning_rule 方法已废弃，请使用配置化清洗系统为表 {table_name} 配置清洗规则")



    def clean_source_data(self, source_df, source_table, province='gjyb', target_table=None):
        """
        对源数据进行清洗，在数据转换前执行
        支持配置化清洗规则和向后兼容的硬编码规则

        Args:
            source_df (pd.DataFrame): 源数据DataFrame
            source_table (str): 源表名
            province (str): 省份标识，默认为'gjyb'
            target_table (str): 目标表名，用于选择特定的清洗函数

        Returns:
            pd.DataFrame: 清洗后的源数据DataFrame
        """
        if source_df.empty:
            return source_df

        # 创建副本避免修改原数据
        cleaned_df = source_df.copy()

        # 使用配置化清洗规则
        if has_cleaning_rules(province, source_table):
            logger.info(f"应用省份 '{province}' 表 '{source_table}' 的配置化清洗规则")
            cleaned_df = self._apply_configurable_cleaning(cleaned_df, source_table, province, target_table)
        else:
            logger.info(f"表 '{source_table}' 无专用清洗规则，应用通用清洗")
            cleaned_df = self._apply_general_cleaning(cleaned_df, source_table=source_table, province=province)

        logger.info(f"源数据清洗完成: {source_table} -> {target_table or '未指定'}, 处理 {len(cleaned_df)} 条记录")
        return cleaned_df

    def _apply_configurable_cleaning(self, df, source_table, province, target_table=None):
        """
        应用配置化清洗规则

        Args:
            df (pd.DataFrame): 数据DataFrame
            source_table (str): 源表名
            province (str): 省份标识
            target_table (str): 目标表名，用于选择特定的清洗函数

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 获取表级别的清洗规则
        table_rules = get_table_cleaning_rules(province, source_table)

        # 获取字段级别的清洗规则
        field_rules = get_field_cleaning_rules(province, source_table)

        # 获取自定义清洗函数
        custom_functions = get_custom_cleaning_functions(province)

        # 应用表级别的清洗规则
        if table_rules:
            logger.info(f"应用表 '{source_table}' 的表级别清洗规则")
            df = self._apply_table_level_cleaning(df, table_rules, custom_functions, source_table, target_table)

        # 应用字段级别的清洗规则
        if field_rules:
            logger.info(f"应用表 '{source_table}' 的字段级别清洗规则")
            df = self._apply_field_level_cleaning(df, field_rules, custom_functions)

        # 最后应用通用清洗
        df = self._apply_general_cleaning(df, source_table=source_table, province=province)

        return df

    def _apply_table_level_cleaning(self, df, table_rules, custom_functions, source_table=None, target_table=None):
        """
        应用表级别的清洗规则

        Args:
            df (pd.DataFrame): 数据DataFrame
            table_rules (dict): 表级别清洗规则
            custom_functions (dict): 自定义清洗函数
            source_table (str): 源表名
            target_table (str): 目标表名

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 执行表级别的清洗逻辑
        for rule_name, rule_config in table_rules.items():
            if rule_name == 'custom_function' and rule_config in custom_functions:
                # 执行自定义清洗函数
                df = custom_functions[rule_config](df)
                logger.info(f"执行了自定义表级别清洗函数: {rule_config}")
            elif rule_name == 'custom_function_by_target' and isinstance(rule_config, dict):
                # 基于目标表选择清洗函数
                if target_table and target_table in rule_config:
                    function_name = rule_config[target_table]
                    if function_name in custom_functions:
                        df = custom_functions[function_name](df)
                        logger.info(f"执行了基于目标表 '{target_table}' 的清洗函数: {function_name}")
                    else:
                        logger.warning(f"清洗函数 '{function_name}' 未找到")
                elif 'default' in rule_config:
                    # 使用默认清洗函数
                    function_name = rule_config['default']
                    if function_name in custom_functions:
                        df = custom_functions[function_name](df)
                        logger.info(f"执行了默认清洗函数: {function_name}")
                    else:
                        logger.warning(f"默认清洗函数 '{function_name}' 未找到")
            elif rule_name == 'address_cleaning' and rule_config.get('enabled', False):
                # 地址清洗
                addr_fields = rule_config.get('fields', ['addr'])
                for field in addr_fields:
                    if field in df.columns:
                        df[field] = df[field].apply(
                            lambda x: CommonCleaningFunctions.clean_address_basic(x)
                            if isinstance(x, str) and len(x) > rule_config.get('min_length', 200)
                            else x
                        )
                        logger.info(f"对字段 '{field}' 应用了地址清洗")
            elif rule_name == 'type_validation' and rule_config.get('enabled', False):
                # 机构类型验证清洗
                df = self._apply_type_validation(df, rule_config)

        return df

    def _apply_type_validation(self, df, rule_config):
        """
        应用机构类型验证清洗

        Args:
            df (pd.DataFrame): 数据DataFrame
            rule_config (dict): 类型验证规则配置

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        type_field = rule_config.get('type_field', 'medinsType')
        type_name_field = rule_config.get('type_name_field', 'medinsTypeName')
        dict_id = rule_config.get('dict_id', 24)

        if type_field not in df.columns:
            logger.info(f"字段 '{type_field}' 不存在，跳过类型验证清洗")
            return df

        try:
            # 获取字典映射
            dict_mapping_manager = DictMappingManager()
            institution_type = dict_mapping_manager.get_dict_mapping(dict_id=dict_id)

            if not institution_type.empty:
                # 获取有效的type代码列表
                dict_code_list = institution_type['dict_code'].tolist()
                logger.info(f"获取到的机构类型字典 (dict_id={dict_id}): {dict_code_list}")

                # 记录清洗前的统计
                before_clean_count = df[type_field].notna().sum()

                # 清洗type字段：只保留在字典范围内的值
                invalid_types = df[~df[type_field].isin(dict_code_list) & df[type_field].notna()][type_field].unique()
                if len(invalid_types) > 0:
                    logger.info(f"发现无效的{type_field}值: {invalid_types.tolist()}")

                df[type_field] = df[type_field].apply(lambda x: x if x in dict_code_list else None)

                # 清洗type_name字段：如果对应的type为None，则type_name也设为None
                if type_name_field in df.columns:
                    type_is_null = df[type_field].isna()
                    df.loc[type_is_null, type_name_field] = None

                # 记录清洗后的统计
                after_clean_count = df[type_field].notna().sum()
                logger.info(f"{type_field}字段清洗完成: 类型范围为 {dict_code_list}")
                logger.info(f"{type_field}字段清洗统计: 清洗前 {before_clean_count} 条有效记录，清洗后 {after_clean_count} 条有效记录")
            else:
                logger.warning(f"未获取到dict_id={dict_id}的字典数据，跳过{type_field}字段清洗")
        except Exception as e:
            logger.error(f"{type_field}字段清洗失败: {e}")

        return df

    def _apply_field_level_cleaning(self, df, field_rules, custom_functions):
        """
        应用字段级别的清洗规则

        Args:
            df (pd.DataFrame): 数据DataFrame
            field_rules (dict): 字段级别清洗规则
            custom_functions (dict): 自定义清洗函数

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        for field_name, field_config in field_rules.items():
            if field_name not in df.columns:
                continue

            for rule_name, rule_config in field_config.items():
                if rule_name == 'custom_function' and rule_config in custom_functions:
                    # 执行自定义清洗函数
                    df[field_name] = df[field_name].apply(custom_functions[rule_config])
                    logger.info(f"对字段 '{field_name}' 执行了自定义清洗函数: {rule_config}")
                elif rule_name == 'address_cleaning' and rule_config.get('enabled', False):
                    # 地址清洗
                    min_length = rule_config.get('min_length', 200)
                    df[field_name] = df[field_name].apply(
                        lambda x: CommonCleaningFunctions.clean_address_basic(x)
                        if isinstance(x, str) and len(x) > min_length
                        else x
                    )
                    df[field_name] = df[field_name].apply(
                        lambda x: CommonCleaningFunctions.clean_address_enhanced(x)
                        if isinstance(x, str) and len(x) <= min_length
                        else x
                    )
                    logger.info(f"对字段 '{field_name}' 应用了地址清洗")
                elif rule_name == 'phone_cleaning' and rule_config.get('enabled', False):
                    # 电话清洗（基础版本）
                    df[field_name] = df[field_name].apply(CommonCleaningFunctions.clean_phone_number)
                    logger.info(f"对字段 '{field_name}' 应用了基础电话清洗")
                elif rule_name == 'phone_cleaning_advanced' and rule_config.get('enabled', False):
                    # 电话清洗（高级版本）
                    df[field_name] = df[field_name].apply(CommonCleaningFunctions.clean_phone_number_advanced)
                    logger.info(f"对字段 '{field_name}' 应用了高级电话清洗")
                elif rule_name == 'email_cleaning' and rule_config.get('enabled', False):
                    # 邮箱清洗
                    df[field_name] = df[field_name].apply(CommonCleaningFunctions.clean_email)
                    logger.info(f"对字段 '{field_name}' 应用了邮箱清洗")
                elif rule_name == 'id_number_cleaning' and rule_config.get('enabled', False):
                    # 身份证号清洗
                    df[field_name] = df[field_name].apply(CommonCleaningFunctions.clean_id_number)
                    logger.info(f"对字段 '{field_name}' 应用了身份证号清洗")
                elif rule_name == 'fullwidth_to_halfwidth' and rule_config.get('enabled', False):
                    # 全角转半角处理
                    df[field_name] = df[field_name].apply(
                        lambda x: CommonCleaningFunctions.convert_fullwidth_to_halfwidth(x)
                        if isinstance(x, str) else x
                    )
                    logger.info(f"对字段 '{field_name}' 应用了全角转半角处理")

        return df

    def _apply_general_cleaning(self, df, source_table=None, province='gjyb'):
        """
        应用通用清洗规则

        Args:
            df (pd.DataFrame): 数据DataFrame
            source_table (str): 源表名，用于确定哪些字段需要保留空字符串
            province (str): 省份标识，默认为'gjyb'

        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        # 从统一配置获取需要保留空字符串的字段列表
        preserve_fields = []
        if source_table:
            config = get_data_cleaning_config(province, source_table)
            preserve_fields = config.get('preserve_empty_string_fields', [])

        # 通用清洗逻辑：去除首尾空格、处理空值等
        for col in df.columns:
            if df[col].dtype == 'object':  # 字符串类型
                # 先处理None值，避免被转换为字符串'None'
                if col in preserve_fields:
                    # 对于需要保留空字符串的字段，将None转换为空字符串
                    df[col] = df[col].fillna('')
                    # 然后对非None值进行字符串处理
                    mask = df[col] != ''
                    df.loc[mask, col] = df.loc[mask, col].astype(str).str.strip()
                    logger.info(f"字段 '{col}' 的None值已转换为空字符串")
                else:
                    # 对于其他字段，先转换为字符串再处理
                    df[col] = df[col].astype(str).str.strip()
                    # 将字符串'None'和空字符串转换为None
                    df[col] = df[col].replace(['None', ''], None)

        return df

    # 重复的清洗函数已移除，请使用配置化清洗系统
    # 相关配置在各省份的 data_cleaning_config.py 中

    # 静态方法已移动到 CommonCleaningFunctions 类中
    # 保留向后兼容的方法引用
    @staticmethod
    def clean_address_basic(address):
        """基础地址清洗函数（向后兼容）"""
        return CommonCleaningFunctions.clean_address_basic(address)

    @staticmethod
    def clean_phone_number(phone):
        """清洗电话号码（向后兼容）"""
        return CommonCleaningFunctions.clean_phone_number(phone)

    @staticmethod
    def clean_email(email):
        """清洗邮箱地址（向后兼容）"""
        return CommonCleaningFunctions.clean_email(email)

    @staticmethod
    def clean_id_number(id_number):
        """清洗身份证号码（向后兼容）"""
        return CommonCleaningFunctions.clean_id_number(id_number)

    def clean_data_for_operations(self, data_list):
        """
        对需要更新和新增的数据进行清洗
        注意：此方法主要用于upsert模式下的额外清洗，
        主要的数据清洗应该在数据转换前通过clean_source_data函数完成

        Args:
            data_list (list): 数据列表，每个元素是pandas Series

        Returns:
            list: 清洗后的数据列表
        """
        if not data_list:
            return data_list

        cleaned_data = []
        for row in data_list:
            # 对addr字段进行额外清洗（如果存在且长度大于200）
            # 这是一个保险措施，主要清洗应该在数据转换前完成
            if 'addr' in row.index and isinstance(row['addr'], str) and len(row['addr']) > 200:
                row = row.copy()
                row['addr'] = self.clean_address_basic(row['addr'])
                logger.debug(f"在upsert操作中对addr字段进行了额外清洗")

            cleaned_data.append(row)

        logger.info(f"upsert操作中清洗了 {len(cleaned_data)} 条记录")
        return cleaned_data


# TextCleaner 类的功能已整合到 CommonCleaningFunctions 中
# 保留向后兼容的类引用
class TextCleaner:
    """
    文本清洗器（向后兼容）
    功能已整合到 CommonCleaningFunctions 中
    """

    @staticmethod
    def remove_extra_spaces(text):
        """移除多余的空格（向后兼容）"""
        return CommonCleaningFunctions.remove_extra_spaces(text)

    @staticmethod
    def remove_special_characters(text, keep_chars=''):
        """移除特殊字符（向后兼容）"""
        return CommonCleaningFunctions.remove_special_characters(text, keep_chars)

    @staticmethod
    def normalize_punctuation(text):
        """标准化标点符号（向后兼容）"""
        # 这个功能在 CommonCleaningFunctions 中没有实现，保留原实现
        if not isinstance(text, str):
            return text

        # 中文标点转英文标点
        punctuation_map = {
            '，': ',',
            '。': '.',
            '；': ';',
            '：': ':',
            '？': '?',
            '！': '!',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '（': '(',
            '）': ')',
            '【': '[',
            '】': ']',
            '《': '<',
            '》': '>',
        }

        for chinese, english in punctuation_map.items():
            text = text.replace(chinese, english)

        return text


# 创建全局实例，方便直接使用
default_cleaner = DataCleaner()
default_text_cleaner = TextCleaner()

# 提供便捷的函数接口
def clean_source_data(source_df, source_table, province='gjyb', target_table=None):
    """
    清洗源数据的便捷函数

    Args:
        source_df (pd.DataFrame): 源数据DataFrame
        source_table (str): 源表名
        province (str): 省份标识，默认为'gjyb'
        target_table (str): 目标表名，用于选择特定的清洗函数

    Returns:
        pd.DataFrame: 清洗后的源数据DataFrame
    """
    return default_cleaner.clean_source_data(source_df, source_table, province, target_table)


def clean_address_basic(address):
    """
    基础地址清洗的便捷函数

    Args:
        address: 原始地址字符串

    Returns:
        清洗后的地址字符串
    """
    return DataCleaner.clean_address_basic(address)
