from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalNationalNegotiatedDrugProviders(BaseModel):
    drug_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='药品名称', **_db_comment_kwarg('药品名称'))
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='机构名称', **_db_comment_kwarg('机构名称'))
    type_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='机构类型', **_db_comment_kwarg('机构类型'))
    province_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构省份编码', **_db_comment_kwarg('机构省份编码'))
    province_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='机构省份', **_db_comment_kwarg('机构省份'))
    city_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='机构城市编码', **_db_comment_kwarg('机构城市编码'))
    city_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='机构城市', **_db_comment_kwarg('机构城市'))
    address = models.CharField(max_length=512, blank=True, null=True, verbose_name='机构地址', **_db_comment_kwarg('机构地址'))
    lat = models.CharField(max_length=128, blank=True, null=True, verbose_name='经度', **_db_comment_kwarg('经度'))
    lnt = models.CharField(max_length=128, blank=True, null=True, verbose_name='纬度', **_db_comment_kwarg('纬度'))
    production_company_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='生产企业名称', **_db_comment_kwarg('生产企业名称'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_national_negotiated_drug_providers'
        verbose_name = '国家谈判药品销售机构'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name
