import datetime
import logging
import os
import warnings

import idna
import numpy as np
import pandas as pd
import pymysql
from django.core.mail import EmailMessage
from openpyxl import load_workbook
from openpyxl.styles import PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

from dw import settings
from transfrom.utils.date import get_week_range
from transfrom.utils.utils import query_sql

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)

DB = settings.DATABASES['default']  # dw数据数据库
product_set_code_list = ['binzhou_yhbV4', 'rizhao_nxbV4', 'dezhou_hmbV3']

# TO = ['<EMAIL>']
TO = ['<EMAIL>','<EMAIL>']
CC= ['<EMAIL>']

def get_connection(DB):
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn


def get_week_data(product_set_code, start_date):
    # 本周的开始截止日期
    start_week, end_week = get_week_range(start_date)
    print(start_week, end_week)
    last_week_end_date = start_week - datetime.timedelta(days=1)
    # 上上周的最后一天作为上周的开始日期
    last_week_start_date = start_week - datetime.timedelta(days=8)
    last_week_start_date_time = last_week_start_date.strftime('%Y-%m-%d') + ' 00:00:00'
    last_week_end_date_time = last_week_end_date.strftime('%Y-%m-%d') + ' 23:59:59'

    start_datetime = last_week_end_date.strftime('%Y-%m-%d') + ' 00:00:00'
    end_datetime = end_week.strftime('%Y-%m-%d') + ' 23:59:59'
    with get_connection(DB) as conn:
        df_count_cumsum_total = pd.read_sql(
            query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=product_set_code,
                                                      statistical_type='累计值', unit='单',
                                                      freq='日', start_datetime=start_datetime,
                                                      end_datetime=end_datetime), conn)
        df_online_cumsum = df_count_cumsum_total[
            df_count_cumsum_total['name'].str.contains('销量-线上-累计值') & ~df_count_cumsum_total[
                'name'].str.contains('小时')][['end_time', 'value']]
        df_offline_cumsum = df_count_cumsum_total[
            df_count_cumsum_total['name'].str.contains('销量-线下-累计值') & ~df_count_cumsum_total[
                'name'].str.contains('小时')][['end_time', 'value']]

        # 累计销量线上、线下、总数据。.
        online_total_sum = df_online_cumsum['value'].max()
        offline_total_sum = df_offline_cumsum['value'].max()
        total_sum = online_total_sum + offline_total_sum
        df_online_cumsum.fillna(0, inplace=True)
        df_offline_cumsum.fillna(0, inplace=True)

        # 本周线上销量，用日期最大的减去最小的
        online_week_sum = df_online_cumsum['value'].max() - df_online_cumsum['value'].min()
        # 本周线下销量，用日期最大的减去最小的
        offline_week_sum = df_offline_cumsum['value'].max() - df_offline_cumsum['value'].min()
        # 本周销量
        week_sum = online_week_sum + offline_week_sum

        # 上周数据
        df_count_cumsum_total_last_week = pd.read_sql(
            query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=product_set_code,
                                                      statistical_type='累计值', unit='单',
                                                      freq='日', start_datetime=last_week_start_date_time,
                                                      end_datetime=last_week_end_date_time), conn)
        print(query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=product_set_code,
                                                      statistical_type='累计值', unit='单',
                                                      freq='日', start_datetime=last_week_start_date_time,
                                                      end_datetime=last_week_end_date_time))
        df_online_cumsum_last_week = df_count_cumsum_total_last_week[
            df_count_cumsum_total_last_week['name'].str.contains('销量-线上-累计值') & ~df_count_cumsum_total_last_week[
                'name'].str.contains('小时')][['end_time', 'value']]
        df_offline_cumsum_last_week = df_count_cumsum_total_last_week[
            df_count_cumsum_total_last_week['name'].str.contains('销量-线下-累计值') & ~df_count_cumsum_total_last_week[
                'name'].str.contains('小时')][['end_time', 'value']]
        # 本周线上销量，用日期最大的减去最小的
        online_last_week_sum = df_online_cumsum_last_week['value'].max() - df_online_cumsum_last_week['value'].min()
        # 本周线下销量，用日期最大的减去最小的
        offline_last_week_sum = df_offline_cumsum_last_week['value'].max() - df_offline_cumsum_last_week['value'].min()
        # 本周销量
        last_week_sum = online_last_week_sum + offline_last_week_sum
        # 本周与上周的差值，前面是本周，后面是差值
        if pd.isna(week_sum):
            week_sum = 0
        if pd.isna(last_week_sum):
            last_week_sum = 0
        if pd.isna(online_last_week_sum):
            online_last_week_sum = 0
        if pd.isna(offline_last_week_sum):
            offline_last_week_sum = 0
        if pd.isna(online_week_sum):
            online_week_sum = 0
        if pd.isna(offline_week_sum):
            offline_week_sum = 0
        week_diff = str(week_sum) + '(' + str(int(week_sum - last_week_sum)) + ')'
        week_diff_online = str(online_week_sum) + '(' + str(int(online_week_sum - online_last_week_sum)) + ')'
        week_diff_offline = str(offline_week_sum) + '(' + str(int(offline_week_sum - offline_last_week_sum)) + ')'

        return week_sum, online_week_sum, offline_week_sum, last_week_sum, online_last_week_sum, offline_last_week_sum, week_diff, week_diff_online, week_diff_offline, online_total_sum, offline_total_sum, total_sum


def shandong_week_report():
    count_list = []
    last_week_value_list = []
    for city in product_set_code_list:
        week_sum, online_week_sum, offline_week_sum, last_week_sum, online_last_week_sum, offline_last_week_sum, week_diff, week_diff_online, week_diff_offline, online_total_sum, offline_total_sum, total_sum = get_week_data(
            city, (datetime.date.today()).strftime("%Y-%m-%d"))
        if city == 'binzhou_yhbV4':
            todo_list = ['12月20日', total_sum, online_total_sum, offline_total_sum]
        elif city == 'rizhao_nxbV4':
            todo_list = ['12月31日', total_sum, online_total_sum, offline_total_sum]
        elif city == 'dezhou_hmbV3':
            todo_list = ['2月20日', total_sum, online_total_sum, offline_total_sum]
        last_week_value_list.extend([np.nan, week_diff, week_diff_online, week_diff_offline])
        count_list.extend(todo_list)

    # 三个城市的去年销量、线上、线下、上线时间、上线天数、参保截止时间、剩余天数，目标量
    df = pd.DataFrame(
        columns=['city', 'last_year', 'last_year_value', 'target_name', 'target', 'name', 'count', 'target_value',
                 'target_rate', 'last_week', 'last_week_value'])
    city_list = ['滨州', '滨州', '滨州', '滨州', '日照', '日照', '日照', '日照', '德州', '德州', '德州', '德州']
    last_year_list = ['上线时间', '2023年总销量', '线上', '线下', '上线时间', '2023年总销量', '线上', '线下', '上线时间',
                      '2023年总销量', '线上', '线下']
    last_year_value_list = ['10月29日', 541000, 210000, 331000, '11月5日', 351250, 114443, 236807, '11月12日',
                            534400, 76000, 458400]
    target_name_list = ['上线天数', '目标量', '线上', '线下', '上线天数', '目标量', '线上', '线下', '上线天数', '目标量',
                        '线上', '线下']
    target_list = [(datetime.date(2025, 1, 5) - datetime.date(2024, 10, 29)).days + 1,
                   560000, 224000, 336000, (datetime.date(2025, 1, 5) - datetime.date(2024, 11, 5)).days + 1,
                   500000, 175000, 325000, (datetime.date(2025, 1, 5) - datetime.date(2024, 11, 12)).days + 1,
                   580000, 290000, 290000]

    name_list = ['参保截止时间', '累计销量', '线上', '线下', '参保截止时间', '累计销量', '线上', '线下', '参保截止时间',
                 '累计销量', '线上', '线下']
    target_value_list = ['剩余天数', '目标完成率', '线上', '线下', '剩余天数', '目标完成率', '线上', '线下', '剩余天数',
                         '目标完成率', '线上', '线下']
    target_rate_list = [(datetime.date(2024, 12, 20) - datetime.date(2025, 1, 5)).days, 0, 0, 0,
                        (datetime.date(2024, 12, 31) - datetime.date(2025, 1, 5)).days, 0, 0, 0,
                        (datetime.date(2025, 2, 20) - datetime.date(2025, 1, 5)).days, 0, 0, 0]
    # target_rate_list，则设置为0
    target_rate_list = [max(0, target) for target in target_rate_list]
    last_week_list = [np.nan, '上周销量', '线上', '线下', np.nan, '上周销量', '线上', '线下', np.nan, '上周销量', '线上',
                      '线下']
    df['city'] = city_list
    df['last_year'] = last_year_list
    df['last_year_value'] = last_year_value_list
    df['target_name'] = target_name_list
    df['target'] = target_list
    df['name'] = name_list
    df['count'] = count_list
    df['target_value'] = target_value_list
    df['target_rate'] = target_rate_list
    df['last_week'] = last_week_list
    df['last_week_value'] = last_week_value_list

    # 如果target_value列的值不等于剩余天数的行，重新计算target_rate列的值
    for i in range(len(df)):
        if df.loc[i, 'target_value'] != '剩余天数':
            df.loc[i, 'target_rate'] = str(round(df.loc[i, 'count'] / df.loc[i, 'target'] * 100, 2)) + '%'

    date = datetime.datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'山东惠民保周报_{date}.xlsx')
    df.to_excel(path, index=False,header=False)

    # 加载新的 Excel 文件
    book = load_workbook(path)
    sheet = book['Sheet1']

    # 定义浅蓝色填充
    light_blue_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")

    # 定义边框样式
    thin_border = Border(left=Side(style='thin'),
                         right=Side(style='thin'),
                         top=Side(style='thin'),
                         bottom=Side(style='thin'))

    # 需要处理的行号
    rows_to_process = [1, 5, 9]

    for row in rows_to_process:
        # 设置行的底色为浅蓝色
        for col in range(1, sheet.max_column + 1):
            cell = sheet.cell(row=row, column=col)
            cell.fill = light_blue_fill
            cell.border = thin_border

        # 合并 I 列到 K 列
        sheet.merge_cells(start_row=row, start_column=9, end_row=row, end_column=11)
        merged_cell = sheet.cell(row=row, column=9)
        merged_cell.alignment = Alignment(vertical='center', horizontal='center')

    # 合并第一列的指定行
    merge_ranges = [(1, 4), (5, 8), (9, 12)]
    for start_row, end_row in merge_ranges:
        sheet.merge_cells(start_row=start_row, start_column=1, end_row=end_row, end_column=1)
        merged_cell = sheet.cell(row=start_row, column=1)
        merged_cell.alignment = Alignment(vertical='center', horizontal='center')

    # 为所有有值的单元格添加边框
    for row in range(1, sheet.max_row + 1):
        for col in range(1, sheet.max_column + 1):
            cell = sheet.cell(row=row, column=col)
            if cell.value is not None:
                cell.border = thin_border

    # 自动调整列宽
    for col_idx in range(1, sheet.max_column + 1):
        max_length = 0
        column = get_column_letter(col_idx)  # 获取列字母
        for cell in sheet[get_column_letter(col_idx)]:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(cell.value)
            except:
                pass
        adjusted_width = max((max_length + 2) * 1.6, 9)  # 调整宽度以适应内容
        sheet.column_dimensions[column].width = adjusted_width

    # 保存文件
    book.save(path)



def email_week_report():
    date = datetime.datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'山东惠民保周报_{date}.xlsx')
    print(path)
    shandong_week_report()
    mail = EmailMessage(
        subject=f'山东惠民保周报_{date}',
        body='见附件',
        to=TO,
        cc=CC
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)

