# Generated by Django 3.2.12 on 2025-03-04 13:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0007_claimpayagerange_claimpayarearange_claimpaygroup_claimpayproduct'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimPayGender',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('gender', models.CharField(blank=True, max_length=32, null=True, verbose_name='性别')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
            ],
            options={
                'verbose_name': '理赔-性别分布情况',
                'verbose_name_plural': '理赔-性别分布情况',
                'db_table': 'claim_pay_gender',
            },
        ),
        migrations.CreateModel(
            name='ClaimPayType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('type', models.CharField(blank=True, max_length=32, null=True, verbose_name='申请方式')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_number_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付人次占比')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
            ],
            options={
                'verbose_name': '理赔-申请方式赔付情况',
                'verbose_name_plural': '理赔-申请方式赔付情况',
                'db_table': 'claim_pay_type',
            },
        ),
        migrations.CreateModel(
            name='ClaimSellerPay',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('channel_name', models.CharField(blank=True, max_length=32, null=True, verbose_name='渠道名称')),
                ('pay_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人次')),
                ('pay_number_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付人次占比')),
                ('pay_person_number', models.IntegerField(blank=True, null=True, verbose_name='赔付人数')),
                ('pay_person_number_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付人数占比')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('pay_amount_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额占比')),
                ('premium_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='保费')),
                ('claim_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付率')),
            ],
            options={
                'verbose_name': '理赔-保司赔付情况',
                'verbose_name_plural': '理赔-保司赔付情况',
                'db_table': 'claim_seller_pay',
            },
        ),
    ]
