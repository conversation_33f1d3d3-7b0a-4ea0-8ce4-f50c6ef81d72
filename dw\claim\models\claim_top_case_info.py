from django.db import models
from common.models import BaseModel


class ClaimTopCaseInfo(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市') # 当is_provincial为1时，city为空
    name = models.CharField(max_length=32, blank=True, null=True, verbose_name='姓名')
    medicare_area = models.CharField(max_length=32, blank=True, null=True, verbose_name='医保参保地')
    past_symptom = models.CharField(max_length=32, blank=True, null=True, verbose_name='是否既往症')
    age = models.IntegerField(blank=True, null=True, verbose_name='年龄')
    gender = models.Char<PERSON>ield(max_length=32, blank=True, null=True, verbose_name='性别')
    disease = models.CharField(max_length=256, blank=True, null=True, verbose_name='疾病')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    total_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='总费用')
    fund_paid_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='基金支付金额')
    self_burden_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='个人负担金额')
    reduction_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='减负率')
    claim_type = models.CharField(max_length=32, blank=True, null=True, verbose_name='申请方式')

    class Meta:
        db_table = 'claim_top_case_info'
        verbose_name = '理赔-理赔案件TOP情况'
        verbose_name_plural = verbose_name

