import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)

import json
import warnings

from pathlib import Path
from pprint import pprint

import idna
import pandas as pd
import pymysql
from datetime import date
from decimal import Decimal

from transfrom.utils.utils import query_sql

# 设置Django环境
sys.path.append(str(Path(__file__).resolve().parent.parent.parent.parent))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "dw.settings")

import django
django.setup()

from django.db import transaction
from transfrom.utils.utils import custom_update_or_create, clean_orphaned_data
from dw import settings
from insure.models import InsurePvUv, InsureWebsiteStatistics, InsureBeacon

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

DB = settings.DATABASES['default']
DB_UMAMI = settings.DATABASES['umami']


def get_connection():
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn


def get_connection_umami():
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB_UMAMI["HOST"]).decode('utf-8'), port=int(DB_UMAMI["PORT"]),
                           user=DB_UMAMI["USER"],
                           password=DB_UMAMI["PASSWORD"], database=DB_UMAMI["NAME"])
    return conn


def get_person_count(website_id, start_time,sql=query_sql('SQL_UV')):
    """
    获取访客量 SQL_UV
    :param website_id:
    :return:
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id, start_time=start_time), conn)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['person_count']


def get_daily_uv(website_id,product_set_code, start_time,sql = query_sql("SQL_DAILY_UV")):
    """
    获取日度访客量
    :param website_id:
    :return:
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id,start_time=start_time), conn)

    return fill_missing_dates(df, start_time, product_set_code,type_column='uv')


def get_view_count(website_id, start_time,sql = query_sql("SQL_PV")):
    """
    获取浏览量数据
    :param website_id:
    :return:
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id, start_time=start_time), conn)

    if df.empty:
        return 0
    else:
        return df.iloc[0]['view_num']


def get_daily_pv(website_id, product_set_code, start_time, sql=query_sql("SQL_DAILY_PV")):
    """
    获取日度浏览量数据，自动补全缺失日期的数据
    
    :param website_id: 网站ID
    :param product_set_code: 产品集编码  
    :param start_time: 开始时间
    :param sql: SQL查询语句
    :return: 补全后的DataFrame
    """
    import pandas as pd
    from datetime import datetime
    
    # 获取原始数据
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id, start_time=start_time), conn)
    
    # 补全时间序列数据
    return fill_missing_dates(df, start_time, product_set_code)



def fill_missing_dates(df, start_time, product_set_code, date_column='end_date', type_column='pv'):
    """
    补全数据的缺失日期 - 最简洁版本
    """
    import pandas as pd
    from datetime import datetime
    
    # 创建完整的日期范围
    full_date_range = pd.date_range(
        start=pd.to_datetime(start_time).date(),
        end=datetime.now().date(),
        freq='D'
    )
    
    # 处理空DataFrame或缺少日期列
    if df.empty:
        return pd.DataFrame({
            date_column: full_date_range,
            type_column: 0,
            'source_group': 'total',
            'product_set_code': product_set_code
        })
    
    # 重索引并填充
    df = df.copy()
    df[date_column] = pd.to_datetime(df[date_column])
    df_reindexed = df.set_index(date_column).reindex(full_date_range, fill_value=0)
    
    # 安全地设置必要的列
    for col, default_val in [
        ('source_group', 'total'),
        ('product_set_code', product_set_code),
        (type_column, 0)
    ]:
        if col not in df_reindexed.columns:
            df_reindexed[col] = default_val
        else:
            df_reindexed[col] = df_reindexed[col].fillna(default_val)
    
    return df_reindexed.reset_index().rename(columns={'index': date_column})


def get_visit_count(website_id, start_time,sql = query_sql("SQL_VISIT_NUM")):
    """
    获取访问次数据
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id,start_time=start_time), conn)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['visit_count']


def get_counce_rate(website_id,start_time,sql = query_sql("SQL_ONE_PAGE_VISIT_NUM")):
    """
    获取跳出率数据
    """
    with get_connection_umami() as conn:
        one_page_count = pd.read_sql(sql.format(website_id=website_id,start_time=start_time), conn)
    if one_page_count.empty:
        one_page_count = 0
    else:
        one_page_count = one_page_count.iloc[0]['one_page_count']

    visit_count = get_visit_count(website_id,start_time)
    if visit_count == 0:
        return 0
    else:
        return round(one_page_count / visit_count , 2)


def convert_seconds_to_hms(seconds):
    """
    将秒数转换为小时、分钟和秒的格式。

    参数:
    seconds (int): 总秒数

    返回:
    tuple: (小时, 分钟, 秒)
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return hours, minutes, seconds


def get_visit_duration(website_id,start_time,sql = query_sql("SQL_AVG_VISIT_DURATION")):
    """
    获取平均访问时长数据
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id,start_time = start_time), conn)
    if df.empty:
        avg_seconds = 0
    else:
        avg_seconds = df.iloc[0]['avg_seconds']

    return avg_seconds



def save_daily_data_to_db(df_daily_pv, df_daily_uv, product_set_code):
    """
    将日度PV和UV数据保存到数据库
    :param df_daily_pv: 日度PV数据
    :param df_daily_uv: 日度UV数据
    :param product_set_code: 产品集编码
    """
    try:
        # 合并PV和UV数据，准备用于清理比较
        df_pv_for_clean = df_daily_pv.copy()
        df_pv_for_clean['type'] = 'pv'
        df_pv_for_clean['product_set_code'] = product_set_code

        df_uv_for_clean = df_daily_uv.copy()
        df_uv_for_clean['type'] = 'uv'
        df_uv_for_clean['product_set_code'] = product_set_code

        # 合并所有新数据
        df_all_new_data = pd.concat([df_pv_for_clean, df_uv_for_clean], ignore_index=True)

        # 确保source_group字段存在
        if 'source_group' not in df_all_new_data.columns:
            df_all_new_data['source_group'] = 'total'

        # 标准化数据格式，确保与数据库格式一致
        if 'end_date' in df_all_new_data.columns:
            # 确保日期格式一致
            df_all_new_data['end_date'] = pd.to_datetime(df_all_new_data['end_date']).dt.date

        # 确保字段顺序和类型正确
        df_all_new_data = df_all_new_data[['product_set_code', 'end_date', 'source_group', 'type', 'count']]

        print("清理前数据预览:")
        print(df_all_new_data.head(10))
        print(f"数据类型: {df_all_new_data.dtypes.to_dict()}")

        # 使用通用清理函数清理多余数据
        print("开始清理多余数据...")
        unique_fields = ['product_set_code', 'end_date', 'source_group', 'type']

        # 清理孤立数据（数据库中存在但新数据中不存在的记录）
        deleted_count = clean_orphaned_data(
            model_class=InsurePvUv,
            new_data_df=df_all_new_data,
            unique_fields=unique_fields,
            key_field='id',
            filter_conditions={'product_set_code': product_set_code},  # 只清理当前产品的数据
            verbose=True
        )

        # 处理PV数据
        print("开始处理PV数据...")
        pv_count = 0
        for _, row in df_daily_pv.iterrows():
            obj = custom_update_or_create(
                model_class=InsurePvUv,
                product_set_code=product_set_code,
                end_date=row['end_date'],
                source_group=row.get('source_group', 'total'),
                type='pv',
                defaults={
                    'count': row['count']
                }
            )
            pv_count += 1

        # 处理UV数据
        print("开始处理UV数据...")
        uv_count = 0
        for _, row in df_daily_uv.iterrows():
            obj = custom_update_or_create(
                model_class=InsurePvUv,
                product_set_code=product_set_code,
                end_date=row['end_date'],
                source_group=row.get('source_group', 'total'),
                type='uv',
                defaults={
                    'count': row['count']
                }
            )
            uv_count += 1

        print(f"PV数据处理完成，共处理 {pv_count} 条记录")
        print(f"UV数据处理完成，共处理 {uv_count} 条记录")
        if deleted_count > 0:
            print(f"清理了 {deleted_count} 条多余数据")
        print("数据保存成功！")

    except Exception as e:
        print(f"保存数据时出错: {e}")
        raise e


def get_beacon_data(website_id,start_time,sql = query_sql("SQL_EVENT_UV")):
    """
    获取埋点数据
    """
    with get_connection_umami() as conn:
        df = pd.read_sql(sql.format(website_id=website_id, start_time=start_time), conn)
    sql_mapping = """
    select `key` as name,label as event_name from system_dict_value
    where dict_id = '14'
    and status = '1'
    """
    with get_connection() as conn:
        df_mapping = pd.read_sql(sql_mapping, conn)

    df = pd.merge(df, df_mapping, on='event_name', how='left')
    df.rename(columns={'name': '事件名称', 'num': '事件数量'}, inplace=True)
    print(df)
    # 访客数，因为部分埋点是14号之后才有，所以添加限制条件
    view_count = int(get_person_count(website_id, start_time))
    vist_count = int(df[df['事件名称'] == '进入产品详情页']['事件数量'].sum())
    # add_person_count = int(df[df['事件名称'] == '添加参保人信息']['事件数量'].sum())
    buy_product = int(df[df['事件名称'] == '购买产品']['事件数量'].sum())
    pay_count = int(df[(df['事件名称'] == '医保个账支付') | (df['事件名称'] == '自费支付')]['事件数量'].sum())
    pay_success_count = int(df[df['事件名称'] == '进入支付成功页']['事件数量'].sum())
    beacon_list = [view_count, vist_count, buy_product, pay_count, pay_success_count]
    print(view_count, vist_count, buy_product, pay_count, pay_success_count)
    beacon_ratio_list = [1, round(vist_count / view_count, 4), round(buy_product / view_count , 4),
                         round(pay_count / view_count , 4), round(pay_success_count / view_count , 4)]
    return beacon_list, beacon_ratio_list




def save_website_statistics(total_page_views, total_visits, total_visitors,
                           bounce_rate, avg_visit_duration, product_set_code,
                           statistics_date, data_source):
    """
    保存网站统计数据到 insure_website_statistics 表

    Args:
        total_page_views: 总浏览量
        total_visits: 总访问次数
        total_visitors: 总访客数
        bounce_rate: 跳出率
        avg_visit_duration: 平均访问时长
        product_set_code: 产品编码
        statistics_date: 统计日期
        data_source: 数据来源
    """
    try:
        website_stats = {
            'total_page_views': total_page_views,
            'total_visits': total_visits,
            'total_visitors': total_visitors,
            'bounce_rate': Decimal(str(bounce_rate)),
            'avg_visit_duration': Decimal(str(avg_visit_duration))
        }

        website_obj = custom_update_or_create(
            model_class=InsureWebsiteStatistics,
            product_set_code=product_set_code,
            statistics_date=statistics_date,
            data_source=data_source,
            defaults=website_stats
        )

        print(f"网站统计数据保存成功: 浏览量={total_page_views:,}, "
              f"访问次数={total_visits:,}, 访客数={total_visitors:,}")
        return True

    except Exception as e:
        print(f"保存网站统计数据时出错: {e}")
        return False


def save_beacon_statistics(beacon_data, product_set_code, statistics_date, data_source):
    """
    保存埋点数据到 insure_beacon 表

    Args:
        beacon_data: 埋点数据列表
        product_set_code: 产品编码
        statistics_date: 统计日期
        data_source: 数据来源
    """
    step_names = ['点击', '访问', '点击参保', '生成订单', '支付成功']

    try:
        if beacon_data and len(beacon_data) >= 5:
            print("正在保存埋点数据...")

            with transaction.atomic():
                for i, (step_name, count) in enumerate(zip(step_names, beacon_data)):
                    step_order = i + 1

                    # 计算转化率（相对于第一步的转化率）
                    if i == 0 and beacon_data[0] > 0:
                        conversion_rate = Decimal('1')
                    elif beacon_data[0] > 0:
                        conversion_rate = Decimal(str(count / beacon_data[0])).quantize(Decimal('0.0001'))
                    else:
                        conversion_rate = Decimal('1')

                    beacon_obj = custom_update_or_create(
                        model_class=InsureBeacon,
                        product_set_code=product_set_code,
                        statistics_date=statistics_date,
                        data_source=data_source,
                        step_order=step_order,
                        defaults={
                            'step_name': step_name,
                            'conversion_rate': conversion_rate,
                            'absolute_count': count,
                            'remarks': f"宁惠保埋点数据 - {step_name} - 数量:{count} 转化率:{conversion_rate*100}%"
                        }
                    )

                    print(f"步骤{step_order} {step_name}: 数量={count}, 转化率={conversion_rate}%")

                print("埋点数据保存成功!")
                print("转化漏斗:")
                for i, (step_name, count) in enumerate(zip(step_names, beacon_data)):
                    if beacon_data[0] > 0:
                        rate = count / beacon_data[0] * 100
                        print(f"  {step_name:>8}: {count:>8} 人 ({rate:>6.2f}%)")
                    else:
                        print(f"  {step_name:>8}: {count:>8} 人")

            return True
        else:
            print("埋点数据不完整，跳过埋点数据保存")
            return False

    except Exception as e:
        print(f"保存埋点数据时出错: {e}")
        return False




def content():
    website_id = 'c73e45a2-073b-402c-8e83-4caa1f8a60ca'
    sale_start_time = '2024-09-14 15:00:00'
    # start_time 为sale_start_time减去8个小时，因为umami的时间是utc时间，需要减去8个小时
    start_time = pd.to_datetime(sale_start_time) - pd.Timedelta(hours=8)
    start_time = start_time.strftime('%Y-%m-%d %H:%M:%S')
    product_set_code = 'ninghuibaoV6'
    statistics_date = date.today()
    data_source = "umami"

    print(f"开始处理宁惠保V6数据...")
    print(f"产品编码: {product_set_code}")
    print(f"统计日期: {statistics_date}")
    print("-" * 50)

    # 1. 获取并保存网站统计数据
    print("正在获取网站统计数据...")
    person_count = get_person_count(website_id, start_time)
    view_count = get_view_count(website_id, start_time)
    visit_count = get_visit_count(website_id,start_time)
    visit_duration = get_visit_duration(website_id,start_time)
    counce_rate = get_counce_rate(website_id,start_time)

    # 直接保存网站统计数据到数据库
    save_website_statistics(
        int(view_count), int(visit_count), int(person_count),
        counce_rate, visit_duration, product_set_code, statistics_date, data_source
    )

    # 2. 获取并保存埋点数据
    print("正在获取埋点数据...")
    y_data, beacon_ratio_list = get_beacon_data(website_id, start_time)

    # 直接保存埋点数据到数据库 - 传递实际人数数据，而不是比例数据
    save_beacon_statistics(y_data, product_set_code, statistics_date, data_source)

    # 3. 获取并保存PV/UV日度数据
    print("正在获取PV/UV日度数据...")
    df_daily_pv = get_daily_pv(website_id, product_set_code, start_time)
    df_daily_uv = get_daily_uv(website_id, product_set_code, start_time)

    # 保存PV/UV数据到数据库
    save_daily_data_to_db(df_daily_pv, df_daily_uv, product_set_code)

    print("所有数据处理完成!")
    return {
        'total_page_views': int(view_count),
        'total_visits': int(visit_count),
        'total_visitors': int(person_count),
        'bounce_rate': counce_rate,
        'avg_visit_duration': visit_duration,
        'beacon_data': y_data  # 返回实际人数数据，而不是比例数据
    }




if __name__ == '__main__':
    # 运行宁惠保V6数据处理和保存
    print("开始处理宁惠保V6数据...")
    content()
    print("\n处理完成!")

