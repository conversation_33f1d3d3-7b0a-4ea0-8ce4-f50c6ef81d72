from django.contrib import admin

from insure.models import InsureAgeSex, InsureAgent, InsureArea, InsureOnline, InsureMobile, InsurePvUv, InsureGroup,\
InsureBeacon, InsureWebsiteStatistics, InsureVisitorAnalytics


# Register your models here.


@admin.register(InsureAgeSex)
class InsureAgeSexAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'product_set_code', 'publish_time', 'sex', 'age_distribution', 'value', 'additional_info', 'create_time',
        'update_time')
    list_filter = ('product_set_code', 'publish_time', 'additional_info')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', '-publish_time']


@admin.register(InsureAgent)
class InsureAgentAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'publish_time', 'name', 'employee_count', 'average_count',
                    'total_count', 'personal_count', 'group_count', 'insure_ratio', 'position', 'target',
                    'target_ratio', 'today_count', 'yesterday_count', 'week_target', 'week_count',
                    'week_complete_ratio', 'week_target_ratio', 'additional_info', 'create_time', 'update_time')
    list_filter = ('product_set_code', 'publish_time', 'additional_info')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', '-publish_time']


@admin.register(InsureArea)
class InsureAreaAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'publish_time', 'name', 'total_count',
                    'ratio', 'insure_ratio', 'position', 'today_count',
                    'yesterday_count', 'additional_info', 'create_time', 'update_time')
    list_filter = ('product_set_code', 'publish_time', 'additional_info')
    search_fields = ('product_set_code',)
    ordering = ['product_set_code', '-publish_time']


@admin.register(InsureOnline)
class InsureOnlineAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'publish_time', 'channel_name', 'total_count', 'insure_ratio', 'position',
                    'target', 'target_ratio', 'today_count', 'yesterday_count', 'week_target', 'week_count',
                    'week_complete_ratio', 'week_target_ratio', 'additional_info', 'create_time',
                    'update_time')
    list_filter = ('product_set_code', 'additional_info')
    search_fields = ('product_set_code', 'publish_time')
    ordering = ['-id']


@admin.register(InsureMobile)
class InsureMobileAdmin(admin.ModelAdmin):
    list_display = (
    'id', 'product_short_code', 'channel_name', 'name', 'mobile', 'credential_number', 'additional_info')
    list_filter = ('product_short_code', 'channel_name')
    ordering = ['-id']


@admin.register(InsurePvUv)
class InsurePvUvAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'end_date','source_group', 'source_code', 'type', 'count')
    list_filter = ('product_set_code',)


@admin.register(InsureGroup)
class InsureGroupAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code','seller_name', 'company_name', 'name', 'credential_number',
                    'product_code')
    list_filter = ('product_set_code',)


@admin.register(InsureBeacon)
class InsureBeaconAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'statistics_date', 'data_source', 'step_order', 'step_name',
                    'conversion_rate', 'absolute_count', 'remarks')
    list_filter = ('product_set_code', 'statistics_date', 'data_source')
    search_fields = ('product_set_code',)


@admin.register(InsureWebsiteStatistics)
class InsureWebsiteStatisticsAdmin(admin.ModelAdmin):
    list_display = ('id', 'product_set_code', 'statistics_date', 'data_source', 'total_page_views', 'total_visits',
                    'total_visitors', 'bounce_rate', 'avg_visit_duration', 'remarks')
    list_filter = ('product_set_code', 'statistics_date', 'data_source')
    search_fields = ('product_set_code',)


@admin.register(InsureVisitorAnalytics)
class InsureVisitorAnalyticsAdmin(admin.ModelAdmin):
    list_display = ('id', 'data_type', 'date', 'hour', 'hour_range', 'weekday', 'conversion_rate', 'visitor_count','os', 'is_workday', 'create_time', 'update_time')
    list_filter = ('data_type', 'date', 'weekday', 'is_workday','os')
    search_fields = ('data_type',)
    ordering = ['-create_time']

    fieldsets = (
        ('基本信息', {
            'fields': ('data_type',)
        }),
        ('时间维度', {
            'fields': ('date', 'hour', 'hour_range','weekday', 'is_workday'),
            'description': '根据数据类型选择相应的时间字段'
        }),
        ('数据指标', {
            'fields': ('conversion_rate', 'visitor_count','os'),
            'description': '转换率和访客数据'
        }),
        ('系统信息', {
            'fields': ('create_time', 'update_time'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('create_time', 'update_time')

    def get_weekday_display(self, obj):
        """显示星期几的中文名称"""
        if obj.weekday is not None:
            return dict(obj.WEEKDAY_CHOICES)[obj.weekday]
        return '-'
    get_weekday_display.short_description = '星期几'

    def get_data_type_display(self, obj):
        """显示数据类型的中文名称"""
        return dict(obj.DATA_TYPE_CHOICES)[obj.data_type]
    get_data_type_display.short_description = '数据类型'

    def get_is_workday_display(self, obj):
        """显示是否工作日"""
        if obj.is_workday is None:
            return '-'
        return '工作日' if obj.is_workday else '非工作日'
    get_is_workday_display.short_description = '是否工作日'