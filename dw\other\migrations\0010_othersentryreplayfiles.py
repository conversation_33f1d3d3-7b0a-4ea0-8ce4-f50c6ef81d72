# Generated by Django 3.2.12 on 2024-11-21 13:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0009_alter_othermanagementstaff_agent_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherSentryReplayFiles',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('file_name', models.CharField(blank=True, max_length=128, null=True, verbose_name='压缩后的文件名')),
                ('order_list', models.TextField(blank=True, null=True, verbose_name='对应的订单id')),
                ('product_set_code', models.CharField(blank=True, max_length=128, null=True, verbose_name='产品集')),
                ('type', models.Char<PERSON>ield(blank=True, max_length=128, null=True, verbose_name='压缩类型')),
                ('description', models.CharField(blank=True, max_length=256, null=True, verbose_name='描述信息')),
            ],
            options={
                'verbose_name': '健康险销售sentry回放文件',
                'verbose_name_plural': '健康险销售sentry回放文件',
                'db_table': 'other_sentry_replay_files',
            },
        ),
    ]
