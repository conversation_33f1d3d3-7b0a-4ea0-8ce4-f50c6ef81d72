from django.db import models
from common.models import BaseModel


class ClaimAmountRangePay(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市')
    amount_range = models.CharField(max_length=32, blank=True, null=True, verbose_name='赔付金额范围')
    type = models.CharField(max_length=32, blank=True, null=True, verbose_name='赔付类型')
    pay_number = models.IntegerField(blank=True, null=True, verbose_name='赔付数量')
    pay_number_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付数量占比')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    pay_amount_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付金额占比')

    class Meta:
        db_table = 'claim_amount_range_pay'
        verbose_name = '理赔-赔付金额分布'
        verbose_name_plural = verbose_name

