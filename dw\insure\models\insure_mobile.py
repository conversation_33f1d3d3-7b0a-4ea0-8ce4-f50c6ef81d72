from django.db import models
from common.models import BaseModel


class InsureMobile(BaseModel):
    product_short_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品类编码')
    channel_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='渠道名称')
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='姓名')
    mobile = models.CharField(max_length=32, blank=True, null=True, verbose_name='手机号码')
    credential_number = models.CharField(max_length=64, blank=True, null=True, verbose_name='证件号码')
    # 由于统计可能细化，例如分地区、产品、保司等，会导致表格过大，因此增加一个字段作为补充信息
    additional_info = models.CharField(max_length=128, blank=True, null=True, verbose_name='补充信息')

    class Meta:
        db_table = 'insure_mobile'
        verbose_name = '健康险渠道手机号码'
        verbose_name_plural = verbose_name

