import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)
import logging
from datetime import datetime

import pandas as pd
import os
import pymysql
import idna
import warnings
from dw import settings
from django.core.mail import EmailMessage
from transfrom.utils.utils import  query_sql,age_group

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


TO = ['<EMAIL>','<EMAIL>','<EMAIL>']
# TO = ['<EMAIL>']
CC= ['<EMAIL>']

DB = settings.DATABASES['claim']


def get_connection():
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                                user=DB["USER"],
                                password=DB["PASSWORD"], database=DB["NAME"])
    return conn



def get_yx_seller_aduit_time():
    sql = """
    SELECT
            ic.id AS claim_id,
            MAX( CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) ) AS max_create_time,
            p.name as product_set_name,
            ic.product_serial_code
    FROM
            insurance_claim ic
            LEFT JOIN insurance_claim_event ice ON ice.claim_id = ic.id
            join product_set p on ic.product_set_code = p.code
    WHERE
            ic.STATUS IN ( 'WAIT_INSURANCE_COMPANY_AUDITED', 'INSURANCE_COMPANY_ACCEPTED' )
            AND ice.`status` = 'WAIT_INSURANCE_COMPANY_AUDITED'
            AND ic.product_serial_code in ('neimenggu_hmb','shanxi_jkb','hunan_amb')
    GROUP BY
    product_set_name,
            ic.id,
            ice.`status`,
            ic.product_serial_code
    order by ic.product_set_code
        """

    sql_detail = """
     SELECT
    ic.id 理赔编号,
    ic.number 案件号,
    ic.policy_number 保单号码,
    ic.accident_date 出险日期,
    date(ice.max_create_time) 提交保司日期,
    sum(total_claim_amount) as 金额,
    ic.product_serial_code

    FROM
        insurance_claim ic
        join claim_insurant ci on ic.id = ci.claim_id
        join (select claim_id,max(create_time) max_create_time from insurance_claim_event where `status` IN ( 'WAIT_INSURANCE_COMPANY_AUDITED', 'INSURANCE_COMPANY_ACCEPTED' )
group by claim_id) ice on ic.id = ice.claim_id
          LEFT JOIN (select claim_id,
	sum(case when liability_type = 1 THEN claim_amount ELSE 0 END)  AS liability_one_claim_amount,
	sum(case when liability_type = 2 THEN claim_amount ELSE 0 END)  AS liability_two_claim_amount,
	sum(case when liability_type = 3 THEN claim_amount ELSE 0 END)  AS liability_three_claim_amount,
	sum(case when liability_type = 4 THEN claim_amount ELSE 0 END)  AS liability_four_claim_amount,
	sum(case when liability_type = 5 THEN claim_amount ELSE 0 END)  AS liability_five_claim_amount,
	sum(case when liability_type = 1 THEN reasonable_amount ELSE 0 END)  AS liability_one_amount,
	sum(case when liability_type = 2 THEN reasonable_amount ELSE 0 END)  AS liability_two_amount,
	sum(case when liability_type = 3 THEN reasonable_amount ELSE 0 END)  AS liability_three_amount,
	sum(case when liability_type = 4 THEN reasonable_amount ELSE 0 END)  AS liability_four_amount,
	sum(case when liability_type = 5 THEN reasonable_amount ELSE 0 END)  AS liability_five_amount,
	sum(claim_amount) total_claim_amount
	from claim_compensation_info
	group by claim_id) ica ON ica.claim_id = ic.id
    WHERE
 ic.id in ('{claim_ids}')
    group by ic.id, ic.product_serial_code
    order by 提交保司日期
    """

    conn = get_connection()
    df = pd.read_sql(sql, conn)
    df['date_diff'] = (datetime.now() - df['max_create_time']).dt.days
    df['product_set_name'] = df['product_set_name'].str.replace('南京宁惠保', '')
    labels = ['0-4天', '5-10天', '11-20天', '20-30天','30天以上']
    bins = [0, 4, 10, 20, 30, float('inf')]
    df['day_group'] = pd.cut(df['date_diff'], bins=bins, labels=labels, right=False)

    # 按产品系列分组处理数据
    product_data = {}

    # 定义产品系列映射
    product_mapping = {
        'neimenggu_hmb': '内蒙古惠蒙保',
        'shanxi_jkb': '山西晋康保',
        'hunan_amb': '湖南爱民保'
    }

    for product_code, product_name in product_mapping.items():
        # 筛选当前产品的数据
        product_df = df[df['product_serial_code'] == product_code].copy()

        if product_df.empty:
            # 如果没有数据，创建空的结构
            product_data[product_code] = {
                'name': product_name,
                'overview_df': pd.DataFrame(),
                'detail_df': pd.DataFrame(),
                'total_unsettled': 0
            }
            continue

        # 获取超期未结案的数据
        df_copy = product_df[product_df['day_group'] != '0-4天']
        claim_ids = [str(id) for id in df_copy['claim_id'].tolist()]

        if claim_ids:
            claim_ids_str = "','".join(claim_ids)
            df_detail = pd.read_sql(sql_detail.format(claim_ids=claim_ids_str), conn)
            df_detail = df_detail[df_detail['product_serial_code'] == product_code]
            df_detail['理赔编号'] = df_detail['理赔编号'].astype(str)
            # 移除product_serial_code列，因为不需要在最终报告中显示
            df_detail = df_detail.drop('product_serial_code', axis=1)
        else:
            df_detail = pd.DataFrame()

        # 统计概览数据
        overview_df = product_df.groupby(['product_set_name', 'day_group']).size().reset_index(name='count')
        total_unsettled = overview_df[overview_df['day_group'] != '0-4天']['count'].sum()

        # 透视表处理
        overview_df = overview_df.pivot_table(index='day_group', columns='product_set_name', values='count', fill_value=0)
        overview_df = overview_df.reset_index()
        overview_df.columns.name = None

        # 列排序：保司已处理时间放在第一列，其他列按照一、二、三等顺序排序
        if not overview_df.empty and len(overview_df.columns) > 1:
            # 获取除了day_group之外的所有列
            other_columns = [col for col in overview_df.columns if col != 'day_group']

            # 定义排序权重函数
            def get_sort_weight(col_name):
                # 匹配中文数字，按优先级顺序检查（先检查复合数字，再检查单个数字）
                if '十一' in col_name:
                    return 11
                elif '十二' in col_name:
                    return 12
                elif '十三' in col_name:
                    return 13
                elif '十四' in col_name:
                    return 14
                elif '十五' in col_name:
                    return 15
                elif '十六' in col_name:
                    return 16
                elif '十七' in col_name:
                    return 17
                elif '十八' in col_name:
                    return 18
                elif '十九' in col_name:
                    return 19
                elif '二十' in col_name:
                    return 20
                elif '十' in col_name:  # 单独的"十"，放在复合数字检查之后
                    return 10
                elif '一' in col_name:
                    return 1
                elif '二' in col_name:
                    return 2
                elif '三' in col_name:
                    return 3
                elif '四' in col_name:
                    return 4
                elif '五' in col_name:
                    return 5
                elif '六' in col_name:
                    return 6
                elif '七' in col_name:
                    return 7
                elif '八' in col_name:
                    return 8
                elif '九' in col_name:
                    return 9
                else:
                    return 999  # 其他列放在最后

            # 按照权重排序其他列
            other_columns_sorted = sorted(other_columns, key=get_sort_weight)

            # 重新排列列的顺序：day_group在第一列，其他列按排序后的顺序
            new_column_order = ['day_group'] + other_columns_sorted
            overview_df = overview_df[new_column_order]

        # 删除 0-4天 的行
        overview_df = overview_df[overview_df['day_group'] != '0-4天']
        overview_df.reset_index(drop=True, inplace=True)

        if not overview_df.empty:
            # 重命名列
            overview_df.rename(columns={'day_group': '保司已处理时间'}, inplace=True)

            # 将数值列转换为int类型
            numeric_columns = overview_df.columns.drop('保司已处理时间')
            if len(numeric_columns) > 0:
                overview_df[numeric_columns] = overview_df[numeric_columns].astype(int)

                # 添加合计行
                total_row = overview_df.sum(numeric_only=True)
                total_row['保司已处理时间'] = '合计'
                overview_df = pd.concat([overview_df, pd.DataFrame([total_row])], ignore_index=True)

        product_data[product_code] = {
            'name': product_name,
            'overview_df': overview_df,
            'detail_df': df_detail,
            'total_unsettled': total_unsettled
        }

    conn.close()
    return product_data



def create_excel_file(product_data, product_name, date):
    """为单个产品创建Excel文件"""
    file_path = os.path.join(os.path.dirname(__file__), f'{product_name}_保司处理时间_{date}.xlsx')

    overview_df = product_data['overview_df']
    detail_df = product_data['detail_df']

    # 使用ExcelWriter将两个DataFrame写入不同的sheet
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        # 写入概览sheet
        if not overview_df.empty:
            overview_df.to_excel(writer, sheet_name='概览', index=False)

            # 获取概览sheet并设置格式
            workbook = writer.book
            overview_sheet = workbook['概览']

            # 自动调整列宽
            for column in overview_sheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                overview_sheet.column_dimensions[column_letter].width = adjusted_width
        else:
            # 如果没有概览数据，创建空的sheet
            pd.DataFrame(['暂无数据']).to_excel(writer, sheet_name='概览', index=False, header=False)

        # 写入详细sheet
        if not detail_df.empty:
            detail_df.to_excel(writer, sheet_name='详细', index=False)

            # 获取详细sheet并设置格式
            workbook = writer.book
            detail_sheet = workbook['详细']

            # 自动调整详细sheet的列宽
            for column in detail_sheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                detail_sheet.column_dimensions[column_letter].width = adjusted_width
        else:
            # 如果没有详细数据，创建空的sheet
            pd.DataFrame(['暂无数据']).to_excel(writer, sheet_name='详细', index=False, header=False)

    return file_path


def email_yx_seller_aduit_time():
    date = datetime.now().strftime('%Y%m%d')
    product_data = get_yx_seller_aduit_time()

    # 存储所有生成的文件路径
    file_paths = []

    # 统计总的超期未结案数量
    total_unsettled_all = 0
    product_summaries = []

    # 为每个产品生成独立的Excel文件
    for data in product_data.values():
        product_name = data['name']
        total_unsettled = data['total_unsettled']
        total_unsettled_all += total_unsettled

        # 创建Excel文件
        file_path = create_excel_file(data, product_name, date)
        file_paths.append(file_path)

        # 记录产品摘要信息
        product_summaries.append(f"{product_name}：{total_unsettled}例")

    # 构建邮件内容
    if total_unsettled_all > 0:
        product_summary_text = "、".join(product_summaries)
        email_body = f"""您好：

    各项目超期未结案情况如下：
    {product_summary_text}

    总计：{total_unsettled_all}例超期未结案，详情见附件！

    谢谢！"""
    else:
        email_body = """您好：

    今日各项目均无超期未结案情况。

    谢谢！"""

    # 发送邮件
    mail = EmailMessage(
        subject=f'每日统计_项目保司处理时间_{date}',
        body=email_body,
        to=TO,
        cc=CC
    )

    # 添加所有附件
    for file_path in file_paths:
        mail.attach_file(file_path)

    mail.send()

    # 清理临时文件
    for file_path in file_paths:
        try:
            os.remove(file_path)
        except Exception as e:
            logger.warning(f"删除临时文件失败: {file_path}, 错误: {e}")
