# 时间戳转换工具使用说明

## 📋 概述

本文档介绍了项目中通用的时间戳转换工具函数，这些函数位于 `transfrom/utils/date.py` 文件中，用于将各种格式的时间戳转换为标准的日期或日期时间格式。

## 🎯 主要功能

### 1. 单个时间戳转换

#### `convert_timestamp_to_date(timestamp)`
将时间戳转换为日期字符串（YYYY-MM-DD格式）

**参数：**
- `timestamp`: 时间戳（毫秒或秒）

**返回值：**
- `str`: 日期字符串 (YYYY-MM-DD) 或 None

**示例：**
```python
from transfrom.utils.date import convert_timestamp_to_date

# 毫秒时间戳
result = convert_timestamp_to_date(1728921600000)  # '2024-10-15'

# 秒时间戳
result = convert_timestamp_to_date(1728921600)     # '2024-10-15'

# 负数时间戳（无效）
result = convert_timestamp_to_date(-62135798400000) # None

# 空值
result = convert_timestamp_to_date(None)           # None
```

#### `convert_timestamp_to_datetime(timestamp, include_time=True)`
将时间戳转换为日期时间字符串

**参数：**
- `timestamp`: 时间戳（毫秒或秒）
- `include_time`: 是否包含时间部分，默认True

**返回值：**
- `str`: 日期时间字符串 (YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD) 或 None

**示例：**
```python
from transfrom.utils.date import convert_timestamp_to_datetime

# 包含时间
result = convert_timestamp_to_datetime(1728921600000)  # '2024-10-15 00:00:00'

# 只要日期
result = convert_timestamp_to_datetime(1728921600000, include_time=False)  # '2024-10-15'
```

### 2. 批量转换

#### `batch_convert_timestamp_to_date(series)`
批量将pandas Series中的时间戳转换为日期字符串

**参数：**
- `series`: pandas Series，包含时间戳数据

**返回值：**
- `pandas Series`: 转换后的日期字符串Series

**示例：**
```python
import pandas as pd
from transfrom.utils.date import batch_convert_timestamp_to_date

# 创建时间戳Series
ts_series = pd.Series([1728921600000, -62135798400000, None, 1620921600000])

# 批量转换
result_series = batch_convert_timestamp_to_date(ts_series)
# 结果：
# 0    2024-10-15
# 1          None
# 2          None
# 3    2021-05-14
# dtype: object
```

## 🔧 支持的时间戳格式

### 1. 毫秒时间戳（13位数字）
- 示例：`1728921600000`
- 转换：除以1000后使用 `datetime.fromtimestamp()`

### 2. 秒时间戳（10位数字）
- 示例：`1728921600`
- 转换：直接使用 `datetime.fromtimestamp()`

### 3. 特殊值处理
- **负数时间戳**：视为无效日期，返回 `None`
- **None/空值**：保持为 `None`
- **异常大的时间戳**：年份超过2100的视为无效，返回 `None`

## 📝 在ETL中的使用

### 在自定义清洗函数中使用

```python
from transfrom.utils.date import batch_convert_timestamp_to_date

def _clean_your_data(self, df):
    """自定义数据清洗函数"""
    
    # 处理日期字段 - 将时间戳转换为日期
    date_fields = ['begndate', 'enddate', 'create_timestamp']
    for date_field in date_fields:
        if date_field in df.columns:
            target_field = f"{date_field}_converted"
            df[target_field] = batch_convert_timestamp_to_date(df[date_field])
            logger.info(f"{date_field}字段转换完成: 时间戳 -> 日期")
    
    return df
```

### 在数据处理脚本中使用

```python
from transfrom.utils.date import convert_timestamp_to_date
import pandas as pd

# 读取数据
df = pd.read_sql("SELECT * FROM your_table", connection)

# 转换时间戳字段
df['begin_date'] = df['begndate'].apply(convert_timestamp_to_date)
df['end_date'] = df['enddate'].apply(convert_timestamp_to_date)

# 或者使用批量转换（更高效）
df['begin_date'] = batch_convert_timestamp_to_date(df['begndate'])
df['end_date'] = batch_convert_timestamp_to_date(df['enddate'])
```

## ⚠️ 注意事项

### 1. 性能考虑
- 对于大量数据，推荐使用 `batch_convert_timestamp_to_date()` 而不是循环调用单个转换函数
- 批量转换函数内部使用了 pandas 的 `apply()` 方法，性能更好

### 2. 错误处理
- 所有转换函数都包含完整的错误处理
- 转换失败时返回 `None`，不会中断处理流程
- 错误信息会记录到日志中（debug级别）

### 3. 数据类型
- 输入可以是数字、字符串或 pandas 的数值类型
- 函数会自动尝试转换为 `float` 类型进行处理

### 4. 时区处理
- 当前版本使用系统本地时区
- 如需特定时区处理，可以扩展函数添加时区参数

## 🚀 扩展建议

如果需要添加新的时间戳转换功能，建议：

1. **在 `date.py` 中添加新函数**
2. **保持函数命名一致性**：`convert_timestamp_to_xxx`
3. **添加完整的文档字符串和示例**
4. **包含错误处理和日志记录**
5. **更新本说明文档**

## 📚 相关文件

- **工具函数**：`transfrom/utils/date.py`
- **使用示例**：`transfrom/tasks/spider/gjyb/pipeline/config/data_cleaning_config.py`
- **测试用例**：可以参考ETL处理中的实际使用

---

*最后更新：2024年10月*
