#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段处理配置管理基础框架
提供配置管理的基础能力，不包含具体的业务配置
"""

import logging
from typing import Dict, Set, List, Optional
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class BaseFieldProcessingConfig(ABC):
    """
    字段处理配置基础类
    各省份可以继承此类实现自己的配置
    """

    def __init__(self):
        """初始化配置管理器"""
        # 支持的处理策略
        self._supported_strategies = {
            'null_to_empty_string',  # NULL值转换为空字符串
            'preserve_empty_string', # 保留空字符串（不转换为None）
            'standard_cleaning'      # 标准清洗（空字符串转None）
        }

    @abstractmethod
    def get_field_strategies(self) -> Dict:
        """
        获取字段处理策略配置
        子类必须实现此方法

        Returns:
            Dict: 字段策略配置，格式为 {源表: {目标表: {字段名: 处理策略}}}
        """
        pass

    def get_data_cleaning_config(self, source_table: str) -> Dict[str, List[str]]:
        """
        获取数据清洗阶段的配置

        Args:
            source_table (str): 源表名

        Returns:
            Dict[str, List[str]]: 配置字典，格式为 {'preserve_empty_string_fields': [字段列表]}
        """
        preserve_fields = []
        field_strategies = self.get_field_strategies()

        if source_table in field_strategies:
            for target_table, field_configs in field_strategies[source_table].items():
                for field, strategy in field_configs.items():
                    if strategy == 'null_to_empty_string':
                        # 源字段名（数据清洗阶段处理的是源字段）
                        preserve_fields.append(field)

        return {
            'preserve_empty_string_fields': preserve_fields
        }

    def get_field_mapping_config(self, source_table: str, target_table: str) -> Dict[str, Set[str]]:
        """
        获取字段映射阶段的配置

        Args:
            source_table (str): 源表名
            target_table (str): 目标表名

        Returns:
            Dict[str, Set[str]]: 配置字典，格式为 {'null_to_empty_string_fields': {字段集合}}
        """
        null_to_empty_fields = set()
        field_strategies = self.get_field_strategies()

        if (source_table in field_strategies and
            target_table in field_strategies[source_table]):

            field_configs = field_strategies[source_table][target_table]
            for field, strategy in field_configs.items():
                if strategy == 'null_to_empty_string':
                    # 目标字段名（字段映射阶段处理的是目标字段）
                    null_to_empty_fields.add(field)

        return {
            'null_to_empty_string_fields': null_to_empty_fields
        }

    def get_data_normalization_config(self, target_table: str) -> Dict[str, List[str]]:
        """
        获取数据标准化阶段的配置

        Args:
            target_table (str): 目标表名

        Returns:
            Dict[str, List[str]]: 配置字典，格式为 {'preserve_empty_string_varchar_fields': [字段列表]}
        """
        preserve_varchar_fields = []
        field_strategies = self.get_field_strategies()

        # 遍历所有源表，找到指向该目标表的字段配置
        for source_table, target_configs in field_strategies.items():
            if target_table in target_configs:
                field_configs = target_configs[target_table]
                for field, strategy in field_configs.items():
                    if strategy == 'null_to_empty_string':
                        preserve_varchar_fields.append(field)

        return {
            'preserve_empty_string_varchar_fields': preserve_varchar_fields
        }

    def get_supported_strategies(self) -> Set[str]:
        """
        获取支持的处理策略列表

        Returns:
            Set[str]: 支持的策略集合
        """
        return self._supported_strategies.copy()


class FieldConfigManager:
    """
    字段配置管理器
    负责管理和协调不同省份的配置
    """

    def __init__(self):
        """初始化配置管理器"""
        self._configs = {}  # 存储各省份的配置实例

    def register_config(self, province: str, config: BaseFieldProcessingConfig):
        """
        注册省份配置

        Args:
            province (str): 省份标识
            config (BaseFieldProcessingConfig): 配置实例
        """
        self._configs[province] = config
        logger.info(f"已注册省份 '{province}' 的字段配置")

    def get_config(self, province: str) -> Optional[BaseFieldProcessingConfig]:
        """
        获取指定省份的配置

        Args:
            province (str): 省份标识

        Returns:
            Optional[BaseFieldProcessingConfig]: 配置实例，如果不存在则返回None
        """
        return self._configs.get(province)

    def get_data_cleaning_config(self, province: str, source_table: str) -> Dict[str, List[str]]:
        """
        获取指定省份的数据清洗配置

        Args:
            province (str): 省份标识
            source_table (str): 源表名

        Returns:
            Dict[str, List[str]]: 配置字典
        """
        config = self.get_config(province)
        if config:
            return config.get_data_cleaning_config(source_table)
        return {'preserve_empty_string_fields': []}

    def get_field_mapping_config(self, province: str, source_table: str, target_table: str) -> Dict[str, Set[str]]:
        """
        获取指定省份的字段映射配置

        Args:
            province (str): 省份标识
            source_table (str): 源表名
            target_table (str): 目标表名

        Returns:
            Dict[str, Set[str]]: 配置字典
        """
        config = self.get_config(province)
        if config:
            return config.get_field_mapping_config(source_table, target_table)
        return {'null_to_empty_string_fields': set()}

    def get_data_normalization_config(self, province: str, target_table: str) -> Dict[str, List[str]]:
        """
        获取指定省份的数据标准化配置

        Args:
            province (str): 省份标识
            target_table (str): 目标表名

        Returns:
            Dict[str, List[str]]: 配置字典
        """
        config = self.get_config(province)
        if config:
            return config.get_data_normalization_config(target_table)
        return {'preserve_empty_string_varchar_fields': []}


# 创建全局配置管理器实例
field_config_manager = FieldConfigManager()


# 提供便捷的函数接口（需要指定省份）
def get_data_cleaning_config(province: str, source_table: str) -> Dict[str, List[str]]:
    """获取数据清洗配置的便捷函数"""
    return field_config_manager.get_data_cleaning_config(province, source_table)


def get_field_mapping_config(province: str, source_table: str, target_table: str) -> Dict[str, Set[str]]:
    """获取字段映射配置的便捷函数"""
    return field_config_manager.get_field_mapping_config(province, source_table, target_table)


def get_data_normalization_config(province: str, target_table: str) -> Dict[str, List[str]]:
    """获取数据标准化配置的便捷函数"""
    return field_config_manager.get_data_normalization_config(province, target_table)


def register_province_config(province: str, config: BaseFieldProcessingConfig) -> None:
    """注册省份配置的便捷函数"""
    field_config_manager.register_config(province, config)
