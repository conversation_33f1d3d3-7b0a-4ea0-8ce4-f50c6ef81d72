import json
import math
import sys
import threading
import queue  # 修改导入方式
import subprocess
import os
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler
import os
import time

import pandas as pd

# 配置日志
logger = logging.getLogger('spider_fuwu_nhsa')
logger.setLevel(logging.INFO)

# 创建格式化器
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# 创建控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 创建文件处理器
current_dir = os.path.dirname(os.path.abspath(__file__))
log_dir = os.path.join(current_dir, 'download', 'log')
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, 'log_fuwu_nhsa_disease_catalog.log')

# 设置日志文件最大为10MB，保留3个备份文件
file_handler = RotatingFileHandler(log_file, maxBytes=10 * 1024 * 1024, backupCount=3, encoding='utf-8')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)


def del_key_recursive(obj, key):
    if isinstance(obj, dict):
        if key in obj:
            del obj[key]
        for v in obj.values():
            del_key_recursive(v, key)
    elif isinstance(obj, list):
        for item in obj:
            del_key_recursive(item, key)


def get_hospital_data(max_pages=5,
                      api_url='https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryFixedHospital',
                      url='/nthl/api/CommQuery/queryFixedHospital', data={}, page_type=2, filename='hospital_data'):
    """
    获取医院数据
    :param max_pages: 获取的最大页数
    :param api_url: API接口地址
    :param url: 请求的URL
    :param data: 请求的数据
    :param page_type: 页码类型
    :param filename: 输出文件名(不带扩展名)
    :return: DataFrame格式的医院数据
    """
    # 检查断点续传文件
    current_dir = os.path.dirname(os.path.abspath(__file__))
    checkpoint_file = os.path.join(current_dir, 'download', f'checkpoint_{filename}.json')
    start_page = 1

    # 从命令行参数获取起始页
    if 'start_page' in data:
        start_page = data['start_page']
        logger.info(f'使用命令行参数指定的起始页：{start_page}')
        # 如果指定了起始页，创建或更新断点文件
        os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump({'last_page': start_page - 1, 'total_records': (start_page - 1) * 100}, f)
        logger.info(f'已创建断点文件，记录上次爬取到第{start_page - 1}页')
    # 如果没有指定起始页，则尝试从断点文件获取
    elif os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint = json.load(f)
                start_page = checkpoint.get('last_page', 1)   # 从上次爬取的下一页开始
                logger.info(f'发现断点续传文件，从第{start_page}页继续爬取')
        except Exception as e:
            logger.warning(f'读取断点续传文件失败：{str(e)}')
            start_page = 1

    # 更新最大页数
    if start_page > max_pages:
        logger.warning(f'起始页{start_page}大于最大页数{max_pages}，重置为1')
        start_page = 1
    try:
        # 运行Node.js脚本获取数据
        script_path = os.path.join(os.path.dirname(__file__), 'yibao.js')

        # 检查文件是否存在
        if not os.path.exists(script_path):
            raise FileNotFoundError(f'Node.js脚本文件不存在：{script_path}')

        logger.info(f'{api_url}开始获取医院数据，起始页：{start_page}，最大页数：{max_pages}')
        # 从data中移除start_page参数
        if 'start_page' in data:
            data.pop('start_page')
        # 构建Node.js命令，使用数组形式传递参数
        script_path = script_path.replace('\\', '\\\\')
        node_script = f"require('{script_path}').fetchHospitalData({max_pages}, '{api_url}', '{url}', {json.dumps(data)}, {page_type}, {start_page}, '{filename}').catch(console.error)"

        # 执行Node.js脚本
        process = subprocess.Popen(
            ['node', '-e', node_script],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )

        # 创建输出队列
        output_queue = queue.Queue()  # 使用queue.Queue

        # 创建并启动输出监控线程
        stdout_thread = threading.Thread(
            target=stream_watcher,
            args=(process.stdout, output_queue, 'stdout')
        )
        stderr_thread = threading.Thread(
            target=stream_watcher,
            args=(process.stderr, output_queue, 'stderr')
        )

        stdout_thread.daemon = True
        stderr_thread.daemon = True
        stdout_thread.start()
        stderr_thread.start()

        # 处理输出
        output_lines, last_page = handle_output(output_queue, logger)

        # 等待进程完成
        process.wait()

        # 检查进程返回码
        if process.returncode != 0:
            raise subprocess.CalledProcessError(
                process.returncode,
                ['node', '-e', node_script]
            )

        # 检查是否成功完成
        if '所有数据获取完成' in '\n'.join(output_lines):
            logger.info("数据爬取已成功完成")
            # 删除断点文件，标记任务完成
            if os.path.exists(checkpoint_file):
                os.remove(checkpoint_file)
            return process_output_files(filename)  # 处理输出文件并返回DataFrame
        else:
            # 保存最后处理的页码到断点文件
            os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                checkpoint_data = {
                    'last_page': last_page if last_page else start_page,
                    'total_records': (last_page if last_page else start_page) * 100,
                    'error': "数据爬取未完成"
                }
                json.dump(checkpoint_data, f)
            raise Exception(f"数据爬取未完成，最后处理的页码：{last_page}")

    except Exception as e:
        logger.error(f'获取医院数据时发生错误：{str(e)}')
        # 检查是否还有重试次数
        retry_count = getattr(get_hospital_data, '_retry_count', 0)
        max_retries = 3  # 最大重试次数

        if retry_count < max_retries:
            retry_count += 1
            setattr(get_hospital_data, '_retry_count', retry_count)
            logger.info(f'准备第{retry_count}次重试...')

            # 保存错误信息到checkpoint文件
            os.makedirs(os.path.dirname(checkpoint_file), exist_ok=True)
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                checkpoint_data = {
                    'last_page': last_page if last_page else start_page,
                    'total_records': (last_page if last_page else start_page) * 100,
                    'error': str(e),
                    'retry_count': retry_count
                }
                json.dump(checkpoint_data, f)

            # 指数退避重试
            wait_time = min(30, 5 * (2 ** (retry_count - 1)))  # 最大等待30秒
            logger.info(f'等待{wait_time}秒后重试...')
            time.sleep(wait_time)
            
            # 从断点文件记录的页码继续爬取
            data['start_page'] = last_page if last_page else start_page
            return get_hospital_data(max_pages, api_url, url, data, page_type, filename)
        else:
            logger.error(f'已达到最大重试次数{max_retries}，停止尝试')
            raise


def stream_watcher(stream, output_queue, stream_name):
    """监控流并将输出放入队列"""
    for line in iter(stream.readline, ''):
        output_queue.put((stream_name, line))
    stream.close()


def handle_output(output_queue, logger):
    """处理队列中的输出"""
    output_lines = []
    last_page = None

    while True:
        try:
            # 使用queue.Empty而不是Queue.Empty
            stream_name, line = output_queue.get(timeout=1)
            line = line.strip()

            if stream_name == 'stdout':
                logger.info(line)
                output_lines.append(line)
                # 从输出中提取当前页码
                if '正在获取第' in line and '页数据' in line:
                    try:
                        current_page = int(line.split('第')[1].split('页')[0])
                        last_page = current_page
                    except (IndexError, ValueError):
                        pass
            elif stream_name == 'stderr':
                logger.error(line)

        except queue.Empty:  # 使用queue.Empty
            # 检查是否所有线程都结束了
            if not any(t.is_alive() for t in threading.enumerate() if t != threading.current_thread()):
                break

    return output_lines, last_page


def process_output_files(filename):
    """处理输出文件并返回DataFrame"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    download_dir = os.path.join(current_dir, 'download')
    json_dir = os.path.join(download_dir, 'json')

    # 查找所有批次数据文件并处理
    all_data = []
    for file in os.listdir(json_dir):
        if file.startswith(f'{filename}_') and file.endswith('.json'):
            with open(os.path.join(json_dir, file), 'r', encoding='utf-8') as f:
                batch_data = json.load(f)
                all_data.extend(batch_data)

    return pd.DataFrame(all_data)


def clean_data(df):
    """
    数据清洗处理
    :param df: 原始DataFrame
    :return: 清洗后的DataFrame
    """
    try:
        # 处理列表类型的数据
        for column in df.columns:
            if df[column].apply(lambda x: isinstance(x, list)).any():
                df[column] = df[column].apply(lambda x: ','.join(x) if isinstance(x, list) else x)

        # 删除重复数据
        df = df.drop_duplicates()

        # 填充空值
        df = df.fillna('')

        # 重置索引
        df = df.reset_index(drop=True)

        return df

    except Exception as e:
        logging.error(f'数据清洗时发生错误：{str(e)}')
        raise


def export_data(df, output_format='csv', file_name='drug_option_detail_data_'):
    """
    导出数据
    :param df: DataFrame数据
    :param output_format: 输出格式（csv/excel/json）
    :return: 导出文件的路径
    :raises ValueError: 当output_format不是支持的格式时
    """
    try:
        # 验证输出格式
        output_format = output_format.lower()
        if output_format not in ['csv', 'excel', 'json']:
            raise ValueError(f'不支持的输出格式：{output_format}，支持的格式有：csv, excel, json')

        # 获取程序执行目录并创建download文件夹
        current_dir = os.path.dirname(os.path.abspath(__file__))
        download_dir = os.path.join(current_dir, 'download')
        os.makedirs(download_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = os.path.join(download_dir,
                                   f'{file_name}_{timestamp}.{"xlsx" if output_format == "excel" else output_format}')

        # 根据格式导出数据
        if output_format == 'csv':
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
        elif output_format == 'excel':
            # 批量处理时间列
            # 获取包含Time或date的列名
            time_columns = df.columns[df.columns.str.contains('Time|date', case=True)]
            if not time_columns.empty:
                # 修改处理逻辑，使用pd.to_numeric安全转换数值
                df[time_columns] = df[time_columns].apply(lambda x: pd.to_numeric(x, errors='coerce')).fillna('')
                # 对非空值进行整数转换
                df[time_columns] = df[time_columns].apply(lambda x: x.apply(lambda y: str(int(y)) if pd.notnull(y) and y != '' else ''))
            other_columns = df.columns[df.columns.str.contains('rid', case=True)]
            if not other_columns.empty:
                df[other_columns] = df[other_columns].apply(lambda x: x.apply(lambda y: str(y) if pd.notnull(y) and y != '' else ''))
            df.to_excel(output_file, index=False)
        elif output_format == 'json':
            df.to_json(output_file, orient='records', force_ascii=False, indent=2)

        logging.info(f'数据已导出到文件：{output_file}')
        return output_file

    except Exception as e:
        logging.error(f'导出数据时发生错误：{str(e)}')
        raise


if __name__ == '__main__':
    json_name = 'service_facilities'
    try:
        # 检查断点文件是否表明任务已完成
        checkpoint_file = os.path.join(os.path.dirname(__file__), 'download', f'checkpoint_{json_name}.json')
        if os.path.exists(checkpoint_file):
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint = json.load(f)
                if checkpoint.get('completed', False):
                    logger.info("任务已经完成，无需重新爬取")
                    sys.exit(0)

        else:
            # 设置参数
            start_page = 1  # 从1041页开始继续爬取
            max_pages = 3411
            api_url = 'https://fuwu.nhsa.gov.cn/ebus/fuwu/api/nthl/api/CommQuery/queryServiceFacilities'
            url = api_url.replace('https://fuwu.nhsa.gov.cn/ebus/fuwu/api', '')
            data = {
                'areaCode': "",
                'medListCodg': "",
                'pageSize': 100,
                'servitemName': "",
                'start_page': start_page
            }

            # 获取医院数据
            hospital_df = get_hospital_data(max_pages, api_url, url, data, 1, json_name)

            if hospital_df is not None:
                # 显示数据基本信息
                print('\n数据基本信息：')
                print(hospital_df.info())

                # 显示前几行数据
                print('\n数据预览：')
                print(hospital_df.head())

                # 导出数据
                export_data(hospital_df, 'excel', json_name)

    except Exception as e:
        logging.error(f'程序执行出错：{str(e)}')
