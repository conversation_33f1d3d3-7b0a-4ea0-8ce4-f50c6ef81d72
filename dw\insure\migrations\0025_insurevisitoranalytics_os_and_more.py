# Generated by Django 4.2.1 on 2025-07-30 09:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0024_insurevisitoranalytics_delete_insureconversionrate_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="insurevisitoranalytics",
            name="os",
            field=models.CharField(
                blank=True,
                db_comment="操作系统名称",
                help_text="操作系统名称",
                max_length=20,
                null=True,
                verbose_name="操作系统",
            ),
        ),
        migrations.AlterField(
            model_name="insurevisitoranalytics",
            name="data_type",
            field=models.CharField(
                choices=[
                    ("conversion_daily", "转换率-按日期"),
                    ("conversion_hourly", "转换率-按小时"),
                    ("conversion_weekly_hourly", "转换率-按星期+小时"),
                    ("visitor_date_hourly", "访客-按日期+小时"),
                    ("visitor_workday_hourly", "访客-按工作日+小时"),
                    ("visitor_os", "访客-操作系统"),
                ],
                db_comment="访客分析数据的类型",
                help_text="访客分析数据的类型",
                max_length=128,
                verbose_name="数据类型",
            ),
        ),
    ]
