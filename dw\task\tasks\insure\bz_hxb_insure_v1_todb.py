import logging
import time

from transfrom.tasks.insure.bz_hxb_insure_v1 import BzHxbInsureV1
from transfrom.utils.utils import send_feishu_message

logger = logging.getLogger(__name__)

def normal_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = BzHxbInsureV1()
    for method in [
        'get_total_person_cumsum',
        'get_today_person_count',
        'get_yesterday_person_count',
        'get_total_cumsum',
        'get_today_count',
        'get_yesterday_count',
        'get_total_amount',
        'get_coverage_rate',
        'get_product_count',
        'get_school',
        'get_school_count',
        'get_area_info',
        'get_age_gender',
        'get_pay_type_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'bz_hxb_insure_v1_todb normal_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'bz_hxb_insure_v1_todb normal_to_db: {method} cost:{_cost} ms')


def history_to_db():
    """
    补充历史数据，并保证历史数据的准确性，图表数据由于调度，不一定可以获取准确的当日数据
    """
    source = BzHxbInsureV1()
    for method in [
        'get_daily_person_cumsum',
        'get_daily_person_count',
        'get_daily_count',
        'get_daily_cumsum',
        'get_daily_amount',
        'get_daily_amount_cumsum',
        'get_daily_coverage_rate'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'bz_hxb_insure_v1_todb history_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'bz_hxb_insure_v1_todb history_to_db: {method} cost:{_cost} ms')



