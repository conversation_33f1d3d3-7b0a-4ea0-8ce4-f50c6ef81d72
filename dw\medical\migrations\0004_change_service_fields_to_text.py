# Generated by Django 4.2.1 on 2025-06-05 13:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("medical", "0003_alter_medicalservicebase_pricing_unit"),
    ]

    operations = [
        migrations.AlterField(
            model_name="medicalservicebase",
            name="treatment_excluded_content",
            field=models.TextField(
                blank=True,
                db_comment="诊疗除外内容",
                null=True,
                verbose_name="诊疗除外内容",
            ),
        ),
        migrations.AlterField(
            model_name="medicalservicebase",
            name="treatment_item_content",
            field=models.TextField(
                blank=True,
                db_comment="诊疗项目内涵",
                null=True,
                verbose_name="诊疗项目内涵",
            ),
        ),
        migrations.AlterField(
            model_name="medicalservicebase",
            name="treatment_item_description",
            field=models.TextField(
                blank=True,
                db_comment="诊疗项目说明",
                null=True,
                verbose_name="诊疗项目说明",
            ),
        ),
    ]
