#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
核心清洗逻辑在data_cleaning.py中
表配置在table_configs.py中
字段配置在field_mapping.py中
"""

# 设置项目路径
import sys
import os

# 添加项目根目录到Python路径
current_file = os.path.abspath(__file__)
project_root = current_file
# 从当前文件向上6级到达dw根目录
for _ in range(6):
    project_root = os.path.dirname(project_root)

if project_root not in sys.path:
    sys.path.insert(0, project_root)

import logging
import warnings

import pandas as pd

# 导入gjyb配置（自动注册到全局配置管理器）
import transfrom.tasks.spider.gjyb.pipeline.config

# 导入通用ETL模块
from transfrom.utils import (
    # 数据库相关
    get_connection,
    DEFAULT_DB,

    # ETL处理器
    BatchOperationManager,

    # 字段映射相关
    FieldMappingManager,
    DictMappingManager,
    DataTransformer
)

# 导入表配置 - 从config目录导入
try:
    from .config.table_configs import (
        TABLE_CONFIGS,
        PROCESSING_MODES,
        OPERATION_MODES,
        print_table_menu,
        print_processing_mode_menu,
        print_operation_mode_menu,
        validate_table_choice,
        get_processing_mode_config,
        get_operation_mode_config,
        DEFAULT_BATCH_SIZE,
        DEFAULT_TABLE
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from config.table_configs import (
        TABLE_CONFIGS,
        PROCESSING_MODES,
        OPERATION_MODES,
        print_table_menu,
        print_processing_mode_menu,
        print_operation_mode_menu,
        validate_table_choice,
        get_processing_mode_config,
        get_operation_mode_config,
        DEFAULT_BATCH_SIZE,
        DEFAULT_TABLE
    )

logger = logging.getLogger(__name__)
warnings.filterwarnings("ignore")
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", 200)
pd.set_option("display.expand_frame_repr", False)
pd.set_option("display.float_format", lambda x: "%.6f" % x)

# 保持向后兼容
DEFALUT_DB = DEFAULT_DB

# 创建全局实例
field_mapping_manager = FieldMappingManager()
dict_mapping_manager = DictMappingManager()
data_transformer = DataTransformer()


# 这些函数已经移动到通用模块中，这里保留引用以保持向后兼容
# 为了向后兼容，创建函数别名
def get_field_mapping(source_table=None, target_table=None):
    """获取字段映射关系 - 向后兼容函数"""
    return field_mapping_manager.get_field_mapping(source_table, target_table)

def get_dict_mapping(dict_id=None):
    """获取字典映射关系 - 向后兼容函数"""
    return dict_mapping_manager.get_dict_mapping(dict_id)

def get_custom_dict_mapping(dict_table, dict_code_field, dict_name_field):
    """获取自定义字典表的映射关系 - 向后兼容函数"""
    return dict_mapping_manager.get_custom_dict_mapping(dict_table, dict_code_field, dict_name_field)

def apply_field_mapping(source_df, mapping_df):
    """应用字段映射转换 - 向后兼容函数"""
    return field_mapping_manager.apply_field_mapping(source_df, mapping_df)

def apply_dict_mapping(df, mapping_row, dict_mapping_df=None):
    """应用字典映射转换 - 向后兼容函数"""
    return dict_mapping_manager.apply_dict_mapping(df, mapping_row, dict_mapping_df)

def validate_mapping(source_table, target_table):
    """验证映射配置的完整性和正确性 - 向后兼容函数"""
    return field_mapping_manager.validate_mapping(source_table, target_table)

def get_source_data_sample(source_table, limit=10):
    """获取源表数据样本 - 向后兼容函数"""
    from transfrom.utils import default_etl_processor
    return default_etl_processor.get_source_data_sample(source_table, limit)

# DatabaseUpsertManager 类已从通用模块导入，这里创建别名以保持向后兼容
# 使用 BatchOperationManager 替代
DatabaseUpsertManager = BatchOperationManager


def execute_etl_transform(source_table, target_table, limit=None,operation_mode='upsert',
                          unique_fields=None, batch_size=10000, excel_save_operations=['update'], target_where_clause=None):
    """
    执行ETL转换的主函数

    Args:
        source_table (str): 源表名
        target_table (str): 目标表名
        limit (int): 限制处理的记录数，None表示处理所有
        operation_mode (str): 操作模式，'upsert' 或 'traditional'
        unique_fields (list): 唯一性字段列表
        batch_size (int): 批处理大小
        excel_save_operations (list): 需要保存Excel的操作类型列表，可选值：['update', 'delete', 'insert']
                                    None表示不保存任何Excel文件，[]表示不保存，['update', 'delete']表示只保存更新和删除
        target_where_clause (str): 目标表数据过滤条件，可选

    Returns:
        bool: 执行是否成功
    """
    try:
        logger.info(f"开始ETL转换: {source_table} -> {target_table}")

        # 处理Excel保存操作类型参数
        if excel_save_operations is None:
            excel_save_operations = []  # 默认不保存任何Excel文件

        if not excel_save_operations:
            logger.info("Excel保存功能已禁用，将使用CSV保存更新对比信息")
        else:
            logger.info(f"Excel保存已启用，将保存以下操作类型: {excel_save_operations}")

        # 🚀 使用通用ETL处理器，传递excel_save_operations参数
        # ⚙️ 数据处理的关键逻辑在这里
        # 核心清洗逻辑在data_cleaning.py中

        # 创建专用的ETL处理器实例，控制Excel保存行为和输出目录
        from transfrom.utils.etl_base import ETLProcessor
        import os

        # 设置输出目录为当前文件的目录
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_file_dir, "output")

        etl_processor = ETLProcessor(output_dir=output_dir, excel_save_operations=excel_save_operations)

        stats = etl_processor.process_etl(
            source_table=source_table,
            target_table=target_table,
            unique_fields=unique_fields,
            operation_mode=operation_mode,
            batch_size=batch_size,
            limit=limit,
            target_where_clause=target_where_clause,
            province='gjyb'  # 指定为国家医保局
        )

        logger.info(f"ETL转换完成: 新增 {stats.get('inserted', 0)} 条, "
                   f"更新 {stats.get('updated', 0)} 条, "
                   f"删除 {stats.get('deleted', 0)} 条, "
                   f"错误 {stats.get('errors', 0)} 条")

        return stats.get('errors', 0) == 0

    except Exception as e:
        logger.error(f"ETL转换失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False


def get_source_data(source_table, limit=None):
    """
    获取源表数据

    Args:
        source_table (str): 源表名
        limit (int): 限制返回的行数

    Returns:
        pd.DataFrame: 源表数据
    """
    sql = f"SELECT * FROM {source_table}"
    if limit:
        sql += f" LIMIT {limit}"

    with get_connection(DEFALUT_DB) as conn:
        df = pd.read_sql(sql, conn)

    return df


def create_sqlalchemy_engine_with_retry(db_config=None, max_retries=3):
    """
    创建SQLAlchemy引擎，带重试机制 - 向后兼容函数

    Args:
        db_config (dict): 数据库配置
        max_retries (int): 最大重试次数

    Returns:
        sqlalchemy.engine.Engine: SQLAlchemy引擎
    """
    # 使用通用模块的函数
    from transfrom.utils import create_sqlalchemy_engine_with_retry as utils_create_engine
    return utils_create_engine(db_config, max_retries)


def main():
    """
    主函数 - 服务ETL映射处理
    """
    print("=== [服务] ETL映射处理 ===")

    # [选择] 选择要处理的表
    print_table_menu()
    table_list = list(TABLE_CONFIGS.keys())
    table_choice = input(f"请输入选择 (1-{len(table_list)}): ").strip()

    is_valid, table_index = validate_table_choice(table_choice, len(table_list))
    if is_valid:
        selected_table = table_list[table_index]
        selected_config = TABLE_CONFIGS[selected_table]
        icon = selected_config.get('icon', '[*]')
        print(f"[OK] 已选择: {icon} {selected_table}")
    else:
        print("[ERROR] 无效选择，使用默认配置")
        selected_table = DEFAULT_TABLE
        selected_config = TABLE_CONFIGS[selected_table]
        icon = selected_config.get('icon', '[*]')
        print(f"[DEFAULT] 使用默认: {icon} {selected_table}")

    # 从配置中提取参数
    source_table = selected_config["source_table"]
    target_table = selected_config["target_table"]
    unique_fields = selected_config["unique_fields"]
    target_where_clause = selected_config.get("target_where_clause")  # 目标表过滤条件（可选）

    # [模式] 获取处理模式选择
    print_processing_mode_menu()
    processing_choice = input(f"请输入选择 (1-{len(PROCESSING_MODES)}): ").strip()
    processing_config = get_processing_mode_config(processing_choice)
    if not processing_config:
        print("[ERROR] 无效选择，使用默认测试模式")
        processing_config = get_processing_mode_config('1')
    print(f"[OK] 已选择: {processing_config['icon']} {processing_config['name']}")

    # [操作] 获取操作模式
    print_operation_mode_menu()
    operation_choice = input(f"请输入选择 (1-{len(OPERATION_MODES)}): ").strip()
    operation_config = get_operation_mode_config(operation_choice)
    if not operation_config:
        print("[ERROR] 无效选择，使用默认Upsert模式")
        operation_config = get_operation_mode_config("1")

    operation_mode = operation_config['mode']
    print(f"[OK] 已选择: {operation_config['icon']} {operation_config['name']}")

    # [Excel保存] 获取Excel保存控制选项
    print(f"\n[Excel保存] 请选择Excel保存模式:")
    print("1. [启用] 保存Excel文件 (默认)")
    print("2. [禁用] 不保存Excel文件")

    # [执行] 根据配置执行处理
    limit = processing_config['limit']

    # [配置] 使用默认批处理大小（底层会自动优化）
    batch_size = DEFAULT_BATCH_SIZE
    # 注意：process_all参数已移除，因为底层process_etl函数不支持此参数

    # 处理自定义数量的情况
    if limit == "custom":
        limit_input = input("[自定义] 请输入处理数量: ").strip()
        limit = int(limit_input) if limit_input else 1000
        print(f"[OK] 设置处理数量: {limit:,} 条")

    # 显示执行摘要
    print(f"\n[摘要] 执行摘要:")
    print(f"   表名: {icon} {selected_table}")
    print(f"   源表: {source_table}")
    print(f"   目标表: {target_table}")
    if target_where_clause:
        print(f"   目标表过滤: {target_where_clause}")
    print(f"   处理模式: {processing_config['icon']} {processing_config['name']}")
    print(f"   操作模式: {operation_config['icon']} {operation_config['name']}")
    print(f"   批处理大小: {batch_size:,}")
    if limit:
        print(f"   处理数量: {limit:,} 条")
    else:
        print(f"   处理数量: 全量数据")

    # 执行ETL转换
    print(f"\n[START] 开始执行ETL转换...")
    success = execute_etl_transform(
        source_table=source_table,
        target_table=target_table,
        limit=limit,
        operation_mode=operation_mode,
        unique_fields=unique_fields,
        batch_size=batch_size,
        target_where_clause=target_where_clause,
    )

    if success:
        print("\n[SUCCESS] ETL任务执行成功!")
    else:
        print("\n[ERROR] ETL任务执行失败!")


if __name__ == "__main__":
    main()
