# 江苏医保局爬虫数据清洗规则说明

## 概述

本文档详细说明了江苏医保局爬虫数据到目标表的ETL清洗规则，包括表级别清洗、字段级别清洗和自定义清洗函数的配置。

## 支持的数据表

| 源表 | 目标表 | 描述 | 唯一性字段 |
|------|--------|------|------------|
| `spider_jsyb_service_facilities` | `medical_service_entity` | 江苏医疗服务项目省市实体 | `charge_item_code`, `charge_item_name`, `code`, `remark` |
| `spider_jsyb_drug` | `medical_drug_entity` | 江苏医保药品省市实体 | `code` |

## 清洗规则分类

### 1. 表级别清洗规则

#### 1.1 江苏医疗服务项目 (`spider_jsyb_service_facilities`)
- **唯一键约束**: 使用table_configs中配置的unique_fields: `['charge_item_code', 'charge_item_name', 'code', 'remark']`
- **字段映射**: 不修改medical_field_mapping表，在清洗阶段直接创建目标字段
- **additional_info处理**: 将价格字段组装成JSON格式写入additional_info字段
- **地理位置标识**: 自动添加江苏省的省份和城市信息
- **数据过滤**: 使用table_configs配置和通用过滤函数，确保配置一致性
- **数据类型转换**: 价格和比例字段的数值类型转换

#### 1.2 江苏医保药品 (`spider_jsyb_drug`)
- **唯一键约束**: 使用table_configs中配置的unique_fields: `['code']`
- **字段映射**: 在清洗阶段直接创建目标字段，映射中文字段到英文字段
- **地理位置标识**: 自动添加江苏省省级标识
- **数据过滤**: 使用table_configs配置和通用过滤函数，确保配置一致性
- **数据类型转换**: 价格、比例等数值字段的类型转换
- **百分比处理**: 个人先行自付比例支持百分比格式转换

### 2. 字段映射详情

#### 2.1 基础信息字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `收费项目编码` | `charge_item_code` | 收费项目编码 |
| `收费项目名称` | `charge_item_name` | 收费项目名称 |
| `国家医疗服务项目代码` | `code` | 国家医疗服务项目代码 |
| `国家医疗服务项目名称` | `name` | 国家医疗服务项目名称 |

#### 2.2 项目内容字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `项目内涵` | `treatment_item_content` | 诊疗项目内涵 |
| `除外内容` | `treatment_excluded_content` | 诊疗除外内容 |
| `说明` | `treatment_item_description` | 诊疗项目说明 |

#### 2.3 支付和价格字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `医保支付类别` | `payment_type` | 医保支付类别 |
| `计价单位` | `pricing_unit` | 计价单位 |
| `供应价格` | `price` | 供应价格 |
| `限定支付` | `limited_payment` | 限定支付 |
| `先行自付` | `initial_payment_ratio` | 先行自付比例 |

#### 2.4 服务相关字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `服务产出` | `service_output` | 服务产出 |
| `价格构成` | `price_composition` | 价格构成 |

#### 2.5 地理位置字段（自动生成）
| 目标字段 | 默认值 | 说明 |
|----------|--------|------|
| `province_code` | `32` | 江苏省编码 |
| `province_name` | `江苏省` | 省份名称 |
| `city_code` | `''` | 城市编码（省级数据为空） |
| `city_name` | `''` | 城市名称（省级数据为空） |
| `level_type` | `省级` | 层级类型 |

### 3. 药品字段映射详情

#### 3.1 基础信息字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `国家药品代码` | `code` | 药品代码（唯一键） |
| `医保药品名称` | `name` | 药品名称 |
| `注册名称` | `generic_name_national` | 注册名称 |
| `批准文号` | `approval_number` | 批准文号 |
| `药品企业` | `production_company_name` | 生产企业名称 |

#### 3.2 医保相关字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `医保支付类别` | `categories_national` | 甲乙类(国家医保药品目录) |
| `医保剂型` | `dosage_form_national` | 剂型(国家医保药品目录) |
| `医保限定支付范围` | `payment_upper_limit` | 医保支付上限 |
| `个人先行自付比例` | `initial_payment_ratio` | 先行自付比例 |

#### 3.3 规格和价格字段
| 源字段 | 目标字段 | 说明 |
|--------|----------|------|
| `实际规格` | `specification` | 药品规格 |
| `最小包装数量` | `minimum_prescription_unit` | 最小处方单位 |
| `省集中采购上限价` | `procure_ceil_price` | 集中采购上限价 |

#### 3.4 分类字段（自动生成）
| 目标字段 | 默认值 | 说明 |
|----------|--------|------|
| `category` | `医保药品` | 药品分类 |
| `category_name` | `医保药品` | 药品分类名称 |
| `level_type` | `province` | 层级类型（英文） |

### 4. 自定义清洗函数详解

#### 4.1 江苏医疗服务项目清洗 (`_clean_jsyb_service_facilities_data`)

**处理字段**: 23个字段的完整映射关系

##### 唯一键处理
**源表唯一键**: `['收费项目编码', '收费项目名称', '国家医疗服务项目代码', '三类医院价格苏南']`
**目标表唯一键**: `['charge_item_code', 'charge_item_name', 'code', 'remark']`

##### 基础字段映射
**功能**: 将源表的中文字段名映射到目标表的英文字段名
**处理逻辑**:
```python
field_mappings = {
    '收费项目编码': 'charge_item_code',
    '收费项目名称': 'charge_item_name',
    '国家医疗服务项目代码': 'code',
    '国家医疗服务项目名称': 'name',
    # ... 其他字段映射
}
```

##### 地理位置信息添加
**功能**: 为江苏省数据添加标准的地理位置标识
**处理逻辑**:
- 省份编码: 固定为 `32` (江苏省)
- 省份名称: 固定为 `江苏省`
- 城市信息: 省级数据，城市编码和名称为空
- 层级类型: 标识为 `省级`

##### additional_info字段处理（重要）
**功能**: 将价格字段组装成JSON格式写入additional_info字段
**格式要求**:
```json
{
  "level1_hospital_price_sunan": 100,
  "level1_hospital_price_suzhong": 95,
  "level1_hospital_price_subei": 90,
  "level2_hospital_price_sunan": 80,
  "level2_hospital_price_suzhong": 75,
  "level2_hospital_price_subei": 70,
  "level3_hospital_price_sunan": 60,
  "level3_hospital_price_suzhong": 55,
  "level3_hospital_price_subei": 50
}
```

**处理逻辑**:
```python
def build_additional_info(row):
    price_mapping = {
        '一类医院价格苏南': 'level1_hospital_price_sunan',
        '一类医院价格苏中': 'level1_hospital_price_suzhong',
        '一类医院价格苏北': 'level1_hospital_price_subei',
        '二类医院价格苏南': 'level2_hospital_price_sunan',
        '二类医院价格苏中': 'level2_hospital_price_suzhong',
        '二类医院价格苏北': 'level2_hospital_price_subei',
        '三类医院价格苏南': 'level3_hospital_price_sunan',
        '三类医院价格苏中': 'level3_hospital_price_suzhong',
        '三类医院价格苏北': 'level3_hospital_price_subei',
    }
    additional_info = {}
    for source_key, target_key in price_mapping.items():
        val = row.get(source_key, None)
        if pd.notna(val) and str(val).strip() not in ['-', '--', '—', '/', '\\', '']:
            try:
                additional_info[target_key] = float(val)
            except (ValueError, TypeError):
                additional_info[target_key] = str(val).strip()
    return json.dumps(additional_info, ensure_ascii=False) if additional_info else '{}'

df['additional_info'] = df.apply(build_additional_info, axis=1)
```

##### 价格字段处理
**功能**: 使用三类医院价格苏南作为主价格字段
**处理逻辑**:
```python
# 主价格字段 - 使用三类医院价格苏南
df['price'] = pd.to_numeric(df['三类医院价格苏南'], errors='coerce')
```

##### 按唯一键过滤和去重
**功能**: 使用table_configs配置和通用函数进行过滤和去重处理
**处理逻辑**:
```python
# 从table_configs获取unique_fields配置，确保配置一致性
from .table_configs import get_jsyb_table_config
table_config = get_jsyb_table_config("江苏医疗服务项目")
unique_fields = table_config.get("unique_fields", ['charge_item_code', 'charge_item_name', 'code', 'remark'])

logger.info(f"使用table_configs中的unique_fields: {unique_fields}")

# 使用通用的过滤和去重函数
df = CommonFieldProcessing.filter_and_deduplicate_data(df, unique_fields)
```

##### 数据比较逻辑优化（v1.2新增）
**功能**: 解决ETL重复更新问题，优化数据值比较逻辑
**核心改进**:
1. **空值标准化**: 统一处理各种空值表示
2. **混合类型比较**: 优化字符串与数值的比较
3. **精度控制**: 改进浮点数比较精度

**空值标准化逻辑**:
```python
def normalize_empty_value(val):
    """标准化空值"""
    if val is None:
        return None
    if isinstance(val, str):
        val = val.strip()
        if val in ['', '-', '--', '—', '/', '\\', 'None', 'null', 'NULL']:
            return None
    return val
```

**数值比较优化**:
```python
def try_numeric_compare(v1, v2):
    """尝试数值比较"""
    try:
        float_val1 = float(v1)
        float_val2 = float(v2)

        # 整数值按整数比较
        if float_val1.is_integer() and float_val2.is_integer():
            return int(float_val1) != int(float_val2)
        else:
            # 浮点数比较考虑精度误差
            return abs(float_val1 - float_val2) > 1e-10
    except (ValueError, TypeError):
        return None
```

#### 4.2 江苏医保药品清洗 (`_clean_jsyb_drug_data`)

**处理字段**: 13个字段的完整映射关系

##### 唯一键处理
**源表唯一键**: `['国家药品代码']`
**目标表唯一键**: `['code']`

##### 基础字段映射
**功能**: 将源表的中文字段名映射到目标表的英文字段名
**处理逻辑**:
```python
field_mappings = {
    '国家药品代码': 'code',
    '医保药品名称': 'name',
    '医保支付类别': 'categories_national',
    '医保剂型': 'dosage_form_national',
    '注册名称': 'generic_name_national',
    '实际规格': 'specification',
    '最小包装数量': 'minimum_prescription_unit',
    '省集中采购上限价': 'procure_ceil_price',
    '批准文号': 'approval_number',
    '药品企业': 'production_company_name',
    '医保限定支付范围': 'payment_upper_limit',
    '个人先行自付比例': 'initial_payment_ratio'
}
```

##### 地理位置信息添加
**功能**: 为江苏省药品数据添加标准的地理位置标识
**处理逻辑**:
- 省份编码: 固定为 `32` (江苏省)
- 省份名称: 固定为 `江苏省`
- 城市信息: 省级数据，城市编码和名称为空
- 层级类型: 标识为 `province` (英文)

##### 数值字段处理
**功能**: 处理价格和比例等数值字段
**处理逻辑**:
```python
# 价格字段处理
df['procure_ceil_price'] = pd.to_numeric(df['procure_ceil_price'], errors='coerce')

# 个人先行自付比例处理（支持百分比格式）
def convert_percentage(val):
    if pd.isna(val):
        return None
    val_str = str(val).strip()
    if val_str.endswith('%'):
        try:
            return float(val_str[:-1]) / 100  # 10% -> 0.1
        except ValueError:
            return None
    else:
        try:
            return float(val_str)
        except ValueError:
            return None

df['initial_payment_ratio'] = df['initial_payment_ratio'].apply(convert_percentage)
```

##### 按唯一键过滤和去重
**功能**: 使用table_configs配置和通用函数进行过滤和去重处理
**处理逻辑**:
```python
# 从table_configs获取unique_fields配置，确保配置一致性
from .table_configs import get_jsyb_table_config
table_config = get_jsyb_table_config("江苏医保药品")
unique_fields = table_config.get("unique_fields", ['code'])

logger.info(f"使用table_configs中的unique_fields: {unique_fields}")

# 使用通用的过滤和去重函数
df = CommonFieldProcessing.filter_and_deduplicate_data(df, unique_fields)
```

## 使用方法

### 自动执行（推荐）

#### 执行所有ETL任务
```bash
cd dw/transfrom/tasks/spider/jsyb/pipeline
python jsyb_etl_mapping.py
```

#### 单独执行医疗服务项目ETL
```bash
cd dw/transfrom/tasks/spider/jsyb/pipeline
python -c "from jsyb_etl_mapping import run_jsyb_service_facilities_etl; run_jsyb_service_facilities_etl()"
```

#### 单独执行药品ETL
```bash
cd dw/transfrom/tasks/spider/jsyb/pipeline
python run_jsyb_drug_etl.py
```

### 编程调用

#### 医疗服务项目ETL
```python
from transfrom.tasks.spider.jsyb.pipeline.jsyb_etl_mapping import run_jsyb_service_facilities_etl

# 江苏医疗服务项目ETL
success = run_jsyb_service_facilities_etl()
```

#### 药品ETL
```python
from transfrom.tasks.spider.jsyb.pipeline.jsyb_etl_mapping import run_jsyb_drug_etl

# 江苏医保药品ETL
success = run_jsyb_drug_etl()
```



## 清洗效果监控

### 验证方法

#### 医疗服务项目数据验证
```sql
-- 检查江苏医疗服务项目目标表数据质量
SELECT COUNT(*) FROM medical_service_entity WHERE province_name='江苏省';

-- 检查字段映射完整性
SELECT
    COUNT(CASE WHEN charge_item_code IS NOT NULL THEN 1 END) as with_charge_code,
    COUNT(CASE WHEN code IS NOT NULL THEN 1 END) as with_service_code,
    COUNT(CASE WHEN price IS NOT NULL THEN 1 END) as with_price
FROM medical_service_entity WHERE province_name='江苏省';
```

#### 药品数据验证
```sql
-- 检查江苏医保药品目标表数据质量
SELECT COUNT(*) FROM medical_drug_entity WHERE province_name='江苏省' AND level_type='province';

-- 检查字段映射完整性
SELECT
    COUNT(CASE WHEN code IS NOT NULL THEN 1 END) as with_drug_code,
    COUNT(CASE WHEN name IS NOT NULL THEN 1 END) as with_drug_name,
    COUNT(CASE WHEN procure_ceil_price IS NOT NULL THEN 1 END) as with_price
FROM medical_drug_entity WHERE province_name='江苏省' AND level_type='province';

-- 检查数据类型转换效果
SELECT
    AVG(procure_ceil_price) as avg_price,
    AVG(initial_payment_ratio) as avg_ratio,
    COUNT(CASE WHEN procure_ceil_price > 0 THEN 1 END) as valid_price_count
FROM medical_drug_entity WHERE province_name='江苏省' AND level_type='province';
```

## 问题排查和诊断工具

### 数据一致性检查
当遇到ETL重复更新问题时，可以使用以下工具进行诊断：

```bash
# 检查数据一致性
python check_data_consistency.py

# 诊断ETL问题
python diagnose_etl_issue.py

# 测试药品ETL
python test_jsyb_drug_etl.py
```

### 常见问题及解决方案

#### 1. 每次执行都更新相同数据
**症状**: ETL执行时显示更新了数据，但源表数据实际未变动
**原因**: 数据比较逻辑将相同值判断为不同（如 `"-"` vs `None`）
**解决**: 已在v1.2版本中修复，统一空值比较逻辑

#### 2. 数据类型不匹配
**症状**: 相同内容但不同类型的值被判断为不同
**原因**: 字符串 `"100"` 与数值 `100` 比较逻辑问题
**解决**: 优化 `values_are_different` 函数的混合类型比较

#### 3. 唯一键冲突
**症状**: 插入数据时出现唯一键约束错误
**原因**: 源表存在重复的唯一键组合
**解决**: 在清洗阶段按唯一键去重，保留第一条记录

### 测试脚本管理

#### 测试脚本生命周期
1. **开发阶段**: 创建测试脚本验证功能
2. **测试完成**: 保留脚本用于回归测试
3. **生产部署**: 可选择性删除临时测试脚本
4. **维护阶段**: 保留核心诊断工具

#### 需要保留的脚本
- `check_data_consistency.py` - 数据一致性检查（长期保留）
- `diagnose_etl_issue.py` - ETL问题诊断（长期保留）

#### 可删除的临时脚本
- `test_fix.py` - 修复效果验证（测试完成后可删除）
- `test_cleaning_logic.py` - 清洗逻辑测试（开发完成后可删除）

### 清洗逻辑变动管理流程

#### 变动类型分类
1. **重大变动**: 影响数据结构、唯一键、核心清洗逻辑
2. **一般变动**: 字段映射调整、数据类型优化
3. **修复变动**: Bug修复、性能优化

#### 文档更新要求
每次修改清洗逻辑后，必须按以下步骤更新文档：

1. **更新版本历史**
   ```markdown
   ### vX.X (版本描述)
   - 变动内容1
   - 变动内容2
   - 影响范围说明
   ```

2. **更新相关章节**
   - 修改对应的清洗函数详解
   - 更新字段映射表（如有变动）
   - 调整使用方法（如有变动）

3. **添加注意事项**
   - 新增的限制条件
   - 兼容性说明
   - 迁移指导

4. **验证文档准确性**
   - 运行测试脚本验证
   - 检查示例代码正确性
   - 确认配置参数有效性

#### 变动记录模板
```markdown
## 变动记录 - YYYY-MM-DD

### 变动类型: [重大/一般/修复]
### 变动文件:
- 文件路径1: 变动描述
- 文件路径2: 变动描述

### 变动原因:
问题描述或需求说明

### 影响范围:
- 数据处理逻辑
- 字段映射关系
- 性能影响

### 测试验证:
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 文档更新完成
```

## 注意事项

1. **唯一性约束**: 源表唯一键为 `['收费项目编码', '收费项目名称', '国家医疗服务项目代码', '三类医院价格苏南']`
2. **地理位置标识**: 所有数据自动标识为江苏省省级数据
3. **按唯一键过滤**: 现在会按照唯一键进行过滤，确保数据唯一性
4. **数据完整性**: 清洗过程优先保护数据完整性
5. **文档同步**: 每次修改清洗逻辑后必须更新本文档
6. **测试验证**: 重大修改后必须运行诊断工具验证效果

## 字段映射说明

### 医疗服务项目核心字段映射
- **收费项目编码** → `charge_item_code`
- **收费项目名称** → `charge_item_name`
- **国家医疗服务项目代码** → `code`
- **国家医疗服务项目名称** → `name`
- **项目内涵** → `treatment_item_content`
- **除外内容** → `treatment_excluded_content`
- **医保支付类别** → `payment_type`
- **计价单位** → `pricing_unit`
- **供应价格** → `price`
- **三类医院价格苏南** → `remark` (用于唯一性约束)

### 药品核心字段映射
- **国家药品代码** → `code` (唯一键)
- **医保药品名称** → `name`
- **医保支付类别** → `categories_national`
- **医保剂型** → `dosage_form_national`
- **注册名称** → `generic_name_national`
- **实际规格** → `specification`
- **省集中采购上限价** → `procure_ceil_price`
- **批准文号** → `approval_number`
- **药品企业** → `production_company_name`
- **个人先行自付比例** → `initial_payment_ratio`

### 地理位置字段
- **level_type**: 固定值 `省级` (医疗服务) / `province` (药品)
- **province_code**: 固定值 `32`
- **province_name**: 固定值 `江苏省`

### 其他字段
其他字段根据源表数据情况进行映射，详细映射逻辑请参考数据清洗配置文件中的字段映射配置。

## 版本历史

### v1.0 (初始版本)
- 江苏医疗服务项目ETL框架
- 14个字段的基础映射
- 价格字段智能处理
- 地理位置自动标识
- 数据清洗和验证逻辑

### v1.1 (字段映射优化版本)
- 优化字段映射逻辑
- 修正地理位置字段的默认值
- 完善ETL测试流程，确保数据质量

### v1.2 (数据比较逻辑优化版本)
- **解决重复更新问题**: 修复每次执行ETL都会更新部分数据的问题
- **优化空值比较逻辑**: 统一处理 `-`, `--`, `—`, `/`, `\`, `None`, `null`, `NULL` 等空值表示
- **增强数值比较能力**: 改进字符串与数值的混合比较，优化浮点数精度比较
- **新增诊断工具**:
  - `check_data_consistency.py` - 数据一致性检查工具
  - `diagnose_etl_issue.py` - ETL问题诊断工具
  - `test_fix.py` - 修复效果验证工具
- **性能优化**: 避免不必要的数据更新操作，提高ETL执行效率

### v1.3 (测试和维护工具完善版本)
- **测试脚本管理**: 明确测试脚本的生命周期和清理要求
- **文档同步机制**: 建立清洗逻辑变动时的文档更新流程
- **问题排查流程**: 完善ETL问题的诊断和解决流程

## 变动记录 - 2025-06-16

### 变动类型: 重大
### 变动文件:
- `dw/transfrom/utils/data_normalization.py`: 优化 values_are_different 函数，增加空值标准化逻辑
- `dw/transfrom/tasks/spider/jsyb/pipeline/files/江苏医保局爬虫数据清洗规则说明.md`: 合并优化说明，完善文档结构

### 变动原因:
解决ETL执行时每次都会更新部分数据的问题，源表数据未变动但被误判为需要更新

### 影响范围:
- 数据比较逻辑：统一空值处理，避免 "-" 与 None 被误判为不同
- ETL性能：减少不必要的数据更新操作
- 数据一致性：提高数据比较的准确性

### 测试验证:
- [x] 单元测试通过（16个测试用例全部通过）
- [x] 集成测试通过（诊断脚本验证修复效果）
- [x] 性能测试通过（避免无效更新操作）
- [x] 文档更新完成（合并优化说明到主文档）

### 解决的问题:
1. **空值比较问题**: `"-"` 字符串与 `None` 值被错误判断为不同
2. **数据类型不匹配**: 字符串 `"100"` 与数值 `100` 比较逻辑问题
3. **重复更新**: 每次ETL执行都更新相同数据的问题

### 新增工具:
- `check_data_consistency.py`: 数据一致性检查工具（已保留）
- `diagnose_etl_issue.py`: ETL问题诊断工具（已保留）
- `test_fix.py`: 修复效果验证工具（已删除）

## 变动记录 - 2025-06-16 (文档清理)

### 变动类型: 一般
### 变动文件:
- `dw/transfrom/tasks/spider/jsyb/pipeline/files/江苏医保局爬虫数据清洗规则说明.md`: 清理过时信息，简化文档结构

### 变动原因:
清理文档中的过时信息，删除不再使用的配置和功能说明，使文档更加简洁准确

### 清理内容:
1. **删除过时的使用方法**: 移除指定操作模式和测试模式的命令行参数说明
2. **删除过时的配置文件位置**: 移除不再使用的配置文件路径说明
3. **简化字段映射说明**: 删除详细的字段映射表，改为简要说明
4. **更新注意事项**: 删除关于medical_field_mapping表的过时说明
5. **更新诊断工具**: 删除已删除脚本的引用

### 影响范围:
- 文档结构更加简洁
- 移除误导性的过时信息
- 保留核心功能说明

### 测试验证:
- [x] 文档结构检查完成
- [x] 核心功能说明保留
- [x] 过时信息清理完成

## 版本历史

### v1.3 (配置统一优化) - 2025-06-18
- **配置一致性修复**: 统一使用table_configs中的unique_fields配置
- **去重逻辑优化**: 使用CommonFieldProcessing.filter_and_deduplicate_data通用函数
- **医疗服务项目**: 从硬编码字段改为使用table_configs配置
- **医保药品**: 从硬编码字段改为使用table_configs配置
- **文档更新**: 更新清洗函数详解，记录配置统一的修改
- **代码维护性**: 提高配置的一致性和可维护性

### v1.2 (数据比较逻辑优化)
- **ETL重复更新修复**: 优化数据值比较逻辑，解决每次执行都更新相同数据的问题
- **空值标准化**: 统一处理各种空值表示形式
- **混合类型比较**: 优化字符串与数值的比较逻辑
- **精度控制**: 改进浮点数比较精度，避免精度误差导致的误判

### v1.1 (文档简化)
- **文档结构优化**: 简化文档结构，移除过时信息
- **配置说明更新**: 删除不再使用的配置文件说明
- **诊断工具更新**: 更新可用的诊断工具列表

### v1.0 (初始版本)
- 江苏医疗服务项目ETL框架
- 江苏医保药品ETL框架
- 字段映射和数据清洗逻辑
- additional_info JSON格式处理
- 地理位置自动标识
- 数据类型转换和验证
