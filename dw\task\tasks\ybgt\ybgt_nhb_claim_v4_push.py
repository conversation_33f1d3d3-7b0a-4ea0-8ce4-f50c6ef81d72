import json
import logging

import pandas as pd
import requests

from transfrom.tasks.ybgt.ybgt_nhb_claim_v4 import YbgtNhbClaimV4
from other.models import OtherYbStatisticNhb
logger = logging.getLogger(__name__)


def ybgt_nhb_claim_v4_push():
    try:
        source = YbgtNhbClaimV4()
        data_type = 'nhb_claim_v4'  # 产品类型，销售数据用insure、理赔数据用claim
        data = source.get_content(source.PRODUCT_CODE, source.END_DATETIME)
        body = {'type': data_type,
                'data': data}
        last_record = OtherYbStatisticNhb.objects.filter(type=data_type,product_code=source.PRODUCT_CODE).order_by('update_time').last()
        post_flg = last_record.is_post #是否推送标识
        if not post_flg:
            response = requests.post(
                url='https://gf.njybjyybz.org.cn:11020/nhb-medicare/prod/nhb_statistic',   #测试环境stage，正式环境prod
                headers={'Content-Type': 'application/json; charset=utf-8'},
                data=json.dumps(body, ensure_ascii=False).encode('utf8'),
                timeout=10
            )
            if response.status_code != 200:
                logger.error(f'nhbv4_ybgt_claim_push error with code {response.status_code}')
            else:
                # 如果当天推送过，会更新表中is_post字段为1，避免重复推送
                last_record.is_post = 1
                last_record.save()
            logger.info(f'nhbv4_ybgt_claim_push success with code {response.status_code}')
            return response.status_code
        else:
            logger.info(f'nhbv4_ybgt_claim_push 已经推送过')
            return f'nhbv4_ybgt_claim_push 已经推送过'
    except Exception as e:
        logger.error(f'nhbv4_ybgt_claim_push error with {e}')
        raise e
