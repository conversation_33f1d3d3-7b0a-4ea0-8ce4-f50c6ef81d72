# Create your views here.
import logging

from django.core.serializers import serialize
from rest_framework import status
from rest_framework.decorators import api_view
from rest_framework.response import Response

from task.tasks.insure.bz_hxb_insure_v1_datav import DataVBzHxbInsureV1
from task.tasks.insure.nhb_insure_v5_datav import DataVNhbInsureV5
from task.tasks.insure.rz_nxb_insure_v4_datav import DataVNxbInsureV4
from task.tasks.insure.gz_ghb_insure_v2_datav import DataVGhbInsureV2
from task.tasks.insure.gz_ghb_insure_v3_datav import DataVGhbInsureV3
from task.tasks.insure.bz_yhb_insure_v4_datav import DataVBzYhbInsureV4
from task.tasks.insure.dz_hmb_insure_v3_datav import DataVDzHmbInsureV3
from task.tasks.insure.bz_hxb_insure_v2_datav import DataVBzHxbInsureV2
from task.tasks.insure.nj_dha_insure_v1_datav import DataVNjDhaInsureV1
from task.tasks.insure.js_op_insure_v1_datav import DataVJsOpInsureV1
from task.tasks.insure.nj_nhb_insure_v1_datav import DataVNjNhbInsureV1
from transfrom.utils.utils import send_feishu_message

logger = logging.getLogger(__name__)


def handle_product_set(product_set_code, data_class):
    """
    处理 product_set_code 请求
    :param product_set_code: 产品集代码
    :param data_class: 数据类
    :return: Response 对象
    """
    try:
        data = data_class()
        dict_data = data.get_datav_data()  # 优先从缓存获取数据，不直接读数据库
        return Response(dict_data, status=status.HTTP_200_OK)
    except Exception as e:
        logger.error(e)
        send_feishu_message(f'datav_hxx_insure 获取数据报错：{e}')
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
def datav_hxx_insure(request):
    """
    获取护学保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)

    if product_set_code == 'binzhou_student':
        return handle_product_set(product_set_code, DataVBzHxbInsureV1)
    elif product_set_code == 'binzhou_studentV2':
        return handle_product_set(product_set_code, DataVBzHxbInsureV2)
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)



@api_view(['GET'])
def datav_nhb_insure(request):
    """
    获取宁惠保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='ninghuibaoV5':
        data = DataVNhbInsureV5()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_nhb_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)



@api_view(['GET'])
def datav_nxb_insure(request):
    """
    获取日照暖心保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='rizhao_nxbV4':
        data = DataVNxbInsureV4()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_nxb_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def datav_ghb_insure(request):
    """
    获取贵州医惠保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='guihuibaoV2':
        data = DataVGhbInsureV2()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_ghb_insure_v2 获取数据报错：{e}')
    elif product_set_code=='guihuibaoV3':
        data = DataVGhbInsureV3()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_ghb_insure_v3 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)



@api_view(['GET'])
def datav_bzyhb_insure(request):
    """
    获取滨州医惠保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='binzhou_yhbV4':
        data = DataVBzYhbInsureV4()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_bzyhb_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def datav_dzhmb_insure(request):
    """
    获取德州惠民保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='dezhou_hmbV3':
        data = DataVDzHmbInsureV3()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_bzyhb_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)



@api_view(['GET'])
def datav_njdha_insure(request):
    """
    获取住院津贴数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='disease_hospital_allowance':
        data = DataVNjDhaInsureV1()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_njdha_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def datav_jsop_insure(request):
    """
    获取江苏门诊保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='jiangsu_outpatientV1':
        data = DataVJsOpInsureV1()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_jsop_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
def datav_njnhb_insure(request):
    """
    获取南京宁护保数据
    """
    # 这里添加您的API逻辑
    product_set_code = request.query_params.get('product_set_code', None)
    # 后续有增加，从这里添加即可
    if product_set_code=='ninghubaoV1':
        data = DataVNjNhbInsureV1()
        try:
            dict_data = data.get_datav_data() # 优先从缓存获取数据，不直接读数据库
            return Response(dict_data, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(e)
            send_feishu_message(f'datav_njnhb_insure 获取数据报错：{e}')
    else:
        return Response({'error': 'Missing product_set_code parameter'}, status=status.HTTP_400_BAD_REQUEST)