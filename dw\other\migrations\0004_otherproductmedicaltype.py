# Generated by Django 3.2.12 on 2024-07-24 15:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0003_alter_otherproductresponse_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='OtherProductMedicalType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='名称')),
                ('medicare_type', models.CharField(blank=True, max_length=200, null=True, verbose_name='医保类型')),
                ('preson_num', models.IntegerField(blank=True, null=True, verbose_name='参保人数')),
            ],
            options={
                'verbose_name': '销售-投保人员医保类型',
                'verbose_name_plural': '销售-投保人员医保类型',
                'db_table': 'other_product_medical_type',
            },
        ),
    ]
