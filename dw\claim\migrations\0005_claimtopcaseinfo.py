# Generated by Django 3.2.12 on 2025-03-04 10:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('claim', '0004_claimliabilitypay_claimpreexistingcondition'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClaimTopCaseInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('name', models.Char<PERSON>ield(blank=True, max_length=32, null=True, verbose_name='姓名')),
                ('medicare_area', models.Char<PERSON><PERSON>(blank=True, max_length=32, null=True, verbose_name='医保参保地')),
                ('past_symptom', models.CharField(blank=True, max_length=32, null=True, verbose_name='是否既往症')),
                ('age', models.IntegerField(blank=True, null=True, verbose_name='年龄')),
                ('gender', models.CharField(blank=True, max_length=32, null=True, verbose_name='性别')),
                ('disease', models.CharField(blank=True, max_length=256, null=True, verbose_name='疾病')),
                ('pay_amount', models.DecimalField(blank=True, decimal_places=4, max_digits=20, null=True, verbose_name='赔付金额')),
                ('claim_type', models.CharField(blank=True, max_length=32, null=True, verbose_name='申请方式')),
            ],
            options={
                'verbose_name': '理赔-理赔案件TOP情况',
                'verbose_name_plural': '理赔-理赔案件TOP情况',
                'db_table': 'claim_top_case_info',
            },
        ),
    ]
