# Generated by Django 4.2.1 on 2025-06-20 09:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("medical", "0025_rename_category_name_medicaldrugentity_type_name_and_more"),
    ]

    operations = [
        migrations.RenameField(
            model_name="medicaldrugbase",
            old_name="category",
            new_name="category_name",
        ),
        migrations.RenameField(
            model_name="medicalnationalnegotiateddrugproviders",
            old_name="type",
            new_name="type_name",
        ),
        migrations.AlterField(
            model_name="medicaldesignatedproviders",
            name="category",
            field=models.Char<PERSON>ield(
                blank=True,
                db_comment="机构类型",
                max_length=32,
                null=True,
                verbose_name="机构类型",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldesignatedproviders",
            name="category_name",
            field=models.CharField(
                blank=True,
                db_comment="机构类型名称",
                max_length=64,
                null=True,
                verbose_name="机构类型名称",
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name="medicaldesignatedproviders",
            name="type",
            field=models.Char<PERSON>ield(
                blank=True,
                db_comment="机构分类",
                max_length=255,
                null=True,
                verbose_name="机构分类",
            ),
        ),
        migrations.AlterField(
            model_name="medicaldesignatedproviders",
            name="type_name",
            field=models.CharField(
                blank=True,
                db_comment="机构分类名称",
                max_length=255,
                null=True,
                verbose_name="机构分类名称",
            ),
        ),
    ]
