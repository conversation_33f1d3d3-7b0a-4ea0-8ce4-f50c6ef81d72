import datetime
from pprint import pprint

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from pandasql import sqldf
from plotly.subplots import make_subplots
from utils.st import query_sql, text_write,empty_line
from utils.utils import number_to_chinese
from pyecharts import options as opts
from pyecharts.charts import Sankey
from streamlit_echarts import st_pyecharts
import warnings
import plotly.figure_factory as ff
import matplotlib.pyplot as plt

import sys
import venn

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')
# sale_start_date = datetime.date(2024, 9, 12)

def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        concat(
		LEFT ( ps.NAME, 5 ),
		'-',
	RIGHT ( ps.NAME,( LENGTH( ps.NAME )- 15 )/ 3 )) version,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        # and ps.code not in ('ninghuibaoV1','ninghuibaoV2','ninghuibaoV3')
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)

    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code



@st.cache_resource(ttl=3600*24*30,show_spinner=False)
def db_get_history_users(product_set_code_list):
    """
    只取主险用户，历史用户，因为历史不动，所以每月缓存一次，而且全部用户共享
    """
    sql = '''

        SELECT client.credential_number AS client_id,
            ps.code product_id
        FROM `order` o
                 JOIN order_item oi ON oi.order_id = o.id
                 JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
                 JOIN product_set ps ON ps.id = o.product_set_id and ps.code in ({product_set_code_list})
                 JOIN product p ON p.id = oi.product_id and p.main = 1
                 JOIN user client ON oic.client_id = client.id
        WHERE 
          o.order_status in ('PAID_SUCCESS', 'WAIT_DEDUCT')
        '''

    df = CONNECTOR_JKX.query(sql.format(product_set_code_list=product_set_code_list),ttl=3600*24,show_spinner=False)
    # 增加团单用户，因为数据库中不完整，团单表中很多历史又没有版本，无法直接放入到数据库中
    sql_group = '''
    select credential_number client_id,product_set_code product_id from insure_group where product_set_code in ({product_set_code_list})
    '''
    df_group = CONNECTOR_DW.query(sql_group.format(product_set_code_list=product_set_code_list),ttl=3600*24,show_spinner=False)
    # 合并数据，并去重
    df = pd.concat([df, df_group], axis=0)
    df.drop_duplicates(inplace=True)
    df.reset_index(drop=True, inplace=True)
    return df


@st.cache_data(ttl=600,show_spinner=False)
def db_get_users(product_set_code_list):
    """
    只取主险用户，本期用户
    """
    sql = '''

        SELECT client.credential_number AS client_id,
            ps.code product_id
        FROM `order` o
                 JOIN order_item oi ON oi.order_id = o.id
                 JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
                 JOIN product_set ps ON ps.id = o.product_set_id and ps.code in ({product_set_code_list})
                 JOIN product p ON p.id = oi.product_id and p.main = 1
                 JOIN user client ON oic.client_id = client.id
        WHERE 
          o.order_status in ('PAID_SUCCESS', 'WAIT_DEDUCT')
        '''

    df = CONNECTOR_JKX.query(sql.format(product_set_code_list=product_set_code_list),ttl=600,show_spinner=False)
    return df


def renewal_person(product_set_code_list,history_product_set_code_list):
    text_write("参保人数来源")
    with st.spinner('数据查询中...'):
        colors = [
            "#67001f",
            "#b2182b",
            "#d6604d",
            "#f4a582",
            "#fddbc7",
            "#d1e5f0",
            "#92c5de",
            "#4393c3",
            "#2166ac",
            "#053061",
        ]

        # 定义产品名称映射
        product_names = {
            'ninghuibaoV1': '一期',
            'ninghuibaoV2': '二期',
            'ninghuibaoV3': '三期',
            'ninghuibaoV4': '四期',
            'ninghuibaoV5': '五期',
            'ninghuibaoV6': '六期',
            'ninghuibaoV7': '七期',
            'ninghuibaoV8': '八期',
            'ninghuibaoV9': '九期',
            'ninghuibaoV10': '十期'
        }
        df_users = db_get_users(product_set_code_list)
        df_history = db_get_history_users(history_product_set_code_list)
        df = pd.concat([df_users, df_history], axis=0)
        df.reset_index(drop=True, inplace=True)
        # 分组并获取每期产品的客户 ID 集合
        grouped = df.groupby('product_id')['client_id'].apply(set).reset_index(name='clients')

        # 获取所有产品 ID
        products = grouped['product_id'].unique()

        # 计算每期产品的交集数量，并排除中间期的交集部分
        intersections = {}
        for i in range(len(products)):
            for j in range(i + 1, len(products)):
                p1 = products[i]
                p2 = products[j]
                clients1 = grouped[grouped['product_id'] == p1]['clients'].iloc[0]
                clients2 = grouped[grouped['product_id'] == p2]['clients'].iloc[0]
                intersection = clients1 & clients2

                # 排除中间期的交集部分
                if i < j - 1:
                    middle_clients = set()
                    for k in range(i + 1, j):
                        middle_clients |= grouped[grouped['product_id'] == products[k]]['clients'].iloc[0]
                    intersection -= middle_clients

                intersections[(p1, p2)] = len(intersection)

        # 计算每期新增用户的数量
        new_clients = {}
        for product in products:
            clients = grouped[grouped['product_id'] == product]['clients'].iloc[0]
            new_clients[f"{product_names[product]}新增"] = len(clients)
        print(new_clients)
        # 生成 links 数据结构
        links = []

        # 添加每期新增用户的数据
        for product, value in new_clients.items():
            links.append({"source": product, "target": product.replace("新增", ""), "value": value})

        # 添加每期产品之间的交集数据
        for (p1, p2), value in intersections.items():
            links.append({"source": product_names[p1], "target": product_names[p2], "value": value})

        df_links = pd.DataFrame(links)
        df_links_total = df_links[df_links['source'].str.contains('新增')]
        df_links_other = df_links[~df_links['source'].str.contains('新增')]
        df_links_other_new = df_links_other.groupby(['target']).agg({'value': 'sum'}).reset_index().rename(
            columns={'value': 'value_other'})
        df_links_total = pd.merge(df_links_total, df_links_other_new, on='target', how='left').fillna(0)
        df_links_total['value'] = df_links_total['value'] - df_links_total['value_other']
        df_links_total = df_links_total[['source', 'target', 'value']]
        df_links_new = pd.concat([df_links_total, df_links_other], axis=0)
        df_links_new.sort_values(by=['target', 'source'], ascending=False, inplace=True)
        links_new = df_links_new.to_dict('records')
        pprint(links_new)
        # 提取 source 和 target 列的唯一值
        df_nodes = pd.concat([df_links_new['source'], df_links_new['target']], axis=0).drop_duplicates().reset_index(
            drop=True)
        list_nodes = df_nodes.values.tolist()

        # 生成 nodes 数据结构，节点名称
        nodes = [{"name": node} for node in list_nodes]

        sankey = Sankey().set_colors(colors)

        sankey.add("",
                   nodes,
                   links_new,
                   linestyle_opt=opts.LineStyleOpts(opacity=0.2, curve=0.5, color="source"),
                   label_opts=opts.LabelOpts(position="right"),
                   tooltip_opts=opts.TooltipOpts(trigger_on="mousemove"),
                   emphasis_opts=opts.EmphasisOpts(focus="adjacency"),  # 显示上下游情况
                   )
        st_pyecharts(sankey, height="430px")

def get_full_range_date(df,sale_start_date,sale_end_date):
    """
    获取完整的日期范围
    """
    # 如果sale_start_date 是字符型，转换为日期类型
    if isinstance(sale_start_date, str):
        sale_start_date = datetime.datetime.strptime(sale_start_date, '%Y-%m-%d').date()
    if isinstance(sale_end_date, str):
        sale_end_date = datetime.datetime.strptime(sale_end_date, '%Y-%m-%d').date()
    # 获取最小日期
    min_date = df['date'].min()
    # 如果 min_date 是空的，使用 sale_start_date 作为默认值
    min_date = min_date if pd.notnull(min_date) else sale_start_date
    # 如果 min_date 是 pandas.Timestamp 类型，转换为 datetime.date
    if isinstance(min_date, pd.Timestamp):
        min_date = min_date.to_pydatetime().date()
    # 获取实际数据与销售起始日中最小的作为开始日期
    full_date_range = pd.date_range(start=min(min_date, sale_start_date),
                                    end=sale_end_date)
    return full_date_range


@st.cache_resource(ttl=3600*24*30,show_spinner=False)
def db_get_prev_users(product_set_code_prev):
    """
    获取上期用户
    :param product_set_code_prev: 上期产品集编码
    :return:
    """
    sql = '''
      select distinct client.credential_number
                                               FROM `order` o
                 JOIN order_item oi ON oi.order_id = o.id
                 JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
                 JOIN product_set ps ON ps.id = o.product_set_id and ps.code = '{product_set_code_prev}'
                 JOIN product p ON p.id = oi.product_id and p.main = 1
                 JOIN user client ON oic.client_id = client.id
          where o.order_status in ('PAID_SUCCESS')
        '''

    df = CONNECTOR_JKX.query(sql.format(product_set_code_prev=product_set_code_prev),ttl=3600*24,show_spinner=False)
    # 增加团单用户，因为数据库中不完整，团单表中很多历史又没有版本，无法直接放入到数据库中
    sql_group = '''
      select distinct credential_number from insure_group where product_set_code = '{product_set_code_prev}'
      '''
    df_group = CONNECTOR_DW.query(sql_group.format(product_set_code_prev=product_set_code_prev),ttl=3600*24,show_spinner=False)
    df = pd.concat([df, df_group], axis=0)
    df.drop_duplicates(inplace=True)
    df.reset_index(drop=True, inplace=True)
    return df


def db_get_daily_renew(product_set_code, sale_start_date,sale_end_date,product_set_code_prev):
    sale_start_datetime = sale_start_date+' 00:00:00'
    sale_end_datetime = sale_end_date+' 23:59:59'
    sql = '''

      SELECT distinct  date(o.create_time) `date`, client.credential_number
    FROM `order` o
             JOIN order_item oi ON oi.order_id = o.id
             JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
             JOIN product_set ps ON ps.id = o.product_set_id and ps.code = '{product_set_code}'
             JOIN product p ON p.id = oi.product_id and p.main = 1
             JOIN user client ON oic.client_id = client.id
    WHERE o.order_status in ('PAID_SUCCESS', 'WAIT_DEDUCT')
    and o.create_time >= '{sale_start_datetime}'
    and o.create_time <= '{sale_end_datetime}'
    '''

    df_user = CONNECTOR_JKX.query(sql.format(product_set_code= product_set_code,
                                             sale_start_datetime=sale_start_datetime,
                                             sale_end_datetime=sale_end_datetime),ttl=600,show_spinner=False)
    df_prev_user = db_get_prev_users(product_set_code_prev)
    # 合并两个DataFrame
    df_merged = pd.merge(df_user, df_prev_user, on='credential_number', how='inner')

    # 统计每日续保人数
    df_renew = df_merged.groupby('date').size().reset_index(name='renew_count')

    # 获取实际数据与销售起始日中最小的作为开始日期
    full_date_range = get_full_range_date(df_renew,sale_start_date,sale_end_date)
    # 填充缺失值
    df_renew = (
        df_renew.set_index('date')
        .reindex(full_date_range)
        .fillna(0)
        .reset_index()
        .rename(columns={'index': 'date'})
    )

    sql = '''
    SELECT date(o.create_time) as date,
           count(1) as count
    FROM `order` o
             JOIN order_item oi ON oi.order_id = o.id
             JOIN order_item_client oic ON oic.order_item_id = oi.id AND oic.is_return = 0
             JOIN product_set ps ON ps.id = o.product_set_id and ps.code = '{product_set_code}'
             JOIN product p ON p.id = oi.product_id and p.main = 1
             JOIN user client ON oic.client_id = client.id
    WHERE o.order_status in ('PAID_SUCCESS', 'WAIT_DEDUCT')
    and o.create_time >= '{sale_start_datetime}'
    and o.create_time <= '{sale_end_datetime}'
    group by date
    '''
    df_v4 = CONNECTOR_JKX.query(sql.format(sale_start_datetime=sale_start_datetime,
                                           sale_end_datetime=sale_end_datetime,
                                           product_set_code=product_set_code),ttl=600,show_spinner=False)
    # 填充缺失值
    df_v4 = (
        df_v4.set_index('date')
        .reindex(full_date_range)
        .fillna(0)
        .reset_index()
        .rename(columns={'index': 'date'})
    )

    df = pd.merge(df_renew, df_v4, on='date', how='left')
    df['renew_rate'] = df.apply(lambda x:round(x['renew_count'] / x['count']*100,2) if x['count'] != 0 else 0, axis=1)
    df['renew_rate'].fillna(0, inplace=True)
    return df


def query_indicator_data(name):
    """
    查询sql
    :param name: 指标名称,需要用引号进行包裹
    """
    CONNECTOR_DW = st.connection('dw', type='sql')
    sql = '''
    select m.`name`,d.publish_time,d.end_time,d.`value` from public_indicator_main m join public_indicator_data d 
    on m.code = d.code 
    where m.name in ({name})
    order by d.end_time desc
    '''
    record = CONNECTOR_DW.query(sql.format(name=name), ttl=600,show_spinner=False)
    return record





def get_product():
    sql = """
    SELECT distinct ps.name product_name FROM `product` p join product_set ps on p.product_set_id = ps.id
where left(ps.name,2) ='南京'
order by p.valid_from
    """

    df_product = CONNECTOR_JKX.query(sql, ttl=600,show_spinner=False)
    df_product["product_name_shift"] = df_product['product_name'].shift(-1)
    return df_product


def renew_pie(product_set_code_list,history_product_set_code_list):
    text_write("参保人数分析")

    # 备份原始的 history_product_set_code_list
    original_history_list = history_product_set_code_list.copy()

    # 如果 history_product_set_code_list中有'ninghuibaoV1' 则删除
    if 'ninghuibaoV1' in history_product_set_code_list:
        history_product_set_code_list.remove('ninghuibaoV1')

    # 计算总的集合数量（当前产品 + 历史产品）
    total_sets = len(product_set_code_list) + len(history_product_set_code_list)

    # 如果集合数量超过6，需要减少历史产品数量
    if total_sets > 6:
        max_history_count = 6 - len(product_set_code_list)
        if max_history_count > 0:
            # 提取版本号并排序，保留最新的几个版本
            version_info = []
            for code in history_product_set_code_list:
                if code.startswith('ninghuibaoV'):
                    try:
                        version_num = int(code.replace('ninghuibaoV', ''))
                        version_info.append((version_num, code))
                    except ValueError:
                        # 如果无法提取版本号，保留原始代码
                        version_info.append((0, code))
                else:
                    version_info.append((0, code))

            # 按版本号降序排序，取最新的几个
            version_info.sort(key=lambda x: x[0], reverse=True)
            history_product_set_code_list = [code for _, code in version_info[:max_history_count]]
        else:
            history_product_set_code_list = []

    # 重新计算总集合数量
    total_sets = len(product_set_code_list) + len(history_product_set_code_list)

    # 检查集合数量是否在有效范围内
    if total_sets < 2:
        st.warning("产品数量不足2个，无法绘制韦恩图")
        return
    elif total_sets > 6:
        st.warning("产品数量超过6个，韦恩图显示可能不够清晰")
        return

    # 数量验证通过后，开始查询数据
    with st.spinner('数据查询中...'):
        product_set_code = "'" + "','".join(product_set_code_list) + "'"
        df_users = db_get_users(product_set_code)

        # 构造查询字符串
        if history_product_set_code_list:
            history_product_set_code_str = "','".join(history_product_set_code_list)
            history_product_set_code_str = "'"+history_product_set_code_str+"'"
            df_history = db_get_history_users(history_product_set_code_str)
            df = pd.concat([df_users, df_history], axis=0)
        else:
            df = df_users

        df.reset_index(drop=True, inplace=True)

        ps_dfs = df.groupby('product_id')
        codes = []
        sets = []
        for code, df_group in ps_dfs:
            codes.append(code)
            sets.append(set(df_group['client_id'].tolist()))

        # 根据集合数量选择合适的韦恩图函数
        try:
            labels = venn.get_labels(sets, fill=['number'])
            cols = st.columns([ 0.4, 0.4])

            if len(sets) == 2:
                fig, ax = venn.venn2(labels, names=codes)
            elif len(sets) == 3:
                fig, ax = venn.venn3(labels, names=codes)
            elif len(sets) == 4:
                fig, ax = venn.venn4(labels, names=codes)
            elif len(sets) == 5:
                fig, ax = venn.venn5(labels, names=codes)
            elif len(sets) == 6:
                fig, ax = venn.venn6(labels, names=codes)
            else:
                return

            with cols[0]:
                st.pyplot(fig)


            # 产品代码转中文期数的函数
            def code_to_chinese_period(code):
                """将产品代码转换为中文期数"""
                if code.startswith('ninghuibaoV'):
                    try:
                        version_num = int(code.replace('ninghuibaoV', ''))
                        return number_to_chinese(version_num) + '期'
                    except ValueError:
                        return code
                return code

            # 创建交集关系数据
            intersection_data = []
            from itertools import combinations

            # 1. 两两产品交集
            for combo in combinations(range(len(codes)), 2):
                i, j = combo
                intersection = sets[i] & sets[j]

                chinese_i = code_to_chinese_period(codes[i])
                chinese_j = code_to_chinese_period(codes[j])

                intersection_data.append({
                    '关系类型': '两两交集',
                    '产品组合': f"{chinese_i} ∩ {chinese_j}",
                    '人数': len(intersection)
                })

            # 2. 如果产品数量>=3，添加三产品交集
            if len(codes) >= 3:
                for combo in combinations(range(len(codes)), 3):
                    i, j, k = combo
                    intersection = sets[i] & sets[j] & sets[k]

                    chinese_i = code_to_chinese_period(codes[i])
                    chinese_j = code_to_chinese_period(codes[j])
                    chinese_k = code_to_chinese_period(codes[k])

                    intersection_data.append({
                        '关系类型': '三产品交集',
                        '产品组合': f"{chinese_i} ∩ {chinese_j} ∩ {chinese_k}",
                        '人数': len(intersection)
                    })

            # 3. 四产品交集（如果产品数量>=4）
            if len(codes) >= 4:
                for combo in combinations(range(len(codes)), 4):
                    intersection = sets[combo[0]]
                    for idx in combo[1:]:
                        intersection &= sets[idx]

                    chinese_names = [code_to_chinese_period(codes[idx]) for idx in combo]

                    intersection_data.append({
                        '关系类型': '四产品交集',
                        '产品组合': ' ∩ '.join(chinese_names),
                        '人数': len(intersection)
                    })

            # 4. 五产品交集（如果产品数量>=5）
            if len(codes) >= 5:
                for combo in combinations(range(len(codes)), 5):
                    intersection = sets[combo[0]]
                    for idx in combo[1:]:
                        intersection &= sets[idx]

                    chinese_names = [code_to_chinese_period(codes[idx]) for idx in combo]

                    intersection_data.append({
                        '关系类型': '五产品交集',
                        '产品组合': ' ∩ '.join(chinese_names),
                        '人数': len(intersection)
                    })

            # 5. 全产品交集（如果产品数量>=6）
            if len(codes) >= 6:
                all_intersection = sets[0]
                for s in sets[1:]:
                    all_intersection &= s

                chinese_names = [code_to_chinese_period(code) for code in codes]

                intersection_data.append({
                    '关系类型': '全产品交集',
                    '产品组合': ' ∩ '.join(chinese_names),
                    '人数': len(all_intersection)
                })

            # 转换为DataFrame并显示
            if intersection_data:
                df_intersection = pd.DataFrame(intersection_data)

                # 按关系类型和人数排序
                type_order = ['两两交集', '三产品交集', '四产品交集', '五产品交集', '全产品交集']
                df_intersection['关系类型_排序'] = df_intersection['关系类型'].apply(
                    lambda x: type_order.index(x) if x in type_order else 999
                )
                df_intersection = df_intersection.sort_values(['关系类型_排序', '人数'], ascending=[True, False])
                df_intersection = df_intersection.drop('关系类型_排序', axis=1)

                # 添加样式
                with cols[1]:
                    empty_line(3)
                    st.dataframe(
                        df_intersection,
                        use_container_width=True,
                        hide_index=True,
                        column_config={
                            "关系类型": st.column_config.TextColumn("关系类型", width="small"),
                            "产品组合": st.column_config.TextColumn("产品组合", width="large"),
                            "人数": st.column_config.NumberColumn("人数", format="%d")
                        }
                    )
            else:
                st.info("暂无交集数据可显示")

        except Exception as e:
            st.error(f"绘制失败: {str(e)}")
            # 显示数据统计作为备选方案
            st.write("### 数据统计信息")
            for i, (code, client_set) in enumerate(zip(codes, sets)):
                st.write(f"**{code}**: {len(client_set)} 人")


            


def renew(df_product):
    text_write('各期续保占比')
    with st.spinner('数据查询中...'):
        product_info_temp = df_product.copy()
        periods_list = product_info_temp['product_set_name'].apply(lambda x: x.replace('南京宁惠保','')).values.tolist()
        percent_periods_list = ['南京宁惠保-' + str(i) + '-续保占比-当期值' for i in periods_list]
        indicator_name = "','".join(percent_periods_list)
        indicator_name = "'" + indicator_name + "'"

        data = query_indicator_data(indicator_name)
        data['product_set_name'] = data['name'].apply(
            lambda x: x.split('-')[0] + x.split('-')[1])
        # 取截止日期end_time最大的一条
        data = data.sort_values(by='end_time', ascending=False).groupby(
            ['product_set_name']).first().reset_index()
        data['renewal_percent'] = data['value']


        product_info_temp['renewal_person'] = np.nan
        data = pd.merge(product_info_temp, data, on='product_set_name', how='left')
        data = data[['product_set_name', 'renewal_percent']].rename(
            columns={'product_set_name': '项目名', 'renewal_percent': '续保占比(%)'})
        data['续保占比(%)'] = data['续保占比(%)'].apply(lambda x: '{:.2%}'.format(x / 100))
        data = data[data['项目名'] != '南京宁惠保一期']
        # print(data)
        # 创建并配置直方图
        fig = px.bar(data, x='项目名', y='续保占比(%)', color='项目名', text_auto=True)
        fig.update_traces(textposition="outside")
        fig.update_layout(showlegend=False, xaxis_title=None, yaxis_title=None, bargap=0.5)  # 去除图例

        # 将饼图渲染到Streamlit应用中
        st.plotly_chart(fig, use_container_width=True)


def renewal_daily(product_set_code,sale_start_date,sale_end_date,product_set_code_prev):
    text_write('每日续保人数占比')

    with st.spinner('数据查询中...'):
        df_renew = db_get_daily_renew(product_set_code=product_set_code, sale_start_date = sale_start_date,sale_end_date=sale_end_date,product_set_code_prev=product_set_code_prev)
        df_renew.rename(columns={'date': '日期', 'renew_rate': '当日续保率(%)','count':'单量'}, inplace=True)

        # 创建双坐标轴图表
        fig = make_subplots(specs=[[{"secondary_y": True}]])

        # 添加续保率线图（主坐标轴）
        fig.add_trace(
            go.Scatter(
                x=df_renew['日期'],
                y=df_renew['当日续保率(%)'],
                mode='lines+markers',
                name='当日续保率(%)',
                line=dict(color='#1f77b4', width=2),
                marker=dict(size=6)
            ),
            secondary_y=False,
        )

        # 添加单量柱状图（副坐标轴）
        fig.add_trace(
            go.Bar(
                x=df_renew['日期'],
                y=df_renew['单量'],
                name='单量',
                opacity=0.6,
                marker=dict(color='#ff7f0e')
            ),
            secondary_y=True,
        )

        # 设置主坐标轴标题
        fig.update_yaxes(title_text="当日续保率(%)", secondary_y=False)

        # 设置副坐标轴标题和格式
        fig.update_yaxes(title_text="单量", secondary_y=True, tickformat=",d")

        # 更新布局
        fig.update_layout(
            showlegend=True,
            xaxis_title=None,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="center",
                x=0.5
            )
        )
        fig.update_xaxes(tickformat="%Y-%m-%d")

        st.plotly_chart(fig, use_container_width=True)


def main():
    st.subheader("续保分析")
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=product_info[product_info['product_set_code'] != 'ninghuibaoV1']['product_set_name'],placeholder="请选择产品")
    st.divider()
    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[
        0]
    product_set_code_prev = \
        product_info[product_info['product_set_name'] == product_set_name]['prev_product_set_code'].values[
            0]
    sale_start_datetime = product_info[product_info['product_set_name'] == product_set_name]['sale_start_time'].values[
        0]
    sale_start_date = pd.to_datetime(sale_start_datetime).strftime('%Y-%m-%d')
    sale_end_date = pd.to_datetime(product_info[product_info['product_set_name'] == product_set_name]['sale_end_time'].values[
        0]).strftime('%Y-%m-%d')
    # 根据筛选进行处理，如果选择4期，则历史就是销售期小雨4期
    product_set_code_list ="'"+"','".join([product_set_code])+"'"
    history_product_set_code = product_info[product_info['sale_start_time'] <sale_start_datetime]['product_set_code'].values.tolist()
    history_product_set_code_list = "'"+"','".join(history_product_set_code)+"'"

    # 包含本期product_info
    df_product = product_info[product_info['sale_start_time'] <=sale_start_datetime]
    df_product.sort_values(by='sale_start_time', inplace=True)

    renewal_person(product_set_code_list,history_product_set_code_list)
    renew_pie([product_set_code],history_product_set_code)

    renew(df_product)

    renewal_daily(product_set_code,sale_start_date,sale_end_date,product_set_code_prev)


if __name__ == "__main__":
    main()
