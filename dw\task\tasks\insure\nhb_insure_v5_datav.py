import datetime
import logging
import re
import warnings
from pprint import pprint
from decimal import Decimal
from pandasql import sqldf

import idna
import numpy as np
import pandas as pd
import pymysql

from dw import settings
from insure.models import InsureArea, InsureAgeSex, InsureOnline, InsureAgent
from public.models import PublicStatistics, PublicAreaBaseInsure
from transfrom.utils.utils import query_sql, sum_or_combine
from task.utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class DataVNhbInsureV5(CacheManager):
    def __init__(self):
        super().__init__()
        self.DB = settings.DATABASES['default']  # dw数据数据库
        self.product_set_code = 'ninghuibaoV5'  # 产品集编码
        self.previous_product_set_code = 'ninghuibaoV4'  # 上一期的产品集编码
        self.version = '南京宁惠保-五期'  # 产品期，用于指标名称标准化
        self.type = 'insure'  # 统计大类
        self.sale_start_date = '2024-09-12'
        self.sale_start_time = '2024-09-12 16:00:00'
        self.sale_start_time_prev = '2023-09-18 00:00:00'
        self.sale_start_time_zero = '2024-09-12 00:00:00'  # 取当日，需要从0点开始
        self.sale_end_date_prev = '2023-12-31'
        # self.end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.end_datetime = '2025-01-24 23:59:59'
        # 24小时前的时间
        self.start_datetime = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')

        self.today = datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
        # 昨天的时间
        self.yesterday = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        # 前天的时间
        self.two_days_ago = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(days=2)).strftime('%Y-%m-%d')
        # 15日前的时间
        self.fifteen_days_ago = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')
        self.fourteen_days_ago = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(days=14)).strftime('%Y-%m-%d %H:%M:%S')
        self.end_time = self.today + ' 00:00:00'
        self.yesterday_time = self.yesterday + ' 00:00:00'
        self.two_days_ago_time = self.two_days_ago + ' 00:00:00'
        self.special_count= 109427 # 预计节后会扣除额单量，基础版23769单，升级版85658单

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def adjust_data(self, df, sale_end_date_prev):
        """
        调整数据，将12-31（销售截止）之后的数据纳入之前的数据
        :param df: 需要处理的数据
        :return:
        """
        df_out_period = df[df['date'] > sale_end_date_prev]
        df_in_period = df[df['date'] <= sale_end_date_prev]
        if df_out_period.empty:
            df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
            df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
            return df_in_period
        else:
            # 计算销售期之前的销量每期的占比，再将销售期外的销量加到销售期内
            df_in_period['sale_ratio'] = df_in_period['value'] / df_in_period['value'].sum()
            df_in_period['sale_ratio_cumsum'] = df_in_period['sale_ratio'].cumsum()
            return df_in_period

    def online_adjust_data(self, product_set_code_prev, sale_end_date_prev):
        """
        调整线上数据，将销售截止之后的数据纳入之前的数据，只计算个单。
        """
        # 获取上期的线上数据
        try:
            sql = query_sql('SQL_DW_INDICATOR_DATA').format(
                product_set_code=product_set_code_prev,
                statistical_type='当期值', unit='单',
                freq='日', start_datetime='2000-01-01',
                end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            with self.get_connection() as conn:
                df = pd.read_sql(
                    sql, conn)
                df.rename(columns={'end_time': 'date'}, inplace=True)

                # 优化：只筛选一次，并使用映射来分配销售渠道
                channels = {
                    # '我的南京': '-销量-线上-我的南京-当期值',
                    # '公众号': '-销量-线上-公众号-当期值',
                    # '支付宝': '-销量-线上-支付宝-当期值',
                    '合计': '-销量-线上-当期值'
                }

                df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
                for channel_name, filter_str in channels.items():
                    channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
                    channel_df['name'] = channel_name  # 添加销售渠道名称列
                    adjust_channel_df = self.adjust_data(channel_df, sale_end_date_prev)  # 调整数据
                    df_onlines = pd.concat([df_onlines, adjust_channel_df])  # 拼接数据
                df_onlines.sort_values(by=['name', 'date'], inplace=True)
                df_onlines.reset_index(drop=True, inplace=True)
                # if product_set_code_prev == 'ninghuibaoV4':
                #     # 日期对齐，往前面移动6天
                #     df_onlines['date'] = pd.to_datetime(df_onlines['date']) - datetime.timedelta(days=6)
                df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
                return df_onlines
        except Exception as e:
            print(f"Error occurred while processing online data: {e}")

    def offline_adjust_data(self, product_set_code_prev, sale_end_date_prev):
        """
        调整线下数据，将12-31（销售截止）之后的数据纳入之前的数据，只计算个单
        :param df: 需要处理的数据
        :return:
        """
        # 提取SQL查询逻辑
        sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=product_set_code_prev,
            statistical_type='当期值',
            unit='单',
            freq='日',
            start_datetime='2000-01-01',
            end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

        try:
            # 使用上下文管理器确保连接正确关闭
            with self.get_connection() as conn:
                df = pd.read_sql(sql_query, conn)
                df.rename(columns={'end_time': 'date'}, inplace=True)

                # 定义公司名称列表
                company_names = [
                    # ('-销量-线下-个单-中国人保-当期值', '中国人保'),
                    # ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
                    # ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
                    # ('-销量-线下-个单-中华联合-当期值', '中华联合'),
                    # ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
                    # ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
                    # ('-销量-线下-个单-太保产险-当期值', '太保产险'),
                    # ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
                    # ('-销量-线下-个单-中银保险-当期值', '中银保险'),
                    ('-销量-线下-个单-当期值', '合计')
                ]

                # 循环处理每个公司数据
                dfs = []
                for pattern, name in company_names:
                    filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
                    adjusted_df = self.adjust_data(filtered_df, sale_end_date_prev)
                    adjusted_df['name'] = name
                    dfs.append(adjusted_df)

                # 合并所有公司数据
                df_offlines = pd.concat(dfs)
                df_offlines.sort_values(by=['name', 'date'], inplace=True)
                df_offlines.reset_index(drop=True, inplace=True)
                # if product_set_code_prev == 'ninghuibaoV4':
                #     # 日期对齐，往前面移动6天
                #     df_offlines['date'] = pd.to_datetime(df_offlines['date']) - datetime.timedelta(days=6)
                df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
                return df_offlines

        except Exception as e:
            # 异常处理
            print(f"Error occurred while processing data: {e}")

    def online_daily_target(self, product_set_code, sale_start_date, product_set_code_prev, sale_end_date_prev):
        """
        计算线上每日目标
        """
        sql = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=product_set_code,
            statistical_type='当期值', unit='单',
            freq='日', start_datetime=sale_start_date,
            end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        with self.get_connection() as conn:
            df = pd.read_sql(sql, conn)
            df.rename(columns={'end_time': 'date'}, inplace=True)
            # 优化：只筛选一次，并使用映射来分配销售渠道
            channels = {
                # '我的南京': '-销量-线上-我的南京-当期值',
                # '公众号': '-销量-线上-公众号-当期值',
                # '支付宝': '-销量-线上-支付宝-当期值',
                '合计': '-销量-线上-当期值'
            }

            df_onlines = pd.DataFrame()  # 初始化空DataFrame用于拼接结果
            for channel_name, filter_str in channels.items():
                channel_df = df[df['name'].str.contains(filter_str)][['date', 'value']]
                channel_df['name'] = channel_name  # 添加销售渠道名称列
                df_onlines = pd.concat([df_onlines, channel_df])  # 拼接数据
            df_onlines.sort_values(by=['name', 'date'], inplace=True)
            df_onlines.reset_index(drop=True, inplace=True)
            df_onlines['day'] = df_onlines['date'].dt.strftime('%m-%d')
            df_onlines['cumulative_count'] = df_onlines.groupby(['name'])['value'].cumsum()

            df_target_online = pd.read_sql(
                "select name,short_name,target from public_target where type='online' and product_set_code='{product_set_code}'".format(
                    product_set_code=product_set_code), conn)
            number_sum = df_target_online.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
            # 保证合计在第一行
            df_target_online = pd.concat([number_sum, df_target_online], axis=0).reset_index(drop=True)
            # 获取去年的目标占比
            df_onlines_prev = self.online_adjust_data(product_set_code_prev, sale_end_date_prev)

            df_onlines_prev = pd.merge(df_onlines_prev, df_target_online, on='name', how='outer')
            df_onlines_prev['target_value_cumsum'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio_cumsum']
            df_onlines_prev['target_value'] = df_onlines_prev['target'] * df_onlines_prev['sale_ratio']
            df_onlines_prev = df_onlines_prev[['name', 'target', 'day', 'target_value', 'target_value_cumsum']]
            df_onlines_prev['target'] = df_onlines_prev['target'].astype(np.int64)
            df_onlines_prev.reset_index(drop=True, inplace=True)
            df_onlines.reset_index(drop=True, inplace=True)
            df = sqldf(
                "select a.*,b.target_value_cumsum ,ifnull(b.target_value,0) target_value,b.target  from df_onlines a left join df_onlines_prev b on a.name=b.name and a.day=b.day")
            df['target_value_cumsum'] = df.groupby('name')['target_value_cumsum'].transform(lambda x: x.ffill())

            # target 为空，根据name分组，取上一个值
            df['target'] = df.groupby('name')['target'].fillna(method='ffill')
            df.fillna(0, inplace=True)

            df.rename(columns={'cumulative_count': '实际(累计)', 'target_value_cumsum': '目标(累计)', 'value': '实际',
                               'target_value': '目标'}, inplace=True)
        return df

    def offline_daily_target(self, product_set_code, sale_start_date, product_set_code_prev, sale_end_date_prev):
        """
        计算线下每日目标
        """
        # 提取SQL查询逻辑
        sql_query = query_sql('SQL_DW_INDICATOR_DATA').format(
            product_set_code=product_set_code,
            statistical_type='当期值',
            unit='单',
            freq='日',
            start_datetime=sale_start_date,
            end_datetime=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )

        # 使用上下文管理器确保连接正确关闭
        with self.get_connection() as conn:
            df = pd.read_sql(sql_query, conn)
            df.rename(columns={'end_time': 'date'}, inplace=True)

            # 定义公司名称列表
            company_names = [
                # ('-销量-线下-个单-中国人保-当期值', '中国人保'),
                # ('-销量-线下-个单-中国人寿-当期值', '中国人寿'),
                # ('-销量-线下-个单-国寿财险-当期值', '国寿财险'),
                # ('-销量-线下-个单-中华联合-当期值', '中华联合'),
                # ('-销量-线下-个单-阳光财险-当期值', '阳光财险'),
                # ('-销量-线下-个单-紫金保险-当期值', '紫金保险'),
                # ('-销量-线下-个单-太保产险-当期值', '太保产险'),
                # ('-销量-线下-个单-利安人寿-当期值', '利安人寿'),
                # ('-销量-线下-个单-中银保险-当期值', '中银保险'),
                ('-销量-线下-个单-当期值', '合计')
            ]

            # 循环处理每个公司数据
            dfs = []
            for pattern, name in company_names:
                filtered_df = df[df['name'].str.contains(pattern)][['date', 'value']]
                filtered_df['name'] = name
                dfs.append(filtered_df)

            # 合并所有公司数据
            df_offlines = pd.concat(dfs)
            df_offlines.sort_values(by=['name', 'date'], inplace=True)
            df_offlines.reset_index(drop=True, inplace=True)
            df_offlines['day'] = df_offlines['date'].dt.strftime('%m-%d')
            df_offlines['cumulative_count'] = df_offlines.groupby(['name'])['value'].cumsum()

            # 获取今年的目标数据，计算出目标的每日分布与每日累计分布
            df_target_offline = pd.read_sql(
                "select name,short_name,target from public_target where type='agent' and product_set_code='{product_set_code}'".format(
                    product_set_code=product_set_code), conn)
            df_target_offline.rename(columns={'name': 'full_name', 'short_name': 'name'}, inplace=True)
            number_sum = df_target_offline.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
            # 保证合计在第一行
            df_target_offline = pd.concat([number_sum, df_target_offline], axis=0).reset_index(drop=True)
            # # 团单数量是多少
            df_group = self.get_seller_group_report_data()
            df_group = df_group[df_group['version'] == '合计']
            df_group = df_group[['short_name', 'value']].rename(columns={'value': 'group_count', 'short_name': 'name'})
            number_sum_group = df_group.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T

            df_group = pd.concat([df_group, number_sum_group], axis=0).reset_index(drop=True)
            df_target_offline = pd.merge(df_target_offline, df_group, on='name', how='left')
            df_target_offline['target'] = df_target_offline['target'] - df_target_offline['group_count']

            # 获取去年的目标占比
            df_offlines_prev = self.offline_adjust_data(product_set_code_prev, sale_end_date_prev)
            df_offlines_prev = pd.merge(df_offlines_prev, df_target_offline, on='name', how='left')
            df_offlines_prev['target_value_cumsum'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio_cumsum']
            df_offlines_prev['target_value'] = df_offlines_prev['target'] * df_offlines_prev['sale_ratio']
            df_offlines_prev = df_offlines_prev[
                ['full_name', 'name', 'day', 'target', 'target_value', 'target_value_cumsum']]
            df_offlines_prev['target'] = df_offlines_prev['target'].astype(np.int64)
            df_offlines_prev.reset_index(drop=True, inplace=True)
            df_offlines.reset_index(drop=True, inplace=True)
            df = sqldf(
                "select a.*,b.target_value_cumsum ,b.full_name,ifnull(b.target_value,0) target_value,b.target target from df_offlines a left join df_offlines_prev b on a.name=b.name and a.day=b.day")

            df['full_name'].fillna(df['name'], inplace=True)
            df['target_value_cumsum'] = df.groupby('name')['target_value_cumsum'].transform(lambda x: x.ffill())
            # target 为空，根据name分组，取上一个值
            df['target'] = df.groupby('name')['target'].transform(lambda x: x.bfill())
            df.fillna(0, inplace=True)

            df.rename(columns={'cumulative_count': '实际(累计)', 'target_value_cumsum': '目标(累计)', 'value': '实际',
                               'target_value': '目标'}, inplace=True)
        return df

    def get_insure_data(self):
        """
        概览列表(累计参保、累计保费、今日参保、昨日参保（较上一日比较）、今日保费、昨日保费（较上一日比较）)
        """
        # 1、累计参保
        with self.get_connection() as conn:
            df_total_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='单'), conn)
            df_total_count = df_total_count[df_total_count['name'].str.contains('销量-个单-累计值')][['value']]
            # df_group_count = pd.DataFrame(self.SELLERS)[['group_count_std','group_count_up']]
            # group_count = df_group_count['group_count_std'].sum()+df_group_count['group_count_up'].sum()
            # group_seller = pd.DataFrame(self.SELLERS)
            # 这边是保司上传的数据，保险公司的团单数据
            df_offline_seller_group = self.get_seller_group_report_data()
            group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
            group_seller.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
            group_count = group_seller['group_count'].sum()

            df_total_count['value'] = df_total_count['value'] + group_count+self.special_count
            if df_total_count.empty:
                df_total_count = pd.DataFrame({'value': [0]})
            # 最终以json输出，转成dict
            dict_total_person_count = df_total_count.to_dict(orient='records')

            # 2、累计保费
            df_total_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='元'), conn)
            df_total_amount = df_total_amount[df_total_amount['name'].str.contains('销售额-累计值')][['value']]
            if df_total_amount.empty:
                df_total_amount = pd.DataFrame({'value': [0]})
            dict_total_amount = df_total_amount.to_dict(orient='records')

            # 3、今日参保
            df_today_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.end_time), conn)
            df_today_count = df_today_count[
                df_today_count['name'].str.contains('-销量-当期值') & ~df_today_count[
                    'name'].str.contains('小时')][['value']]
            if df_today_count.empty:
                df_today_count = pd.DataFrame({'value': [0]})
            dict_today_count = df_today_count.to_dict(orient='records')

            # 4、今日保费
            df_today_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.end_time), conn)
            df_today_amount = df_today_amount[
                df_today_amount['name'].str.contains('-销售额-当期值') & ~df_today_amount[
                    'name'].str.contains('小时')][['value']]
            if df_today_amount.empty:
                df_today_amount = pd.DataFrame({'value': [0]})
            dict_today_amount = df_today_amount.to_dict(orient='records')

            # 5、昨日参保、前日参保、昨日较前日增长减少
            df_yesterday_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.yesterday_time), conn)
            df_day_before_yesterday_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.two_days_ago_time), conn)
            df_yesterday_count = df_yesterday_count[
                df_yesterday_count['name'].str.contains('-销量-当期值') & ~df_yesterday_count[
                    'name'].str.contains('小时')][['value']]
            df_day_before_yesterday_count = df_day_before_yesterday_count[
                df_day_before_yesterday_count['name'].str.contains('-销量-当期值') & ~df_day_before_yesterday_count[
                    'name'].str.contains('小时')][['value']]
            if df_yesterday_count.empty:
                df_yesterday_count = pd.DataFrame({'value': [0]})
                yesterday_count = 0
            else:
                yesterday_count = df_yesterday_count[['value']].values[0]
            day_before_yesterday_count = 0 if df_day_before_yesterday_count.empty else \
                df_day_before_yesterday_count.iloc[0]['value']
            df_count_compare = pd.DataFrame({'value': yesterday_count - day_before_yesterday_count}, index=[0])
            dict_count_compare = df_count_compare.to_dict(orient='records')
            dict_yesterday_count = df_yesterday_count.to_dict(orient='records')

            # 6、昨日保费、前日保费、昨日较前日增长减少
            df_yesterday_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.yesterday_time), conn)
            df_day_before_yesterday_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.two_days_ago_time), conn)
            df_yesterday_amount = df_yesterday_amount[
                df_yesterday_amount['name'].str.contains('-销售额-当期值') & ~df_yesterday_amount[
                    'name'].str.contains('小时')][['value']]
            df_day_before_yesterday_amount = df_day_before_yesterday_amount[
                df_day_before_yesterday_amount['name'].str.contains('-销售额-当期值') & ~df_day_before_yesterday_amount[
                    'name'].str.contains('小时')][['value']]
            if df_yesterday_amount.empty:
                df_yesterday_amount = pd.DataFrame({'value': [0]})
                yesterday_amount = 0
            else:
                yesterday_amount = df_yesterday_amount.iloc[0]['value']
            day_before_yesterday_amount = 0 if df_day_before_yesterday_amount.empty else \
                df_day_before_yesterday_amount.iloc[0]['value']

            df_amount_compare = pd.DataFrame({'value': yesterday_amount - day_before_yesterday_amount}, index=[0])
            dict_amount_compare = df_amount_compare.to_dict(orient='records')
            dict_yesterday_amount = df_yesterday_amount.to_dict(orient='records')

        return dict_total_person_count, dict_total_amount, dict_today_count, dict_today_amount, dict_yesterday_count, dict_count_compare, dict_yesterday_amount, dict_amount_compare

    def get_sale_info(self):
        """
        产品销售情况
        """
        with self.get_connection() as conn:
            # 1、完成率
            df_complete_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_complete_rate = df_complete_rate[df_complete_rate['name'].str.contains('完成率-当期值')][
                ['value']].rename(columns={'value': 'percent'})
            if df_complete_rate.empty:
                df_complete_rate = pd.DataFrame({'percent': [0]})
            if df_complete_rate['percent'].values[0] is None:
                df_complete_rate['percent'] = 0
            df_complete_rate['value'] = df_complete_rate['percent']
            df_complete_rate['percent'] = df_complete_rate['percent'].apply(lambda x: round(x / 100, 4))

            dict_complete_rate = df_complete_rate.to_dict(orient='records')
            # 2、续约占比
            df_continue_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_continue_rate = df_continue_rate[df_continue_rate['name'].str.contains('续保占比-当期值')][
                ['value']].rename(columns={'value': 'percent'})
            df_continue_rate['value'] = df_continue_rate['percent']
            df_continue_rate['percent'] = df_continue_rate['percent'].apply(lambda x: round(x / 100, 4))

            dict_continue_rate = df_continue_rate.to_dict(orient='records')

            # 2.1、续保率
            df_continue_rate_prev = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.previous_product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_continue_rate_prev = df_continue_rate_prev[df_continue_rate_prev['name'].str.contains('-续保率-当期值')][
                ['value']].rename(columns={'value': 'percent'})
            df_continue_rate_prev['value'] = df_continue_rate_prev['percent']
            df_continue_rate_prev['percent'] = df_continue_rate_prev['percent'].apply(lambda x: round(x / 100, 4))

            dict_continue_rate_prev = df_continue_rate_prev.to_dict(orient='records')

        # 3、产品类型分布
        product_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                       statistical_type='product')
        df_product_data = pd.DataFrame(list(product_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_product_data.empty:
            df_product_data = pd.DataFrame()
        else:
            df_product_data['value'] = df_product_data['value'].astype(int)

        dict_product_data = df_product_data.to_dict(orient='records')

        # 4、个单团单分布
        personal_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='personal')
        df_personal_data = pd.DataFrame(list(personal_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_personal_data.empty:
            df_personal_data = pd.DataFrame()
        else:
            df_personal_data['value'] = df_personal_data['value'].astype(int)
        dict_personal_data = df_personal_data.to_dict(orient='records')
        # 5、线上线下分布
        isonline_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='isonline')
        df_isonline_data = pd.DataFrame(list(isonline_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_isonline_data.empty:
            df_isonline_data = pd.DataFrame()
        else:
            df_isonline_data['value'] = df_isonline_data['value'].astype(int)
        dict_isonline_data = df_isonline_data.to_dict(orient='records')

        # 6、自费个账分布
        pay_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                   statistical_type='pay')
        df_pay_data = pd.DataFrame(list(pay_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_pay_data.empty:
            df_pay_data = pd.DataFrame()
        else:
            df_pay_data['value'] = df_pay_data['value'].astype(int)
        dict_pay_data = df_pay_data.to_dict(orient='records')

        # 7、参保地分布
        place_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                     statistical_type='place')
        df_place_data = pd.DataFrame(list(place_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_place_data.empty:
            df_place_data = pd.DataFrame()
        else:
            df_place_data['value'] = df_place_data['value'].astype(int)
        dict_place_data = df_place_data.to_dict(orient='records')

        # 8、险种分布
        medicare_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='medicare')
        df_medicare_data = pd.DataFrame(list(medicare_data.values('key', 'value'))).rename(columns={'key': 'type'})
        if df_medicare_data.empty:
            df_medicare_data = pd.DataFrame()
        else:
            df_medicare_data['value'] = df_medicare_data['value'].astype(int)
        dict_medicare_data = df_medicare_data.to_dict(orient='records')

        return dict_complete_rate, dict_continue_rate, dict_product_data, dict_personal_data, dict_isonline_data, dict_pay_data, dict_place_data, dict_medicare_data, dict_continue_rate_prev

    def get_last_24_hour_data(self):
        """
        最近24小时销量数据
        """
        with self.get_connection() as conn:
            # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
            start_datetime = max(
                datetime.datetime.strptime(self.start_datetime, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='小时', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_24_hour_data = df_last_24_hour_data[df_last_24_hour_data['name'].str.contains('销量-当期值')][
                ['end_time', 'value']]
            df_last_24_hour_data['end_time'] = df_last_24_hour_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data['value'] = df_last_24_hour_data['value'].astype(int)
            df_last_24_hour_data.rename(columns={'end_time': 'x', 'value': 'y'}, inplace=True)
            dict_last_24_hour_data = df_last_24_hour_data.to_dict(orient='records')
            return dict_last_24_hour_data

    def get_last_15_days_data(self):
        """
        最近15日销量数据
        """
        with self.get_connection() as conn:
            start_datetime = max(
                datetime.datetime.strptime(self.fifteen_days_ago, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S'))
            df_last_15_days_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_15_days_data = df_last_15_days_data[df_last_15_days_data['name'].str.contains('销量-当期值')][
                ['end_time', 'value']]
            df_last_15_days_data['end_time'] = df_last_15_days_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_last_15_days_data['value'] = df_last_15_days_data['value'].astype(int)
            df_last_15_days_data.rename(columns={'end_time': 'x', 'value': 'y'}, inplace=True)
            dict_last_15_days_data = df_last_15_days_data.to_dict(orient='records')
            return dict_last_15_days_data

    def get_area_data(self):
        """
        参保地统计报表（排名、参保地、占比、总单数、今日参保、昨日参保、参保率）
        """

        area_data = InsureArea.objects.filter(product_set_code=self.product_set_code)
        df_area_data = pd.DataFrame(list(
            area_data.values('position', 'name', 'ratio', 'total_count', 'today_count', 'yesterday_count',
                             'insure_ratio')))
        # 按照排序先确定顺序，并对合计的顺序置为-
        if df_area_data.empty:
            df_area_data = pd.DataFrame()
        else:
            df_area_data.sort_values(by='position', inplace=True)
            df_area_data.reset_index(inplace=True)
            df_area_data['position'] = df_area_data.index + 1
            df_area_data[['insure_ratio', 'ratio']] = df_area_data[['insure_ratio', 'ratio']].astype(float)
            df_area_data.loc[df_area_data['name'] == '合计', 'position'] = '-'
        dict_area_data = df_area_data.to_dict(orient='records')
        return dict_area_data

    def get_online_data(self):
        """
        线上统计报表(排名、渠道、占比、总单数、今日参保、昨日参保、目标、完成率)
        """
        online_data = InsureOnline.objects.filter(product_set_code=self.product_set_code)
        df_online_data = pd.DataFrame(list(
            online_data.values('position', 'channel_name', 'insure_ratio', 'total_count', 'today_count',
                               'yesterday_count', 'target', 'target_ratio', 'week_complete_ratio', 'week_count',
                               'week_target', 'week_target_ratio')))
        if df_online_data.empty:
            df_online_data = pd.DataFrame()
        else:
            # 按照排序先确定顺序，并对合计的顺序置为-
            df_online_data.sort_values(by=['position'], inplace=True)
            df_online_data.reset_index(inplace=True)
            df_online_data['position'] = df_online_data.index + 1
            df_online_data[['insure_ratio', 'target_ratio', 'week_complete_ratio', 'week_target_ratio']] = \
                df_online_data[['insure_ratio', 'target_ratio', 'week_complete_ratio', 'week_target_ratio']].astype(
                    float)
            df_online_data.loc[df_online_data['channel_name'] == '合计', 'position'] = '-'
            df_online_data['today_increase'] = df_online_data['today_count'] - df_online_data['yesterday_count']
            df_online_data['dod'] = df_online_data.apply(
                lambda x: round(x['today_increase'] / x['yesterday_count'], 3) if x['yesterday_count'] != 0 and x[
                    'today_increase'] != 0
                else 1 if x['yesterday_count'] == 0 and x['today_increase'] != 0
                else 0,
                axis=1
            )

            df_online_data['dod_str'] = df_online_data.apply(
                lambda x: str(round(x['today_increase'] / x['yesterday_count'] * 100, 1)) + '%'
                if x['yesterday_count'] != 0 and x['today_increase'] != 0
                else '100%' if x['yesterday_count'] == 0 and x['today_increase'] != 0
                else '0%',
                axis=1
            )
        dict_online_data = df_online_data.to_dict(orient='records')
        return dict_online_data

    def get_agent_data(self):
        """
        获取线下保司销售数据（排名、保司、占比、代理人数、人均出单、个单、团单、目标、完成率）
        """
        agent_data = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        df_agent_data = pd.DataFrame(list(agent_data.values('position', 'name', 'insure_ratio', 'employee_count',
                                                            'average_count', 'personal_count', 'group_count', 'target',
                                                            'target_ratio', 'today_count', 'yesterday_count',
                                                            'week_complete_ratio', 'week_count',
                                                            'week_target', 'week_target_ratio')))
        if df_agent_data.empty:
            df_agent_data = pd.DataFrame()
        else:
            # 按照排序先确定顺序，并对合计的顺序置为-
            df_agent_data.sort_values(by=['position'], inplace=True)
            df_agent_data[
                ['insure_ratio', 'target_ratio', 'average_count', 'week_complete_ratio', 'week_target_ratio']] = \
            df_agent_data[
                ['insure_ratio', 'target_ratio', 'average_count', 'week_complete_ratio', 'week_target_ratio']].astype(
                float)
            df_agent_data.loc[df_agent_data['name'] == '合计', 'position'] = '-'
            df_agent_data['average_count'] = df_agent_data['average_count'].round(1).astype(float)
            df_agent_data['today_increase'] = df_agent_data['today_count'] - df_agent_data['yesterday_count']
            df_agent_data['dod'] = df_agent_data.apply(
                lambda x: round(x['today_increase'] / x['yesterday_count'], 3) if x['yesterday_count'] != 0 and x[
                    'today_increase'] != 0
                else 1 if x['yesterday_count'] == 0 and x['today_increase'] != 0
                else 0,
                axis=1
            )

            df_agent_data['dod_str'] = df_agent_data.apply(
                lambda x: str(round(x['today_increase'] / x['yesterday_count'] * 100, 1)) + '%'
                if x['yesterday_count'] != 0 and x['today_increase'] != 0
                else '100%' if x['yesterday_count'] == 0 and x['today_increase'] != 0
                else '0%',
                axis=1
            )

        dict_agent_data = df_agent_data.to_dict(orient='records')
        return dict_agent_data

    # 人群特征
    def get_age_data(self):
        """
        平均年龄、年龄中位数
        """
        with self.get_connection() as conn:
            df_avg_age = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='平均值', unit='岁'), conn)
            df_avg_age = df_avg_age[df_avg_age['name'].str.contains('年龄-平均值')][['value']]

            df_median_age = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='中位值', unit='岁'), conn)
            df_median_age = df_median_age[df_median_age['name'].str.contains('年龄-中位值')][['value']]

            dict_avg_age = df_avg_age.to_dict(orient='records')
            dict_median_age = df_median_age.to_dict(orient='records')
            return dict_avg_age, dict_median_age

    def get_age_gender_data(self):
        """
        年龄性别分布
        """
        age_sex_data = InsureAgeSex.objects.filter(product_set_code=self.product_set_code).order_by('age_distribution',
                                                                                                    'sex')
        df_age_sex_data = pd.DataFrame(list(
            age_sex_data.values('sex', 'age_distribution', 'value')))
        new_df_age_sex_data = df_age_sex_data.pivot(index='age_distribution', columns='sex',
                                                    values='value').reset_index()
        new_df_age_sex_data.rename(columns={'age_distribution': 'y', '女': 'x2', '男': 'x1'}, inplace=True)
        dict_age_sex_data = new_df_age_sex_data.to_dict(orient='records')
        return dict_age_sex_data

    def get_age_distribution_data(self):
        """
        年龄段分布
        """
        age_distribution_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                                statistical_type='age_ratio').order_by('key')

        df_age_distribution_data = pd.DataFrame(list(age_distribution_data.values('key', 'value')))
        if df_age_distribution_data.empty:
            df_age_distribution_data = pd.DataFrame()
        else:
            df_age_distribution_data['value'] = df_age_distribution_data['value'].astype(float)
            df_age_distribution_data.rename(columns={'key': 'y', 'value': 'x'}, inplace=True)
        dict_age_distribution_data = df_age_distribution_data.to_dict(orient='records')
        return dict_age_distribution_data

    def get_trend_data(self):
        """
        投保趋势（日度当期值、累计值）
        """
        # 投保趋势
        start_datetime = datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S')
        with self.get_connection() as conn:
            df_count_cumsum_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='累计值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_count_cumsum = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-累计值') & ~df_count_cumsum_total['name'].str.contains(
                    '小时')][
                ['end_time', 'value']]
            df_count_cumsum['end_time'] = df_count_cumsum['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum['value'] = df_count_cumsum['value'].astype(int)
            df_count_cumsum.rename(columns={'value': 'total_value'}, inplace=True)

            df_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_count = df_count[df_count['name'].str.contains('销量-当期值') & ~df_count['name'].str.contains('小时')][
                ['end_time', 'value']]

            df_count['end_time'] = df_count['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count['value'] = df_count['value'].astype(int)

            df_trend_data = pd.merge(df_count, df_count_cumsum, on='end_time', how='outer')
            df_trend_data.fillna(0, inplace=True)
            # 如果df_trend_data的记录数小于7，则往前面补充剩余的日期，填充0
            # 获取实际数据与销售起始日中最小的作为开始日期
            full_date_range = pd.date_range(start=datetime.datetime.strptime(self.end_datetime,'%Y-%m-%d %H:%M:%S').date() - datetime.timedelta(days=6),
                                            end=datetime.datetime.strptime(self.end_datetime,'%Y-%m-%d %H:%M:%S').date(), freq='D')
            if df_trend_data.shape[0] < 7:
                df_trend_data['end_time'] = df_trend_data['end_time'].apply(
                    lambda x: datetime.datetime.strptime(x, '%Y-%m-%d'))
                df_trend_data = (
                    df_trend_data.set_index('end_time')
                    .reindex(full_date_range)
                    .fillna(0)
                    .reset_index()
                    .rename(columns={'index': 'end_time'})
                )
                df_trend_data['end_time'] = df_trend_data['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_trend_data['end_time'] = df_trend_data['end_time'].apply(lambda x: x[5:])
            df_trend_data.rename(columns={'end_time': 'x', 'value': 'y1', 'total_value': 'y2'}, inplace=True)
            dict_trend_data = df_trend_data.to_dict(orient='records')

            return dict_trend_data

    def get_compare_data(self):
        """
        往期对比-全量（累计值） 本期累计值、累计同比
        往期对比-线上（累计值） 本期累计值、累计同比
        往期对比-线下（累计值） 本期累计值、累计同比
        """
        # 投保趋势
        start_datetime = datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S')
        with self.get_connection() as conn:
            df_count_cumsum_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='累计值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_count_cumsum = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-个单-累计值') & ~df_count_cumsum_total[
                    'name'].str.contains(
                    '小时')][
                ['end_time', 'value']]
            df_count_cumsum['end_time'] = df_count_cumsum['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum['value'] = df_count_cumsum['value'].astype(int)
            df_count_cumsum.rename(columns={'value': 'total_value'}, inplace=True)

            df_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_offline = df_count[df_count['name'].str.contains('销量-线下-个单-当期值')][['name', 'end_time', 'value']]
            df_offline['name'] = df_offline['name'].apply(lambda x: x.split('-')[1])
            df_online = df_count[df_count['name'].str.contains('销量-线上-当期值')][['name', 'end_time', 'value']]
            df_online['name'] = df_online['name'].apply(lambda x: x.split('-')[1])
            df_total = pd.concat([df_offline, df_online], axis=0)
            df_count = df_total.groupby(['name', 'end_time'])['value'].sum().reset_index()

            df_count['end_time'] = df_count['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count['value'] = df_count['value'].astype(int)

            # 往期对比，当日销量对比
            df_count_prev = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.previous_product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=self.sale_start_time_prev,
                                                          end_datetime=self.end_datetime), conn)
            df_offline_prev = df_count_prev[df_count_prev['name'].str.contains('销量-个单-线下-当期值')][
                ['name', 'end_time', 'value']]
            df_offline_prev['name'] = df_offline_prev['name'].apply(lambda x: x.split('-')[1])
            df_online_prev = df_count_prev[df_count_prev['name'].str.contains('销量-线上-当期值')][
                ['name', 'end_time', 'value']]
            df_online_prev['name'] = df_online_prev['name'].apply(lambda x: x.split('-')[1])
            df_total_prev = pd.concat([df_offline_prev, df_online_prev], axis=0)
            df_count_prev = df_total_prev.groupby(['name', 'end_time'])['value'].sum().reset_index()

            df_count_prev['end_time'] = df_count_prev['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count_prev['value'] = df_count_prev['value'].astype(int)
            df_count_prev['month_day'] = df_count_prev['end_time'].apply(lambda x: x[-5:])
            df_count['month_day'] = df_count['end_time'].apply(lambda x: x[-5:])
            df_count_prev.rename(columns={'value': 'z'}, inplace=True)
            df_count_bq = df_count.copy()
            df_count_bq.rename(columns={'value': 'y'}, inplace=True)
            df_count_compare_dqz = pd.merge(df_count_bq, df_count_prev[['month_day', 'z']], on='month_day', how='left')
            full_date_range_total = pd.date_range(start=self.sale_start_date,
                                                  end=datetime.datetime.strptime(self.end_datetime,'%Y-%m-%d %H:%M:%S').date(), freq='D')
            df_count_compare_dqz['end_time'] = df_count_compare_dqz['end_time'].apply(
                lambda x: datetime.datetime.strptime(x, '%Y-%m-%d'))
            df_count_compare_dqz = (
                df_count_compare_dqz.set_index('end_time')
                .reindex(full_date_range_total)
                .fillna(0)
                .reset_index()
                .rename(columns={'index': 'end_time'})
            )
            # 近15日的起始日(因为到最开始当天了，所以只要14天)
            start_datetime = max(
                datetime.datetime.strptime(self.fourteen_days_ago, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S'))
            # 取近15天的数据
            df_count_compare_dqz['end_time'] = df_count_compare_dqz['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count_compare_dqz = df_count_compare_dqz[
                df_count_compare_dqz['end_time'] >= datetime.datetime.strftime(start_datetime, '%Y-%m-%d')]

            df_count_compare_dqz.rename(columns={'month_day': 'x'}, inplace=True)
            df_melted = pd.melt(df_count_compare_dqz, id_vars=['x'], value_vars=['y', 'z'], var_name='s',
                                value_name='value')
            df_melted.rename(columns={'value': 'y'}, inplace=True)
            df_melted['s'] = df_melted['s'].map({'y': '本期', 'z': '上期'})
            dict_count_compare_total_dqz = df_melted.to_dict(orient='records')

            # 预计目标数据
            df_online_daily_target = self.online_daily_target(self.product_set_code, self.sale_start_date,
                                                              self.previous_product_set_code,
                                                              self.sale_end_date_prev)
            df_offline_daily_target = self.offline_daily_target(self.product_set_code, self.sale_start_date,
                                                                self.previous_product_set_code,
                                                                self.sale_end_date_prev)
            df_online_daily_target = df_online_daily_target[['day', '目标(累计)']].rename(
                columns={'day': 'month_day', '目标(累计)': 'target_pred'})
            df_offline_daily_target = df_offline_daily_target[['day', '目标(累计)']].rename(
                columns={'day': 'month_day', '目标(累计)': 'target_pred'})
            df_online_daily_target['target_pred'] = df_online_daily_target['target_pred'].round(0)
            df_offline_daily_target['target_pred'] = df_offline_daily_target['target_pred'].round(0)
            df_daily_target = pd.concat([df_online_daily_target, df_offline_daily_target])
            df_daily_target = df_daily_target.groupby('month_day')['target_pred'].sum().reset_index()

            # 往期对比-全量（累计值） 本期累计值、累计同比
            df_count_cumsum_previous_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.previous_product_set_code,
                                                          statistical_type='累计值', unit='单',
                                                          freq='日', start_datetime=self.sale_start_time_prev,
                                                          end_datetime=self.end_datetime), conn)

            df_count_cumsum_previous = df_count_cumsum_previous_total[
                df_count_cumsum_previous_total['name'].str.contains('销量-个单-累计值') & ~
                df_count_cumsum_previous_total[
                    'name'].str.contains('小时')][
                ['end_time', 'value']]
            # 上期数据减去6天，与今年销售期对齐
            # df_count_cumsum_previous['end_time'] = df_count_cumsum_previous['end_time'] - datetime.timedelta(days=6)

            df_count_cumsum_previous['end_time'] = df_count_cumsum_previous['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum_previous['value'] = df_count_cumsum_previous['value'].astype(int)
            df_count_cumsum_previous.rename(columns={'value': 'previous_total_value'}, inplace=True)

            df_count_cumsum['month_day'] = df_count_cumsum['end_time'].apply(lambda x: x[-5:])
            df_count_cumsum_previous['month_day'] = df_count_cumsum_previous['end_time'].apply(lambda x: x[-5:])
            # 如果销售期差别很大，会有问题，比如一个是上半年的，一个是下半年的
            df_count_compare = pd.merge(df_count_cumsum[['month_day', 'total_value']],
                                        df_count_cumsum_previous[['month_day', 'previous_total_value']], on='month_day',
                                        how='left')
            df_count_compare = pd.merge(df_count_compare, df_daily_target, on='month_day', how='left')
            df_count_compare.fillna(0, inplace=True)

            # 数据进行组合
            df_count_compare_melted = df_count_compare.melt(id_vars=['month_day'],
                                                            value_vars=['total_value', 'previous_total_value',
                                                                        'target_pred'],
                                                            var_name='s',
                                                            value_name='y')
            df_count_compare_melted.rename(columns={'month_day': 'x'}, inplace=True)
            type_mapping = {'total_value': '本期', 'previous_total_value': '上期', 'target_pred': '目标'}
            df_count_compare_melted['s'] = df_count_compare_melted['s'].map(type_mapping)
            df_count_compare_melted['colorField'] = df_count_compare_melted['s']

            # 取最后一条记录的 total_value 作为本期累计值
            if df_count_compare.empty:
                count_cumsum = pd.DataFrame({'value': [0]})
                count_yoy = pd.DataFrame({'value': [0]})
            else:
                df_sorted = df_count_cumsum.sort_values(by='end_time', ascending=False)
                df_merge = pd.merge(df_count_cumsum, df_count_cumsum_previous[['month_day', 'previous_total_value']],
                                    on='month_day', how='left')
                df_merge['previous_total_value'].fillna(method='ffill', inplace=True)
                df_merge['previous_total_value'].fillna(0, inplace=True)
                df_merge = df_merge.sort_values(by='end_time', ascending=False)
                df_merge.reset_index(inplace=True)
                df_merge['yoy'] = df_merge.apply(
                    lambda row: (row['total_value'] - row['previous_total_value']) / row[
                        'previous_total_value'] * 100 if row['previous_total_value'] != 0 else 100,
                    axis=1
                )
                count_yoy = pd.DataFrame({'value': [df_merge.iloc[0]['yoy'].round(2)]})
                # 获取 end_time 最大的 total_value 的值
                count_cumsum = pd.DataFrame({'value': [df_sorted.iloc[0]['total_value']]})

            dict_count_cumsum = count_cumsum.to_dict(orient='records')
            # 数据转字典
            dict_count_compare_total = df_count_compare_melted.to_dict(orient='records')
            # 获取累计同比记录，因为要对齐，所有数据不是传统意义上的同比
            # df_yoy_total = pd.read_sql(
            #     query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
            #                                                      statistical_type='累计同比', unit='百分比'), conn)
            # df_yoy = df_yoy_total[df_yoy_total['name'].str.contains('销量-累计同比')][['value']]
            # if df_yoy.empty:
            #     count_yoy = pd.DataFrame({'value': [0]})
            # else:
            #     count_yoy = pd.DataFrame({'value': [df_yoy.iloc[0]['value']]})
            dict_count_yoy = count_yoy.to_dict(orient='records')

            # 往期对比-线上（累计值） 本期累计值、累计同比
            df_count_cumsum_online = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-线上-累计值') & ~df_count_cumsum_total[
                    'name'].str.contains(
                    '小时')][
                ['end_time', 'value']]

            df_count_cumsum_online['end_time'] = df_count_cumsum_online['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum_online['value'] = df_count_cumsum_online['value'].astype(int)
            df_count_cumsum_online.rename(columns={'value': 'total_value'}, inplace=True)

            df_count_cumsum_previous_online = df_count_cumsum_previous_total[
                df_count_cumsum_previous_total['name'].str.contains('销量-线上-累计值') & ~
                df_count_cumsum_previous_total[
                    'name'].str.contains('小时')][
                ['end_time', 'value']]
            # 上期数据减去6天，与今年销售期对齐
            # df_count_cumsum_previous_online['end_time'] = df_count_cumsum_previous_online['end_time'] - datetime.timedelta(days=6)
            df_count_cumsum_previous_online['end_time'] = df_count_cumsum_previous_online['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum_previous_online['value'] = df_count_cumsum_previous_online['value'].astype(int)
            df_count_cumsum_previous_online.rename(columns={'value': 'previous_total_value'}, inplace=True)

            df_count_cumsum_online['month_day'] = df_count_cumsum_online['end_time'].apply(lambda x: x[-5:])
            df_count_cumsum_previous_online['month_day'] = df_count_cumsum_previous_online['end_time'].apply(
                lambda x: x[-5:])
            # 如果销售期差别很大，会有问题，比如一个是上半年的，一个是下半年的
            df_count_compare_online = pd.merge(df_count_cumsum_online[['month_day', 'total_value']],
                                               df_count_cumsum_previous_online[['month_day', 'previous_total_value']],
                                               on='month_day',
                                               how='left')
            df_count_compare_online = pd.merge(df_count_compare_online, df_online_daily_target, on='month_day',
                                               how='left')
            df_count_compare_online.fillna(0, inplace=True)
            # 数据进行组合
            df_count_compare_online_melted = df_count_compare_online.melt(id_vars=['month_day'],
                                                                          value_vars=['total_value',
                                                                                      'previous_total_value',
                                                                                      'target_pred'],
                                                                          var_name='s',
                                                                          value_name='y')
            df_count_compare_online_melted.rename(columns={'month_day': 'x'}, inplace=True)
            df_count_compare_online_melted['s'] = df_count_compare_online_melted['s'].map(
                type_mapping)
            df_count_compare_online_melted['colorField'] = df_count_compare_online_melted['s']
            # 取最后一条记录的 total_value 作为本期累计值
            if df_count_compare_online.empty:
                count_cumsum_online = pd.DataFrame({'value': [0]})
                count_yoy_online = pd.DataFrame({'value': [0]})
            else:
                df_sorted_online = df_count_cumsum_online.sort_values(by='end_time', ascending=False)
                df_merge_online = pd.merge(df_count_cumsum_online,
                                           df_count_cumsum_previous_online[['month_day', 'previous_total_value']],
                                           on='month_day', how='left')
                df_merge_online['previous_total_value'].fillna(method='ffill', inplace=True)
                df_merge_online['previous_total_value'].fillna(0, inplace=True)
                df_merge_online = df_merge_online.sort_values(by='end_time', ascending=False)
                df_merge_online.reset_index(inplace=True)
                df_merge_online['yoy'] = df_merge_online.apply(
                    lambda row: (row['total_value'] - row['previous_total_value']) / row[
                        'previous_total_value'] * 100 if row['previous_total_value'] != 0 else 100,
                    axis=1
                )
                count_yoy_online = pd.DataFrame({'value': [df_merge_online.iloc[0]['yoy'].round(2)]})
                # 获取 end_time 最大的 total_value 的值
                count_cumsum_online = pd.DataFrame({'value': [df_sorted_online.iloc[0]['total_value']]})
            dict_count_cumsum_online = count_cumsum_online.to_dict(orient='records')
            dict_count_compare_online = df_count_compare_online_melted.to_dict(orient='records')

            # df_yoy_online = df_yoy_total[df_yoy_total['name'].str.contains('销量-线上-累计同比')][['value']]
            # if df_yoy_online.empty:
            #     count_yoy_online = pd.DataFrame({'value': [0]})
            # else:
            #     count_yoy_online = pd.DataFrame({'value': [df_yoy_online.iloc[0]['value']]})
            dict_count_yoy_online = count_yoy_online.to_dict(orient='records')
            # 往期对比-线下（累计值） 本期累计值、累计同比
            df_count_cumsum_offline = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-个单-线下-累计值') & ~df_count_cumsum_total[
                    'name'].str.contains(
                    '小时')][
                ['end_time', 'value']]

            df_count_cumsum_offline['end_time'] = df_count_cumsum_offline['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum_offline['value'] = df_count_cumsum_offline['value'].astype(int)
            df_count_cumsum_offline.rename(columns={'value': 'total_value'}, inplace=True)

            df_count_cumsum_previous_offline = df_count_cumsum_previous_total[
                df_count_cumsum_previous_total['name'].str.contains('销量-个单-线下-累计值') & ~
                df_count_cumsum_previous_total[
                    'name'].str.contains('小时')][
                ['end_time', 'value']]
            # 上期数据减去6天，与今年销售期对齐
            # df_count_cumsum_previous_offline['end_time'] = df_count_cumsum_previous_offline['end_time'] - datetime.timedelta(days=6)
            df_count_cumsum_previous_offline['end_time'] = df_count_cumsum_previous_offline['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_count_cumsum_previous_offline['value'] = df_count_cumsum_previous_offline['value'].astype(int)
            df_count_cumsum_previous_offline.rename(columns={'value': 'previous_total_value'}, inplace=True)

            df_count_cumsum_offline['month_day'] = df_count_cumsum_offline['end_time'].apply(lambda x: x[-5:])
            df_count_cumsum_previous_offline['month_day'] = df_count_cumsum_previous_offline['end_time'].apply(
                lambda x: x[-5:])
            # 如果销售期差别很大，会有问题，比如一个是上半年的，一个是下半年的
            df_count_compare_offline = pd.merge(df_count_cumsum_offline[['month_day', 'total_value']],
                                                df_count_cumsum_previous_offline[['month_day', 'previous_total_value']],
                                                on='month_day',
                                                how='left')
            df_count_compare_offline = pd.merge(df_count_compare_offline, df_offline_daily_target, on='month_day',
                                                how='left')
            df_count_compare_offline.fillna(0, inplace=True)
            # 数据进行组合
            df_count_compare_offline_melted = df_count_compare_offline.melt(id_vars=['month_day'],
                                                                            value_vars=['total_value',
                                                                                        'previous_total_value',
                                                                                        'target_pred'],
                                                                            var_name='s',
                                                                            value_name='y')
            df_count_compare_offline_melted.rename(columns={'month_day': 'x'}, inplace=True)
            df_count_compare_offline_melted['s'] = df_count_compare_offline_melted['s'].map(
                type_mapping)
            df_count_compare_offline_melted['colorField'] = df_count_compare_offline_melted['s']
            # 取最后一条记录的 total_value 作为本期累计值
            if df_count_compare_offline.empty:
                count_cumsum_offline = pd.DataFrame({'value': [0]})
                count_yoy_offline = pd.DataFrame({'value': [0]})
            else:
                df_sorted_offline = df_count_cumsum_offline.sort_values(by='end_time', ascending=False)
                df_merge_offline = pd.merge(df_count_cumsum_offline,
                                            df_count_cumsum_previous_offline[['month_day', 'previous_total_value']],
                                            on='month_day', how='left')
                df_merge_offline['previous_total_value'].fillna(method='ffill', inplace=True)
                df_merge_offline['previous_total_value'].fillna(0, inplace=True)
                df_merge_offline = df_merge_offline.sort_values(by='end_time', ascending=False)
                df_merge_offline.reset_index(inplace=True)
                df_merge_offline['yoy'] = df_merge_offline.apply(
                    lambda row: (row['total_value'] - row['previous_total_value']) / row[
                        'previous_total_value'] * 100 if row['previous_total_value'] != 0 else 100,
                    axis=1
                )
                count_yoy_offline = pd.DataFrame({'value': [df_merge_offline.iloc[0]['yoy'].round(2)]})
                # 获取 end_time 最大的 total_value 的值
                count_cumsum_offline = pd.DataFrame({'value': [df_sorted_offline.iloc[0]['total_value']]})
            dict_count_cumsum_offline = count_cumsum_offline.to_dict(orient='records')
            dict_count_compare_offline = df_count_compare_offline_melted.to_dict(orient='records')

            # df_yoy_offline = df_yoy_total[df_yoy_total['name'].str.contains('销量-线下-累计同比')][['value']]
            # if df_yoy_offline.empty:
            #     count_yoy_offline = pd.DataFrame({'value': [0]})
            # else:
            #     count_yoy_offline = pd.DataFrame({'value': [df_yoy_offline.iloc[0]['value']]})
            dict_count_yoy_offline = count_yoy_offline.to_dict(orient='records')

            return dict_count_compare_total, dict_count_cumsum, dict_count_yoy, dict_count_compare_online, dict_count_cumsum_online, dict_count_yoy_online, dict_count_compare_offline, dict_count_cumsum_offline, dict_count_yoy_offline, dict_count_compare_total_dqz

    def get_content(self):
        """
        获取全部数据
        """
        data = {}
        dict_total_person_count, dict_total_amount, dict_today_count, dict_today_amount, dict_yesterday_count, dict_count_compare, dict_yesterday_amount, dict_amount_compare = self.get_insure_data()
        data['累计参保'] = dict_total_person_count
        data['累计保费'] = dict_total_amount
        data['今日参保'] = dict_today_count
        data['今日保费'] = dict_today_amount
        data['昨日参保'] = dict_yesterday_count
        data['昨日参保较前一日'] = dict_count_compare
        data['昨日保费'] = dict_yesterday_amount
        data['昨日保费较前一日'] = dict_amount_compare
        dict_complete_rate, dict_continue_rate, dict_product_data, dict_personal_data, dict_isonline_data, dict_pay_data, dict_place_data, dict_medicare_data, dict_continue_rate_prev = self.get_sale_info()
        data['完成率'] = dict_complete_rate
        data['续约占比'] = dict_continue_rate
        data['续保率'] = dict_continue_rate_prev
        data['产品类型'] = dict_product_data
        data['个单团单'] = dict_personal_data
        data['线上线下'] = dict_isonline_data
        data['自费个账'] = dict_pay_data
        data['参保地'] = dict_place_data
        data['险种'] = dict_medicare_data
        data['近24小时投保量'] = self.get_last_24_hour_data()
        data['近15日投保量'] = self.get_last_15_days_data()
        data['参保地区'] = self.get_area_data()
        data['线上参保'] = self.get_online_data()
        data['线下参保'] = self.get_agent_data()
        dict_avg_age, dict_median_age = self.get_age_data()
        data['平均年龄'] = dict_avg_age
        data['年龄中位数'] = dict_median_age
        data['年龄性别分布'] = self.get_age_gender_data()
        data['年龄段分布'] = self.get_age_distribution_data()
        data['投保趋势'] = self.get_trend_data()
        dict_count_compare_total, count_cumsum, count_yoy, dict_count_compare_online, count_cumsum_online, count_yoy_online, dict_count_compare_offline, count_cumsum_offline, count_yoy_offline, dict_count_compare_total_dqz = self.get_compare_data()
        data['往期对比全量'] = dict_count_compare_total
        data['本期累计投保'] = count_cumsum
        data['本期累计同比'] = count_yoy
        data['往期对比线上'] = dict_count_compare_online
        data['本期线上累计投保'] = count_cumsum_online
        data['本期线上累计同比'] = count_yoy_online
        data['往期对比线下'] = dict_count_compare_offline
        data['本期线下累计投保'] = count_cumsum_offline
        data['本期线下累计同比'] = count_yoy_offline
        data['往期对比全量当日'] = dict_count_compare_total_dqz
        return data

    def cache_content(self):
        """
        缓存全部数据
        """
        try:
            df_content = self.get_content()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache('get_content', df_content)
            return df_content
        except Exception as e:
            logger.error(f'{type(self).__name__} cache_content error:{e}')
            raise ValueError(f'{type(self).__name__} cache_content error:{e}')

    def get_datav_data(self):
        """
        获取数据
        """
        data = self.get_from_cache('get_content')
        return data


if __name__ == '__main__':
    data = DataVNhbInsureV5()
    # product_set_code = data.product_set_code
    # data.cache_area_code()
    # data.get_insure_data()
    # data.get_last_24_hour_data()
    # data.get_last_7_days_data()
    # data.get_school_data()
    # data.get_area_data()
    # data.get_person_data()
    # df = data.get_content()
    # pprint(df)
    # df_age_gender_data = data.get_age_gender_data()
    # print(df_age_gender_data)
    data.cache_content()
    datav = data.get_datav_data()
    pprint(datav)
