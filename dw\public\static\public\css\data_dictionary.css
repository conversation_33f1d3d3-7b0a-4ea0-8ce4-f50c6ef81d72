/* 数据字典样式文件 - 参考网页样式 */

/* 基础样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
    line-height: 1.5715;
    color: rgba(0, 0, 0, 0.85);
    background-color: #f0f2f5;
    padding-top: 35px; /* 为固定导航栏留出空间 */
}

/* 导航栏样式优化 */
.navbar {
    min-height: 35px !important;
    height: 35px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

.navbar-brand {
    font-size: 0.9rem;
    font-weight: 500;
    line-height: 1;
    padding: 0.25rem 0;
}

.navbar-nav .nav-link {
    padding: 0.25rem 1rem !important;
    font-size: 0.8rem;
    line-height: 1;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    font-weight: 500;
}

/* 容器样式 */
.container-fluid {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 面包屑导航 */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 20px;
    font-size: 14px;
}

.breadcrumb-item a {
    color: #1890ff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: rgba(0, 0, 0, 0.45);
}

/* 表标题样式 */
.table-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.table-title .anticon {
    color: #eb2f96;
    margin-right: 8px;
    font-size: 16px;
}

.table-title h1 {
    margin: 0;
    font-size: 20px;
    color: #333;
    font-weight: 500;
}

/* 参数列表样式 */
.parameter-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.parameter-list li {
    display: flex;
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    align-items: flex-start;
    min-height: 48px;
}

.parameter-list li:last-child {
    border-bottom: none;
}

.parameter-list li:first-child {
    background: #fafafa;
    font-weight: 500;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.parameter-list .left {
    min-width: 120px;
    color: #666;
    font-weight: 500;
    flex-shrink: 0;
}

.parameter-list .right {
    flex: 1;
    color: #333;
    word-break: break-all;
    padding-left: 10px;
}

.parameter-list .right a {
    color: #1890ff;
    text-decoration: none;
}

.parameter-list .right a:hover {
    text-decoration: underline;
}

/* 表格样式 */
.ant-table-wrapper {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    border: 1px solid #f0f0f0;
    overflow: hidden;
}

.ant-table {
    border-collapse: collapse;
    width: 100%;
    font-size: 14px;
}

.ant-table-thead th {
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    text-align: left;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
}

.ant-table-tbody td {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    color: #333;
    vertical-align: top;
}

.ant-table-tbody tr:hover {
    background: #fafafa;
}

.ant-table-tbody tr:last-child td {
    border-bottom: none;
}

/* 字段约束标签 */
.field-constraint {
    display: inline-block;
    background: #f6f6f6;
    color: #666;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 4px;
    margin-bottom: 2px;
    white-space: nowrap;
}

.field-constraint.primary {
    background: #e6f7ff;
    color: #1890ff;
    border: 1px solid #91d5ff;
}

.field-constraint.unique {
    background: #f6ffed;
    color: #52c41a;
    border: 1px solid #b7eb8f;
}

.field-constraint.indexed {
    background: #fff7e6;
    color: #fa8c16;
    border: 1px solid #ffd591;
}

/* 操作按钮区域 */
.export-buttons {
    margin-top: 30px;
    text-align: right;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.export-buttons .btn {
    margin-left: 10px;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.btn-secondary {
    background: #f5f5f5;
    border: 1px solid #d9d9d9;
    color: #333;
}

.btn-secondary:hover {
    background: #e6e6e6;
    border-color: #bfbfbf;
    color: #333;
    text-decoration: none;
}

.btn-success {
    background: #52c41a;
    border: 1px solid #52c41a;
    color: #fff;
}

.btn-success:hover {
    background: #73d13d;
    border-color: #73d13d;
    color: #fff;
    text-decoration: none;
}

.btn-primary {
    background: #1890ff;
    border: 1px solid #1890ff;
    color: #fff;
}

.btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: #fff;
    text-decoration: none;
}

/* 图标样式 */
.fas, .fa {
    margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .parameter-list li {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .parameter-list .left {
        min-width: auto;
        margin-bottom: 5px;
    }
    
    .parameter-list .right {
        padding-left: 0;
    }
    
    .ant-table-wrapper {
        overflow-x: auto;
    }
    
    .export-buttons {
        text-align: center;
    }
    
    .export-buttons .btn {
        margin: 5px;
        display: block;
        width: 100%;
    }
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px;
    color: #999;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #1890ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    color: #d9d9d9;
}

.empty-state h3 {
    color: #666;
    margin-bottom: 10px;
}

/* 搜索框样式 */
.search-box {
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box .form-control {
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
}

.search-box .form-control:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}
