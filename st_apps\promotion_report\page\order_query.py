import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, sum_or_combine, simplify_replace
from utils.st import query_sql

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        ps.name AS product_set_name,
        ps.code AS product_set_code,
        DATE_FORMAT(MIN(p.sale_from), '%Y-%m-%d') AS sale_from,
        DATE_FORMAT(MIN(p.pre_sale_from), '%Y-%m-%d') AS pre_sale_from
    FROM
        product_set ps
    JOIN
        product p ON p.product_set_id = ps.id AND p.main = 1
    WHERE
        LEFT(ps.code, 10) in ('rizhao_nxb','dezhou_hmb','binzhou_yh')
    GROUP BY
        ps.name
    ORDER BY
        ps.name DESC;

        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_order_query(product_set_code, credential_number):
    """
    获取保司物料的传播情况，包括阅读和转发
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售开始时间
    :return:
    """
    sql = """
    SELECT u.name person_name,u.credential_number, p.name, p2.policy_number
    FROM 
    `order` o
    JOIN order_item oi ON oi.order_id = o.id
    JOIN order_item_client oic ON oic.order_item_id = oi.id
    JOIN user u ON u.id = oic.client_id
    JOIN product_set ps ON ps.id = o.product_set_id and ps.code = '{product_set_code}'
    JOIN product p ON p.id = oi.product_id
    JOIN order_client_policy_info p2 ON p2.order_item_id = oi.id AND p2.client_id = oic.client_id
    WHERE u.credential_number IN ({credential_number})
        """
    df = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code, credential_number=credential_number),ttl=0)
    df.rename(columns={'person_name':'姓名','name': '产品名称', 'policy_number': '保单号','credential_number':'身份证号'}, inplace=True)
    return df




def main():
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("保单查询")
    product_info = get_product_code()
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns([0.3,0.6])
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[0]
            else:
                product_set_code = None

        credential_number = st.text_area('被保人身份证号码', key='credential_number',help='查询多个身份证号码，请用英文逗号隔开，如：340102199001011234,340102199001015678')
        st.divider()
        if st.button('查询'):
            if credential_number:
                if ',' in credential_number or '，' in credential_number:
                    credential_number = credential_number.replace('，', ',')
                    # 将输入字符串按逗号分割成列表
                    credential_list = credential_number.split(',')
                    # 对每个分割后的字符串添加单引号
                    credential_list_quoted = [f"'{cred}'" for cred in credential_list]
                    # 将这些带有单引号的字符串用逗号连接起来
                    credential_number = ','.join(credential_list_quoted)
                else:
                    credential_number = f"'{credential_number}'"
                with st.spinner('查询中...'):

                    df = get_order_query(product_set_code, credential_number)
                    st.dataframe(df, hide_index=True, use_container_width=True)
            else:
                st.info('请输入被保人身份证号码')


    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
