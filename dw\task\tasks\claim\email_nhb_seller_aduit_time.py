import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)
import logging
from datetime import datetime

import pandas as pd
import os
import pymysql
import idna
import warnings
from dw import settings
from django.core.mail import EmailMessage
from transfrom.utils.utils import  query_sql,age_group

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


TO = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
CC= ['<EMAIL>']

DB = settings.DATABASES['nhb']


def get_connection():
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                                user=DB["USER"],
                                password=DB["PASSWORD"], database=DB["NAME"])
    return conn



def get_nhb_seller_aduit_time():
    sql = """
        SELECT
            ic.id AS claim_id,
            MAX( CONVERT_TZ( ice.create_time, '+00:00', '+08:00' ) ) AS max_create_time ,substring_index(p.name,'-',1) as product_set_name
        FROM
            insurance_claim ic
            LEFT JOIN insurance_claim_event ice ON ice.claim_id = ic.id 
            join product p on ic.product_code = p.code
        WHERE
            ic.STATUS IN ( 'WAIT_INSURANCE_COMPANY_AUDITED', 'INSURANCE_COMPANY_ACCEPTED' ) 
            AND ice.`status` = 'WAIT_INSURANCE_COMPANY_AUDITED' 
            AND ic.`product_code` IN ('ninghuibao-2024-standard', 'ninghuibao-2024-upgrade', 'ninghuibao-2025-standard', 'ninghuibao-2025-upgrade' ) 
        GROUP BY
        product_set_name,
            ic.id,
            ice.`status`
        """

    sql_detail = """
    SELECT	
    ic.id 理赔编号,
    ic.number 报案号,
    ic.policy_number 保单号码,
    ic.accident_date 出险日期,
    sum(case when ica.actual_paid_amount >0 then ica.actual_paid_amount else ica.total_claim_amount end ) as 金额
    
    FROM	
        insurance_claim ic
        join claim_insurant ci on ic.id = ci.claim_id
        join insurance_claim_amount ica on ica.claim_id = ic.id 
    WHERE	
         ica.delete_time is null
        and ic.id in ('{claim_ids}')
    group by ic.id
    order by 出险日期
    """

    conn = get_connection()
    df = pd.read_sql(sql, conn)
    df['date_diff'] = (datetime.now() - df['max_create_time']).dt.days
    df['product_set_name'] = df['product_set_name'].str.replace('南京宁惠保', '')
    labels = ['0-4天', '5-10天', '11-20天', '20-30天','30天以上']
    bins = [0, 4, 10, 20, 30, float('inf')]
    df['day_group'] = pd.cut(df['date_diff'], bins=bins, labels=labels, right=False)
    df_copy = df[df['day_group'] != '0-4天']
    claim_ids = [str(id) for id in df_copy['claim_id'].tolist()]
    claim_ids = "','".join(claim_ids)

    df_detail = pd.read_sql(sql_detail.format(claim_ids=claim_ids), conn)
    df_detail['理赔编号'] = df_detail['理赔编号'].astype(str)

    df = df.groupby(['product_set_name', 'day_group']).size().reset_index(name='count')
    # 统计所有未结案的总数量
    total_unsettled = df[df['day_group'] != '0-4天']['count'].sum()

    df = df.pivot_table(index='day_group', columns='product_set_name', values='count', fill_value=0)
    df = df.reset_index()
    df.columns.name = None
    # 删除 0-4天 的行
    df = df[df['day_group'] != '0-4天']
    df.reset_index(drop=True, inplace=True)
    df.rename(columns={'day_group': '保司已处理时间','四期':'四期（件）','五期':'五期（件）'}, inplace=True)
    # 将df中数值列都转成int
    # 获取除了'保司已处理时间'列以外的所有列名
    numeric_columns = df.columns.drop('保司已处理时间')
    # 将这些列转换为int类型
    df[numeric_columns] = df[numeric_columns].astype(int)
    # 添加合计行
    total_row = df.sum(numeric_only=True)
    total_row['保司已处理时间'] = '合计'
    df = pd.concat([df, pd.DataFrame([total_row])], ignore_index=True)
    return df, total_unsettled,df_detail



def email_nhb_seller_aduit_time():
    date = datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'宁惠保保司处理时间_{date}.xlsx')
    df_nhb_seller_aduit_time, total_unsettled, df_detail = get_nhb_seller_aduit_time()
    
    # 使用ExcelWriter将两个DataFrame写入不同的sheet
    with pd.ExcelWriter(path, engine='openpyxl') as writer:
        # 写入概览sheet
        df_nhb_seller_aduit_time.to_excel(writer, sheet_name='概览', index=False)
        
        # 获取概览sheet并设置格式
        workbook = writer.book
        overview_sheet = workbook['概览']
        
        # 自动调整列宽
        for column in overview_sheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            overview_sheet.column_dimensions[column_letter].width = adjusted_width
        
        # 写入详细sheet
        df_detail.to_excel(writer, sheet_name='详细', index=False)
        
        # 获取详细sheet并设置格式
        detail_sheet = workbook['详细']
        
        # 自动调整详细sheet的列宽
        for column in detail_sheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            detail_sheet.column_dimensions[column_letter].width = adjusted_width

    email_body = f"""您好：
    南京宁惠保已有{total_unsettled}例，超期未结案，详情见附件！
    谢谢！"""

    mail = EmailMessage(
        subject=f'每日统计_宁惠保保司处理时间_{date}',
        body=email_body,
        to=TO,
        cc=CC
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)
