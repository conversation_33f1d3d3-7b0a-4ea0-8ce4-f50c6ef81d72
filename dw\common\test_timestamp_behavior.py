"""
测试时间戳默认值行为的脚本
验证各数据库的create_time和update_time是否按预期工作
"""

from django.db import connection, transaction
from django.core.management.base import BaseCommand
from .db_utils import DatabaseCompatibilityUtils
from .db_migration import DatabaseMigrationManager
import time


class TimestampBehaviorTester:
    """时间戳行为测试器"""

    def __init__(self, database_alias='default'):
        self.database_alias = database_alias
        self.db_utils = DatabaseCompatibilityUtils()
        self.migration_manager = DatabaseMigrationManager(database_alias)
        self.db_type = self.db_utils.get_database_type(database_alias)

    def create_test_table(self):
        """创建测试表"""
        test_table_sql = {
            'mysql': """
                CREATE TABLE IF NOT EXISTS test_timestamp_table (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100),
                    create_time DATETIME,
                    update_time DATETIME
                )
            """,
            'postgresql': """
                CREATE TABLE IF NOT EXISTS test_timestamp_table (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    create_time TIMESTAMP,
                    update_time TIMESTAMP
                )
            """,
            'sqlite': """
                CREATE TABLE IF NOT EXISTS test_timestamp_table (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name VARCHAR(100),
                    create_time DATETIME,
                    update_time DATETIME
                )
            """
        }

        sql = test_table_sql.get(self.db_type)
        if sql:
            with connection.cursor() as cursor:
                cursor.execute(sql)
            print(f"[OK] 测试表创建成功 ({self.db_type})")
        else:
            raise Exception(f"不支持的数据库类型: {self.db_type}")

    def apply_timestamp_defaults(self):
        """应用时间戳默认值"""
        print(f"为测试表添加时间戳默认值...")
        success = self.migration_manager.add_timestamp_defaults('test_timestamp_table')
        if success:
            print("[OK] 时间戳默认值添加成功")
        else:
            raise Exception("时间戳默认值添加失败")

    def test_insert_behavior(self):
        """测试INSERT行为"""
        print("\n=== 测试INSERT行为 ===")

        with connection.cursor() as cursor:
            # 插入测试数据
            cursor.execute("INSERT INTO test_timestamp_table (name) VALUES (%s)", ["测试记录1"])

            # 查询插入的数据
            cursor.execute("""
                SELECT id, name, create_time, update_time
                FROM test_timestamp_table
                WHERE name = %s
            """, ["测试记录1"])

            result = cursor.fetchone()
            if result:
                record_id, name, create_time, update_time = result
                print(f"插入记录: ID={record_id}, Name={name}")
                print(f"create_time: {create_time}")
                print(f"update_time: {update_time}")

                # 验证时间戳是否设置
                if create_time and update_time:
                    print("[OK] INSERT时create_time和update_time都已设置")
                    return record_id, create_time, update_time
                else:
                    print("[ERROR] INSERT时时间戳未正确设置")
                    return None
            else:
                print("[ERROR] 未找到插入的记录")
                return None

    def test_update_behavior(self, record_id, original_create_time, original_update_time):
        """测试UPDATE行为"""
        print("\n=== 测试UPDATE行为 ===")

        # 等待1秒确保时间差异
        time.sleep(1)

        with connection.cursor() as cursor:
            # 更新记录
            cursor.execute("""
                UPDATE test_timestamp_table
                SET name = %s
                WHERE id = %s
            """, ["测试记录1_更新", record_id])

            # 查询更新后的数据
            cursor.execute("""
                SELECT name, create_time, update_time
                FROM test_timestamp_table
                WHERE id = %s
            """, [record_id])

            result = cursor.fetchone()
            if result:
                name, new_create_time, new_update_time = result
                print(f"更新后记录: Name={name}")
                print(f"原始create_time: {original_create_time}")
                print(f"新的create_time: {new_create_time}")
                print(f"原始update_time: {original_update_time}")
                print(f"新的update_time: {new_update_time}")

                # 验证行为
                create_time_unchanged = str(original_create_time) == str(new_create_time)
                update_time_changed = str(original_update_time) != str(new_update_time)

                if create_time_unchanged:
                    print("[OK] create_time在UPDATE时保持不变")
                else:
                    print("[ERROR] create_time在UPDATE时发生了变化")

                if update_time_changed:
                    print("[OK] update_time在UPDATE时自动更新")
                else:
                    print("[ERROR] update_time在UPDATE时未自动更新")

                return create_time_unchanged and update_time_changed
            else:
                print("[ERROR] 未找到更新的记录")
                return False

    def cleanup_test_table(self):
        """清理测试表"""
        with connection.cursor() as cursor:
            cursor.execute("DROP TABLE IF EXISTS test_timestamp_table")

            # 清理PostgreSQL的触发器和函数
            if self.db_type == 'postgresql':
                cursor.execute("DROP TRIGGER IF EXISTS trigger_update_test_timestamp_table_timestamp ON test_timestamp_table")
                cursor.execute("DROP FUNCTION IF EXISTS update_test_timestamp_table_timestamp()")

            # 清理SQLite的触发器
            elif self.db_type == 'sqlite':
                cursor.execute("DROP TRIGGER IF EXISTS trigger_update_test_timestamp_table_timestamp")

        print("[OK] 测试表清理完成")

    def run_full_test(self):
        """运行完整测试"""
        print(f"开始测试数据库时间戳行为 (数据库类型: {self.db_type})")
        print("="*50)

        try:
            # 1. 创建测试表
            self.create_test_table()

            # 2. 应用时间戳默认值
            self.apply_timestamp_defaults()

            # 3. 测试INSERT行为
            insert_result = self.test_insert_behavior()
            if not insert_result:
                return False

            record_id, create_time, update_time = insert_result

            # 4. 测试UPDATE行为
            update_result = self.test_update_behavior(record_id, create_time, update_time)

            # 5. 输出测试结果
            print("\n" + "="*50)
            if update_result:
                print("[SUCCESS] 时间戳行为测试通过！")
                print("[OK] create_time: 只在INSERT时设置")
                print("[OK] update_time: INSERT时设置，UPDATE时自动更新")
            else:
                print("[FAILED] 时间戳行为测试失败！")

            return update_result

        except Exception as e:
            print(f"[ERROR] 测试过程中发生错误: {str(e)}")
            return False

        finally:
            # 6. 清理测试表
            try:
                self.cleanup_test_table()
            except Exception as e:
                print(f"清理测试表时发生错误: {str(e)}")


def test_timestamp_behavior(database_alias='default'):
    """测试时间戳行为的便捷函数"""
    tester = TimestampBehaviorTester(database_alias)
    return tester.run_full_test()


if __name__ == "__main__":
    # 直接运行测试
    test_timestamp_behavior()
