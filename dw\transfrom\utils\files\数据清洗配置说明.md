# 数据清洗配置指南

## 📋 概述

本文档详细说明如何在ETL系统中配置和使用数据清洗功能。系统采用**双配置文件架构**，将字段处理策略和具体清洗规则分离，提供更好的可维护性和扩展性。

## 🏗️ 架构设计

### 双配置文件架构

ETL系统的数据清洗配置采用**双配置文件架构**，实现关注点分离：

#### 1. 字段处理策略配置（`field_config.py`）
- **职责**：定义字段级别的处理策略（做什么）
- **内容**：NULL值处理、空字符串处理等高层策略
- **示例**：`null_to_empty_string`、`preserve_empty_string`、`standard_cleaning`

#### 2. 具体清洗规则配置（`data_cleaning_config.py`）
- **职责**：定义具体的清洗规则和函数（怎么做）
- **内容**：地址清洗、电话清洗、类型验证等具体实现
- **示例**：`address_cleaning`、`phone_cleaning_advanced`、`type_validation`

### 配置文件结构

```
transfrom/
├── utils/
│   ├── data_cleaning.py          # 核心清洗逻辑实现（已清理重复函数）
│   ├── field_config.py           # 字段处理策略配置框架
│   └── data_cleaning_config.py   # 具体清洗规则配置框架
└── tasks/
    └── spider/
        ├── gjyb/                 # 国家医保局
        │   └── pipeline/
        │       └── config/
        │           ├── __init__.py              # 自动注册gjyb配置
        │           ├── field_config.py         # gjyb字段处理策略
        │           └── data_cleaning_config.py # gjyb具体清洗规则
        ├── jsyb/                 # 江苏医保局
        │   └── pipeline/
        │       └── config/
        │           ├── __init__.py              # 自动注册jsyb配置
        │           ├── field_config.py         # jsyb字段处理策略
        │           └── data_cleaning_config.py # jsyb具体清洗规则
        └── njyb/                 # 南京医保局
            └── pipeline/
                └── config/
                    ├── __init__.py              # 自动注册njyb配置
                    ├── field_config.py         # njyb字段处理策略
                    └── data_cleaning_config.py # njyb具体清洗规则
```

### 配置调用流程

```
ETL流程 → 数据清洗 → 字段策略处理 → 数据转换
         ↑              ↑
   data_cleaning_config  field_config
   (具体清洗规则)        (字段处理策略)
```

## 🔧 配置方法

### 0. 一对多字段映射特殊处理

当源表中的一个字段需要映射到目标表的多个字段时，需要进行特殊处理。系统提供了两种解决方案：

#### 方案1：字段映射阶段特殊处理（推荐）

适用于简单的字段分割逻辑，如按分隔符分割字段。

**配置步骤**：

1. **在 `field_mapping.py` 中配置特殊映射规则**：
   ```python
   # transfrom/utils/field_mapping.py
   def _needs_special_processing(self, source_field, target_field, source_table):
       special_mappings = {
           'spider_fuwu_service_facilities': {
               'medListCodg': ['code', 'charge_item_code']  # 一对多映射
           }
       }
       # 检查是否需要特殊处理
   ```

2. **实现特殊处理逻辑**：
   ```python
   def _apply_special_processing(self, source_series, source_field, target_field, source_table, province):
       if source_field == 'medListCodg':
           if target_field == 'code':
               # 提取第一个'-'之前的部分
               return source_series.apply(self._extract_code_part)
           elif target_field == 'charge_item_code':
               # 提取第一个'-'之后的部分
               return source_series.apply(self._extract_charge_code_part)
   ```

**优势**：
- 框架级别支持，可复用
- 配置简单，逻辑清晰
- 自动处理字段映射冲突

#### 方案2：数据清洗阶段直接创建目标字段

适用于复杂的业务逻辑处理，如需要关联其他表数据。

**配置步骤**：

1. **在数据清洗函数中直接创建目标字段**：
   ```python
   def _clean_service_facilities_data(self, df):
       # 直接创建目标字段
       df['code'] = df['medListCodg'].apply(self._extract_code_part)
       df['charge_item_code'] = df['medListCodg'].apply(self._extract_charge_code_part)
       return df
   ```

2. **字段映射阶段自动跳过已存在的字段**：
   ```python
   # 系统会自动检测目标字段是否已存在，如存在则跳过映射
   if target_field in source_df.columns:
       result_df[target_field] = source_df[target_field]  # 使用清洗阶段的值
   ```

**优势**：
- 支持复杂业务逻辑
- 可以关联其他表数据
- 灵活性更高

#### 选择建议

- **简单分割逻辑**：使用方案1（字段映射阶段处理）
- **复杂业务逻辑**：使用方案2（数据清洗阶段处理）
- **需要关联数据**：使用方案2（数据清洗阶段处理）

### 1. 字段处理策略配置（field_config.py）

字段处理策略配置定义字段级别的处理策略，主要用于ETL转换阶段的字段映射和类型标准化。

#### 1.1 创建省份字段策略配置

```python
# transfrom/tasks/spider/gjyb/pipeline/config/field_config.py
from transfrom.utils.field_config import BaseFieldProcessingConfig
from typing import Dict

class GjybFieldProcessingConfig(BaseFieldProcessingConfig):
    def get_field_strategies(self) -> Dict:
        return {
            'spider_fuwu_fixed_hospital': {
                'medical_designated_providers': {
                    # 目前采用统一的标准清洗策略
                    # 所有字段统一使用标准清洗（空字符串转NULL）
                    # 这样确保数据一致性，避免源表字段缺失导致的不一致问题
                }
            },
            'spider_fuwu_retail_pharmacy': {
                'medical_designated_providers': {
                    # 同样采用统一的标准清洗策略
                }
            }
        }

gjyb_field_config = GjybFieldProcessingConfig()
```

#### 1.2 注册字段策略配置

```python
# transfrom/tasks/spider/gjyb/pipeline/config/__init__.py
from transfrom.utils.field_config import register_province_config
from .field_config import gjyb_field_config

register_province_config('gjyb', gjyb_field_config)
```

### 2. 具体清洗规则配置（data_cleaning_config.py）

具体清洗规则配置定义实际的数据清洗逻辑，在数据清洗阶段执行。

#### 2.1 创建省份清洗规则配置

```python
# transfrom/tasks/spider/gjyb/pipeline/config/data_cleaning_config.py
from transfrom.utils.data_cleaning_config import BaseDataCleaningConfig
from typing import Dict, Callable

class GjybDataCleaningConfig(BaseDataCleaningConfig):
    def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
        """表级别的清洗规则"""
        return {
            'spider_fuwu_fixed_hospital': {
                'address_cleaning': {
                    'enabled': True,
                    'fields': ['addr'],
                    'min_length': 200
                },
                'type_validation': {
                    'enabled': True,
                    'dict_id': 24,
                    'type_field': 'medinsType',
                    'type_name_field': 'medinsTypeName'
                }
            }
        }

    def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
        """字段级别的清洗规则"""
        return {
            'spider_fuwu_fixed_hospital': {
                'addr': {
                    'address_cleaning': {
                        'enabled': True,
                        'min_length': 200
                    }
                },
                'tel': {
                    'phone_cleaning_advanced': {
                        'enabled': True
                    }
                }
            }
        }

gjyb_data_cleaning_config = GjybDataCleaningConfig()
```

#### 2.2 注册清洗规则配置

```python
# transfrom/tasks/spider/gjyb/pipeline/config/__init__.py
from transfrom.utils.field_config import register_province_config
from transfrom.utils.data_cleaning_config import register_province_cleaning_config
from .field_config import gjyb_field_config
from .data_cleaning_config import gjyb_data_cleaning_config

# 注册字段处理策略配置
register_province_config('gjyb', gjyb_field_config)
# 注册数据清洗规则配置
register_province_cleaning_config('gjyb', gjyb_data_cleaning_config)
```

## 🚀 使用方法

### 1. 在ETL流程中使用

ETL流程会自动调用两层配置：

```python
# 导入省份配置（触发自动注册）
import transfrom.tasks.spider.gjyb.pipeline.config

# ETL流程中的使用（在process_etl函数中自动调用）
from transfrom.utils import process_etl

# 执行完整的ETL流程，包括数据清洗和字段策略处理
stats = process_etl(
    source_table='spider_fuwu_fixed_hospital',
    target_table='medical_designated_providers',
    unique_fields=['medinsCode'],
    operation_mode='upsert',
    province='gjyb'  # 指定省份
)
```

### 2. 单独使用数据清洗

```python
from transfrom.utils import clean_source_data

# 仅进行数据清洗（使用data_cleaning_config）
cleaned_df = clean_source_data(source_df, 'spider_fuwu_fixed_hospital', province='gjyb')
```

### 3. 直接获取配置

```python
# 获取字段处理策略配置
from transfrom.utils.field_config import get_data_cleaning_config
field_config = get_data_cleaning_config('gjyb', 'spider_fuwu_fixed_hospital')

# 获取具体清洗规则配置
from transfrom.utils.data_cleaning_config import get_table_cleaning_rules, get_field_cleaning_rules
table_rules = get_table_cleaning_rules('gjyb', 'spider_fuwu_fixed_hospital')
field_rules = get_field_cleaning_rules('gjyb', 'spider_fuwu_fixed_hospital')
```

## 📊 支持的配置类型

### 字段处理策略（field_config.py）

| 策略名称 | 描述 | 适用场景 |
|---------|------|---------|
| `null_to_empty_string` | NULL值转换为空字符串 | 业务上需要保留空字符串的字段 |
| `preserve_empty_string` | 保留空字符串（不转换为None） | 需要区分空字符串和NULL的字段 |
| `standard_cleaning` | 标准清洗（空字符串转None） | 大多数普通字段 |

### 具体清洗规则（data_cleaning_config.py）

| 规则类型 | 描述 | 配置位置 |
|---------|------|---------|
| `address_cleaning` | 地址清洗规则 | 表级别或字段级别 |
| `phone_cleaning_advanced` | 高级电话号码清洗 | 字段级别 |
| `type_validation` | 类型验证清洗 | 表级别 |
| `email_cleaning` | 邮箱地址清洗 | 字段级别 |

## 📋 详细参数说明

### 1. 通用参数

#### `enabled` 参数
- **类型**：`boolean`
- **默认值**：`True`
- **含义**：是否启用该清洗规则
- **示例**：
  ```python
  'address_cleaning': {
      'enabled': True,    # 启用地址清洗
      # 其他参数...
  }
  ```
- **说明**：
  - `True`：启用该清洗规则，会执行相应的清洗逻辑
  - `False`：禁用该清洗规则，跳过清洗处理
  - 用于临时关闭某个清洗规则而不删除配置

### 2. 地址清洗参数 (`address_cleaning`)

#### `min_length` 参数
- **类型**：`integer`
- **默认值**：`200`
- **含义**：触发地址清洗的最小字符长度阈值
- **示例**：
  ```python
  'address_cleaning': {
      'enabled': True,
      'min_length': 200   # 只有长度超过200字符的地址才会被清洗
  }
  ```
- **说明**：
  - 只有当地址字符串长度 > `min_length` 时才会执行清洗
  - 避免对短地址进行不必要的清洗处理
  - 常用值：`100`（宽松）、`200`（标准）、`300`（严格）

#### `fields` 参数（表级别配置）
- **类型**：`list[string]`
- **含义**：需要进行地址清洗的字段列表
- **示例**：
  ```python
  'address_cleaning': {
      'enabled': True,
      'fields': ['addr', 'address', 'location'],  # 多个地址字段
      'min_length': 200
  }
  ```
- **说明**：
  - 仅在表级别配置中使用
  - 指定哪些字段需要应用地址清洗规则
  - 字段级别配置中不需要此参数（字段名已确定）

### 3. 电话清洗参数 (`phone_cleaning_advanced`)

#### 基础配置
- **类型**：`dict`
- **示例**：
  ```python
  'tel': {
      'phone_cleaning_advanced': {
          'enabled': True     # 启用高级电话清洗
      }
  }
  ```
- **说明**：
  - 支持20+种复杂电话格式
  - 包括全角字符转换、带空格手机号处理等
  - 详细功能参考：`transfrom/utils/files/电话号码清洗修复说明.md`

### 4. 类型验证参数 (`type_validation`)

#### `dict_id` 参数
- **类型**：`integer`
- **含义**：数据字典的ID，用于验证字段值的有效性
- **示例**：
  ```python
  'type_validation': {
      'enabled': True,
      'dict_id': 24,                    # 机构类型字典ID
      'type_field': 'medinsType',       # 类型代码字段
      'type_name_field': 'medinsTypeName'  # 类型名称字段
  }
  ```
- **说明**：
  - 从 `medical_dict_data` 表中获取对应的字典数据
  - 常用字典ID：`24`（机构类型）、`25`（其他类型）

#### `type_field` 参数
- **类型**：`string`
- **含义**：存储类型代码的字段名
- **说明**：
  - 该字段的值会与字典中的 `dict_code` 进行匹配
  - 无效的代码会被设置为 `None`

#### `type_name_field` 参数
- **类型**：`string`
- **含义**：存储类型名称的字段名
- **说明**：
  - 当对应的 `type_field` 被设置为 `None` 时，该字段也会被设置为 `None`
  - 保持类型代码和类型名称的一致性

### 5. 邮箱清洗参数 (`email_cleaning`)

#### 基础配置
- **类型**：`dict`
- **示例**：
  ```python
  'email': {
      'email_cleaning': {
          'enabled': True,
          'validate_format': True,    # 验证邮箱格式
          'normalize_domain': True    # 标准化域名
      }
  }
  ```
- **说明**：
  - `validate_format`：验证邮箱格式是否正确
  - `normalize_domain`：标准化域名（如统一大小写）

### 6. 自定义清洗参数

#### 自定义函数配置
- **类型**：`dict`
- **示例**：
  ```python
  'custom_field': {
      'custom_cleaning': {
          'enabled': True,
          'function_name': 'clean_custom_data',  # 自定义函数名
          'parameters': {                        # 传递给函数的参数
              'param1': 'value1',
              'param2': 100
          }
      }
  }
  ```
- **说明**：
  - `function_name`：在 `get_custom_cleaning_functions()` 中定义的函数名
  - `parameters`：传递给自定义函数的额外参数

## 📖 参数速查表

### 通用参数

| 参数名 | 类型 | 默认值 | 含义 | 示例 |
|--------|------|--------|------|------|
| `enabled` | `boolean` | `True` | 是否启用该清洗规则 | `'enabled': True` |

### 地址清洗参数 (`address_cleaning`)

| 参数名 | 类型 | 默认值 | 含义 | 示例 |
|--------|------|--------|------|------|
| `enabled` | `boolean` | `True` | 是否启用地址清洗 | `'enabled': True` |
| `min_length` | `integer` | `200` | 触发清洗的最小字符长度 | `'min_length': 200` |
| `fields` | `list[string]` | - | 需要清洗的字段列表（仅表级别） | `'fields': ['addr']` |

### 电话清洗参数 (`phone_cleaning_advanced`)

| 参数名 | 类型 | 默认值 | 含义 | 示例 |
|--------|------|--------|------|------|
| `enabled` | `boolean` | `True` | 是否启用高级电话清洗 | `'enabled': True` |

### 类型验证参数 (`type_validation`)

| 参数名 | 类型 | 默认值 | 含义 | 示例 |
|--------|------|--------|------|------|
| `enabled` | `boolean` | `True` | 是否启用类型验证 | `'enabled': True` |
| `dict_id` | `integer` | - | 数据字典ID | `'dict_id': 24` |
| `type_field` | `string` | - | 类型代码字段名 | `'type_field': 'medinsType'` |
| `type_name_field` | `string` | - | 类型名称字段名 | `'type_name_field': 'medinsTypeName'` |

### 邮箱清洗参数 (`email_cleaning`)

| 参数名 | 类型 | 默认值 | 含义 | 示例 |
|--------|------|--------|------|------|
| `enabled` | `boolean` | `True` | 是否启用邮箱清洗 | `'enabled': True` |
| `validate_format` | `boolean` | `True` | 是否验证邮箱格式 | `'validate_format': True` |
| `normalize_domain` | `boolean` | `True` | 是否标准化域名 | `'normalize_domain': True` |

### 自定义清洗参数 (`custom_cleaning`)

| 参数名 | 类型 | 默认值 | 含义 | 示例 |
|--------|------|--------|------|------|
| `enabled` | `boolean` | `True` | 是否启用自定义清洗 | `'enabled': True` |
| `function_name` | `string` | - | 自定义函数名 | `'function_name': 'clean_custom_data'` |
| `parameters` | `dict` | `{}` | 传递给函数的参数 | `'parameters': {'param1': 'value1'}` |

## 🏗️ 配置层级详解

### 三层配置架构

数据清洗系统采用三层配置架构，按执行顺序为：
1. **表级别配置** → 2. **字段级别配置** → 3. **通用清洗**

#### 1. 表级别配置 (`get_table_cleaning_rules`)
- **适用场景**：跨字段的复杂清洗逻辑、整表数据处理
- **配置结构**：`{表名: {策略名: 配置}}`
- **执行时机**：最先执行，在字段级别配置之前
- **配置文件**：`transfrom/tasks/spider/{province}/pipeline/config/data_cleaning_config.py`
- **典型用例**：
  ```python
  'spider_fuwu_retail_pharmacy': {
      'type_validation': {           # 跨字段验证
          'enabled': True,
          'dict_id': 24,
          'type_field': 'medinsType',      # 字段1
          'type_name_field': 'medinsTypeName'  # 字段2
      },
      'custom_function': 'clean_fuwu_pharmacy_data'  # 自定义清洗函数
  }
  ```

#### 2. 字段级别配置 (`get_field_cleaning_rules`)
- **适用场景**：单个字段的专门清洗
- **配置结构**：`{表名: {字段名: {策略名: 配置}}}`
- **执行时机**：在表级别配置之后执行
- **配置文件**：`transfrom/tasks/spider/{province}/pipeline/config/data_cleaning_config.py`
- **典型用例**：
  ```python
  'spider_fuwu_retail_pharmacy': {
      'addr': {
          'fullwidth_to_halfwidth': {    # 全角转半角
              'enabled': True
          },
          'address_cleaning': {          # 地址清洗
              'enabled': True,
              'min_length': 200
          },
          'custom_function': 'clean_addr_field'  # 字段级自定义函数
      },
      'tel': {
          'phone_cleaning_advanced': {   # 电话清洗
              'enabled': True
          }
      }
  }
  ```

#### 3. 自定义清洗函数配置 (`get_custom_cleaning_functions`)
- **适用场景**：复杂的业务逻辑清洗、多字段联合处理
- **配置结构**：`{函数名: 函数对象}`
- **调用方式**：通过表级别或字段级别的 `custom_function` 配置调用
- **配置文件**：`transfrom/tasks/spider/{province}/pipeline/config/data_cleaning_config.py`
- **典型用例**：
  ```python
  def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
      return {
          'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data,
          'clean_addr_field': self._clean_addr_field,
          'validate_medins_type': self._validate_medins_type
      }
  ```

## 🔧 配置使用指南

### 配置执行顺序和优先级

数据清洗系统按以下顺序执行：
1. **表级别清洗** → 2. **字段级别清洗** → 3. **通用清洗**

```python
# ETL流程中的执行顺序
def _apply_configurable_cleaning(df, source_table, province):
    # 1. 获取配置
    table_rules = get_table_cleaning_rules(province, source_table)
    field_rules = get_field_cleaning_rules(province, source_table)
    custom_functions = get_custom_cleaning_functions(province)

    # 2. 先执行表级别清洗（包括自定义函数）
    if table_rules:
        df = self._apply_table_level_cleaning(df, table_rules, custom_functions)

    # 3. 再执行字段级别清洗（包括字段级自定义函数）
    if field_rules:
        df = self._apply_field_level_cleaning(df, field_rules, custom_functions)

    # 4. 最后执行通用清洗
    df = self._apply_general_cleaning(df, source_table, province)

    return df
```

### 自定义清洗函数的调用方式

#### ⚠️ 重要提醒：自定义函数必须通过配置调用

自定义清洗函数**不会自动执行**，必须在表级别或字段级别配置中通过 `custom_function` 显式调用：

```python
# ❌ 错误：仅定义函数，不会被调用
def get_custom_cleaning_functions(self):
    return {
        'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data  # 仅定义，不会执行
    }

# ✅ 正确：在表级别配置中调用
def get_table_cleaning_rules(self):
    return {
        'spider_fuwu_retail_pharmacy': {
            'custom_function': 'clean_fuwu_pharmacy_data'  # 显式调用
        }
    }

# ✅ 或在字段级别配置中调用
def get_field_cleaning_rules(self):
    return {
        'spider_fuwu_retail_pharmacy': {
            'addr': {
                'custom_function': 'clean_addr_field'  # 字段级调用
            }
        }
    }
```

## ⚠️ 配置注意事项

### 0. 一对多字段映射注意事项

#### 🔑 关键原则

1. **避免字段映射冲突**：
   - 当使用数据清洗阶段处理一对多映射时，系统会自动跳过已存在的目标字段映射
   - 确保字段映射配置表中仍然保留相应的映射记录，用于文档和配置完整性

2. **数据获取策略**：
   - 对于需要关联国家标准数据的表（如 `spider_fuwu_service_facilities`），确保ETL查询同时获取国家标准数据和地方数据
   - 在测试模式下，使用特殊查询策略确保包含必要的关联数据

3. **执行顺序理解**：
   ```
   ETL流程: 数据获取 → 数据清洗 → 字段映射 → 数据保存
                     ↑           ↑
                一对多处理    自动跳过已存在字段
   ```

#### ⚠️ 常见陷阱

1. **测试数据不完整**：
   ```python
   # ❌ 错误：测试模式下只获取前1000条数据，可能缺少关联数据
   source_data = get_source_data(limit=1000)  # 可能只有地方数据，缺少国家标准数据

   # ✅ 正确：确保获取完整的关联数据
   # 在 etl_base.py 中实现特殊查询策略
   if source_table == 'spider_fuwu_service_facilities' and limit:
       national_data = get_national_data()  # 获取国家标准数据
       local_data = get_local_data(limit)   # 获取地方数据
       source_data = concat([national_data, local_data])
   ```

2. **字段映射覆盖问题**：
   ```python
   # ❌ 错误：数据清洗阶段设置了正确值，但字段映射阶段被覆盖
   # 数据清洗阶段
   df['name'] = get_name_from_national_data()  # 正确的关联值

   # 字段映射阶段（如果没有特殊处理）
   df['name'] = df['servitemName']  # 覆盖了正确值！

   # ✅ 正确：字段映射阶段检测并跳过已存在字段
   if target_field in source_df.columns:
       result_df[target_field] = source_df[target_field]  # 保留清洗阶段的值
   ```

3. **关联数据缺失**：
   ```python
   # ❌ 错误：没有验证关联数据是否存在
   national_mapping = {}  # 空的映射字典
   df['name'] = df['code'].map(national_mapping)  # 全部返回NaN

   # ✅ 正确：验证关联数据并记录日志
   if not national_data.empty:
       national_mapping = create_mapping(national_data)
       logger.info(f"获取到 {len(national_mapping)} 条国家标准数据用于关联")
   else:
       logger.warning("未获取到国家标准数据，name字段关联将失败")
   ```

#### 🛠️ 调试技巧

1. **验证数据获取**：
   ```python
   # 检查是否获取到完整数据
   logger.info(f"总数据量: {len(df)}")
   logger.info(f"国家标准数据: {len(df[df['admdvs'] == '100000'])}")
   logger.info(f"地方数据: {len(df[df['admdvs'] != '100000'])}")
   ```

2. **验证字段处理**：
   ```python
   # 检查一对多映射结果
   logger.info(f"code字段样本: {df['code'].head().tolist()}")
   logger.info(f"charge_item_code字段样本: {df['charge_item_code'].head().tolist()}")
   logger.info(f"name关联成功率: {(df['name'] != '').sum()}/{len(df)}")
   ```

3. **验证最终结果**：
   ```python
   # 在目标表中验证数据
   from medical.models import MedicalServiceBase
   sample = MedicalServiceBase.objects.first()
   logger.info(f"目标表样本 - code: {sample.code}, name: {sample.name}")
   ```

### 1. 避免重复配置

同一个清洗逻辑不要在表级别和字段级别重复配置：

❌ **错误示例**（重复配置）：
```python
# 表级别
'address_cleaning': {
    'enabled': True,
    'fields': ['addr'],
    'min_length': 200
}

# 字段级别（重复！）
'addr': {
    'address_cleaning': {
        'enabled': True,
        'min_length': 200
    }
}
```

✅ **正确示例**（选择一种配置方式）：
```python
# 方式1：仅使用表级别配置（推荐用于多字段）
'address_cleaning': {
    'enabled': True,
    'fields': ['addr', 'address', 'location'],
    'min_length': 200
}

# 方式2：仅使用字段级别配置（推荐用于单字段）
'addr': {
    'address_cleaning': {
        'enabled': True,
        'min_length': 200
    }
}
```

### 2. 自定义函数调用配置

#### 表级别自定义函数调用
```python
def get_table_cleaning_rules(self):
    return {
        'spider_fuwu_retail_pharmacy': {
            'type_validation': {
                'enabled': True,
                'dict_id': 24
            },
            'custom_function': 'clean_fuwu_pharmacy_data'  # 必须配置此项
        }
    }
```

#### 字段级别自定义函数调用
```python
def get_field_cleaning_rules(self):
    return {
        'spider_fuwu_retail_pharmacy': {
            'addr': {
                'fullwidth_to_halfwidth': {'enabled': False},  # 禁用内置功能
                'custom_function': 'clean_addr_with_fullwidth'  # 使用自定义函数
            }
        }
    }
```

### 3. 配置文件位置和命名规范

#### 文件位置
- **配置文件**：`transfrom/tasks/spider/{province}/pipeline/config/data_cleaning_config.py`
- **注册文件**：`transfrom/tasks/spider/{province}/pipeline/config/__init__.py`

#### 命名规范
- **类名**：`{Province}DataCleaningConfig`（如：`GjybDataCleaningConfig`）
- **实例名**：`{province}_data_cleaning_config`（如：`gjyb_data_cleaning_config`）
- **函数名**：`_clean_{table_type}_data`（如：`_clean_fuwu_pharmacy_data`）

## 🔍 完整配置示例

### 一对多字段映射完整示例

以下是一个完整的一对多字段映射配置示例，展示了如何处理 `medListCodg` 字段映射到 `code` 和 `charge_item_code` 两个目标字段：

#### 方案1：字段映射阶段处理示例

```python
# transfrom/utils/field_mapping.py

def _needs_special_processing(self, source_field, target_field, source_table):
    """判断是否需要特殊处理（针对一对多映射）"""
    special_mappings = {
        'spider_fuwu_service_facilities': {
            'medListCodg': ['code', 'charge_item_code'],  # 一对多映射
            'servitemName': ['name', 'charge_item_name']  # 一对多映射
        }
    }

    if source_table in special_mappings:
        if source_field in special_mappings[source_table]:
            return target_field in special_mappings[source_table][source_field]

    return False

def _apply_special_processing(self, source_series, source_field, target_field, source_table, province):
    """应用特殊处理逻辑"""
    # 针对medListCodg字段的特殊处理
    if source_field == 'medListCodg' and source_table == 'spider_fuwu_service_facilities':
        if target_field == 'code':
            # 提取第一个'-'之前的部分
            return source_series.apply(self._extract_code_part)
        elif target_field == 'charge_item_code':
            # 提取第一个'-'之后的部分
            return source_series.apply(self._extract_charge_code_part)

    return source_series

def _extract_code_part(self, value):
    """提取code部分（第一个'-'之前）"""
    import pandas as pd
    if pd.notna(value) and str(value).strip():
        value_str = str(value).strip()
        if '-' in value_str:
            return value_str.split('-')[0]
        else:
            return value_str
    return ''

def _extract_charge_code_part(self, value):
    """提取charge_item_code部分（第一个'-'之后）"""
    import pandas as pd
    if pd.notna(value) and str(value).strip():
        value_str = str(value).strip()
        if '-' in value_str:
            parts = value_str.split('-', 1)  # 只分割一次
            if len(parts) > 1:
                return parts[1]  # 返回第一个'-'后面的所有内容
            else:
                return ''
        else:
            return ''
    return ''
```

#### 方案2：数据清洗阶段处理示例

```python
# transfrom/tasks/spider/gjyb/pipeline/config/data_cleaning_config.py

def _clean_fuwu_service_facilities_data(self, df):
    """医疗服务项目数据自定义清洗函数（包含一对多字段处理）"""

    # 1. 处理medListCodg字段的一对多映射
    if 'medListCodg' in df.columns:
        # 直接创建目标字段
        df['code'] = df['medListCodg'].apply(self._extract_code_part)
        df['charge_item_code'] = df['medListCodg'].apply(self._extract_charge_code_part)
        logger.info("medListCodg一对多映射处理完成: code和charge_item_code字段已创建")

    # 2. 处理servitemName字段的一对多映射（需要关联国家标准数据）
    if 'servitemName' in df.columns:
        # 获取国家标准数据用于name关联
        national_data = df[df['admdvs'].astype(str) == '100000'].copy()
        if not national_data.empty:
            national_mapping = national_data.set_index('medListCodg')['servitemName'].to_dict()

            # 创建name字段：通过code关联国家标准数据
            def get_name_by_code(medListCodg_value):
                if pd.notna(medListCodg_value) and str(medListCodg_value).strip():
                    code = str(medListCodg_value).split('-')[0] if '-' in str(medListCodg_value) else str(medListCodg_value)
                    return national_mapping.get(code, '')
                return ''

            df['name'] = df['medListCodg'].apply(get_name_by_code)
            df['charge_item_name'] = df['servitemName'].fillna('')

            logger.info("servitemName一对多映射处理完成: name通过关联获取，charge_item_name直接使用原值")

    return df

def get_table_cleaning_rules(self):
    return {
        'spider_fuwu_service_facilities': {
            'custom_function': 'clean_fuwu_service_facilities_data'  # 调用自定义函数
        }
    }

def get_custom_cleaning_functions(self):
    return {
        'clean_fuwu_service_facilities_data': self._clean_fuwu_service_facilities_data
    }
```

#### 字段映射配置

```python
# medical_field_mapping表中的配置
# 注意：一对多映射时，同一个源字段会有多条映射记录

INSERT INTO medical_field_mapping VALUES
('spider_fuwu_service_facilities', 'medical_service_base', 'medListCodg', 'code', '', '医疗服务编码'),
('spider_fuwu_service_facilities', 'medical_service_base', 'medListCodg', 'charge_item_code', '', '收费项目编码'),
('spider_fuwu_service_facilities', 'medical_service_base', 'servitemName', 'name', '', '服务项目名称'),
('spider_fuwu_service_facilities', 'medical_service_base', 'servitemName', 'charge_item_name', '', '收费项目名称');
```

### 国家医保局完整配置示例

以下是一个完整的配置示例，展示了如何正确配置表级别、字段级别和自定义清洗函数：

```python
# transfrom/tasks/spider/gjyb/pipeline/config/data_cleaning_config.py

from transfrom.utils.data_cleaning_config import BaseDataCleaningConfig
from transfrom.utils.data_cleaning_config import CommonCleaningFunctions
from typing import Dict, Callable
import logging

logger = logging.getLogger(__name__)

class GjybDataCleaningConfig(BaseDataCleaningConfig):
    """国家医保局数据清洗配置"""

    def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
        """表级别清洗规则配置"""
        return {
            'spider_fuwu_fixed_hospital': {
                'type_validation': {
                    'enabled': True,
                    'dict_id': 24,
                    'type_field': 'medinsType',
                    'type_name_field': 'medinsTypeName'
                }
                # 注意：医院不需要自定义函数，使用字段级别清洗即可
            },
            'spider_fuwu_retail_pharmacy': {
                'type_validation': {
                    'enabled': True,
                    'dict_id': 24,
                    'type_field': 'medinsType',
                    'type_name_field': 'medinsTypeName'
                },
                'custom_function': 'clean_fuwu_pharmacy_data'  # 🔑 关键：调用自定义函数
            }
        }

    def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
        """字段级别清洗规则配置"""
        return {
            'spider_fuwu_fixed_hospital': {
                'addr': {
                    'fullwidth_to_halfwidth': {'enabled': True},    # 启用全角转半角
                    'address_cleaning': {'enabled': True, 'min_length': 200}
                }
            },
            'spider_fuwu_retail_pharmacy': {
                'addr': {
                    'fullwidth_to_halfwidth': {'enabled': False},   # 🔑 关键：禁用，使用自定义函数
                    'address_cleaning': {'enabled': True, 'min_length': 200}
                },
                'tel': {
                    'phone_cleaning_advanced': {'enabled': True}
                }
            }
        }

    def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
        """自定义清洗函数配置"""
        return {
            'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data,
            'validate_medins_type': self._validate_medins_type
        }

    def _clean_fuwu_pharmacy_data(self, df):
        """药店数据自定义清洗函数"""
        if 'addr' in df.columns:
            # 先进行地址基础清洗
            df['addr'] = df['addr'].apply(
                lambda x: CommonCleaningFunctions.clean_address_basic(x)
                if isinstance(x, str) and len(x) > 200 else x
            )

            # 再进行全角转半角（这里是自定义逻辑）
            df['addr'] = df['addr'].apply(
                lambda x: CommonCleaningFunctions.convert_fullwidth_to_halfwidth(x)
                if isinstance(x, str) else x
            )
            print("addr字段清洗完成: 已经从全角转为半角")

        return df

# 创建配置实例
gjyb_data_cleaning_config = GjybDataCleaningConfig()
```

### 配置注册示例

```python
# transfrom/tasks/spider/gjyb/pipeline/config/__init__.py

from transfrom.utils.data_cleaning_config import register_province_cleaning_config
from .data_cleaning_config import gjyb_data_cleaning_config

# 注册数据清洗规则配置
register_province_cleaning_config('gjyb', gjyb_data_cleaning_config)
```

## ❓ 常见问题解答

### Q1: 为什么我的自定义清洗函数没有被执行？

**A:** 最常见的原因是没有在表级别或字段级别配置中通过 `custom_function` 调用自定义函数。

```python
# ❌ 错误：仅定义函数，不会被调用
def get_custom_cleaning_functions(self):
    return {
        'my_custom_function': self._my_custom_function  # 仅定义，不会执行
    }

# ✅ 正确：在表级别配置中调用
def get_table_cleaning_rules(self):
    return {
        'my_table': {
            'custom_function': 'my_custom_function'  # 必须添加此配置
        }
    }
```

### Q2: 字段级别清洗和自定义函数中的相同逻辑，哪个会执行？

**A:** 按执行顺序，表级别清洗（包括自定义函数）先执行，然后是字段级别清洗。如果你想让自定义函数中的逻辑生效，应该禁用字段级别的相同功能：

```python
# 在字段级别禁用内置功能
'addr': {
    'fullwidth_to_halfwidth': {'enabled': False},  # 禁用内置功能
}

# 在表级别调用自定义函数
'custom_function': 'my_custom_function'  # 自定义函数中包含全角转半角逻辑
```

### Q3: 如何测试我的清洗配置是否正确？

**A:** 可以创建测试脚本验证：

```python
from transfrom.utils.data_cleaning import clean_source_data
import pandas as pd

# 创建测试数据
test_data = pd.DataFrame({
    'addr': ['测试地址１２３'],
    'tel': ['０１０－１２３４５６７８']
})

# 测试清洗
cleaned_data = clean_source_data(test_data, 'spider_fuwu_retail_pharmacy', province='gjyb')
print("清洗前:", test_data['addr'].iloc[0])
print("清洗后:", cleaned_data['addr'].iloc[0])
```

### Q4: 数据比对结果为"新增 0 条, 更新 0 条, 删除 0 条"，清洗函数还会执行吗？

**A:** 会执行！数据清洗在ETL流程的第2步执行，在数据比对之前。执行顺序是：
1. 获取源数据
2. **数据清洗** ← 在这里执行
3. 数据转换
4. 数据比对和保存

### Q5: 如何查看清洗函数的执行日志？

**A:** 在自定义清洗函数中添加日志输出：

```python
def _clean_fuwu_pharmacy_data(self, df):
    logger.info(f"开始执行药店数据清洗，数据量: {len(df)}")

    if 'addr' in df.columns:
        before_count = df['addr'].apply(lambda x: '１２３' in str(x)).sum()
        # 执行清洗逻辑...
        after_count = df['addr'].apply(lambda x: '123' in str(x)).sum()
        logger.info(f"全角转半角处理: {before_count} → {after_count}")

    return df
```

### Q6: 如何为新省份添加清洗配置？

**A:** 按以下步骤操作：

1. **创建配置文件**：`transfrom/tasks/spider/{province}/pipeline/config/data_cleaning_config.py`
2. **继承基类**：`class {Province}DataCleaningConfig(BaseDataCleaningConfig)`
3. **实现三个方法**：`get_table_cleaning_rules`、`get_field_cleaning_rules`、`get_custom_cleaning_functions`
4. **注册配置**：在 `__init__.py` 中调用 `register_province_cleaning_config`

## 📋 配置检查清单

在配置数据清洗规则时，请按以下清单检查：

### ✅ 基础配置检查
- [ ] 配置文件位置正确：`transfrom/tasks/spider/{province}/pipeline/config/data_cleaning_config.py`
- [ ] 类名符合规范：`{Province}DataCleaningConfig`
- [ ] 继承了基类：`BaseDataCleaningConfig`
- [ ] 实现了三个必需方法：`get_table_cleaning_rules`、`get_field_cleaning_rules`、`get_custom_cleaning_functions`
- [ ] 在 `__init__.py` 中正确注册了配置

### ✅ 自定义函数配置检查
- [ ] 自定义函数已在 `get_custom_cleaning_functions` 中定义
- [ ] 在表级别或字段级别配置中通过 `custom_function` 调用了自定义函数
- [ ] 自定义函数名称在配置中拼写正确
- [ ] 自定义函数返回了处理后的 DataFrame

### ✅ 避免冲突检查
- [ ] 没有在表级别和字段级别重复配置相同的清洗逻辑
- [ ] 如果使用自定义函数处理某个功能，已禁用字段级别的相同功能
- [ ] 配置的优先级符合预期（表级别 → 字段级别 → 通用清洗）

### ✅ 测试验证检查
- [ ] 创建了测试数据验证清洗效果
- [ ] 检查了清洗函数的日志输出
- [ ] 验证了清洗前后数据的变化
- [ ] 确认清洗函数在ETL流程中被正确调用

## 🎯 最佳实践总结

1. **优先使用内置清洗功能**：对于常见的清洗需求（如全角转半角、电话清洗等），优先使用内置的字段级别清洗功能。

2. **自定义函数用于复杂逻辑**：只有在需要复杂业务逻辑或多字段联合处理时，才使用自定义清洗函数。

3. **避免重复配置**：同一个清洗逻辑不要在多个层级重复配置，选择最合适的层级进行配置。

4. **测试驱动配置**：在配置清洗规则时，先编写测试用例，确保配置的正确性。

5. **日志记录**：在自定义清洗函数中添加适当的日志记录，便于调试和监控。

6. **文档更新**：添加新的清洗规则时，及时更新相关文档和注释。

---

## 📞 技术支持

如有问题，请联系数据工程团队或参考以下资源：

### 核心框架文档
- 字段处理策略框架: `transfrom/utils/field_config.py`
- 具体清洗规则框架: `transfrom/utils/data_cleaning_config.py`
- 清洗功能实现: `transfrom/utils/data_cleaning.py`

### 实际配置示例
- 国家医保局配置: `transfrom/tasks/spider/gjyb/pipeline/config/`
- 江苏医保配置: `transfrom/tasks/spider/jsyb/pipeline/config/`
- 南京医保配置: `transfrom/tasks/spider/njyb/pipeline/config/`

### 使用示例
- ETL流程实现: `transfrom/tasks/spider/gjyb/pipeline/fuwu_etl_mapping.py`
- ETL基础框架: `transfrom/utils/etl_base.py`

### 配置指南
- 本文档: `transfrom/utils/files/数据清洗配置说明.md`
- 字段配置指南: `transfrom/utils/files/field_config_README.md`