import logging
from datetime import datetime

import pandas as pd
import os
import pymysql
import idna
import warnings
from dw import settings
from django.core.mail import EmailMessage
from transfrom.utils.utils import  query_sql
from transfrom.utils.date import get_week_range

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


TO = ['<EMAIL>']
CC= ['<EMAIL>']

DB = settings.DATABASES['jkx']

def get_connection():
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                                user=DB["USER"],
                                password=DB["PASSWORD"], database=DB["NAME"])
    return conn

#防止重复退单、支付成功导致数据不准，需要将已经发送的单子存下来，再次发送时，只发送未发送过的单子
def get_rft():
    """
    获取热敷贴活动的数据
    """
    sql_all_user = """
    SELECT
	 distinct u1.id user_id,u1.mobile
FROM
	`order` o
	JOIN order_item oi ON o.id = oi.order_id
	JOIN order_item_client oic ON oic.order_item_id = oi.id and oic.is_return = 0
	join `user` u1 on u1.id = o.buyer_id
	join `user` u2 on u2.id = oic.client_id
		JOIN product_set ps ON ps.id = o.product_set_id 
	AND ps.CODE = 'ninghuibaoV5'
	JOIN product p ON oi.product_id = p.id and p.main=1
	where 	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
	union
	SELECT
	 	 distinct u2.id user_id,u2.mobile
FROM
	`order` o
	JOIN order_item oi ON o.id = oi.order_id
	JOIN order_item_client oic ON oic.order_item_id = oi.id and oic.is_return = 0
	join `user` u1 on u1.id = o.buyer_id
	join `user` u2 on u2.id = oic.client_id
		JOIN product_set ps ON ps.id = o.product_set_id 
	AND ps.CODE = 'ninghuibaoV5'
	JOIN product p ON oi.product_id = p.id and p.main=1
	where 	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
    """

    # 所有领取的用户
    sql = """
    SELECT DISTINCT
    au.user_id,
        au.user_name 用户,
        au.phone 手机号,
        p.NAME 省份,
        c.NAME 城市,
        d.NAME 地区,
        au.address 详细地址,
        case when au.receive_type ='HomeDelivery' then '送药上门' else '普通快递' end as 方式
        from activity a
        JOIN activity_user au ON a.id = au.activity_id
        JOIN district d ON au.district_id = d.id
        JOIN province p ON p.id = au.province_id
        JOIN city c ON c.id = au.city_id 
    WHERE
        a.id = '1710746316084089870' 
        AND au.delete_time IS NULL
    """
    # 读取已经发送名单，跟数据库查询的名单先过滤
    path = os.path.join(os.path.dirname(__file__), f'已经发送名单.xlsx')
    df_has_send = pd.read_excel(path)
    df_has_send['领取状态'].fillna('1',inplace=True)
    df_all_send = pd.read_sql(sql,get_connection())
    df_not_send = pd.merge(df_all_send,df_has_send[['user_id','领取状态']],how='left',on=['user_id'])
    df_not_send = df_not_send[df_not_send['领取状态'].isnull()]
    df_not_send = df_not_send.drop_duplicates(subset=['user_id'])

    df_all_user = pd.read_sql(sql_all_user, get_connection())
    # 看梭鱼没有发的单子，是否在所有的有效名单中
    df_not_send = pd.merge(df_not_send, df_all_user, how='inner', on=['user_id'])
    df = pd.concat([df_not_send.drop(columns=['mobile']), df_has_send])
    df['领取时间'].fillna(datetime.now().strftime('%Y-%m-%d'), inplace=True)
    df_not_send.drop(columns=['user_id','mobile','领取状态'],inplace=True)
    df['user_id'] = df['user_id'].astype(str)
    df['手机号'] = df['手机号'].astype(str)
    df['领取状态'] = '1'
    return df_not_send,df

def email_nhb_rft():
    date = datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'宁惠保5期热敷贴活动新增名单_{date}.xlsx')
    df_not_send,df = get_rft()
    has_send_path = os.path.join(os.path.dirname(__file__), f'已经发送名单.xlsx')
    df.to_excel(has_send_path,index=False)
    df_not_send.to_excel(path, index=False)

    mail = EmailMessage(
        subject=f'新增统计_宁惠保5期热敷贴活动新增名单_{date}',
        body='见附件',
        to=TO,
        cc=CC
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)

