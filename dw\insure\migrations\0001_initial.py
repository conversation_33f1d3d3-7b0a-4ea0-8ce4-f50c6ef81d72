# Generated by Django 4.2.13 on 2024-07-19 12:37

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="InsureAgent",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="发布时间"),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="代理人名称"
                    ),
                ),
                (
                    "employee_count",
                    models.IntegerField(blank=True, null=True, verbose_name="代理人数量"),
                ),
                (
                    "average_count",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="代理人人均单量",
                    ),
                ),
                (
                    "total_count",
                    models.IntegerField(blank=True, null=True, verbose_name="销售总量"),
                ),
                (
                    "personal_count",
                    models.IntegerField(blank=True, null=True, verbose_name="个单数量"),
                ),
                (
                    "group_count",
                    models.IntegerField(blank=True, null=True, verbose_name="团单数量"),
                ),
                (
                    "insure_ratio",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="销售占比",
                    ),
                ),
                (
                    "position",
                    models.IntegerField(blank=True, null=True, verbose_name="销售排名"),
                ),
                (
                    "target",
                    models.IntegerField(blank=True, null=True, verbose_name="销售目标数量"),
                ),
                (
                    "target_ratio",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="销售目标完成率",
                    ),
                ),
                (
                    "today_count",
                    models.IntegerField(blank=True, null=True, verbose_name="当日销售量"),
                ),
                (
                    "yesterday_count",
                    models.IntegerField(blank=True, null=True, verbose_name="昨日销售量"),
                ),
            ],
            options={
                "verbose_name": "健康险代理人参保信息",
                "verbose_name_plural": "健康险代理人参保信息",
                "db_table": "insure_agent",
            },
        ),
        migrations.CreateModel(
            name="InsureAgeSex",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="发布时间"),
                ),
                (
                    "sex",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="性别"
                    ),
                ),
                (
                    "age_distribution",
                    models.CharField(
                        blank=True, max_length=50, null=True, verbose_name="年龄分布"
                    ),
                ),
                (
                    "value",
                    models.IntegerField(blank=True, null=True, verbose_name="结果值"),
                ),
            ],
            options={
                "verbose_name": "健康险年龄性别分布表",
                "verbose_name_plural": "健康险年龄性别分布表",
                "db_table": "insure_age_sex",
            },
        ),
        migrations.CreateModel(
            name="InsureArea",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="发布时间"),
                ),
                (
                    "name",
                    models.CharField(
                        blank=True, max_length=64, null=True, verbose_name="地区名称"
                    ),
                ),
                (
                    "total_count",
                    models.IntegerField(blank=True, null=True, verbose_name="参保总数"),
                ),
                (
                    "ratio",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="参保占比",
                    ),
                ),
                (
                    "insure_ratio",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="参保率;健康险与居民医保的比率",
                    ),
                ),
                (
                    "position",
                    models.IntegerField(blank=True, null=True, verbose_name="排名"),
                ),
                (
                    "today_count",
                    models.IntegerField(blank=True, null=True, verbose_name="今日参保数量"),
                ),
                (
                    "yesterday_count",
                    models.IntegerField(blank=True, null=True, verbose_name="昨日参保数量"),
                ),
            ],
            options={
                "verbose_name": "健康险地区参保信息",
                "verbose_name_plural": "健康险地区参保信息",
                "db_table": "insure_area",
            },
        ),
        migrations.CreateModel(
            name="InsureOnline",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "update_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "product_set_code",
                    models.CharField(
                        blank=True, max_length=32, null=True, verbose_name="产品集编码"
                    ),
                ),
                (
                    "publish_time",
                    models.DateTimeField(blank=True, null=True, verbose_name="发布时间"),
                ),
                (
                    "channel_name",
                    models.CharField(
                        blank=True, max_length=128, null=True, verbose_name="渠道名称"
                    ),
                ),
                (
                    "total_count",
                    models.IntegerField(blank=True, null=True, verbose_name="销售总量"),
                ),
                (
                    "insure_ratio",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="销售占比",
                    ),
                ),
                (
                    "position",
                    models.IntegerField(blank=True, null=True, verbose_name="销售排名"),
                ),
                (
                    "target",
                    models.IntegerField(blank=True, null=True, verbose_name="销售目标数量"),
                ),
                (
                    "target_ratio",
                    models.DecimalField(
                        blank=True,
                        decimal_places=4,
                        max_digits=20,
                        null=True,
                        verbose_name="销售目标完成率",
                    ),
                ),
                (
                    "today_count",
                    models.IntegerField(blank=True, null=True, verbose_name="当日销售量"),
                ),
                (
                    "yesterday_count",
                    models.IntegerField(blank=True, null=True, verbose_name="昨日销售量"),
                ),
            ],
            options={
                "verbose_name": "健康险线上参保信息",
                "verbose_name_plural": "健康险线上参保信息",
                "db_table": "insure_online",
            },
        ),
        migrations.AddConstraint(
            model_name="insureonline",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "publish_time", "channel_name"),
                name="unique_insure_online_combination",
            ),
        ),
        migrations.AddConstraint(
            model_name="insurearea",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "publish_time", "name"),
                name="unique_insure_area_combination",
            ),
        ),
        migrations.AddConstraint(
            model_name="insureagesex",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "publish_time", "sex", "age_distribution"),
                name="unique_insure_age_sex_combination",
            ),
        ),
        migrations.AddConstraint(
            model_name="insureagent",
            constraint=models.UniqueConstraint(
                fields=("product_set_code", "publish_time", "name"),
                name="unique_insure_agent_combination",
            ),
        ),
    ]
