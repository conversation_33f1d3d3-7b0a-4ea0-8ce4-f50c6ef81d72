"""
测试时间戳默认值行为的Django管理命令
"""

from django.core.management.base import BaseCommand, CommandError
from common.test_timestamp_behavior import TimestampBehaviorTester


class Command(BaseCommand):
    help = '测试时间戳默认值的行为是否正确'

    def add_arguments(self, parser):
        parser.add_argument(
            '--database',
            type=str,
            default='default',
            help='数据库别名（默认：default）'
        )

    def handle(self, *args, **options):
        database_alias = options['database']

        self.stdout.write("开始测试时间戳默认值行为...")

        try:
            tester = TimestampBehaviorTester(database_alias)
            success = tester.run_full_test()

            if success:
                self.stdout.write(
                    self.style.SUCCESS("[SUCCESS] 时间戳行为测试通过！")
                )
            else:
                self.stdout.write(
                    self.style.ERROR("[FAILED] 时间戳行为测试失败！")
                )

        except Exception as e:
            raise CommandError(f"测试执行失败: {str(e)}")
