import pandas as pd
import datetime


def get_shifted_dates(start_date, end_date):
    """
    获取前一年的日期
    :param start_date: 开始日期
    :param end_date: 结束日期
    :return:
    """
    if start_date is not None:
        start_date_shift = (pd.to_datetime(start_date) - pd.Timedelta(days=365)).strftime('%Y-%m-%d')
    else:
        start_date_shift = None

    if end_date is not None:
        end_date_shift = (pd.to_datetime(end_date) - pd.Timedelta(days=365)).strftime('%Y-%m-%d')
    else:
        end_date_shift = (datetime.datetime.now() - pd.Timedelta(days=365)).strftime('%Y-%m-%d')

    return start_date_shift, end_date_shift


def get_week_of_month(date_str):
    """
    获取指定日期所在月份的第几周
    :param date_str: 日期字符串
    :return: 第几周
    """
    date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
    first_day = date.replace(day=1)
    day_of_month = date.day
    day_of_week = first_day.weekday()
    week_of_month = (day_of_month + day_of_week) // 7 + 1
    return week_of_month



def get_week_range(date_str):
    """
    获取指定日期所在周的开始日期和结束日期
    :param date_str: 日期字符串
    :return: 开始日期和结束日期
    """
    date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
    start_of_week = date - datetime.timedelta(days=date.weekday())
    end_of_week = date
    return start_of_week, end_of_week


def get_this_monday():
    """
    获取本周一日期
    :return:
    """
    # 获取当前系统日期
    today = datetime.date.today()
    # 计算今天距离本周一的偏移天数
    # 周一是0，周二是1，以此类推，所以如果今天不是周一，则需要减去今天对应的偏移天数
    days_to_monday = today.weekday()
    # 计算本周一的日期
    monday = today - datetime.timedelta(days=days_to_monday)
    return monday


def get_empty_date(end_date, channel_list=None):
    """
    获取空白日期数据，获取年初到截止日的空数据集，含圆心、宸汐两个审核渠道
    :param end_date: 截止日期
    :param channel_list: 审核渠道列表
    :return:
    """
    # 计算周报数据
    if channel_list is None:
        channel_list = ['圆心', '宸汐']
    year, week_number, _ = end_date.isocalendar()
    # 创建日期范围
    date_range_data = pd.date_range(start=f"{year}-01-01", end=end_date, freq='W-MON')
    # 创建 DataFrame
    df_week_data = pd.DataFrame({'日期': date_range_data, '第几周': [d.isocalendar()[1] for d in date_range_data]})
    df_week_data['merge_key'] = 0

    # 获取超时的渠道名称——这边定死了渠道，防止当周数据为0，导致最新数据有误
    df_channel_name = pd.DataFrame({'渠道': channel_list}, index=[0, 1])
    df_channel_name.reset_index(drop=True, inplace=True)
    df_channel_name['merge_key'] = 0
    df_week_data = pd.merge(df_week_data[['第几周', 'merge_key']], df_channel_name, on='merge_key', how='outer')
    return df_week_data