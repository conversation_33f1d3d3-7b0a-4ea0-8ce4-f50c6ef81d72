#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏医保局ETL映射主程序
处理从spider_jsyb_service_facilities到medical_service_entity的数据转换
"""

import os
import sys
import logging
from typing import Dict, List, Optional
import warnings

warnings.filterwarnings("ignore")

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 从 dw/transfrom/tasks/spider/jsyb/pipeline 向上5级到达 dw 目录
project_root = os.path.abspath(os.path.join(current_dir, '../../../../../'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dw.settings')

import django
django.setup()

from transfrom.tasks.spider.jsyb.pipeline.config.table_configs import get_jsyb_table_config, get_all_jsyb_table_configs
from transfrom.tasks.spider.jsyb.pipeline.config.data_cleaning_config import jsyb_data_cleaning_config
from transfrom.tasks.spider.jsyb.pipeline.config.field_config import jsyb_field_config
from transfrom.utils.etl_base import ETLProcessor

# 配置日志
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('jsyb_etl.log'),
#         logging.StreamHandler()
#     ]
# )

logger = logging.getLogger(__name__)


def execute_jsyb_etl_transform(
    source_table: str,
    target_table: str,
    unique_fields: List[str],
    operation_mode: str = 'upsert',
    batch_size: int = 1000,
    target_where_clause: Optional[str] = None,
    limit: Optional[int] = None
) -> bool:
    """
    执行江苏医保局ETL转换

    Args:
        source_table: 源表名
        target_table: 目标表名
        unique_fields: 唯一性字段列表
        operation_mode: 操作模式 ('insert', 'update', 'upsert')
        batch_size: 批处理大小
        target_where_clause: 目标表过滤条件
        limit: 限制处理记录数

    Returns:
        bool: 执行是否成功
    """
    try:
        logger.info(f"开始执行江苏医保局ETL转换: {source_table} → {target_table}")

        # 设置输出目录
        import os
        current_file_dir = os.path.dirname(os.path.abspath(__file__))
        output_dir = os.path.join(current_file_dir, "output")

        # 创建ETL处理器
        etl_processor = ETLProcessor(
            output_dir=output_dir,
            excel_save_operations=['update', 'delete', 'insert']  # 保存所有操作的Excel文件
        )

        # 执行ETL转换
        stats = etl_processor.process_etl(
            source_table=source_table,
            target_table=target_table,
            unique_fields=unique_fields,
            operation_mode=operation_mode,
            batch_size=batch_size,
            target_where_clause=target_where_clause,
            limit=limit,
            province='jsyb'  # 指定为江苏医保局
        )

        # 检查执行结果
        success = stats.get('total_processed', 0) >= 0  # 只要没有异常就认为成功

        if success:
            logger.info(f"江苏医保局ETL转换成功完成: {source_table} → {target_table}")
            logger.info(f"处理统计: {stats}")
        else:
            logger.error(f"江苏医保局ETL转换失败: {source_table} → {target_table}")

        return success

    except Exception as e:
        logger.error(f"执行江苏医保局ETL转换时出错: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


def run_jsyb_service_facilities_etl():
    """
    运行江苏医疗服务项目ETL
    """
    config = get_jsyb_table_config("江苏医疗服务项目")
    if not config:
        logger.error("未找到江苏医疗服务项目的配置")
        return False

    return execute_jsyb_etl_transform(
        source_table=config["source_table"],
        target_table=config["target_table"],
        unique_fields=config["unique_fields"],
        operation_mode='upsert',
        target_where_clause=config.get("target_where_clause")
    )


def run_jsyb_drug_etl():
    """
    运行江苏医保药品ETL
    """
    config = get_jsyb_table_config("江苏医保药品")
    if not config:
        logger.error("未找到江苏医保药品的配置")
        return False

    return execute_jsyb_etl_transform(
        source_table=config["source_table"],
        target_table=config["target_table"],
        unique_fields=config["unique_fields"],
        operation_mode='upsert',
        target_where_clause=config.get("target_where_clause")
    )


def main():
    """
    主函数 - 执行所有江苏医保局ETL任务
    """
    logger.info("开始执行江苏医保局ETL任务")

    # 获取所有配置
    all_configs = get_all_jsyb_table_configs()

    success_count = 0
    total_count = len(all_configs)

    for table_name, config in all_configs.items():
        logger.info(f"处理表: {table_name}")

        success = execute_jsyb_etl_transform(
            source_table=config["source_table"],
            target_table=config["target_table"],
            unique_fields=config["unique_fields"],
            operation_mode='upsert',
            target_where_clause=config.get("target_where_clause")
        )

        if success:
            success_count += 1
            logger.info(f"[成功] {table_name} 处理成功")
        else:
            logger.error(f"[失败] {table_name} 处理失败")

    logger.info(f"江苏医保局ETL任务完成: {success_count}/{total_count} 个表处理成功")
    return success_count == total_count


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)