{% extends "public/data_dictionary/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
.search-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
}

.search-box {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.search-input {
    font-size: 18px;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    width: 100%;
    transition: border-color 0.3s;
}

.search-input:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.search-filters {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
}

.result-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.result-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #333;
}

.result-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.result-item:hover {
    background: #f8f9fa;
}

.result-item:last-child {
    border-bottom: none;
}

.result-title {
    font-weight: 500;
    color: #1890ff;
    text-decoration: none;
    font-size: 16px;
}

.result-title:hover {
    text-decoration: underline;
}

.result-meta {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

.result-description {
    color: #333;
    margin-top: 8px;
    font-size: 14px;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.search-stats {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
}

.highlight {
    background: #fff3cd;
    padding: 1px 3px;
    border-radius: 2px;
}
</style>
{% endblock %}

{% block content %}
<div class="search-container">
    <!-- 搜索框 -->
    <div class="search-box">
        <form method="get" id="searchForm">
            <input type="text" name="q" value="{{ results.query }}" 
                   class="search-input" placeholder="搜索表名、字段名或注释..." 
                   autocomplete="off" autofocus>
            
            <div class="search-filters">
                <label>搜索范围:</label>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="type" value="all" 
                           {% if search_type == 'all' %}checked{% endif %} id="searchAll">
                    <label class="form-check-label" for="searchAll">全部</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="type" value="tables" 
                           {% if search_type == 'tables' %}checked{% endif %} id="searchTables">
                    <label class="form-check-label" for="searchTables">表</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="type" value="columns" 
                           {% if search_type == 'columns' %}checked{% endif %} id="searchColumns">
                    <label class="form-check-label" for="searchColumns">字段</label>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
            </div>
        </form>
    </div>

    <!-- 搜索结果 -->
    {% if results.query %}
        {% if results.total_count > 0 %}
            <div class="search-stats">
                找到 <strong>{{ results.total_count }}</strong> 个结果，关键词: <strong>"{{ results.query }}"</strong>
            </div>

            <!-- 表搜索结果 -->
            {% if results.tables %}
            <div class="result-section">
                <div class="result-header">
                    <i class="fas fa-table"></i> 表 ({{ results.tables|length }})
                </div>
                {% for table in results.tables %}
                <div class="result-item">
                    <a href="{% url 'data_dictionary:table_detail' table.id %}" class="result-title">
                        {{ table.database_name }}.{{ table.name }}
                    </a>
                    <div class="result-meta">
                        <span class="badge bg-secondary">{{ table.type }}</span>
                        {% if table.data_source %}
                            <span class="text-muted">• 数据来源: {{ table.data_source }}</span>
                        {% endif %}
                    </div>
                    {% if table.comment %}
                    <div class="result-description">
                        {{ table.comment }}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- 字段搜索结果 -->
            {% if results.columns %}
            <div class="result-section">
                <div class="result-header">
                    <i class="fas fa-columns"></i> 字段 ({{ results.columns|length }})
                </div>
                {% for column in results.columns %}
                <div class="result-item">
                    <a href="{% url 'data_dictionary:table_detail' column.table_id %}" class="result-title">
                        {{ column.database_name }}.{{ column.table_name }}.{{ column.name }}
                    </a>
                    <div class="result-meta">
                        <span class="badge bg-info">{{ column.data_type }}</span>
                        {% if column.is_primary_key %}
                            <span class="badge bg-warning">主键</span>
                        {% endif %}
                        {% if column.is_unique %}
                            <span class="badge bg-success">唯一</span>
                        {% endif %}
                    </div>
                    {% if column.comment %}
                    <div class="result-description">
                        {{ column.comment }}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; color: #ddd;"></i>
                <h3>未找到相关结果</h3>
                <p>尝试使用不同的关键词或检查拼写</p>
            </div>
        {% endif %}
    {% else %}
        <div class="no-results">
            <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; color: #ddd;"></i>
            <h3>全局搜索</h3>
            <p>在所有数据库、表和字段中搜索</p>
            <div class="mt-3">
                <small class="text-muted">
                    支持搜索表名、字段名、注释等内容
                </small>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 搜索框自动完成
    var searchInput = $('.search-input');
    var searchTimeout;
    
    // 实时搜索建议（可选功能）
    searchInput.on('input', function() {
        clearTimeout(searchTimeout);
        var query = $(this).val().trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                // 这里可以添加实时搜索建议的AJAX请求
                console.log('搜索建议:', query);
            }, 300);
        }
    });
    
    // 搜索类型改变时自动提交
    $('input[name="type"]').change(function() {
        if ($('input[name="q"]').val().trim()) {
            $('#searchForm').submit();
        }
    });
    
    // 键盘快捷键
    $(document).keydown(function(e) {
        // Ctrl+K 聚焦搜索框
        if (e.ctrlKey && e.keyCode === 75) {
            e.preventDefault();
            searchInput.focus().select();
        }
        // ESC 清空搜索框
        if (e.keyCode === 27) {
            searchInput.val('').focus();
        }
    });
    
    // 高亮搜索关键词
    var query = "{{ results.query|escapejs }}";
    if (query) {
        $('.result-title, .result-description').each(function() {
            var text = $(this).html();
            var regex = new RegExp('(' + query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
            var highlighted = text.replace(regex, '<span class="highlight">$1</span>');
            $(this).html(highlighted);
        });
    }
});
</script>
{% endblock %}
