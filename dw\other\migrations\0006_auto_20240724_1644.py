# Generated by Django 3.2.12 on 2024-07-24 16:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0005_rename_preson_num_otherproductmedicaltype_person_num'),
    ]

    operations = [
        migrations.Remove<PERSON>ield(
            model_name='otherproductinsuretype',
            name='free_meidical',
        ),
        migrations.RemoveField(
            model_name='otherproductinsuretype',
            name='jiangsu_basic_medicare',
        ),
        migrations.RemoveField(
            model_name='otherproductinsuretype',
            name='nanjing_basic_medicare',
        ),
        migrations.RemoveField(
            model_name='otherproductinsuretype',
            name='nanjing_new_citizens',
        ),
        migrations.RemoveField(
            model_name='otherproductinsuretype',
            name='other',
        ),
        migrations.AddField(
            model_name='otherproductinsuretype',
            name='person_num',
            field=models.IntegerField(blank=True, null=True, verbose_name='参保人数'),
        ),
        migrations.AddField(
            model_name='otherproductinsuretype',
            name='type',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='人员医保类型'),
        ),
    ]
