# Generated by Django 4.2.1 on 2025-07-25 13:15

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("medical", "0027_rename_category_medicaldrugentity_category_name"),
    ]

    operations = [
        migrations.AlterModelTableComment(
            name="medicalchineseherbaldrugbase",
            table_comment="医保中草药信息基础表",
        ),
        migrations.AlterModelTableComment(
            name="medicaldesignatedproviders",
            table_comment="医保定点医疗服务机构信息表",
        ),
        migrations.AlterModelTableComment(
            name="medicaldrugbase",
            table_comment="医保药品信息基础表",
        ),
        migrations.AlterModelTableComment(
            name="medicaldrugentity",
            table_comment="医保药品省市实体信息表",
        ),
        migrations.AlterModelTableComment(
            name="medicalfieldmapping",
            table_comment="医保字段映射表",
        ),
        migrations.AlterModelTableComment(
            name="medicalmedicinediagnosis",
            table_comment="疾病诊断手术操作信息表",
        ),
        migrations.AlterModelTableComment(
            name="medicalnationalnegotiateddrug",
            table_comment="国家谈判药品配备机构目录",
        ),
        migrations.AlterModelTableComment(
            name="medicalnationalnegotiateddrugproviders",
            table_comment="国家谈判药品销售机构",
        ),
        migrations.AlterModelTableComment(
            name="medicalselfprepareddrugbase",
            table_comment="医保自制药信息基础表",
        ),
        migrations.AlterModelTableComment(
            name="medicalservicebase",
            table_comment="医疗服务项目分类基础表",
        ),
        migrations.AlterModelTableComment(
            name="medicalserviceentity",
            table_comment="医疗服务项目省市实体表",
        ),
        migrations.AlterModelTableComment(
            name="medicalsuppliesbase",
            table_comment="医保耗材分类基础表",
        ),
        migrations.AlterModelTableComment(
            name="medicalsuppliesentity",
            table_comment="医保耗材省市实体表",
        ),
        migrations.AlterModelTableComment(
            name="medicalsuppliesregistermessage",
            table_comment="医保耗材注册信息表",
        ),
    ]
