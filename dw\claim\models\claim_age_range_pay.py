from django.db import models
from common.models import BaseModel


class ClaimAgeRangePay(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市')
    age_range = models.CharField(max_length=32, blank=True, null=True, verbose_name='年龄分布范围')
    pay_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人次')
    pay_person_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人数')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    pay_avg_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='人均赔付金额')
    pay_amount_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付金额占比')
    insure_person_number = models.IntegerField(blank=True, null=True, verbose_name='投保人数')
    incidence_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='出险率')

    class Meta:
        db_table = 'claim_age_range_pay'
        verbose_name = '理赔-赔付年龄分布情况'
        verbose_name_plural = verbose_name

