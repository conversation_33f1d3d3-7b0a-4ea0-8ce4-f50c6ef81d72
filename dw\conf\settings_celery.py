
###----Celery redis 配置-----###
CELERY_RESULT_BACKEND = 'django-db'  #使用django orm 作为结果存储
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
# 结果序列化方案
CELERY_RESULT_SERIALIZER = 'json'
# 有些情况下可以防止死锁
CELERY_FORCE_EXECV = True
# 任务结果过期时间，秒
CELERY_TASK_RESULT_EXPIRES = 60 * 60 * 24
# 每个worker执行了多少任务就会死掉，默认是无限的
CELERY_WORKER_MAX_TASKS_PER_CHILD = 200
# 允许重启worker
CELERYD_POOL_RESTARTS = True
# 时区配置
CELERY_TIMEZONE = "Asia/Shanghai"
DJANGO_CELERY_BEAT_TZ_AWARE = False
CELERY_ENABLE_UTC = False
CELERYBEAT_SCHEDULER = 'django_celery_beat.schedulers.DatabaseScheduler'