from django.db import models
from common.models import BaseModel


class ClaimAreaPay(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市') # 当is_provincial为1时，city为空
    area_name = models.CharField(max_length=32, blank=True, null=True, verbose_name='地区名称')
    district = models.CharField(max_length=32, blank=True, null=True, verbose_name='区县')
    pay_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人次')
    pay_person_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人数')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    pay_avg_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='人均赔付金额')
    pay_avg_amount_per_case = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='件均赔付金额')
    pay_amount_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付金额占比')
    premium_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='保费')
    insured_person_number = models.IntegerField(blank=True, null=True, verbose_name='参保人数')
    claim_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付率')

    class Meta:
        db_table = 'claim_area_pay'
        verbose_name = '理赔-赔付地区分布'
        verbose_name_plural = verbose_name

