#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国家医保局(gjyb)pipeline配置模块初始化
自动注册配置到全局配置管理器
"""

from transfrom.utils.field_config import register_province_config
from .field_config import gjyb_field_config

# 导入数据清洗配置并注册
from .data_cleaning_config import gjyb_data_cleaning_config
from transfrom.utils.data_cleaning_config import register_province_cleaning_config

# 自动注册国家医保局配置
register_province_config('gjyb', gjyb_field_config)

# 自动注册国家医保局数据清洗配置
register_province_cleaning_config('gjyb', gjyb_data_cleaning_config)
