"""
数据库迁移核心逻辑
提供跨数据库兼容的迁移功能
"""

from django.db import connection, transaction
from django.conf import settings
from .db_utils import DatabaseCompatibilityUtils
from .models import DatabaseMigrationLog
import logging
import traceback
from datetime import datetime

logger = logging.getLogger(__name__)


class DatabaseMigrationManager:
    """数据库迁移管理器"""

    def __init__(self, database_alias='default'):
        self.database_alias = database_alias
        self.db_utils = DatabaseCompatibilityUtils()
        self.db_type = self.db_utils.get_database_type(database_alias)
        self.db_version = self.db_utils.get_database_version(database_alias)

    def execute_migration(self, migration_name, sql_statements, reverse_sql_statements=None):
        """
        执行数据库迁移

        Args:
            migration_name: 迁移名称
            sql_statements: 要执行的SQL语句列表
            reverse_sql_statements: 回滚SQL语句列表

        Returns:
            bool: 执行是否成功
        """
        logger.info(f"开始执行迁移: {migration_name}")
        logger.info(f"数据库类型: {self.db_type}, 版本: {self.db_version}")

        # 确保sql_statements是列表
        if isinstance(sql_statements, str):
            sql_statements = [sql_statements]

        executed_sqls = []

        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    for sql in sql_statements:
                        if sql.strip() and not sql.strip().startswith('--'):
                            logger.info(f"执行SQL: {sql}")
                            cursor.execute(sql)
                            executed_sqls.append(sql)

                # 记录成功的迁移
                self._log_migration(
                    migration_name=migration_name,
                    sql_executed='\n'.join(executed_sqls),
                    status='success'
                )

                logger.info(f"迁移 {migration_name} 执行成功")
                return True

        except Exception as e:
            error_msg = f"迁移执行失败: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)

            # 记录失败的迁移
            self._log_migration(
                migration_name=migration_name,
                sql_executed='\n'.join(executed_sqls),
                status='failed',
                error_message=error_msg
            )

            return False

    def rollback_migration(self, migration_name, reverse_sql_statements):
        """
        回滚数据库迁移

        Args:
            migration_name: 迁移名称
            reverse_sql_statements: 回滚SQL语句列表

        Returns:
            bool: 回滚是否成功
        """
        logger.info(f"开始回滚迁移: {migration_name}")

        if isinstance(reverse_sql_statements, str):
            reverse_sql_statements = [reverse_sql_statements]

        executed_sqls = []

        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    for sql in reverse_sql_statements:
                        if sql.strip() and not sql.strip().startswith('--'):
                            logger.info(f"执行回滚SQL: {sql}")
                            cursor.execute(sql)
                            executed_sqls.append(sql)

                # 记录回滚操作
                self._log_migration(
                    migration_name=f"{migration_name}_rollback",
                    sql_executed='\n'.join(executed_sqls),
                    status='rollback'
                )

                logger.info(f"迁移 {migration_name} 回滚成功")
                return True

        except Exception as e:
            error_msg = f"迁移回滚失败: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)

            # 记录回滚失败
            self._log_migration(
                migration_name=f"{migration_name}_rollback",
                sql_executed='\n'.join(executed_sqls),
                status='failed',
                error_message=error_msg
            )

            return False

    def add_timestamp_defaults(self, table_name):
        """
        为表添加时间戳默认值

        Args:
            table_name: 表名

        Returns:
            bool: 执行是否成功
        """
        migration_name = f"add_timestamp_defaults_{table_name}"
        sql_statements = self.db_utils.generate_timestamp_columns_sql(table_name, self.database_alias)

        # 生成回滚SQL
        reverse_sql = self._generate_reverse_timestamp_sql(table_name)

        return self.execute_migration(migration_name, sql_statements, reverse_sql)

    def modify_column_with_default(self, table_name, column_name, column_type,
                                 default_value=None, comment=None):
        """
        修改列并添加默认值

        Args:
            table_name: 表名
            column_name: 列名
            column_type: 列类型
            default_value: 默认值
            comment: 注释

        Returns:
            bool: 执行是否成功
        """
        migration_name = f"modify_column_{table_name}_{column_name}"

        sql_statements = self.db_utils.generate_alter_table_sql(
            table_name, column_name, column_type, default_value, comment, self.database_alias
        )

        # 确保返回的是列表
        if isinstance(sql_statements, str):
            sql_statements = [sql_statements]

        # 生成回滚SQL（移除默认值）
        reverse_sql = self._generate_reverse_column_sql(table_name, column_name, column_type)

        return self.execute_migration(migration_name, sql_statements, reverse_sql)

    def check_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            with connection.cursor() as cursor:
                if self.db_type == 'mysql':
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.tables
                        WHERE table_schema = DATABASE() AND table_name = %s
                    """, [table_name])
                elif self.db_type == 'postgresql':
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.tables
                        WHERE table_name = %s
                    """, [table_name])
                elif self.db_type == 'sqlite':
                    cursor.execute("""
                        SELECT COUNT(*) FROM sqlite_master
                        WHERE type='table' AND name = ?
                    """, [table_name])
                else:
                    return False

                result = cursor.fetchone()
                return result[0] > 0 if result else False
        except Exception as e:
            logger.error(f"检查表存在性失败: {e}")
            return False

    def check_column_exists(self, table_name, column_name):
        """检查列是否存在"""
        try:
            with connection.cursor() as cursor:
                if self.db_type == 'mysql':
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.columns
                        WHERE table_schema = DATABASE() AND table_name = %s AND column_name = %s
                    """, [table_name, column_name])
                elif self.db_type == 'postgresql':
                    cursor.execute("""
                        SELECT COUNT(*) FROM information_schema.columns
                        WHERE table_name = %s AND column_name = %s
                    """, [table_name, column_name])
                elif self.db_type == 'sqlite':
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    return any(col[1] == column_name for col in columns)
                else:
                    return False

                if self.db_type != 'sqlite':
                    result = cursor.fetchone()
                    return result[0] > 0 if result else False

        except Exception as e:
            logger.error(f"检查列存在性失败: {e}")
            return False

    def check_column_has_default(self, table_name, column_name):
        """检查列是否有默认值"""
        try:
            with connection.cursor() as cursor:
                if self.db_type == 'mysql':
                    cursor.execute("""
                        SELECT column_default, extra
                        FROM information_schema.columns
                        WHERE table_schema = DATABASE() AND table_name = %s AND column_name = %s
                    """, [table_name, column_name])
                    result = cursor.fetchone()
                    if result:
                        column_default, extra = result
                        # 检查是否有默认值或ON UPDATE
                        has_default = column_default is not None
                        has_on_update = 'on update' in (extra or '').lower()
                        return has_default, has_on_update
                    return False, False

                elif self.db_type == 'postgresql':
                    cursor.execute("""
                        SELECT column_default
                        FROM information_schema.columns
                        WHERE table_name = %s AND column_name = %s
                    """, [table_name, column_name])
                    result = cursor.fetchone()
                    if result:
                        has_default = result[0] is not None
                        return has_default, False  # PostgreSQL使用触发器，这里无法直接检测
                    return False, False

                elif self.db_type == 'sqlite':
                    cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = cursor.fetchall()
                    for col in columns:
                        if col[1] == column_name:  # col[1] is column name
                            has_default = col[4] is not None  # col[4] is default value
                            return has_default, False
                    return False, False
                else:
                    return False, False

        except Exception as e:
            logger.error(f"检查列默认值失败: {e}")
            return False, False

    def _log_migration(self, migration_name, sql_executed, status, error_message=None):
        """记录迁移日志"""
        try:
            # 检查日志表是否存在
            if not self.check_table_exists('database_migration_log'):
                print(f"警告: 日志表不存在，跳过日志记录")
                return

            DatabaseMigrationLog.objects.create(
                migration_name=migration_name,
                database_type=self.db_type,
                sql_executed=sql_executed,
                status=status,
                error_message=error_message
            )
        except Exception as e:
            print(f"警告: 记录迁移日志失败: {e}")
            # 不抛出异常，让主要功能继续执行

    def _generate_reverse_timestamp_sql(self, table_name):
        """生成时间戳字段的回滚SQL"""
        if self.db_type == 'mysql':
            return [
                f"ALTER TABLE {table_name} MODIFY COLUMN create_time DATETIME",
                f"ALTER TABLE {table_name} MODIFY COLUMN update_time DATETIME"
            ]
        elif self.db_type == 'postgresql':
            return [
                f"ALTER TABLE {table_name} ALTER COLUMN create_time DROP DEFAULT",
                f"ALTER TABLE {table_name} ALTER COLUMN update_time DROP DEFAULT",
                f"DROP TRIGGER IF EXISTS trigger_update_{table_name}_timestamp ON {table_name}",
                f"DROP FUNCTION IF EXISTS update_{table_name}_timestamp()"
            ]
        else:
            return [f"-- 回滚SQL for {self.db_type} not implemented"]

    def _generate_reverse_column_sql(self, table_name, column_name, column_type):
        """生成列修改的回滚SQL"""
        if self.db_type == 'mysql':
            return [f"ALTER TABLE {table_name} MODIFY COLUMN {column_name} {column_type}"]
        elif self.db_type == 'postgresql':
            return [f"ALTER TABLE {table_name} ALTER COLUMN {column_name} DROP DEFAULT"]
        else:
            return [f"-- 回滚SQL for {self.db_type} not implemented"]
