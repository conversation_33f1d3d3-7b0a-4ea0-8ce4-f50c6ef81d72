from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class MedicalServiceBase(BaseModel):
    code = models.CharField(max_length=128, blank=True, null=True, verbose_name='服务项目代码', **_db_comment_kwarg('服务项目代码'))
    name = models.CharField(max_length=128, blank=True, null=True, verbose_name='服务项目名称', **_db_comment_kwarg('服务项目名称'))
    charge_item_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='收费项目代码', **_db_comment_kwarg('收费项目代码'))
    charge_item_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='收费项目名称', **_db_comment_kwarg('收费项目名称'))
    admin_region_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='行政区域编码', **_db_comment_kwarg('行政区域编码'))
    admin_region_name = models.CharField(max_length=128, blank=True, null=True, verbose_name='行政区域名称', **_db_comment_kwarg('行政区域名称'))
    treatment_item_content = models.TextField(blank=True, null=True, verbose_name='诊疗项目内涵', **_db_comment_kwarg('诊疗项目内涵'))
    treatment_item_description = models.TextField(blank=True, null=True, verbose_name='诊疗项目说明', **_db_comment_kwarg('诊疗项目说明'))
    treatment_excluded_content = models.TextField(blank=True, null=True, verbose_name='诊疗除外内容', **_db_comment_kwarg('诊疗除外内容'))
    pricing_unit = models.CharField(max_length=512, blank=True, null=True, verbose_name='计价单位', **_db_comment_kwarg('计价单位'))
    begin_date = models.DateField(blank=True, null=True, verbose_name='开始日期', **_db_comment_kwarg('开始日期'))
    end_date = models.DateField(blank=True, null=True, verbose_name='结束日期', **_db_comment_kwarg('结束日期'))
    remark = models.CharField(max_length=512, blank=True, null=True, verbose_name='备注', **_db_comment_kwarg('备注'))
    rid = models.CharField(max_length=255, blank=True, null=True, verbose_name='记录标识', **_db_comment_kwarg('记录标识'))

    class Meta:
        db_table = 'medical_service_base'
        verbose_name = '医疗服务项目分类基础表'
        verbose_name_plural = verbose_name
        if django.VERSION >= (4, 2):
            db_table_comment = verbose_name
