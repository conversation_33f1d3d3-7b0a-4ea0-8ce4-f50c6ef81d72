credentials:
  usernames:
    admin:
      email: <EMAIL>
      failed_login_attempts: 0 # Will be managed automatically
      logged_in: False # Will be managed automatically
      name: admin
      password: EwT6sYmDNks9aSq3 # Will be hashed automatically
    njapp:
      email: <EMAIL>
      failed_login_attempts: 0 # Will be managed automatically
      logged_in: False # Will be managed automatically
      name: njapp
      password: wTVDx7MOIgmUqnjc # Will be hashed automatically
cookie:
  expiry_days: 30
  key: "tde!zkh@QFA4w264yre" # Must be string
  name: "PROMOTION_NJAPP_REPORT"
pre-authorized:
  emails:
  - <EMAIL>