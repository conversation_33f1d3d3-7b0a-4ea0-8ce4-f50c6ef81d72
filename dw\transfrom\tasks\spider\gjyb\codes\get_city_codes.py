from bs4 import BeautifulSoup
import pandas as pd
import os

def process_city_codes(html_content):
    soup = BeautifulSoup(html_content, 'html.parser')
    city_codes = {}
    
    # 查找所有城市节点
    city_nodes = soup.find_all('li', class_='el-cascader-node')
    
    for node in city_nodes:
        # 获取城市编码
        input_tag = node.find('input', class_='el-radio__original')
        if input_tag and 'value' in input_tag.attrs:
            code = input_tag['value']
            
            # 获取城市名称
            city_span = node.find('span', {'data-v-173cce12': ''})
            if city_span:
                city_name = city_span.text.strip()
                city_codes[code] = city_name
    
    return city_codes

def main():
    # 读取web.txt文件
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        web_txt_path = os.path.join(current_dir, 'web.txt')
        with open(web_txt_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 处理城市编码
        city_codes = process_city_codes(html_content)
        
        # 将结果转换为DataFrame并保存为Excel文件
        df = pd.DataFrame(list(city_codes.items()), columns=['城市编码', '城市名称'])
        excel_path = os.path.join(current_dir, 'city_codes.xlsx')
        df.to_excel(excel_path, index=False)
            
        print(f'成功提取{len(city_codes)}个城市编码并保存到Excel文件')
        
    except Exception as e:
        print(f'处理过程中出现错误: {str(e)}')

if __name__ == '__main__':
    main()