#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用字段处理模块
提供可复用的字段处理函数，避免在各省份配置中重复代码
"""

import logging
import pandas as pd
import re
from typing import Any, Optional

logger = logging.getLogger(__name__)


class CommonFieldProcessing:
    """
    通用字段处理函数集合
    提供各种常用的字段处理功能，可被各省份配置引用
    """

    @staticmethod
    def process_code_field_with_padding(value: Any, target_length: int = 15, fill_char: str = '0') -> str:
        """
        处理code字段，支持补0到指定长度
        
        这是一个通用的code字段处理函数，可以被多个模块复用
        支持根据description中的要求进行不同长度的补0处理
        
        Args:
            value: 原始字段值
            target_length: 目标长度，默认15位
            fill_char: 填充字符，默认为'0'
            
        Returns:
            str: 处理后的code值
        """
        if pd.isna(value) or value is None:
            return ''
            
        value_str = str(value).strip()
        if not value_str:
            return ''
            
        # 如果已经达到或超过目标长度，直接返回
        if len(value_str) >= target_length:
            return value_str
            
        # 补0到目标长度
        return value_str.zfill(target_length)

    @staticmethod
    def extract_code_from_composite_field(value: Any, separator: str = '-', part: str = 'before') -> str:
        """
        从复合字段中提取code部分
        
        通用的字段分割函数，支持从类似 "A001-B002" 的字段中提取不同部分
        
        Args:
            value: 原始字段值
            separator: 分隔符，默认为'-'
            part: 提取部分，'before'表示分隔符前，'after'表示分隔符后
            
        Returns:
            str: 提取的部分
        """
        if pd.isna(value) or value is None:
            return ''
            
        value_str = str(value).strip()
        if not value_str:
            return ''
            
        if separator not in value_str:
            # 如果没有分隔符
            if part == 'before':
                return value_str  # 返回整个值
            else:
                return ''  # 返回空
                
        if part == 'before':
            # 返回第一个分隔符之前的部分
            return value_str.split(separator)[0]
        elif part == 'after':
            # 返回第一个分隔符之后的所有内容
            parts = value_str.split(separator, 1)  # 只分割一次
            return parts[1] if len(parts) > 1 else ''
        else:
            return value_str

    @staticmethod
    def validate_field_against_dict(df: pd.DataFrame, field_name: str, dict_id: int, 
                                   name_field: Optional[str] = None) -> pd.DataFrame:
        """
        通用的字典验证函数
        
        验证字段值是否在指定字典范围内，清理无效值
        
        Args:
            df: 数据DataFrame
            field_name: 要验证的字段名
            dict_id: 字典ID
            name_field: 对应的名称字段（可选）
            
        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        if field_name not in df.columns:
            return df
            
        try:
            # 延迟导入避免循环导入
            from transfrom.utils.field_mapping import DictMappingManager
            dict_mapping_manager = DictMappingManager()
            dict_data = dict_mapping_manager.get_dict_mapping(dict_id=dict_id)
            
            if not dict_data.empty:
                # 获取有效的代码列表
                valid_codes = dict_data['dict_code'].tolist()
                logger.info(f"获取到字典ID {dict_id} 的有效代码: {valid_codes}")
                
                # 记录清洗前的统计
                before_count = df[field_name].notna().sum()
                
                # 清洗字段：只保留在字典范围内的值
                invalid_values = df[~df[field_name].isin(valid_codes) & df[field_name].notna()][field_name].unique()
                if len(invalid_values) > 0:
                    logger.info(f"发现无效的{field_name}值: {invalid_values.tolist()}")
                
                # 使用向量化操作替代apply，提升性能
                mask_valid = df[field_name].isin(valid_codes)
                df.loc[~mask_valid, field_name] = None
                
                # 如果有对应的名称字段，同步清理
                if name_field and name_field in df.columns:
                    field_is_null = df[field_name].isna()
                    df.loc[field_is_null, name_field] = None
                
                # 记录清洗后的统计
                after_count = df[field_name].notna().sum()
                logger.info(f"{field_name}字段字典验证完成: 清洗前 {before_count} 条，清洗后 {after_count} 条")
            else:
                logger.warning(f"未获取到dict_id={dict_id}的字典数据，跳过{field_name}字段验证")
        except Exception as e:
            logger.error(f"{field_name}字段字典验证失败: {e}")
            
        return df

    @staticmethod
    def clean_invalid_values(df: pd.DataFrame, field_name: str, 
                           invalid_values: list = None) -> pd.DataFrame:
        """
        通用的无效值清理函数
        
        Args:
            df: 数据DataFrame
            field_name: 要清理的字段名
            invalid_values: 无效值列表，默认为常见的无效值
            
        Returns:
            pd.DataFrame: 清洗后的DataFrame
        """
        if field_name not in df.columns:
            return df
            
        if invalid_values is None:
            invalid_values = ['-', '--', '—', '/', '\\', '', 'nan', 'NaN', 'null', 'NULL', '无']
            
        # 记录清洗前的统计
        before_count = df[field_name].notna().sum()
        
        # 统计各种无效值的数量
        value_counts = {}
        for invalid_val in invalid_values:
            count = (df[field_name] == invalid_val).sum()
            if count > 0:
                value_counts[invalid_val] = count
        
        # 应用清洗 - 使用向量化操作
        mask_invalid = df[field_name].isin(invalid_values)
        df.loc[mask_invalid, field_name] = None
        
        # 记录清洗后的统计
        after_count = df[field_name].notna().sum()
        if value_counts:
            processed_info = ', '.join([f"{count}条'{val}'值" for val, count in value_counts.items()])
            logger.info(f"{field_name}字段无效值清理完成: 清洗前 {before_count} 条，清洗后 {after_count} 条，处理了 {processed_info}")
        
        return df

    @staticmethod
    def camel_to_snake_case(name: str) -> str:
        """
        将驼峰命名转换为下划线分割
        
        通用的命名转换函数，可被多个模块复用
        
        Args:
            name: 驼峰命名字符串
            
        Returns:
            str: 下划线分割的字符串
        """
        # 在大写字母前插入下划线，然后转为小写
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    @staticmethod
    def build_additional_info_from_mapping(row: pd.Series, source_table: str, 
                                         target_table: str = 'medical_service_entity') -> str:
        """
        通用的additional_info构建函数
        
        根据映射表的description字段决定哪些字段放入additional_info
        
        Args:
            row: 数据行
            source_table: 源表名
            target_table: 目标表名
            
        Returns:
            str: JSON格式的additional_info字符串
        """
        import json
        
        additional_info = {}
        
        try:
            # 从映射表获取需要放入additional_info的字段
            from medical.models.medical_field_mapping import MedicalFieldMapping
            dict_fields = MedicalFieldMapping.objects.filter(
                source_table=source_table,
                target_table=target_table,
                target_field='additional_info',
                description__contains='以字典形式'
            ).values_list('source_field', flat=True)
            
            dict_field_list = list(dict_fields)
            
            for field_name, field_value in row.items():
                # 只处理标记为"以字典形式"的字段
                if field_name not in dict_field_list:
                    continue
                
                # 将驼峰命名转换为下划线分割
                snake_case_key = CommonFieldProcessing.camel_to_snake_case(field_name)
                
                # 处理字段值
                if pd.notna(field_value) and str(field_value).strip() not in ['-', '--', '—', '/', '\\', '']:
                    try:
                        # 尝试转换为数值
                        if isinstance(field_value, (int, float)):
                            additional_info[snake_case_key] = field_value
                        else:
                            # 尝试转换字符串为数值
                            try:
                                additional_info[snake_case_key] = float(field_value)
                            except (ValueError, TypeError):
                                # 如果转换失败，保留原值
                                additional_info[snake_case_key] = str(field_value).strip()
                    except (ValueError, TypeError):
                        additional_info[snake_case_key] = str(field_value).strip()
                        
        except Exception as e:
            logger.warning(f"构建additional_info时出错: {e}")
            
        return json.dumps(additional_info, ensure_ascii=False) if additional_info else '{}'

    @staticmethod
    def filter_and_deduplicate_data(df: pd.DataFrame, key_fields: list) -> pd.DataFrame:
        """
        通用的数据过滤和去重函数
        
        Args:
            df: 数据DataFrame
            key_fields: 关键字段列表，用于过滤和去重
            
        Returns:
            pd.DataFrame: 过滤和去重后的DataFrame
        """
        # 检查可用的关键字段
        available_key_fields = [field for field in key_fields if field in df.columns]
        
        if not available_key_fields:
            logger.warning(f"没有找到可用的关键字段: {key_fields}")
            return df
            
        # 按照唯一键进行过滤
        before_filter = len(df)
        filter_conditions = []
        for field in available_key_fields:
            filter_conditions.append((df[field].notna() & (df[field] != '')))
        
        if filter_conditions:
            # 使用OR条件：任意一个关键字段不为空即可
            combined_condition = filter_conditions[0]
            for condition in filter_conditions[1:]:
                combined_condition = combined_condition | condition
            
            df = df[combined_condition]
            after_filter = len(df)
            if before_filter != after_filter:
                logger.info(f"按关键字段过滤无效记录: {before_filter} → {after_filter}")
        
        # 去重处理
        before_dedup = len(df)
        unique_fields = []
        for field in available_key_fields:
            if field in df.columns and df[field].notna().any():
                unique_fields.append(field)
        
        if unique_fields:
            df = df.drop_duplicates(subset=unique_fields, keep='first')
            after_dedup = len(df)
            if before_dedup != after_dedup:
                logger.info(f"按关键字段去重: {before_dedup} → {after_dedup}")
        
        return df
