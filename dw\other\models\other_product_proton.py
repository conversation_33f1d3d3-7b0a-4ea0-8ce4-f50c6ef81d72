from django.db import models
from common.models import BaseModel


class OtherProductProton(BaseModel):
    product_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='产品名称',unique=True)
    total_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='累计赔付')
    payout_ratio = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='赔付率')
    apply_num = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='理赔申请')
    close_num = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='已完成结案')
    pay_num = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='共理赔')
    past_symptom_pay_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='既往症赔付')
    past_symptom_pay_person = models.IntegerField(blank=True, null=True, verbose_name='既往症赔付人数')
    avg_pay_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='人均赔付')
    max_pay_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='最高获赔')
    avg_burden_reduce_ratio = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='个人负担平均降低')
    max_reduce_ratio = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='最高降低')
    avg_reduce_ratio = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='平均降低')
    total_claim_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='已申请理赔医疗总费用')
    social_pay_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='社保统筹支付')
    burden_amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='个人负担')


    class Meta:
        db_table = 'other_product_proton'
        verbose_name = '理赔-质子重离子赔付数据'
        verbose_name_plural = verbose_name
