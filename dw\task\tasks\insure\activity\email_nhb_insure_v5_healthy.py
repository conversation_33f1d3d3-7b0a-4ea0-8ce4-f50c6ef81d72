import logging
from datetime import datetime

import pandas as pd
import os
import pymysql
import idna
import warnings
from dw import settings
from django.core.mail import EmailMessage
from transfrom.utils.utils import  query_sql
from transfrom.utils.date import get_week_range

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


TO = ['<EMAIL>']
CC= ['<EMAIL>']

DB = settings.DATABASES['jkx']

def get_connection():
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                                user=DB["USER"],
                                password=DB["PASSWORD"], database=DB["NAME"])
    return conn

#防止重复退单、支付成功导致数据不准，需要将已经发送的单子存下来，再次发送时，只发送未发送过的单子
def get_sale_info():
    """
    获取营销-健康管理的销量情况
    """


    # 所有领取的用户
    sql = """
        select * from 
(SELECT
	date( o.create_time ) AS 日期,
	mc.channel_code 渠道编码,
	CASE
		WHEN LEFT ( mc.channel_name, 3 ) = '渠道码' THEN
		mcps.remark ELSE mc.channel_name 
	END  AS 渠道名称,
	sum( oic.premium ) AS 金额,
	count(distinct oic.client_id) 投保人数,CAST( p.main AS UNSIGNED ) 是否主险
FROM
	`order` o
	JOIN order_item oi ON oi.order_id = o.id
	JOIN order_item_client oic ON oic.order_item_id = oi.id 
	AND oic.is_return = 0
	JOIN product_set ps ON ps.id = o.product_set_id 
	AND ps.CODE = 'ninghuibaoV5'
	JOIN product p ON oi.product_id = p.id and p.main = 1
	JOIN marketing_channel mc ON mc.channel_id = o.source_id
	LEFT JOIN marketing_channel_product_set mcps ON mc.channel_code = mcps.channel_code  and mcps.product_set_code = ps.`code`
WHERE
	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
-- 	and o.create_time >='2024-10-21 00:00:00'
and o.delete_time is null
GROUP BY
	日期,
	渠道名称) a 
	where a.渠道编码 = 'S105'
	order by a.日期
    """


    df = pd.read_sql(sql, get_connection())
    return df

def email_nhb_healthy():
    date = datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'宁惠保5期营销-健康管理销量统计_{date}.xlsx')
    df = get_sale_info()
    df.to_excel(path, index=False)

    mail = EmailMessage(
        subject=f'宁惠保5期营销-健康管理销量统计_{date}',
        body='见附件',
        to=TO,
        cc=CC
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)

