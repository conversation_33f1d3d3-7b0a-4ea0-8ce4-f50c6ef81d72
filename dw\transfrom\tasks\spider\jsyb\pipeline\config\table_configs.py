#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
江苏医保局ETL表配置文件
定义各种表的ETL处理配置
"""

# 🏥 江苏医保局ETL表配置字典
JSYB_TABLE_CONFIGS = {
    "江苏医疗服务项目": {
        "source_table": "spider_jsyb_service_facilities",
        "target_table": "medical_service_entity",
        # 修改唯一键为目标表的4个字段：收费项目编码、收费项目名称、国家医疗服务项目代码、备注字段
        "unique_fields": [
            'charge_item_code', 'charge_item_name', 'code', 'remark'
        ],
        "description": "江苏医疗服务项目省市实体数据",
        "icon": "[江苏服务]",
        "target_where_clause": "province_name='江苏省' and level_type = 'province' "  # 目标表数据过滤条件
    },

    "江苏医保药品": {
        "source_table": "spider_jsyb_drug",
        "target_table": "medical_drug_entity",
        # 唯一键为国家药品代码
        "unique_fields": [
            'code'
        ],
        "description": "江苏医保药品省市实体数据",
        "icon": "[江苏药品]",
        "target_where_clause": "province_name='江苏省' and level_type = 'province'"  # 目标表数据过滤条件
    }
}

def get_jsyb_table_config(table_name: str):
    """
    获取指定表的配置信息
    
    Args:
        table_name: 表名称（配置字典中的key）
        
    Returns:
        dict: 表配置信息，如果不存在返回None
    """
    return JSYB_TABLE_CONFIGS.get(table_name)

def get_all_jsyb_table_configs():
    """
    获取所有江苏医保局表配置
    
    Returns:
        dict: 所有表配置信息
    """
    return JSYB_TABLE_CONFIGS

def list_jsyb_table_names():
    """
    获取所有配置的表名称列表
    
    Returns:
        list: 表名称列表
    """
    return list(JSYB_TABLE_CONFIGS.keys())
