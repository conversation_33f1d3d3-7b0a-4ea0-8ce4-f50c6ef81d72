"""
数据字典缓存管理器
提供智能的缓存机制，确保数据一致性
支持Redis缓存和优雅降级到内存缓存
"""
from django.core.cache import cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.conf import settings
import logging
import time
import redis
from typing import Optional, List, Dict, Any

logger = logging.getLogger(__name__)

class DataDictionaryCacheManager:
    """数据字典缓存管理器 - 支持Redis和优雅降级"""

    # 缓存键前缀
    CACHE_PREFIX = "data_dict"

    # 缓存版本键
    VERSION_KEY = f"{CACHE_PREFIX}:version"

    # 默认缓存时间（秒）
    DEFAULT_TIMEOUT = 300  # 5分钟

    # 长期缓存时间（秒）
    LONG_TIMEOUT = 3600  # 1小时

    def __init__(self):
        self._redis_available = None
        self._fallback_cache = {}  # 内存缓存作为后备
        self._check_redis_availability()

    def _check_redis_availability(self):
        """检查Redis是否可用"""
        try:
            # 从Django配置中获取Redis连接信息
            from django.conf import settings

            # 尝试使用Django的缓存配置
            if hasattr(settings, 'CACHES') and 'default' in settings.CACHES:
                cache_config = settings.CACHES['default']
                if 'django_redis' in cache_config.get('BACKEND', ''):
                    # 使用Django缓存进行测试
                    from django.core.cache import cache
                    cache.set('redis_test_key', 'test_value', 1)
                    test_result = cache.get('redis_test_key')
                    if test_result == 'test_value':
                        self._redis_available = True
                        logger.info("Redis缓存可用 (通过Django配置)")
                        return

            # 备用方案：直接连接Redis
            import redis
            # 从环境配置中获取Redis信息
            redis_host = getattr(settings, 'REDIS_HOST', '127.0.0.1')
            redis_password = getattr(settings, 'REDIS_PASSWORD', '2279856')

            r = redis.Redis(
                host=redis_host,
                port=6379,
                db=1,  # 使用数据库1，与配置一致
                password=redis_password,
                socket_timeout=2
            )
            r.ping()
            self._redis_available = True
            logger.info(f"Redis缓存可用 (直接连接 {redis_host}:6379/1)")

        except Exception as e:
            self._redis_available = False
            logger.warning(f"Redis不可用，将使用内存缓存: {e}")

    def _get_cache_backend(self):
        """获取缓存后端"""
        if self._redis_available is None:
            self._check_redis_availability()

        if self._redis_available:
            try:
                # 测试Django缓存是否正常工作
                test_key = f'cache_test_{int(time.time())}'
                cache.set(test_key, 'test_value', 1)
                result = cache.get(test_key)
                if result == 'test_value':
                    cache.delete(test_key)  # 清理测试键
                    return 'django_cache'
                else:
                    raise Exception("缓存读写测试失败")
            except Exception as e:
                logger.warning(f"Django缓存异常，切换到内存缓存: {e}")
                self._redis_available = False
                return 'memory_cache'
        else:
            return 'memory_cache'

    def _memory_cache_get(self, key, default=None):
        """内存缓存获取"""
        item = self._fallback_cache.get(key)
        if item is None:
            return default

        # 检查是否过期
        if time.time() > item['expires']:
            del self._fallback_cache[key]
            return default

        return item['value']

    def _memory_cache_set(self, key, value, timeout=None):
        """内存缓存设置"""
        if timeout is None:
            timeout = self.DEFAULT_TIMEOUT

        expires = time.time() + timeout
        self._fallback_cache[key] = {
            'value': value,
            'expires': expires
        }

        # 简单的内存清理：如果缓存项过多，清理过期项
        if len(self._fallback_cache) > 1000:
            self._cleanup_memory_cache()

    def _memory_cache_delete(self, key):
        """内存缓存删除"""
        self._fallback_cache.pop(key, None)

    def _cleanup_memory_cache(self):
        """清理过期的内存缓存项"""
        current_time = time.time()
        expired_keys = [
            key for key, item in self._fallback_cache.items()
            if current_time > item['expires']
        ]
        for key in expired_keys:
            del self._fallback_cache[key]
    
    def get_cache_version(self) -> int:
        """获取当前缓存版本"""
        backend = self._get_cache_backend()

        if backend == 'django_cache':
            try:
                version = cache.get(self.VERSION_KEY)
                if version is None:
                    version = int(time.time())
                    cache.set(self.VERSION_KEY, version, self.LONG_TIMEOUT)
                return version
            except Exception as e:
                logger.warning(f"Django缓存获取版本失败: {e}")
                return int(time.time())
        else:
            # 内存缓存版本
            return self._memory_cache_get(self.VERSION_KEY, int(time.time()))

    def invalidate_cache(self, pattern: Optional[str] = None):
        """使缓存失效"""
        try:
            backend = self._get_cache_backend()

            if pattern:
                # 增加特定模式的版本号
                version_key = f"{self.VERSION_KEY}:{pattern}"
                if backend == 'django_cache':
                    cache.delete(version_key)
                else:
                    self._memory_cache_delete(version_key)
                logger.info(f"缓存模式失效: {pattern}")
            else:
                # 增加全局版本号，使所有缓存失效
                new_version = int(time.time())
                if backend == 'django_cache':
                    cache.set(self.VERSION_KEY, new_version, self.LONG_TIMEOUT)
                else:
                    self._memory_cache_set(self.VERSION_KEY, new_version, self.LONG_TIMEOUT)
                    # 清空内存缓存
                    self._fallback_cache.clear()
                logger.info(f"全局缓存失效，新版本: {new_version}")
        except Exception as e:
            logger.error(f"缓存失效操作失败: {e}")

    def get_versioned_key(self, key: str, pattern: Optional[str] = None) -> str:
        """获取带版本的缓存键"""
        backend = self._get_cache_backend()

        if pattern:
            version_key = f"{self.VERSION_KEY}:{pattern}"
            if backend == 'django_cache':
                try:
                    version = cache.get(version_key, self.get_cache_version())
                except:
                    version = self.get_cache_version()
            else:
                version = self._memory_cache_get(version_key, self.get_cache_version())
        else:
            version = self.get_cache_version()
        return f"{self.CACHE_PREFIX}:v{version}:{key}"

    def get(self, key: str, default=None, pattern: Optional[str] = None):
        """获取缓存数据"""
        try:
            backend = self._get_cache_backend()
            versioned_key = self.get_versioned_key(key, pattern)

            if backend == 'django_cache':
                return cache.get(versioned_key, default)
            else:
                return self._memory_cache_get(versioned_key, default)
        except Exception as e:
            logger.warning(f"缓存获取失败: {e}")
            return default

    def set(self, key: str, value: Any, timeout: Optional[int] = None, pattern: Optional[str] = None):
        """设置缓存数据"""
        try:
            if timeout is None:
                timeout = self.DEFAULT_TIMEOUT

            backend = self._get_cache_backend()
            versioned_key = self.get_versioned_key(key, pattern)

            if backend == 'django_cache':
                cache.set(versioned_key, value, timeout)
            else:
                self._memory_cache_set(versioned_key, value, timeout)

            logger.debug(f"缓存设置 ({backend}): {versioned_key}")
        except Exception as e:
            logger.warning(f"缓存设置失败: {e}")

    def delete(self, key: str, pattern: Optional[str] = None):
        """删除缓存数据"""
        try:
            backend = self._get_cache_backend()
            versioned_key = self.get_versioned_key(key, pattern)

            if backend == 'django_cache':
                cache.delete(versioned_key)
            else:
                self._memory_cache_delete(versioned_key)

            logger.debug(f"缓存删除 ({backend}): {versioned_key}")
        except Exception as e:
            logger.warning(f"缓存删除失败: {e}")
    
    def get_table_types(self, database_id: int) -> List[str]:
        """获取表类型列表（带缓存）"""
        cache_key = f"table_types_{database_id}"
        table_types = self.get(cache_key, pattern="table_info")

        if table_types is None:
            from public.models import PublicTableInfo
            table_types = list(
                PublicTableInfo.objects.filter(database_id=database_id)
                .values_list('type', flat=True)
                .distinct()
            )
            self.set(cache_key, table_types, pattern="table_info")
            logger.debug(f"表类型缓存更新: database_id={database_id}")

        return table_types
    
    def get_database_stats(self, database_id: int) -> Dict[str, int]:
        """获取数据库统计信息（带缓存）"""
        cache_key = f"db_stats_{database_id}"
        stats = self.get(cache_key, pattern="table_info")

        if stats is None:
            from public.models import PublicTableInfo
            queryset = PublicTableInfo.objects.filter(database_id=database_id)
            stats = {
                'total_tables': queryset.count(),
                'active_tables': queryset.filter(is_deprecated=False).count(),
                'deprecated_tables': queryset.filter(is_deprecated=True).count(),
            }
            self.set(cache_key, stats, timeout=self.LONG_TIMEOUT, pattern="table_info")
            logger.debug(f"数据库统计缓存更新: database_id={database_id}")

        return stats

    def get_table_column_count(self, table_id: int) -> int:
        """获取表字段数量（带缓存）"""
        cache_key = f"table_columns_{table_id}"
        count = self.get(cache_key, pattern="column_info")

        if count is None:
            from public.models import PublicColumnInfo
            count = PublicColumnInfo.objects.filter(table_id=table_id).count()
            self.set(cache_key, count, timeout=self.LONG_TIMEOUT, pattern="column_info")

        return count

    def get_table_index_count(self, table_id: int) -> int:
        """获取表索引数量（带缓存）"""
        cache_key = f"table_indexes_{table_id}"
        count = self.get(cache_key, pattern="index_info")

        if count is None:
            from public.models import PublicIndexInfo
            count = PublicIndexInfo.objects.filter(table_id=table_id).count()
            self.set(cache_key, count, timeout=self.LONG_TIMEOUT, pattern="index_info")

        return count
    
    def clear_all_cache(self):
        """清空所有缓存"""
        self.invalidate_cache()
        logger.info("所有数据字典缓存已清空")

    def clear_table_cache(self, table_id: Optional[int] = None):
        """清空表相关缓存"""
        if table_id:
            self.delete(f"table_columns_{table_id}", pattern="column_info")
            self.delete(f"table_indexes_{table_id}", pattern="index_info")
            logger.info(f"表缓存已清空: table_id={table_id}")
        else:
            self.invalidate_cache("column_info")
            self.invalidate_cache("index_info")
            logger.info("所有表缓存已清空")

    def clear_database_cache(self, database_id: Optional[int] = None):
        """清空数据库相关缓存"""
        if database_id:
            self.delete(f"table_types_{database_id}", pattern="table_info")
            self.delete(f"db_stats_{database_id}", pattern="table_info")
            logger.info(f"数据库缓存已清空: database_id={database_id}")
        else:
            self.invalidate_cache("table_info")
            logger.info("所有数据库缓存已清空")

    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态信息"""
        backend = self._get_cache_backend()
        return {
            'backend': backend,
            'redis_available': self._redis_available,
            'memory_cache_size': len(self._fallback_cache),
            'cache_version': self.get_cache_version()
        }


# 创建全局缓存管理器实例
cache_manager = DataDictionaryCacheManager()


# 信号处理器 - 自动缓存失效
@receiver(post_save, sender='public.PublicTableInfo')
def invalidate_table_info_cache(sender, instance, **kwargs):
    """表信息变更时使相关缓存失效"""
    cache_manager.clear_database_cache(instance.database_id)
    logger.info(f"表信息缓存失效: {instance.name}")


@receiver(post_delete, sender='public.PublicTableInfo')
def invalidate_table_info_cache_on_delete(sender, instance, **kwargs):
    """表删除时使相关缓存失效"""
    cache_manager.clear_database_cache(instance.database_id)
    logger.info(f"表删除缓存失效: {instance.name}")


@receiver(post_save, sender='public.PublicColumnInfo')
def invalidate_column_info_cache(sender, instance, **kwargs):
    """字段信息变更时使相关缓存失效"""
    cache_manager.clear_table_cache(instance.table_id)
    logger.info(f"字段信息缓存失效: {instance.table_name}.{instance.name}")


@receiver(post_delete, sender='public.PublicColumnInfo')
def invalidate_column_info_cache_on_delete(sender, instance, **kwargs):
    """字段删除时使相关缓存失效"""
    cache_manager.clear_table_cache(instance.table_id)
    logger.info(f"字段删除缓存失效: {instance.table_name}.{instance.name}")


@receiver(post_save, sender='public.PublicIndexInfo')
def invalidate_index_info_cache(sender, instance, **kwargs):
    """索引信息变更时使相关缓存失效"""
    cache_manager.clear_table_cache(instance.table_id)
    logger.info(f"索引信息缓存失效: {instance.table_name}.{instance.name}")


@receiver(post_delete, sender='public.PublicIndexInfo')
def invalidate_index_info_cache_on_delete(sender, instance, **kwargs):
    """索引删除时使相关缓存失效"""
    cache_manager.clear_table_cache(instance.table_id)
    logger.info(f"索引删除缓存失效: {instance.table_name}.{instance.name}")


@receiver(post_save, sender='public.PublicSqlTemplate')
def invalidate_sql_template_cache(sender, instance, **kwargs):
    """SQL模板变更时使相关缓存失效"""
    cache_manager.invalidate_cache("sql_templates")
    logger.info(f"SQL模板缓存失效: {instance.name}")


@receiver(post_delete, sender='public.PublicSqlTemplate')
def invalidate_sql_template_cache_on_delete(sender, instance, **kwargs):
    """SQL模板删除时使相关缓存失效"""
    cache_manager.invalidate_cache("sql_templates")
    logger.info(f"SQL模板删除缓存失效: {instance.name}")
