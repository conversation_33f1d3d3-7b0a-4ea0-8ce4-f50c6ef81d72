import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, sum_or_combine
from utils.st import query_sql,text_write,sub_text_write,empty_line

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
        SELECT
        name product_set_name,
        code product_set_code
    FROM
        product_set
     where left(name, 2) in ('南京')
    order by code desc 
        '''

    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_product_info():
    """
    获取产品集代码、名称、产品名称、产品代码
    :return:
    """
    SQL_PRODUCT_INFO = '''
    SELECT
        ps.name product_set_name,
        ps.code product_set_code,
        p.name product_name,
        p.code product_code
    FROM
        product_set ps join product p 
        on ps.id = p.product_set_id
    where p.delete_time is null
    and p.main=1
    order by ps.name desc 
        '''

    df_product_info = CONNECTOR_JKX.query(SQL_PRODUCT_INFO, show_spinner='查询中...', ttl=600)
    return df_product_info


def get_management_info(product_set_code, seller_short_name, conn=CONNECTOR_DW):
    """
    获取某一产品保司的管理人员信息
    :param product_set_code: 产品集代码
    :param seller_short_name: 保司简称
    :param conn:
    :return:
    """
    sql = """
    select o.product_set_code,o.short_name,o.agent_id employee_id,o.name,d.key as type_name from other_management_staff o join system_dict_value d on o.type = d.label
    where product_set_code='{product_set_code}'
    and o.short_name='{seller_short_name}'
    """.format(product_set_code=product_set_code, seller_short_name=seller_short_name)
    df = conn.query(sql, show_spinner='查询中...', ttl=0)
    return df


def get_department_info(conn=CONNECTOR_JKX, sql=query_sql('SQL_DEPARTMENT_INFO')):
    """
    获取产品销售机构及部门信息
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_department_info = conn.query(sql, show_spinner='查询中...', ttl=0)
    return df_department_info


def get_invitee_info(conn=CONNECTOR_JKX, sql=query_sql('SQL_INVITEE_INFO')):
    """
    获取非一级代理人信息（用于层级拼接用）
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_invitee_info = conn.query(sql, show_spinner='查询中...', ttl=0)
    return df_invitee_info


def update_full_name(row):
    """
    将代理人信息拼接成完整姓名
    """
    # 将所有可能的非字符串值转换为字符串
    first_inviter_full_name = str(row['first_inviter_full_name'])
    second_inviter_full_name = str(row['second_inviter_full_name'])
    third_inviter_full_name = str(row['third_inviter_full_name'])

    if pd.isnull(row['first_inviter_id']):

        return row['full_name'] + '/' + row['employee_full_name']
    elif pd.isnull(row['second_inviter_id']):
        return row['full_name'] + '/' + first_inviter_full_name + '/' + row['employee_full_name']
    elif pd.isnull(row['third_inviter_id']):
        return row[
            'full_name'] + '/' + first_inviter_full_name + '/' + second_inviter_full_name + '/' + row[
            'employee_full_name']
    else:
        return row[
            'full_name'] + '/' + first_inviter_full_name + '/' + second_inviter_full_name + '/' + third_inviter_full_name + '/' + \
            row['employee_full_name']


def create_cascader_structure(df):
    """
    根据df创建级联结构，用于城市级联控件选择
    """
    # 创建一个空的级联结构
    cascader_items = []

    # 遍历省份
    for province in df['province_name'].unique():
        province_item = sac.CasItem(province, children=[])
        # 遍历城市
        cities = df[df['province_name'] == province]['city_name'].unique()
        for city in cities:
            city_item = sac.CasItem(city, children=[])
            # 遍历区县
            districts = df[(df['province_name'] == province) & (df['city_name'] == city)]['district_name'].unique()
            for district in districts:
                city_item.children.append(sac.CasItem(district))
            province_item.children.append(city_item)
        cascader_items.append(province_item)
    return cascader_items


def level_transformer(row):
    # 将层级转成中文
    row = int(row)
    chinese_level = {1: '一级', 2: '二级', 3: '三级', 4: '四级', 5: '五级', 6: '六级', 7: '七级', 8: '八级', 9: '九级',
                     10: '十级'}
    return chinese_level.get(row, '未知')


def merge_and_aggregate(df, level_columns,use_product=False):
    """
    根据提供的列名合并数据，并对指定列进行聚合。

    :param df: 输入的 DataFrame
    :param level_columns: 包含各级邀请人信息的列名列表
    :return: 处理后的 DataFrame
    """
    # 初始化一个空的 DataFrame 来存储合并后的结果
    merged_df = pd.DataFrame()

    # 遍历每一级邀请人信息
    for cols in level_columns:
        if not use_product:
            temp_df = df[cols].rename(columns={
                cols[0]: 'employee_id',
                cols[1]: 'employee_full_name',
                cols[2]: 'number'
            })
        else:
            temp_df = df[cols].rename(columns={
                cols[0]: 'employee_id',
                cols[1]: 'employee_full_name',
                cols[2]: 'number',
                cols[3]: 'product_name'
            })
        merged_df = pd.concat([merged_df, temp_df])

    # 删除 NaN 值
    merged_df.dropna(subset=['employee_id'], inplace=True)

    return merged_df


def create_full_name(parts):
    """
    将给定的部分列表转换为完整名称。
    """
    return '/'.join(parts)


def df_column_filter(df,df1,show_product=False):
    """
    筛选df_employee_info的列
    :param df: 需要处理的数据
    :param df1: 产品信息
    :param show_product:是否显示产品信息
    :return:
    """
    if not show_product:
        df_first_employee_sale = df[
            ['department_first_name', 'department_name', 'employee_name', 'number', 'self_number', 'not_self_sale']]
    else:
        column_list = ['department_first_name', 'department_name', 'employee_name', 'number', 'self_number', 'not_self_sale']
        for col in df1['product_name'].unique().tolist():
            column_list.append(col)
            column_list.append(f'self_{col}')
            column_list.append(f'not_self_{col}')
        df_first_employee_sale = df[column_list]
    return df_first_employee_sale


def df_column_rename(df,df1,show_product=False):
    """
    筛选df_employee_info的列
    :param df: 需要处理的数据
    :param df1: 产品信息
    :param show_product:是否显示产品信息
    :return:
    """
    if not show_product:
        df_first_employee_sale = df[
            ['psoition', 'department_first_name', 'department_name', 'employee_name', 'number', 'self_number',
             'not_self_sale']]
        df_first_employee_sale.rename(
            columns={'psoition': '排名', 'department_first_name': '经营单位', 'department_name': '部门',
                     'employee_name': '姓名', 'number': '参保数量', 'self_number': '本级单量', 'not_self_sale': '下级单量'},
            inplace=True)
    else:
        column_list = ['psoition', 'department_first_name', 'department_name', 'employee_name', 'number', 'self_number', 'not_self_sale']
        for col in df1['product_name'].unique().tolist():
            column_list.append(col)
            column_list.append(f'self_{col}')
            column_list.append(f'not_self_{col}')
        df_first_employee_sale = df[column_list]
        df_first_employee_sale.rename(
            columns={'psoition': '排名', 'department_first_name': '经营单位', 'department_name': '部门',
                     'employee_name': '姓名', 'number': '参保数量', 'self_number': '本级单量', 'not_self_sale': '下级单量'},
            inplace=True)
        for col in df_first_employee_sale.columns:
            if col.startswith('self_'):
                df_first_employee_sale.rename(columns={col: f'本级{col[5:]}'}, inplace=True)
            elif col.startswith('not_self_'):
                df_first_employee_sale.rename(columns={col: f'下级{col[9:]}'}, inplace=True)
    return df_first_employee_sale



@st.cache_data(ttl=60)
def get_employee_info(product_set_code, product_set_name, organization_id, department_name, start_date,
                      end_date,show_product, conn=CONNECTOR_JKX, sql=query_sql('SQL_EMPLOYEE_INFO')):
    """
    获取代理人信息
    :param product_set_code: 产品集代码
    :param product_set_name: 产品集名称
    :param start_date: 销售开始日期
    :param end_date: 销售结束日期
    :param organization_id: 销售机构ID
    :param department_name: 销售部门名称
    :param show_product: 是否显示产品信息
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    # 如果没有传入日期，赋予默认值
    if start_date is None:
        start_date = '1900-01-01'
    if end_date is None:
        end_date = datetime.date.today()
    # 数据并未按照层级进行销量拼接、status是否删除不特殊处理，如果需要，后端会物理删除
    df_employee_info = conn.query(
        sql.format(product_set_code=product_set_code, start_date=start_date, end_date=end_date,
                   organization_id=organization_id),
        show_spinner='查询中...', ttl=0)
    if df_employee_info.empty:
        return df_employee_info, df_employee_info
    # 筛选出指定城市的销售员工
    # 拼接人员的完整信息，姓名-手机号-工号
    df_employee_info['employee_full_name'] = np.where(df_employee_info['employee_mobile'].notnull(),
                                                      df_employee_info['employee_name'] + '-' + df_employee_info[
                                                          'employee_mobile'], df_employee_info['employee_name'])
    df_employee_info['employee_full_name'] = np.where(df_employee_info['staff_no'].notnull(),
                                                      df_employee_info['employee_full_name'] + '-' + df_employee_info[
                                                          'staff_no'], df_employee_info['employee_full_name'])

    df_employee_info.reset_index(drop=True, inplace=True)

    # 获取非一级代理人信息
    df_invitee_info = get_invitee_info()
    df_invitee_info.rename(columns={'invitee_id': 'employee_id'}, inplace=True)

    # 筛选df_employee_info的 district_name 值在city_list列表中
    if len(df_employee_info['province_name'].unique()) > 0:
        # 去除所有有区域的记录，一级代理人才有
        df_employee_area = df_employee_info[df_employee_info['province_name'].notnull()][
            ['province_name', 'city_name', 'district_name', 'employee_id']].drop_duplicates()
        # 非一级代理人继承一级代理人的区域
        df_employee_info = sqldf(
            "select a.*,c.province_name province_name_1,c.city_name city_name_1,c.district_name district_name_1 from df_employee_info a left join df_invitee_info b on a.employee_id = b.employee_id "
            "left join df_employee_area c on c.employee_id = b.first_inviter_id")
        # 继承一级代理人的区域
        df_employee_info['province_name'] = np.where(df_employee_info['province_name'].isnull(),
                                                     df_employee_info['province_name_1'],
                                                     df_employee_info['province_name'])
        df_employee_info['city_name'] = np.where(df_employee_info['city_name'].isnull(),
                                                 df_employee_info['city_name_1'], df_employee_info['city_name'])
        df_employee_info['district_name'] = np.where(df_employee_info['district_name'].isnull(),
                                                     df_employee_info['district_name_1'],
                                                     df_employee_info['district_name'])

    # 拼接人员名称的拼接信息 名字+手机号+工号
    df_invitee_info = sqldf('''select distinct a.*,case when b.staff_no is null then (b.employee_name || '-' || b.employee_mobile) 
                        else (b.employee_name || '-' || b.employee_mobile || '-' || b.staff_no) end as first_inviter_full_name,
                        case when c.staff_no is null then (c.employee_name  || '-' || c.employee_mobile) 
                        else (c.employee_name  || '-' || c.employee_mobile || '-' || c.staff_no) end as second_inviter_full_name,
                        case when d.staff_no is null then (d.employee_name || '-' || d.employee_mobile) 
                        else (d.employee_name || '-' || d.employee_mobile || '-' || d.staff_no) end as third_inviter_full_name
                        from df_invitee_info a left join df_employee_info b on a.first_inviter_id = b.employee_id
                                    left join df_employee_info c on c.employee_id = a.second_inviter_id
                                    left join df_employee_info d on d.employee_id = a.third_inviter_id
    ''')
    df_invitee_info[['first_inviter_full_name', 'second_inviter_full_name', 'third_inviter_full_name']] = \
        df_invitee_info[['first_inviter_full_name', 'second_inviter_full_name', 'third_inviter_full_name']].astype(str)
    # 将"-"替换成空
    df_invitee_info['second_inviter_full_name'] = df_invitee_info['second_inviter_full_name'].apply(
        lambda x: np.nan if x == '-' else x)
    df_invitee_info['third_inviter_full_name'] = df_invitee_info['third_inviter_full_name'].apply(
        lambda x: np.nan if x == '-' else x)
    df_employee_info = pd.merge(df_employee_info, df_invitee_info, on='employee_id', how='left')

    df_employee_info.reset_index(drop=True, inplace=True)
    # 查询代理人自己销售数量
    if not show_product:
        df_first_employee_sale = df_employee_info.groupby(['employee_id']).agg({'number': 'sum'}).reset_index()
    else:
        df_first_employee_sale = df_employee_info.groupby(['employee_id','product_name']).agg({'number': 'sum'}).reset_index()
        # 将product_name列的值转成列
        df_first_employee_sale = df_first_employee_sale.pivot_table(index='employee_id', columns='product_name', values='number', aggfunc='sum').reset_index()
        df_first_employee_sale.fillna(0, inplace=True)
        df_first_employee_sale['number'] = df_first_employee_sale[df_first_employee_sale.columns[1:]].sum(axis=1)
        # 修改列名 除了employee_id、number列，其他列名都加上self_前缀
        df_first_employee_sale.columns = [col if col == 'employee_id' or col == 'number' else 'self_' + col for col in df_first_employee_sale.columns]
    df_first_employee_sale.rename(columns={'number': 'self_number'}, inplace=True)
    # 将下一层级的单量汇总至前面的层级
    df_employee_info['first_number'] = np.where(df_employee_info['first_inviter_id'].notnull(),
                                                df_employee_info['number'], 0)
    df_employee_info['second_number'] = np.where(df_employee_info['second_inviter_id'].notnull(),
                                                 df_employee_info['number'], 0)
    df_employee_info['third_number'] = np.where(df_employee_info['third_inviter_id'].notnull(),
                                                df_employee_info['number'], 0)
    # 将多级信息汇总合并，主要是下一级代理人的数量要算到一级代理人上
    level_columns_for_general = [
        ['employee_id', 'employee_full_name', 'number', 'max_create_time', 'min_create_time'],
        ['first_inviter_id', 'first_inviter_full_name', 'first_number', 'max_create_time', 'min_create_time'],
        ['second_inviter_id', 'second_inviter_full_name', 'second_number', 'max_create_time', 'min_create_time'],
        ['third_inviter_id', 'third_inviter_full_name', 'third_number', 'max_create_time', 'min_create_time']
    ]
    df_employee = merge_and_aggregate(df_employee_info, level_columns_for_general)
    # 将多级信息汇总合并，主要是下一级代理人的数量要算到一级代理人上,包括产品，产品数据后续需要转成列名
    level_columns_for_product = [
        ['employee_id', 'employee_full_name', 'number', 'product_name'],
        ['first_inviter_id', 'first_inviter_full_name', 'first_number', 'product_name'],
        ['second_inviter_id', 'second_inviter_full_name', 'second_number', 'product_name'],
        ['third_inviter_id', 'third_inviter_full_name', 'third_number', 'product_name']
    ]

    df_employee_product = merge_and_aggregate(df_employee_info, level_columns_for_product)
    df_employee_product = df_employee_product.groupby(['employee_id', 'employee_full_name', 'product_name']).agg(
        {'number': 'sum'}).reset_index()

    # 最大日期和最小日期列，用于下载数据拼接
    df_date_summary = df_employee[df_employee['number'] > 0].groupby(
        ['employee_id', 'employee_full_name']).agg({'max_create_time': 'max', 'min_create_time': 'min'}).reset_index()

    df_employee = df_employee.groupby(['employee_id', 'employee_full_name']).agg({'number': 'sum'}).reset_index()
    # 将一级代理人的机构、部门信息赋值到二、三级代理人
    df_employee = sqldf(
        "select distinct a.*,b.city_name,b.district_name,"
        "case when b.organization_name is null then c.organization_name else b.organization_name end as organization_name ,"
        "case when b.department_first_name is null then c.department_first_name else b.department_first_name end as department_first_name, "
        "case when b.department_name is null then c.department_name else b.department_name end as department_name "
        "from df_employee a "
        " left join df_employee_info b on a.employee_id = b.employee_id"
        " left join df_employee_info c on c.employee_id = b.first_inviter_id"
        " order by b.department_first_name,b.department_name,a.number desc")
    df_employee = pd.merge(df_employee, df_invitee_info, on='employee_id', how='left')
    if df_employee['district_name'].notnull().any():
        df_employee['full_name'] = df_employee['organization_name'] + '/' + df_employee['city_name'] + '/' + \
                                   df_employee['district_name'] + '/' + df_employee['department_first_name'] + '/' + \
                                   df_employee['department_name']
    else:
        df_employee['full_name'] = df_employee['organization_name'] + '/' + df_employee['department_first_name'] + '/' + \
                                   df_employee['department_name']
    df_employee.drop_duplicates(inplace=True)
    df_employee.fillna(np.nan, inplace=True)
    df_employee.reset_index(drop=True, inplace=True)
    if df_employee.shape[0] > 0:
        # 去除删除的员工数据以及断层员工数据（例如2级员工删除、3级员工也会有问题，一并删除）
        df_employee = df_employee[(df_employee['employee_full_name'] != '-') & (df_employee['full_name'].notnull())]
        df_employee['full_name'] = df_employee.apply(update_full_name, axis=1)
    # 获取机构信息
    df_department_info = get_department_info()
    df_department_info = df_department_info[(df_department_info['product_name'] == product_set_name) & (
            df_department_info['organization_name'] == department_name)]
    df_department_info.reset_index(drop=True, inplace=True)

    if 'district_name' in df_department_info.columns and df_department_info['district_name'].notnull().any():
        # 看是否筛选区域，如果没有筛选，默认全部数据

        df_department_info['city_full_name'] = df_department_info.apply(
            lambda row: create_full_name([row['organization_name'], row['city_name']]), axis=1)
        df_department_info['district_full_name'] = df_department_info.apply(
            lambda row: create_full_name([row['organization_name'], row['city_name'], row['district_name']]), axis=1)
        df_department_info['first_full_name'] = df_department_info.apply(lambda row: create_full_name(
            [row['organization_name'], row['city_name'], row['district_name'], row['first_name']]), axis=1)
        df_department_info['second_full_name'] = df_department_info.apply(lambda row: create_full_name(
            [row['organization_name'], row['city_name'], row['district_name'], row['first_name'], row['name']]), axis=1)

        columns_to_concat = ['organization_name', 'city_full_name', 'district_full_name', 'first_full_name',
                             'second_full_name']
        df_department_full_name = pd.concat(
            [df_department_info[[col]].rename(columns={col: 'full_name'}) for col in columns_to_concat if
             col in df_department_info.columns], ignore_index=True)

    else:
        # 如果不含区域，拼接的时候需要剔除，保证后续的树形结构正确
        df_department_info['first_full_name'] = df_department_info.apply(
            lambda row: create_full_name([row['organization_name'], row['first_name']]), axis=1)
        df_department_info['second_full_name'] = df_department_info.apply(
            lambda row: create_full_name([row['organization_name'], row['first_name'], row['name']]), axis=1)
        columns_to_concat = ['organization_name', 'first_full_name', 'second_full_name']
        df_department_full_name = pd.concat(
            [df_department_info[[col]].rename(columns={col: 'full_name'}) for col in columns_to_concat if
             col in df_department_info.columns], ignore_index=True)

    df_department_full_name.drop_duplicates(inplace=True)
    df_department_full_name.reset_index(drop=True, inplace=True)
    # 统计保司的销量
    df_orginization_data = df_employee.query("first_inviter_id.isnull()").groupby(['organization_name']).agg(
        {'number': 'sum'}).reset_index()
    # 如果区域不为空，则先要有城市、区域的统计数据
    df_filtered = df_employee[df_employee['first_inviter_id'].isnull()]
    if df_employee['district_name'].notnull().any():
        # 统计城市级别的数据
        df_city_data = df_filtered.groupby(['organization_name', 'city_name']).agg(
            {'number': 'sum'}).reset_index()
        df_city_data['full_name'] = df_city_data.apply(
            lambda row: create_full_name([row['organization_name'], row['city_name']]), axis=1)

        # 统计区级别的数据
        df_district_data = df_filtered.groupby(['organization_name', 'city_name', 'district_name']).agg(
            {'number': 'sum'}).reset_index()
        df_district_data['full_name'] = df_district_data.apply(
            lambda row: create_full_name([row['organization_name'], row['city_name'], row['district_name']]), axis=1)

        # 统计一级部门的销量
        df_first_department_data = df_filtered.groupby(
            ['organization_name', 'city_name', 'district_name', 'department_first_name']).agg(
            {'number': 'sum'}).reset_index()
        df_first_department_data['full_name'] = df_first_department_data.apply(
            lambda row: create_full_name(
                [row['organization_name'], row['city_name'], row['district_name'], row['department_first_name']]),
            axis=1)

        # 统计二级部门的销量
        df_second_department_data = df_filtered.groupby(
            ['organization_name', 'city_name', 'district_name', 'department_first_name', 'department_name']).agg(
            {'number': 'sum'}).reset_index()
        df_second_department_data['full_name'] = df_second_department_data.apply(
            lambda row: create_full_name(
                [row['organization_name'], row['city_name'], row['district_name'], row['department_first_name'],
                 row['department_name']]), axis=1)

        # 合并数据
        df_department_data = pd.concat([
            df_orginization_data[['organization_name', 'number']].rename(columns={'organization_name': 'full_name'}),
            df_city_data[['full_name', 'number']],
            df_district_data[['full_name', 'number']],
            df_first_department_data[['full_name', 'number']],
            df_second_department_data[['full_name', 'number']]
        ])
    else:
        # 统计一级部门的销量
        df_first_department_data = df_filtered.groupby(
            ['organization_name', 'department_first_name']).agg(
            {'number': 'sum'}).reset_index()
        df_first_department_data['full_name'] = df_first_department_data.apply(
            lambda row: create_full_name([row['organization_name'], row['department_first_name']]), axis=1)

        # 统计二级部门的销量
        df_second_department_data = df_filtered.groupby(
            ['organization_name', 'department_first_name', 'department_name']).agg({'number': 'sum'}).reset_index()
        df_second_department_data['full_name'] = df_second_department_data.apply(
            lambda row: create_full_name(
                [row['organization_name'], row['department_first_name'], row['department_name']]), axis=1)

        # 合并数据
        df_department_data = pd.concat([
            df_orginization_data[['organization_name', 'number']].rename(columns={'organization_name': 'full_name'}),
            df_first_department_data[['full_name', 'number']],
            df_second_department_data[['full_name', 'number']]
        ])

    df_department_data.dropna(subset=['full_name'], inplace=True)
    df_department_data.drop_duplicates(inplace=True)
    df_department_data.reset_index(drop=True, inplace=True)
    df_department_data = pd.merge(df_department_full_name, df_department_data, on='full_name', how='left')
    df_department_data.fillna(0, inplace=True)
    # 根据类型筛选数据
    df_department_data = pd.concat([df_department_data, df_employee[['full_name', 'number']]])

    df_department_data.reset_index(drop=True, inplace=True)

    # 处理需要下载的数据格式
    df_download = sqldf(
        "select a.*,b.organization_name as organization_name_1 ,b.department_first_name department_first_name_1,b.department_name department_name_1 from df_employee_info a left join df_employee_info b on a.first_inviter_id = b.employee_id ")
    df_download['organization_name'] = np.where(df_download['organization_name'].isnull(),
                                                df_download['organization_name_1'], df_download['organization_name'])
    df_download['department_first_name'] = np.where(df_download['department_first_name'].isnull(),
                                                    df_download['department_first_name_1'],
                                                    df_download['department_first_name'])
    df_download['department_name'] = np.where(df_download['department_name'].isnull(),
                                              df_download['department_name_1'], df_download['department_name'])
    df_download['department_full_name'] = df_download['department_first_name'] + df_download['department_name']

    # 获取全部人员数据
    df_total_employee = df_download[
        ['organization_name', 'department_full_name', 'department_first_name', 'department_name', 'employee_name',
         'invitee_level', 'inviter_name', 'staff_no',
         'employee_mobile', 'employee_id']].drop_duplicates().reset_index(drop=True)

    # 获取代理人的销售情况，已经每层累加
    df_total_employee = sqldf(
        "select a.*,b.number from df_total_employee a left join df_employee b on a.employee_id = b.employee_id")

    # 获取代理人最早、最晚的销售时间
    df_total_employee = sqldf(
        "select a.*,b.max_create_time,b.min_create_time from df_total_employee a left join df_date_summary b on a.employee_id = b.employee_id")
    # 获取某期所有的人员、产品数据，完整数据
    df_product_info = get_product_info()
    df_product_info = df_product_info[df_product_info['product_set_code'] == product_set_code]
    employee_product_combinations = pd.MultiIndex.from_product(
        [df_total_employee['employee_id'].unique(), df_product_info['product_name'].unique()],
        names=['employee_id', 'product_name'])
    df_full_combinations = pd.DataFrame(index=employee_product_combinations).reset_index()
    df_employee_product = sqldf(
        "select a.*,ifnull(b.number,0) number from df_full_combinations a left join df_employee_product b on a.employee_id = b.employee_id and a.product_name = b.product_name")

    df_employee_product_pivot = df_employee_product.pivot_table(index='employee_id', columns='product_name',
                                                                values='number', aggfunc='sum').reset_index()
    # 按照顺序排列，数据库里面的顺序
    sorted_product_names = df_product_info['product_name'].unique().tolist()
    sorted_product_names.append('employee_id')
    df_employee_product_pivot = df_employee_product_pivot[sorted_product_names]
    df_total_employee.sort_values(by=['organization_name', 'department_full_name', 'number'], ascending=False,
                                  inplace=True)
    df_total_employee = df_total_employee[
        ['organization_name', 'department_first_name', 'department_name', 'employee_name', 'invitee_level',
         'inviter_name', 'staff_no',
         'employee_mobile', 'min_create_time', 'max_create_time', 'number', 'employee_id']]
    df_total_employee = df_total_employee.merge(df_employee_product_pivot, on='employee_id', how='left')
    # 代理人层级信息标准化转化
    df_total_employee['invitee_level'] = df_total_employee['invitee_level'].apply(lambda x: level_transformer(x))
    # 处理部门缺失的一级代理人数据，将主险的数据赋值到number字段中，如果number为空 ，主险的列名为sorted_product_names的值
    # sorted_product_names列表长度不定，动态获取列名，并求和
    df_total_employee['number_compute'] = df_total_employee[df_product_info['product_name'].unique().tolist()].sum(
        axis=1)
    # 如果number为空，用计算替换
    df_total_employee.loc[
        (df_total_employee['number'].isnull()) & (df_total_employee['number_compute'].notna()), 'number'] = \
        df_total_employee['number_compute']
    # 拼接一级代理人自己的销量、并计算非一级代理人的销量
    df_total_employee = pd.merge(df_total_employee, df_first_employee_sale, on='employee_id', how='left')
    # 获取self_开头的列，赋值空值为0
    self_columns = [col for col in df_total_employee.columns if col.startswith('self_')]
    df_total_employee[self_columns] = df_total_employee[self_columns].fillna(0)

    # 计算总销量
    df_total_employee['not_self_sale'] = df_total_employee['number'] - df_total_employee['self_number']
    if show_product:
        for col in df_product_info['product_name'].unique().tolist():
            df_total_employee[f'not_self_{col}'] = df_total_employee[col] - df_total_employee[f'self_{col}']
    df_total_employee.drop(['number_compute'], axis=1, inplace=True)
    df_management_info = get_management_info(product_set_code, '中国人保')
    df_management_info['employee_id'] = df_management_info['employee_id'].astype(str)

    # 一把手销售
    df_first_employee = df_management_info[df_management_info['type_name'] == '一把手']
    df_first_employee_sale = pd.merge(df_total_employee, df_first_employee, on='employee_id', how='inner')
    df_first_employee_sale = df_column_filter(df_first_employee_sale,df_product_info,show_product)
    df_first_employee_sale.sort_values(by=['number'], ascending=False, inplace=True)

    first_number_sum = df_first_employee_sale.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    df_first_employee_sale = pd.concat([df_first_employee_sale, first_number_sum])
    df_first_employee_sale.reset_index(drop=True, inplace=True)
    df_first_employee_sale['psoition'] = df_first_employee_sale.index + 1
    df_first_employee_sale = df_column_rename(df_first_employee_sale, df_product_info, show_product)
    # 分管销量
    df_sec_employee = df_management_info[df_management_info['type_name'] == '分管']
    df_sec_employee_sale = pd.merge(df_total_employee, df_sec_employee, on='employee_id', how='inner')
    df_sec_employee_sale = df_column_filter(df_sec_employee_sale, df_product_info, show_product)
    df_sec_employee_sale.sort_values(by=['number'], ascending=False, inplace=True)

    sec_number_sum = df_sec_employee_sale.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    df_sec_employee_sale = pd.concat([df_sec_employee_sale, sec_number_sum])
    df_sec_employee_sale.reset_index(drop=True, inplace=True)
    df_sec_employee_sale['psoition'] = df_sec_employee_sale.index + 1
    df_sec_employee_sale = df_column_rename(df_sec_employee_sale, df_product_info, show_product)

    # 团队长销量
    df_third_employee = df_management_info[df_management_info['type_name'] == '团队长']
    df_third_employee_sale = pd.merge(df_total_employee, df_third_employee, on='employee_id', how='inner')
    df_third_employee_sale = df_column_filter(df_third_employee_sale, df_product_info, show_product)
    df_third_employee_sale.sort_values(by=['number'], ascending=False, inplace=True)

    third_number_sum = df_third_employee_sale.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    df_third_employee_sale = pd.concat([df_third_employee_sale, third_number_sum])
    df_third_employee_sale.reset_index(drop=True, inplace=True)
    df_third_employee_sale['psoition'] = df_third_employee_sale.index + 1
    df_third_employee_sale = df_column_rename(df_third_employee_sale, df_product_info, show_product)

    # 本部门领导销量
    df_four_employee = df_management_info[df_management_info['type_name'] == '部门领导']
    df_four_employee_sale = pd.merge(df_total_employee, df_four_employee, on='employee_id', how='inner')
    df_four_employee_sale = df_column_filter(df_four_employee_sale, df_product_info, show_product)
    df_four_employee_sale.sort_values(by=['number'], ascending=False, inplace=True)

    four_number_sum = df_four_employee_sale.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    df_four_employee_sale = pd.concat([df_four_employee_sale, four_number_sum])
    df_four_employee_sale.reset_index(drop=True, inplace=True)
    df_four_employee_sale['psoition'] = df_four_employee_sale.index + 1
    df_four_employee_sale = df_column_rename(df_four_employee_sale, df_product_info, show_product)

    # 邮政销量
    df_five_employee = df_management_info[df_management_info['type_name'] == '其他-邮政']
    df_five_employee_sale = pd.merge(df_total_employee, df_five_employee, on='employee_id', how='inner')
    if not show_product:
        df_five_employee_sale = df_five_employee_sale[
            ['department_first_name', 'department_name', 'employee_name','invitee_level', 'number', 'self_number', 'not_self_sale']]
        # 因为有人有多个id，分组统计
        df_five_employee_sale = df_five_employee_sale.groupby(['employee_name']).agg(
            {'department_first_name': 'first', 'department_name': 'first', 'number': 'sum', 'self_number': 'sum',
             'not_self_sale': 'sum', 'invitee_level': 'first'}).reset_index()
    else:
        column_list = ['department_first_name', 'department_name', 'employee_name','invitee_level', 'number', 'self_number', 'not_self_sale']
        for col in df_product_info['product_name'].unique().tolist():
            column_list.append(col)
            column_list.append(f'self_{col}')
            column_list.append(f'not_self_{col}')
        df_five_employee_sale = df_five_employee_sale[column_list]
        # 因为有人有多个id，分组统计
        # 获取所有列名
        column_fixed = ['department_first_name', 'department_name', 'employee_name', 'invitee_level', 'number',
                       'self_number', 'not_self_sale']
        all_columns = df_five_employee_sale.columns.tolist()
        # 动态生成聚合规则
        agg_dict = {col: 'sum' for col in all_columns if col not in column_fixed}
        agg_dict.update({
            'department_first_name': 'first',
            'department_name': 'first',
            'invitee_level': 'first',
            'number': 'sum',
            'self_number': 'sum',
            'not_self_sale': 'sum'
        })
        # 进行分组统计
        df_five_employee_sale = df_five_employee_sale.groupby(['employee_name']).agg(agg_dict).reset_index()


    df_five_employee_sale.sort_values(by=['number'], ascending=False, inplace=True)

    five_number_sum = df_five_employee_sale.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T
    df_five_employee_sale = pd.concat([df_five_employee_sale, five_number_sum])
    df_five_employee_sale.reset_index(drop=True, inplace=True)
    df_five_employee_sale['psoition'] = df_five_employee_sale.index + 1
    df_five_employee_sale = df_column_rename(df_five_employee_sale, df_product_info, show_product)

    return df_first_employee_sale, df_sec_employee_sale, df_third_employee_sale, df_four_employee_sale, df_five_employee_sale


@st.fragment
def download_excel(df):
    # 下载数据，添加装饰器，按钮不会导致页面刷新
    excel_name = Path.cwd().joinpath('temp_files').joinpath("代理人统计报表.xlsx")
    file_name = f"代理人统计报表{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"
    df.to_excel(excel_name, index=False)
    st.download_button(
        label='下载数据',
        data=open(excel_name, 'rb').read(),
        file_name=file_name,
        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )


def main():
    # 权限检查
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("人保单量统计")
    product_info = get_product_code()
    df_department = get_department_info()
    # 选择日期
    sale_from = datetime.date(2021, 1, 7)
    sale_until = datetime.date.today()
    # send_feishu_message(product_set_code_iframe)
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[
                        0]
            else:
                product_set_code = None
        cols = st.columns([0.2, 0.2])

        with cols[0]:
            start_date = st.date_input('销售开始日期', min_value=sale_from, max_value=sale_until, value=None,
                                       key='start_date')
        with cols[1]:
            end_date = st.date_input('销售结束日期', min_value=sale_from, max_value=sale_until, value=None,
                                     key='end_date')

        show_product = st.checkbox('分产品统计', value=False)

        st.divider()
        if st.button('查询'):
            if start_date and end_date and start_date > end_date:
                st.error('开始日期不能大于结束日期')
            else:
                with st.spinner('查询中...'):
                    department_id = '1678177298202756103'
                    department_name = '中国人民财产保险股份有限公司南京市分公司'
                    df_first_employee_sale, df_sec_employee_sale, df_third_employee_sale, df_four_employee_sale, df_five_employee_sale = get_employee_info(
                        product_set_code, column_name, department_id,
                        department_name, start_date, end_date,show_product)
                    text_write('管理层单量统计')
                    sub_text_write('一把手单量统计')
                    empty_line(1)
                    st.dataframe(df_first_employee_sale, hide_index=True, use_container_width=True)
                    sub_text_write('分管单量统计')
                    empty_line(1)
                    st.dataframe(df_sec_employee_sale, hide_index=True, use_container_width=True)
                    sub_text_write('团队长单量统计')
                    empty_line(1)
                    st.dataframe(df_third_employee_sale, hide_index=True, use_container_width=True)
                    sub_text_write('部门领导单量统计')
                    empty_line(1)
                    st.dataframe(df_four_employee_sale, hide_index=True, use_container_width=True)
                    text_write('邮政单量统计')
                    empty_line(1)
                    st.dataframe(df_five_employee_sale, hide_index=True, use_container_width=True)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()