# 字段处理配置管理架构

## 📋 概述

新的字段处理配置管理架构采用分省份配置的设计，解决了原来需要在多个文件中重复配置的问题，同时支持不同省份有不同的字段处理策略。

## 🏗️ 架构设计

### 1. 基础框架 (utils层)
- **位置**: `transfrom/utils/field_config.py`
- **功能**: 提供配置管理的基础能力
- **内容**:
  - `BaseFieldProcessingConfig` - 抽象基类
  - `FieldConfigManager` - 配置管理器
  - 便捷函数接口

### 2. 省份配置 (各省份pipeline目录)
- **位置**: `transfrom/tasks/spider/{province}/pipeline/config/field_config.py`
- **功能**: 实现具体的业务配置
- **内容**: 继承`BaseFieldProcessingConfig`，实现`get_field_strategies()`方法

## 📁 目录结构

```
transfrom/
├── utils/
│   └── field_config.py                    # 基础框架
└── tasks/
    └── spider/
        ├── gjyb/
        │   └── pipeline/
        │       └── config/
        │           ├── __init__.py         # 自动注册gjyb配置
        │           └── field_config.py     # gjyb具体配置
        ├── jsyb/
        │   └── pipeline/
        │       └── config/
        │           ├── __init__.py         # 自动注册jsyb配置
        │           └── field_config.py     # jsyb具体配置
        └── njyb/
            └── pipeline/
                └── config/
                    ├── __init__.py         # 自动注册njyb配置
                    └── field_config.py     # njyb具体配置
```

## 🚀 使用方法

### 1. 创建新省份配置

#### 步骤1: 创建配置类
```python
# transfrom/tasks/spider/njyb/pipeline/config/field_config.py
from transfrom.utils.field_config import BaseFieldProcessingConfig

class NjybFieldProcessingConfig(BaseFieldProcessingConfig):
    def get_field_strategies(self) -> Dict:
        return {
            'spider_fuwu_fixed_hospital': {
                'medical_designated_providers': {
                    'level': 'null_to_empty_string',
                    'custom_field': 'null_to_empty_string',
                    # 南京特有的字段配置
                }
            }
        }

njyb_field_config = NjybFieldProcessingConfig()
```

#### 步骤2: 自动注册配置
```python
# transfrom/tasks/spider/njyb/pipeline/config/__init__.py
from transfrom.utils.field_config import register_province_config
from .field_config import njyb_field_config

register_province_config('njyb', njyb_field_config)
```

### 2. 使用配置

#### 在ETL代码中使用
```python
# 导入省份配置（触发自动注册）
import transfrom.tasks.spider.gjyb.pipeline.config

# 使用配置
from transfrom.utils.data_cleaning import clean_source_data

# 清洗数据时指定省份
cleaned_df = clean_source_data(source_df, 'spider_fuwu_fixed_hospital', province='gjyb')
```

#### 直接获取配置
```python
from transfrom.utils.field_config import (
    get_data_cleaning_config,
    get_field_mapping_config,
    get_data_normalization_config
)

# 获取特定省份的配置
cleaning_config = get_data_cleaning_config('gjyb', 'spider_fuwu_fixed_hospital')
mapping_config = get_field_mapping_config('gjyb', 'source_table', 'target_table')
normalization_config = get_data_normalization_config('gjyb', 'target_table')
```

## 🎯 配置策略

### 支持的处理策略
- `null_to_empty_string`: NULL值转换为空字符串
- `preserve_empty_string`: 保留空字符串（不转换为None）
- `standard_cleaning`: 标准清洗（空字符串转None）

### 配置示例
```python
def get_field_strategies(self) -> Dict:
    return {
        '源表名': {
            '目标表名': {
                '字段名1': 'null_to_empty_string',
                '字段名2': 'preserve_empty_string',
                '字段名3': 'standard_cleaning'
            }
        }
    }
```

## ✅ 优势

### 1. 配置隔离
- 各省份配置独立，互不影响
- 便于并行开发和维护

### 2. 统一管理
- 基础框架统一，避免重复代码
- 配置自动注册，使用简单

### 3. 灵活扩展
- 新增省份只需创建对应配置文件
- 支持省份特有的字段处理策略

### 4. 向后兼容
- 保持原有的函数接口
- 默认使用gjyb配置

## 🔧 迁移指南

### 从旧架构迁移
1. **移除硬编码配置**: 删除各模块中的硬编码字段配置
2. **创建省份配置**: 按新架构创建省份特定配置
3. **更新调用代码**: 在ETL代码中指定province参数

### 注意事项
- 确保在使用前导入对应的省份配置模块
- 省份标识要保持一致（如'gjyb', 'jsyb', 'njyb'）
- 配置文件中的字段名要与实际数据字段名匹配

## 🐛 故障排除

### 常见问题
1. **配置未生效**: 检查是否导入了省份配置模块
2. **字段处理异常**: 检查字段名是否正确配置
3. **省份不存在**: 会使用空配置，不会报错

### 调试方法
```python
from transfrom.utils.field_config import field_config_manager

# 查看已注册的省份
print(field_config_manager._configs.keys())

# 查看特定省份的配置
config = field_config_manager.get_config('gjyb')
if config:
    print(config.get_field_strategies())
```
