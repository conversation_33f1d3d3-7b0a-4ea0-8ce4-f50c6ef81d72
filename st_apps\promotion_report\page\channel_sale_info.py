import datetime
import warnings
from pathlib import Path
import jwt
import numpy as np
import pandas as pd
import streamlit as st
import streamlit_antd_components as sac
from pandasql import sqldf
from st_aggrid import JsCode, AgGrid, GridOptionsBuilder, GridUpdateMode
from utils.auth import auth_from_session, get_agent_auth, JWT_SECRET, agent_auth_check
from utils.utils import send_feishu_message, sum_or_combine, simplify_replace
from utils.st import query_sql

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
        ps.name AS product_set_name,
        ps.code AS product_set_code,
        DATE_FORMAT(MIN(p.sale_from), '%Y-%m-%d') AS sale_from,
        DATE_FORMAT(MIN(p.pre_sale_from), '%Y-%m-%d') AS pre_sale_from
    FROM
        product_set ps
    JOIN
        product p ON p.product_set_id = ps.id AND p.main = 1
    WHERE
        LEFT(ps.code, 10) in ('rizhao_nxb','dezhou_hmb','binzhou_yh')
    GROUP BY
        ps.name
    ORDER BY
        ps.name DESC;

        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    return df_product_code


def get_daily_seller_forward(product_set_code, sale_start_datetime):
    """
    获取保司物料的传播情况，包括阅读和转发
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售开始时间
    :return:
    """
    sql = """
    SELECT
    date( m.create_time ) create_time,
        s.short_name AS 保司,
        m.type AS 动作,
        count(*) AS 数量 
    FROM
        material_operating_trajectory m
        join material mt on m.material_id = mt.id
        JOIN employee e ON m.agent_id = e.id
        JOIN seller s ON e.organization_id = s.id
    WHERE
         mt.product_set = '{product_set_code}' 
         and m.create_time >= '{sale_start_datetime}'
    GROUP BY
    date( m.create_time ),
        m.type,
        s.short_name
    order by date( m.create_time)
        """
    df = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code, sale_start_datetime=sale_start_datetime),
                             ttl=0)
    print(sql.format(product_set_code=product_set_code, sale_start_datetime=sale_start_datetime))
    simplify_replace(df, '动作', {'FORWARD': '转发', 'PLAY': '阅读'})
    pivot_df = df.pivot_table(index=['create_time', '保司'], columns='动作', values='数量', aggfunc=sum).reset_index()
    # 将空值替换为 0
    pivot_df['转发'] = pivot_df['转发'].fillna(0)
    pivot_df['阅读'] = pivot_df['阅读'].fillna(0)
    pivot_df['合计'] = pivot_df['转发'] + pivot_df['阅读']
    return pivot_df


def get_channel_info(product_set_code, end_date, sale_date):
    """
    获取线下保司的数据、线上数据，包括排名、保司、占比、代理人数、人均出单、个单、团单、今日参保、昨日参保、目标、完成率
    """
    today = datetime.datetime.strptime(end_date, '%Y-%m-%d')
    yesterday = datetime.datetime.strptime(end_date, '%Y-%m-%d') - datetime.timedelta(days=1)
    publish_time = end_date + ' 23:59:59'
    df_daily_seller_forward = get_daily_seller_forward(product_set_code, sale_date)

    df_daily_seller_forward = df_daily_seller_forward[df_daily_seller_forward['create_time'] <= today.date()]
    df_daily_seller_forward = df_daily_seller_forward.groupby(['保司']).agg({'转发': 'sum', }).reset_index()
    df_daily_seller_forward.rename(columns={'转发': '海报转发次数', '保司': 'seller'}, inplace=True)
    df_daily_seller_forward['seller'] = df_daily_seller_forward['seller'].apply(lambda x: '线上' if '线上' in x else x)

    # 获取保司代理人数量
    if product_set_code[:10] == 'rizhao_nxb':
        df_seller_person = CONNECTOR_JKX.query(
            query_sql('SQL_SELLER_PERSON_FIRST').format(product_set_code=product_set_code), ttl=0)
    else:
        df_seller_person = CONNECTOR_JKX.query(
            query_sql('SQL_SELLER_PERSON').format(product_set_code=product_set_code), ttl=0)
    data = CONNECTOR_JKX.query(
        query_sql('SQL_JKX_DAILY_SALE').format(product_set_code=product_set_code,
                                               end_datetime=publish_time), ttl=0)
    data = data[data['main'] == 1]
    # 线上单独处理
    df_online_info = data.query("is_online==1").groupby(['date', 'is_personal']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index()
    df_online_info['seller'] = '线上'

    # 线下处理
    df_offline_seller_info = data.query("is_online==0").groupby(['seller', 'date', 'is_personal']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index()
    # 合并数据，线上线下
    df_offline_seller_info = pd.concat([df_offline_seller_info, df_online_info], axis=0).reset_index(drop=True)

    df_offline_seller = df_offline_seller_info.groupby(['seller']).agg({'count': 'sum', 'amount': 'sum'}).reset_index()
    df_offline_seller = pd.merge(df_offline_seller, df_seller_person, how='outer', on='seller')

    df_offline_seller['seller_is_online'] = np.where(df_offline_seller['seller'].str.contains('线上'), 1, 0)
    df_offline_seller['seller'] = np.where(df_offline_seller['seller'].str.contains('线上'), '线上',
                                           df_offline_seller['seller'])
    df_offline_seller['seller_name'] = df_offline_seller['seller_name'].fillna(df_offline_seller['seller'])
    df_offline_seller.drop(columns=['seller_name'], inplace=True)
    df_offline_seller = df_offline_seller.groupby(['seller']).sum().reset_index()
    # 个单数量
    df_offline_seller_personal = df_offline_seller_info.query("is_personal==1").groupby(['seller']).agg(
        {'count': 'sum'}).reset_index().rename(columns={'count': 'personal_count'})
    # 团单数量
    # 数据库取值逻辑
    df_offline_seller_group = df_offline_seller_info.query("is_personal==0").groupby(['seller']).agg(
        {'count': 'sum'}).reset_index().rename(columns={'count': 'group_count'})
    df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_personal, how='left', on='seller')
    df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_group, how='left', on='seller')
    # 手动计算
    df_offline_seller['personal_count'] = df_offline_seller['personal_count'].fillna(0).astype(int)
    df_offline_seller['group_count'] = df_offline_seller['group_count'].fillna(0).astype(int)
    df_offline_seller['count'] = df_offline_seller['personal_count'] + df_offline_seller['group_count']

    df_offline_seller.fillna(0, inplace=True)
    # 只计算共保体的占比
    df_offline_seller['insure_ratio'] = round(
        df_offline_seller['count'] / df_offline_seller[df_offline_seller['seller'] != '线上']['count'].sum(), 3)

    # df_offline_seller_info['date'] = df_offline_seller_info['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_offline_seller_today = df_offline_seller_info[df_offline_seller_info['date'] == today.date()]
    # 今日团单
    df_group_today = df_offline_seller_info[
        (df_offline_seller_info['date'] == today.date()) & (df_offline_seller_info['is_personal'] == 0)]
    df_offline_seller_yesterday = df_offline_seller_info[df_offline_seller_info['date'] == yesterday.date()]
    df_offline_seller_today = df_offline_seller_today.groupby(['seller']).agg(
        {'count': 'sum', 'amount': 'sum'}).reset_index().rename(
        columns={'count': 'today_count', 'amount': 'today_amount'})

    df_group_today = df_group_today.groupby(['seller']).agg(
        {'count': 'sum'}).reset_index().rename(
        columns={'count': 'today_group_count'})

    df_offline_seller_yesterday = df_offline_seller_yesterday.groupby(['seller']).agg(
        {'count': 'sum'}).reset_index().rename(columns={'count': 'yesterday_count'})
    df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_today, how='left', on='seller')
    df_offline_seller = pd.merge(df_offline_seller, df_offline_seller_yesterday, how='left', on='seller')
    df_offline_seller = pd.merge(df_offline_seller, df_group_today, how='left', on='seller')
    df_offline_seller.fillna(0, inplace=True)

    df_offline_seller.reset_index(drop=True, inplace=True)
    df_offline_seller = pd.merge(df_offline_seller, df_daily_seller_forward, on='seller', how='outer')
    # 强制排序
    df_offline_zgrb = df_offline_seller[df_offline_seller['seller'] == '人保财险']
    df_offline_t = df_offline_seller[df_offline_seller['seller'] == '线上']
    df_offline_other = df_offline_seller[
        (df_offline_seller['seller'] != '人保财险') & (df_offline_seller['seller'] != '线上')]
    df_offline_other.sort_values(by=['count'], ascending=False, inplace=True)
    # 合并成完整数据
    df_offline_seller_only = pd.concat([df_offline_zgrb, df_offline_other], axis=0).reset_index(drop=True)
    df_offline_seller = pd.concat([df_offline_zgrb, df_offline_other, df_offline_t], axis=0).reset_index(drop=True)

    number_sum_seller = df_offline_seller_only.apply(lambda x: sum_or_combine(x, '小计'), axis=0).to_frame().T
    number_sum_seller['insure_ratio'] = 1
    number_sum = df_offline_seller.apply(lambda x: sum_or_combine(x), axis=0).to_frame().T

    df_offline_seller = pd.concat([df_offline_seller_only, number_sum_seller, df_offline_t, number_sum],
                                  axis=0).reset_index(drop=True)
    df_offline_seller['insure_ratio'] = df_offline_seller['insure_ratio'].apply(lambda x: str(round(x * 100, 2)) + '%')
    df_offline_seller['insure_ratio'] = df_offline_seller.apply(
        lambda x: np.nan if x['seller'] in ['线上', '合计'] else x['insure_ratio'], axis=1)
    df_offline_seller.drop(columns=['seller_is_online'], inplace=True)
    df_offline_seller.rename(
        columns={'seller': '渠道', 'count': '累计参保人数', 'amount': '累计参保保费', 'personal_count': '个人保单数量',
                 'group_count': '累计团单人数', 'today_count': '当日参保人数', 'today_amount': '当日参保保费',
                 'yesterday_count': '昨日保单数量', 'today_group_count': '当日团单人数', 'employee_num': '注册代理人(一级)',
                 'insure_ratio': '线下参保占比'},
        inplace=True)
    df_offline_seller = df_offline_seller[
        ['渠道', '当日参保人数', '当日参保保费', '当日团单人数', '累计团单人数', '累计参保人数', '累计参保保费',
         '线下参保占比', '注册代理人(一级)', '海报转发次数']]
    return df_offline_seller


def main():
    is_iframe, seller_name, disable, product_set_code_iframe, seller_subsidiary_name = agent_auth_check()
    st.subheader("分渠道销量统计")
    product_info = get_product_code()
    # 选择日期
    sale_from = datetime.date(2021, 1, 7)
    sale_until = datetime.date.today()
    if product_set_code_iframe in product_info['product_set_code'].values or product_set_code_iframe is None:
        cols = st.columns(3)
        with cols[0]:
            # 如果是权利端来的，而且有产品名称，则默认显示该产品名称，否则控件放开
            if is_iframe == 1 and product_set_code_iframe is not None:
                product_set_name_iframe = \
                    product_info[product_info['product_set_code'] == product_set_code_iframe][
                        'product_set_name'].values[
                        0]

                column_name = st.selectbox("请选择产品", options=[product_set_name_iframe],
                                           placeholder="请选择产品", disabled=True)
            else:
                column_name = st.selectbox("请选择产品", index=None, options=product_info['product_set_name'],
                                           placeholder="请选择产品")
            if column_name:
                product_set_code = \
                    product_info[product_info['product_set_name'] == column_name]['product_set_code'].values[0]
                sale_date = min(product_info[product_info['product_set_name'] == column_name]['sale_from'].values[0],
                                product_info[product_info['product_set_name'] == column_name]['pre_sale_from'].values[
                                    0])
            else:
                product_set_code = None
                sale_date = '2000-01-01'
        with cols[1]:
            end_date = st.date_input('截止日期', min_value=sale_from, max_value=sale_until, value=sale_until,
                                     key='end_date')

        st.divider()
        if st.button('查询'):
            with st.spinner('查询中...'):
                df = get_channel_info(product_set_code, end_date.strftime('%Y-%m-%d'), sale_date)
                st.dataframe(df,hide_index=True,use_container_width=True)

    else:
        st.info('未找到产品信息')


if __name__ == '__main__':
    main()
