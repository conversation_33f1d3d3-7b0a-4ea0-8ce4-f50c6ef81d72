"""
自定义Django管理命令：跨数据库兼容的迁移工具
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from common.db_migration import DatabaseMigrationManager
from common.db_utils import DatabaseCompatibilityUtils
import json


class Command(BaseCommand):
    help = '执行跨数据库兼容的数据迁移，支持MySQL、PostgreSQL、SQLite等'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            choices=['info', 'add_timestamps', 'modify_column', 'rollback', 'check', 'auto_discover'],
            default='info',
            help='要执行的操作类型'
        )

        parser.add_argument(
            '--table',
            type=str,
            help='目标表名'
        )

        parser.add_argument(
            '--column',
            type=str,
            help='目标列名'
        )

        parser.add_argument(
            '--column-type',
            type=str,
            help='列类型（如：VARCHAR(255), DATETIME等）'
        )

        parser.add_argument(
            '--default-value',
            type=str,
            help='默认值'
        )

        parser.add_argument(
            '--comment',
            type=str,
            help='列注释'
        )

        parser.add_argument(
            '--database',
            type=str,
            default='default',
            help='数据库别名（默认：default）'
        )

        parser.add_argument(
            '--migration-name',
            type=str,
            help='迁移名称（用于回滚操作）'
        )

        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要执行的SQL，不实际执行'
        )

        parser.add_argument(
            '--app-names',
            type=str,
            nargs='*',
            help='要处理的app名称列表，如：insure claim other'
        )

        parser.add_argument(
            '--table-patterns',
            type=str,
            nargs='*',
            help='表名模式列表，如：insure_* claim_*'
        )

        parser.add_argument(
            '--exclude-tables',
            type=str,
            nargs='*',
            help='要排除的表名列表'
        )

    def handle(self, *args, **options):
        action = options['action']
        database_alias = options['database']

        # 初始化迁移管理器
        migration_manager = DatabaseMigrationManager(database_alias)
        db_utils = DatabaseCompatibilityUtils()

        try:
            if action == 'info':
                self._show_database_info(db_utils, database_alias)

            elif action == 'add_timestamps':
                self._add_timestamps(migration_manager, options)

            elif action == 'modify_column':
                self._modify_column(migration_manager, options)

            elif action == 'check':
                self._check_table_column(migration_manager, options)

            elif action == 'rollback':
                self._rollback_migration(migration_manager, options)

            elif action == 'auto_discover':
                self._auto_discover_tables(migration_manager, options)

            else:
                raise CommandError(f"不支持的操作: {action}")

        except Exception as e:
            raise CommandError(f"命令执行失败: {str(e)}")

    def _show_database_info(self, db_utils, database_alias):
        """显示数据库信息"""
        db_type = db_utils.get_database_type(database_alias)
        db_version = db_utils.get_database_version(database_alias)

        self.stdout.write(self.style.SUCCESS("=== 数据库信息 ==="))
        self.stdout.write(f"数据库别名: {database_alias}")
        self.stdout.write(f"数据库类型: {db_type}")
        self.stdout.write(f"数据库版本: {db_version}")

        # 显示支持的语法
        self.stdout.write(self.style.SUCCESS("\n=== 支持的语法 ==="))
        syntax_keys = ['current_timestamp', 'datetime_type', 'text_type', 'varchar_type']
        for key in syntax_keys:
            syntax = db_utils.get_syntax(key, database_alias)
            self.stdout.write(f"{key}: {syntax}")

    def _add_timestamps(self, migration_manager, options):
        """为表添加时间戳默认值"""
        table_name = options.get('table')
        if not table_name:
            raise CommandError("--table 参数是必需的")

        # 检查表是否存在
        if not migration_manager.check_table_exists(table_name):
            raise CommandError(f"表 {table_name} 不存在")

        if options.get('dry_run'):
            # 仅显示SQL
            sql_statements = migration_manager.db_utils.generate_timestamp_columns_sql(
                table_name, migration_manager.database_alias
            )
            self.stdout.write(self.style.WARNING("=== 将要执行的SQL（dry-run模式） ==="))
            for sql in sql_statements:
                self.stdout.write(sql)
        else:
            # 实际执行
            success = migration_manager.add_timestamp_defaults(table_name)
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f"成功为表 {table_name} 添加时间戳默认值")
                )
            else:
                raise CommandError(f"为表 {table_name} 添加时间戳默认值失败")

    def _modify_column(self, migration_manager, options):
        """修改列并添加默认值"""
        table_name = options.get('table')
        column_name = options.get('column')
        column_type = options.get('column_type')

        if not all([table_name, column_name, column_type]):
            raise CommandError("--table, --column, --column-type 参数都是必需的")

        # 检查表和列是否存在
        if not migration_manager.check_table_exists(table_name):
            raise CommandError(f"表 {table_name} 不存在")

        if not migration_manager.check_column_exists(table_name, column_name):
            raise CommandError(f"列 {table_name}.{column_name} 不存在")

        default_value = options.get('default_value')
        comment = options.get('comment')

        if options.get('dry_run'):
            # 仅显示SQL
            sql_statements = migration_manager.db_utils.generate_alter_table_sql(
                table_name, column_name, column_type, default_value, comment,
                migration_manager.database_alias
            )
            if isinstance(sql_statements, str):
                sql_statements = [sql_statements]

            self.stdout.write(self.style.WARNING("=== 将要执行的SQL（dry-run模式） ==="))
            for sql in sql_statements:
                self.stdout.write(sql)
        else:
            # 实际执行
            success = migration_manager.modify_column_with_default(
                table_name, column_name, column_type, default_value, comment
            )
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f"成功修改列 {table_name}.{column_name}")
                )
            else:
                raise CommandError(f"修改列 {table_name}.{column_name} 失败")

    def _check_table_column(self, migration_manager, options):
        """检查表和列的存在性"""
        table_name = options.get('table')
        if not table_name:
            raise CommandError("--table 参数是必需的")

        table_exists = migration_manager.check_table_exists(table_name)
        self.stdout.write(f"表 {table_name} 存在: {table_exists}")

        column_name = options.get('column')
        if column_name and table_exists:
            column_exists = migration_manager.check_column_exists(table_name, column_name)
            self.stdout.write(f"列 {table_name}.{column_name} 存在: {column_exists}")

    def _rollback_migration(self, migration_manager, options):
        """回滚迁移（需要实现具体的回滚逻辑）"""
        migration_name = options.get('migration_name')
        if not migration_name:
            raise CommandError("--migration-name 参数是必需的")

        self.stdout.write(
            self.style.WARNING(f"回滚功能需要根据具体的迁移记录实现，迁移名称: {migration_name}")
        )

    def _auto_discover_tables(self, migration_manager, options):
        """自动发现并处理表"""
        from django.db import connection

        app_names = options.get('app_names')
        table_patterns = options.get('table_patterns')
        exclude_tables = options.get('exclude_tables', [])
        dry_run = options.get('dry_run', False)

        # 获取所有包含时间戳字段的表
        all_tables = self._get_tables_with_timestamps(connection)

        # 过滤表
        target_tables = self._filter_discovered_tables(
            all_tables, app_names, table_patterns, exclude_tables
        )

        self.stdout.write(f"发现 {len(target_tables)} 个符合条件的表:")
        for table in target_tables:
            self.stdout.write(f"  - {table}")

        if not target_tables:
            self.stdout.write(self.style.WARNING("没有找到符合条件的表"))
            return

        if dry_run:
            self.stdout.write(self.style.WARNING("\n=== 预览模式，将要执行的操作 ==="))
            for table_name in target_tables:
                sql_statements = migration_manager.db_utils.generate_timestamp_columns_sql(
                    table_name, migration_manager.database_alias
                )
                self.stdout.write(f"\n表 {table_name}:")
                for sql in sql_statements:
                    self.stdout.write(f"  {sql}")
        else:
            # 实际执行
            self.stdout.write(f"\n开始为 {len(target_tables)} 个表添加时间戳默认值...")

            successful = []
            failed = []

            for table_name in target_tables:
                try:
                    success = migration_manager.add_timestamp_defaults(table_name)
                    if success:
                        self.stdout.write(
                            self.style.SUCCESS(f"[OK] 表 {table_name} 处理成功")
                        )
                        successful.append(table_name)
                    else:
                        self.stdout.write(
                            self.style.ERROR(f"[ERROR] 表 {table_name} 处理失败")
                        )
                        failed.append(table_name)
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"[ERROR] 表 {table_name} 处理出错: {str(e)}")
                    )
                    failed.append(table_name)

            # 输出摘要
            self.stdout.write(f"\n=== 处理完成 ===")
            self.stdout.write(f"成功: {len(successful)} 个表")
            self.stdout.write(f"失败: {len(failed)} 个表")

            if failed:
                self.stdout.write(self.style.ERROR(f"失败的表: {', '.join(failed)}"))

    def _get_tables_with_timestamps(self, connection):
        """获取所有包含create_time和update_time字段的表"""
        with connection.cursor() as cursor:
            db_type = connection.vendor

            if db_type == 'mysql':
                cursor.execute("""
                    SELECT DISTINCT table_name
                    FROM information_schema.columns
                    WHERE table_schema = DATABASE()
                    AND column_name IN ('create_time', 'update_time')
                    GROUP BY table_name
                    HAVING COUNT(DISTINCT column_name) = 2
                """)
            elif db_type == 'postgresql':
                cursor.execute("""
                    SELECT DISTINCT table_name
                    FROM information_schema.columns
                    WHERE column_name IN ('create_time', 'update_time')
                    GROUP BY table_name
                    HAVING COUNT(DISTINCT column_name) = 2
                """)
            elif db_type == 'sqlite':
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                all_tables = [row[0] for row in cursor.fetchall()]
                tables_with_timestamps = []

                for table in all_tables:
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [col[1] for col in cursor.fetchall()]
                    if 'create_time' in columns and 'update_time' in columns:
                        tables_with_timestamps.append(table)

                return tables_with_timestamps
            else:
                return []

            return [row[0] for row in cursor.fetchall()]

    def _filter_discovered_tables(self, all_tables, app_names, table_patterns, exclude_tables):
        """根据条件过滤表"""
        import fnmatch

        filtered_tables = all_tables.copy()

        # 按app名称过滤
        if app_names:
            app_filtered = []
            for app_name in app_names:
                app_filtered.extend([t for t in filtered_tables if t.startswith(f"{app_name}_")])
            filtered_tables = app_filtered

        # 按表名模式过滤
        if table_patterns:
            pattern_filtered = []
            for pattern in table_patterns:
                pattern_filtered.extend([t for t in filtered_tables if fnmatch.fnmatch(t, pattern)])
            filtered_tables = pattern_filtered

        # 排除指定表
        if exclude_tables:
            filtered_tables = [t for t in filtered_tables if t not in exclude_tables]

        return list(set(filtered_tables))  # 去重
