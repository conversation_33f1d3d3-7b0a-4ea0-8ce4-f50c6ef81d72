"""
修复时间戳字段注释的管理命令

专门用于为create_time和update_time字段添加数据库注释
"""

from django.core.management.base import BaseCommand
from django.db import connection
from django.apps import apps
from common.db_utils import DatabaseCompatibilityUtils
from common.db_migration import DatabaseMigrationManager


class Command(BaseCommand):
    help = '为时间戳字段添加数据库注释'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要执行的操作，不实际执行',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制执行，跳过确认',
        )
        parser.add_argument(
            '--apps',
            nargs='*',
            help='指定要处理的应用名称',
            default=['public', 'claim', 'insure', 'other', 'medical']
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.force = options['force']
        self.target_apps = options['apps']

        self.stdout.write(
            self.style.SUCCESS('开始修复时间戳字段注释...')
        )

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('这是预览模式，不会实际执行任何操作')
            )

        # 1. 获取所有需要处理的表
        tables_to_fix = self.get_tables_with_timestamps()

        if not tables_to_fix:
            self.stdout.write(
                self.style.SUCCESS('没有找到需要处理的表')
            )
            return

        self.stdout.write(f'找到 {len(tables_to_fix)} 个需要处理的表')

        # 2. 处理每个表
        self.fix_timestamp_comments(tables_to_fix)

    def get_tables_with_timestamps(self):
        """获取所有包含时间戳字段的表"""
        tables = []
        migration_manager = DatabaseMigrationManager()

        for app_name in self.target_apps:
            try:
                app_config = apps.get_app_config(app_name)
                models = app_config.get_models()

                for model in models:
                    # 检查模型是否有时间戳字段
                    if hasattr(model, 'create_time') and hasattr(model, 'update_time'):
                        table_name = model._meta.db_table

                        # 检查表是否存在
                        if migration_manager.check_table_exists(table_name):
                            tables.append({
                                'app': app_name,
                                'model': model.__name__,
                                'table': table_name
                            })
                            self.stdout.write(f'  发现表: {table_name} ({app_name}.{model.__name__})')

            except Exception as e:
                self.stdout.write(
                    self.style.WARNING(f'检查应用 {app_name} 时出错: {e}')
                )

        return tables

    def fix_timestamp_comments(self, tables):
        """修复时间戳字段注释"""
        db_utils = DatabaseCompatibilityUtils()
        db_type = db_utils.get_database_type()

        self.stdout.write(f'\n数据库类型: {db_type}')

        if not self.force and not self.dry_run:
            confirm = input(f'确认要为 {len(tables)} 个表添加时间戳字段注释吗？(y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write('操作已取消')
                return

        success_count = 0
        error_count = 0

        for table_info in tables:
            table_name = table_info['table']
            app_name = table_info['app']
            model_name = table_info['model']

            self.stdout.write(f'\n处理表: {table_name} ({app_name}.{model_name})')

            try:
                # 生成SQL语句
                sql_statements = self.generate_comment_sql(table_name, db_type)

                if self.dry_run:
                    self.stdout.write('  [DRY RUN] 将执行的SQL:')
                    for sql in sql_statements:
                        self.stdout.write(f'    {sql}')
                else:
                    # 执行SQL
                    self.execute_sql(sql_statements, table_name)

                success_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'  [OK] {table_name} 处理完成')
                )

            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(f'  [ERROR] {table_name} 处理失败: {e}')
                )

        # 显示总结
        self.stdout.write(f'\n=== 处理总结 ===')
        self.stdout.write(f'成功: {success_count} 个表')
        self.stdout.write(f'失败: {error_count} 个表')

        if success_count > 0 and not self.dry_run:
            self.stdout.write(
                self.style.SUCCESS('时间戳字段注释修复完成！')
            )

    def generate_comment_sql(self, table_name, db_type):
        """生成添加注释的SQL语句"""
        if db_type == 'mysql':
            return [
                f"ALTER TABLE {table_name} MODIFY COLUMN create_time DATETIME COMMENT '创建时间'",
                f"ALTER TABLE {table_name} MODIFY COLUMN update_time DATETIME COMMENT '更新时间'"
            ]
        elif db_type == 'postgresql':
            return [
                f"COMMENT ON COLUMN {table_name}.create_time IS '创建时间'",
                f"COMMENT ON COLUMN {table_name}.update_time IS '更新时间'"
            ]
        elif db_type == 'sqlite':
            # SQLite不支持单独添加列注释，需要重建表
            self.stdout.write(
                self.style.WARNING(f'  SQLite不支持单独添加列注释，跳过 {table_name}')
            )
            return []
        else:
            self.stdout.write(
                self.style.WARNING(f'  不支持的数据库类型: {db_type}，跳过 {table_name}')
            )
            return []

    def execute_sql(self, sql_statements, table_name):
        """执行SQL语句"""
        if not sql_statements:
            return

        with connection.cursor() as cursor:
            for sql in sql_statements:
                self.stdout.write(f'    执行: {sql}')
                cursor.execute(sql)
