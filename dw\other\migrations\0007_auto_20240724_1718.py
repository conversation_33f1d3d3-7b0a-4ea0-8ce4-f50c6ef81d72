# Generated by Django 3.2.12 on 2024-07-24 17:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('other', '0006_auto_20240724_1644'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='otherproduct',
            name='product_name',
            field=models.CharField(blank=True, max_length=200, null=True, unique=True, verbose_name='产品名称'),
        ),
        migrations.AlterField(
            model_name='otherproductauditperson',
            name='name',
            field=models.Char<PERSON>ield(blank=True, max_length=200, null=True, unique=True, verbose_name='名称'),
        ),
        migrations.AlterField(
            model_name='otherproductcode',
            name='name',
            field=models.CharField(blank=True, max_length=200, null=True, unique=True, verbose_name='名称'),
        ),
        migrations.AlterField(
            model_name='otherproductproton',
            name='product_name',
            field=models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=200, null=True, unique=True, verbose_name='产品名称'),
        ),
        migrations.AddConstraint(
            model_name='otherproductinsuretype',
            constraint=models.UniqueConstraint(fields=('product_name', 'type'), name='unique_other_product_insure_type_combination'),
        ),
        migrations.AddConstraint(
            model_name='otherproductmedicaltype',
            constraint=models.UniqueConstraint(fields=('product_name', 'medicare_type'), name='unique_other_product_medical_type_combination'),
        ),
        migrations.AddConstraint(
            model_name='otherproductresponse',
            constraint=models.UniqueConstraint(fields=('product_name', 'type'), name='unique_other_product_response_combination'),
        ),
    ]
