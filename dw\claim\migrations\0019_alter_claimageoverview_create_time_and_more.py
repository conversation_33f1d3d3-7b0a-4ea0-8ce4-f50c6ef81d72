# Generated by Django 4.2.1 on 2025-06-11 09:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("claim", "0018_alter_claimageoverview_create_time_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="claimageoverview",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimageoverview",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimagerangepay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimagerangepay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimamountrangepay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimamountrangepay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimareapay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimareapay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimgenderpay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimgenderpay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimgrouppay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimgrouppay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimliabilitypay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimliabilitypay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimmonthlypay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimmonthlypay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimpayoverview",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimpayoverview",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimpaytype",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimpaytype",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimpreexistingcondition",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimpreexistingcondition",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimproductpay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimproductpay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimprotonheavyion",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimprotonheavyion",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimsellerpay",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimsellerpay",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimtopcaseinfo",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimtopcaseinfo",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimxuepingxian",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="claimxuepingxian",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
    ]
