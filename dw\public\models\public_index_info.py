from django.db import models
from common.models import BaseModel
import django


def _db_comment_kwarg(comment: str):
    """
    为字段添加数据库注释的辅助函数
    只在Django 4.2+版本中添加db_comment参数
    """
    if django.VERSION >= (4, 2):
        return {"db_comment": comment}
    return {}


class PublicIndexInfo(BaseModel):
    """
    索引信息表，存储各个表的索引信息，每行记录一个完整索引
    """
    table_id = models.IntegerField(
        blank=True,
        null=True,
        verbose_name='所属表ID',
        help_text='所属表ID，用于关联public_table_info.id',
        **_db_comment_kwarg('所属表ID，用于关联public_table_info.id')
    )
    table_name = models.CharField(
        max_length=128,
        default='',
        verbose_name='所属表名称',
        help_text='所属表名称',
        **_db_comment_kwarg('所属表名称')
    )
    name = models.CharField(
        max_length=128,
        verbose_name='索引名称',
        help_text='索引名称',
        **_db_comment_kwarg('索引名称')
    )
    type = models.CharField(
        max_length=32,
        default='BTREE',
        verbose_name='索引类型',
        help_text='索引类型，如BTREE、HASH、FULLTEXT、SPATIAL',
        **_db_comment_kwarg('索引类型，如BTREE、HASH、FULLTEXT、SPATIAL')
    )
    is_unique = models.BooleanField(
        default=False,
        verbose_name='是否唯一索引',
        help_text='是否唯一索引，TRUE表示唯一索引',
        **_db_comment_kwarg('是否唯一索引，TRUE表示唯一索引')
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name='是否主键索引',
        help_text='是否主键索引，TRUE表示主键索引',
        **_db_comment_kwarg('是否主键索引，TRUE表示主键索引')
    )
    column_names = models.TextField(
        verbose_name='索引字段',
        help_text='索引包含的字段名，多个字段用逗号分隔',
        **_db_comment_kwarg('索引包含的字段名，多个字段用逗号分隔')
    )
    column_orders = models.TextField(
        blank=True,
        null=True,
        verbose_name='字段排序',
        help_text='字段排序方式，用逗号分隔，如ASC,DESC,ASC',
        **_db_comment_kwarg('字段排序方式，用逗号分隔，如ASC,DESC,ASC')
    )
    comment = models.CharField(
        max_length=512,
        blank=True,
        null=True,
        verbose_name='索引注释',
        help_text='索引注释说明',
        **_db_comment_kwarg('索引注释说明')
    )

    class Meta:
        db_table = 'public_index_info'
        verbose_name = '索引信息表'
        verbose_name_plural = verbose_name
        unique_together = [['table_id', 'name']]
        indexes = [
            models.Index(fields=['table_id'], name='idx_pub_idx_table_id'),
            models.Index(fields=['table_name'], name='idx_pub_idx_table_name'),
            models.Index(fields=['is_unique'], name='idx_pub_idx_unique'),
            models.Index(fields=['is_primary'], name='idx_pub_idx_primary'),
            models.Index(fields=['type'], name='idx_pub_idx_type'),
        ]

    def __str__(self):
        return f"{self.table_name}.{self.name}"

    def get_column_list(self):
        """
        获取索引包含的字段列表
        """
        if self.column_names:
            return [col.strip() for col in self.column_names.split(',')]
        return []

    def get_column_orders_list(self):
        """
        获取字段排序方式列表
        """
        if self.column_orders:
            return [order.strip() for order in self.column_orders.split(',')]
        return []


# 动态为 Meta 添加 db_table_comment（仅 Django 4.2+ 支持）
if django.VERSION >= (4, 2):
    PublicIndexInfo.Meta.db_table_comment = '索引信息表，存储各个表的索引信息，每行记录一个完整索引'
