from django.db import models
from common.models import BaseModel


class OtherProductAuditPerson(BaseModel):
    name = models.CharField(max_length=200, blank=True, null=True, verbose_name='名称',unique=True)
    code = models.CharField(max_length=512, blank=True, null=True, verbose_name='产品编码集合')

    class Meta:
        db_table = 'other_product_audit_person'
        verbose_name = '理赔-审核人员数据范围表'
        verbose_name_plural = verbose_name
