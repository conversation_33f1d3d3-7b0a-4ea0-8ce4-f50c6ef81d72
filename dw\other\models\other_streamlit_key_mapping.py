from django.db import models
from common.models import BaseModel


class OtherStreamlitKeyMapping(BaseModel):
    product_serial_code = models.CharField(max_length=128, blank=True, null=True, verbose_name='产品系列')
    type = models.CharField(max_length=128, blank=True, null=True, verbose_name='类型')
    client_id = models.CharField(max_length=256, blank=True, null=True, verbose_name='app编码')
    client_secret = models.CharField(max_length=256, blank=True, null=True, verbose_name='app密钥')
    url = models.CharField(max_length=256, blank=True, null=True, verbose_name='url地址')

    class Meta:
        db_table = 'other_streamlit_key_mapping'
        verbose_name = 'ST应用信息映射'
        verbose_name_plural = verbose_name
