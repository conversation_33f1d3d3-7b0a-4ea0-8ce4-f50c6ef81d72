# NULL值处理策略文档

## 概述

本文档说明了在ETL数据处理过程中，针对特定字段的NULL值处理策略，特别是将源表的NULL值转换为空字符串的设计决策和实现方案。

## 问题背景

### 原始问题

在医保定点机构数据ETL过程中，发现以下问题：

1. 源表中的NULL值在入库后变成了字符串 `'None'`
2. `outpatient_chronic_disease`和 `outpatient_chronic_disease_name`字段受影响最严重
3. 影响数据质量和后续的数据比对逻辑

### 根本原因

1. **数据清洗阶段**：`astype(str)`操作将None值转换为字符串 `'None'`
2. **字段映射阶段**：直接复制源字段值，未进行NULL值处理
3. **历史数据**：目标表中已存在多种形式的"空值"表示

## 解决方案

### 核心策略

**将特定字段的NULL值转换为空字符串 `''`，而不是保持NULL或转换为字符串 `'None'`**

### 适用字段

- `outpatient_chronic_disease`（门诊慢性病编码）
- `outpatient_chronic_disease_name`（门诊慢性病名称）

### 实现层次

1. **数据清洗层**：在 `data_cleaning.py`中配置需要保留空字符串的字段
2. **字段映射层**：在 `field_mapping.py`中对特定字段进行NULL到空字符串的转换

## 技术实现

### 1. 数据清洗配置

```python
# dw/transfrom/utils/data_cleaning.py
self.preserve_empty_string_fields = {
    'spider_fuwu_fixed_hospital': [
        'outMedOpspDiseCodeLs',      # 源字段名
        'outMedOpspDiseNameLs'       # 源字段名
    ],
    'spider_fuwu_retail_pharmacy': [
        'outMedOpspDiseCodeLs',      # 源字段名
        'outMedOpspDiseNameLs'       # 源字段名
    ],
}
```

### 2. 数据清洗逻辑

```python
# 先处理None值，避免被转换为字符串'None'
if col in preserve_fields:
    # 对于需要保留空字符串的字段，将None转换为空字符串
    df[col] = df[col].fillna('')
    # 然后对非None值进行字符串处理
    mask = df[col] != ''
    df.loc[mask, col] = df.loc[mask, col].astype(str).str.strip()
else:
    # 对于其他字段，先转换为字符串再处理
    df[col] = df[col].astype(str).str.strip()
    # 将字符串'None'和空字符串转换为None
    df[col] = df[col].replace(['None', ''], None)
```

### 3. 字段映射保护

```python
# dw/transfrom/utils/field_mapping.py
self.null_to_empty_string_fields = {
    'outpatient_chronic_disease',
    'outpatient_chronic_disease_name'
}

# 在字段映射时进行转换
if target_field in self.null_to_empty_string_fields:
    result_df[target_field] = result_df[target_field].fillna('')
```

## 设计决策分析

### 为什么选择空字符串而不是NULL？

#### 1. 业务语义等价性

- **NULL**：数据库层面的空值
- **空字符串**：业务层面的"无值"
- 对于门诊慢性病字段，两者在业务上完全等价，都表示"患者没有门诊慢性病"

#### 2. 数据比对稳定性

| 比对场景 | 源值     | 目标值   | 比对结果 | 影响        |
| -------- | -------- | -------- | -------- | ----------- |
| 理想状态 | `''`   | `''`   | 相同     | 无需更新 ✅ |
| NULL保持 | `NULL` | `''`   | 不同     | 触发更新 ❌ |
| NULL保持 | `NULL` | `NULL` | 相同     | 无需更新 ✅ |

#### 3. 历史数据兼容性

目标表现状：

- 字符串 `'None'`：300条（历史错误数据）
- NULL值：200条
- 空字符串：500条

**方案对比**：

| 方案           | 初期更新率 | 最终状态          | 长期稳定性 |
| -------------- | ---------- | ----------------- | ---------- |
| 转换为空字符串 | 50%        | 数据统一为 `''` | 高 ✅      |
| 保持NULL       | 80%        | 混合NULL和 `''` | 低 ❌      |

#### 4. 系统维护便利性

- **查询简单**：`WHERE field = ''` 比 `WHERE field IS NULL` 更直观
- **数据修复**：可以统一处理历史数据
- **调试方便**：空字符串在日志中更容易识别

## 配置管理

### 添加新字段

如需为其他字段应用相同策略：

1. **数据清洗层配置**：

```python
# 在对应的源表配置中添加源字段名
self.preserve_empty_string_fields['table_name'].append('source_field_name')
```

2. **字段映射层配置**：

```python
# 添加目标字段名
self.null_to_empty_string_fields.add('target_field_name')
```

### 动态配置方法

```python
# 数据清洗器
cleaner = DataCleaner()
cleaner.add_preserve_empty_string_fields('table_name', ['field1', 'field2'])

# 字段映射器
mapper = FieldMappingManager()
mapper.add_null_to_empty_string_field('field_name')
```

## 数据修复

### 历史数据清理

对于已入库的错误数据，可执行以下SQL进行修复：

```sql
-- 修复字符串'None'为空字符串
UPDATE medical_designated_providers 
SET outpatient_chronic_disease = '', 
    outpatient_chronic_disease_name = ''
WHERE outpatient_chronic_disease = 'None' 
   OR outpatient_chronic_disease_name = 'None';

-- 统一NULL值为空字符串（可选）
UPDATE medical_designated_providers 
SET outpatient_chronic_disease = COALESCE(outpatient_chronic_disease, ''),
    outpatient_chronic_disease_name = COALESCE(outpatient_chronic_disease_name, '');
```

### 数据验证

```sql
-- 检查数据分布
SELECT 
    CASE 
        WHEN outpatient_chronic_disease IS NULL THEN 'NULL'
        WHEN outpatient_chronic_disease = '' THEN 'EMPTY'
        WHEN outpatient_chronic_disease = 'None' THEN 'STRING_NONE'
        ELSE 'HAS_VALUE'
    END as value_type,
    COUNT(*) as count
FROM medical_designated_providers
GROUP BY value_type;
```

## 监控和维护

### 日志监控

关注以下日志信息：

- `字段 'xxx' 的None值已转换为空字符串`
- `字段 'xxx' 保留空字符串，不转换为None`

### 数据质量检查

定期检查是否有新的字符串 `'None'`数据产生：

```sql
SELECT COUNT(*) as error_count
FROM medical_designated_providers 
WHERE outpatient_chronic_disease = 'None' 
   OR outpatient_chronic_disease_name = 'None';
```

## 最佳实践

### 1. 字段分类处理

- **业务空值字段**：转换为空字符串（如门诊慢性病字段）
- **技术空值字段**：保持NULL（如可选的数值字段）
- **必填字段**：不应出现NULL，需要默认值或数据验证

### 2. 配置原则

- 明确区分源字段名和目标字段名
- 在数据清洗和字段映射两个层次都进行保护
- 为特殊处理的字段添加详细注释

### 3. 测试验证

- 单元测试：验证NULL值转换逻辑
- 集成测试：验证完整ETL流程
- 数据验证：检查实际入库结果

## 相关文件

- `dw/transfrom/utils/data_cleaning.py`：数据清洗逻辑
- `dw/transfrom/utils/field_mapping.py`：字段映射逻辑
- `dw/transfrom/utils/data_normalization.py`：数据比对逻辑

## 版本历史

| 版本 | 日期       | 变更内容                                     |
| ---- | ---------- | -------------------------------------------- |
| 1.0  | 2025-05-30 | 初始版本，解决NULL值转换为字符串'None'的问题 |

---

*本文档说明了NULL值处理的设计决策和实现方案，确保数据质量和系统稳定性。*
