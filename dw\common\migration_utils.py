"""
Django迁移工具函数
提供在Django迁移中直接使用的跨数据库兼容函数
注意：这个文件不在migrations目录下，避免被Django当作迁移文件
"""

from django.db import migrations
from .db_migration import DatabaseMigrationManager
from .db_utils import DatabaseCompatibilityUtils


class CrossDatabaseMigration:
    """
    跨数据库兼容的迁移操作类
    可以直接在Django迁移文件中使用
    """
    
    @staticmethod
    def add_timestamp_defaults_operation(table_name):
        """
        创建一个Django迁移操作，为指定表添加时间戳默认值
        
        Args:
            table_name: 表名
            
        Returns:
            migrations.RunSQL: Django迁移操作
        """
        def forward_func(apps, schema_editor):
            db_alias = schema_editor.connection.alias
            migration_manager = DatabaseMigrationManager(db_alias)
            db_utils = DatabaseCompatibilityUtils()
            
            # 检查表是否存在
            if not migration_manager.check_table_exists(table_name):
                print(f"警告: 表 {table_name} 不存在，跳过迁移")
                return
            
            # 检查列是否存在
            has_create_time = migration_manager.check_column_exists(table_name, 'create_time')
            has_update_time = migration_manager.check_column_exists(table_name, 'update_time')
            
            if not (has_create_time and has_update_time):
                print(f"警告: 表 {table_name} 缺少时间戳列，跳过迁移")
                return
            
            # 生成SQL
            sql_statements = db_utils.generate_timestamp_columns_sql(table_name, db_alias)
            
            # 执行迁移
            success = migration_manager.execute_migration(
                migration_name=f"django_add_timestamp_defaults_{table_name}",
                sql_statements=sql_statements
            )
            
            if success:
                print(f"[OK] 表 {table_name} 时间戳默认值添加成功")
            else:
                raise Exception(f"表 {table_name} 时间戳默认值添加失败")
        
        def reverse_func(apps, schema_editor):
            db_alias = schema_editor.connection.alias
            migration_manager = DatabaseMigrationManager(db_alias)
            
            if migration_manager.check_table_exists(table_name):
                reverse_sql = migration_manager._generate_reverse_timestamp_sql(table_name)
                migration_manager.execute_migration(
                    migration_name=f"django_rollback_timestamp_defaults_{table_name}",
                    sql_statements=reverse_sql
                )
        
        return migrations.RunPython(
            code=forward_func,
            reverse_code=reverse_func,
        )


# 便捷函数，可以直接在迁移文件中使用
def add_timestamp_defaults(table_name):
    """便捷函数：为表添加时间戳默认值"""
    return CrossDatabaseMigration.add_timestamp_defaults_operation(table_name)


def modify_column_with_default(table_name, column_name, column_type, 
                             default_value=None, comment=None):
    """便捷函数：修改列并添加默认值"""
    # 这里可以添加具体实现
    pass


def cross_db_sql(sql_templates, reverse_sql_templates=None):
    """便捷函数：执行跨数据库兼容的SQL"""
    # 这里可以添加具体实现
    pass
