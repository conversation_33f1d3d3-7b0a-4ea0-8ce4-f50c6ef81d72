from django.db import models
from common.models import BaseModel


class OtherYbStatisticNhb(BaseModel):
    product_code = models.CharField(max_length=200, blank=True, null=True, verbose_name='产品编码')
    type = models.CharField(max_length=200, blank=True, null=True, verbose_name='类型')
    content = models.JSONField(blank=True, null=True, verbose_name='内容数据')
    is_post = models.BooleanField(default=False, blank=True, null=True, verbose_name='是否已推送')

    class Meta:
        db_table = 'other_yb_statistic_nhb'
        verbose_name = '医保高铁-宁惠保理赔报表'
        verbose_name_plural = verbose_name
