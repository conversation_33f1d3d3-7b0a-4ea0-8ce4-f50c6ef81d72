import datetime
import json
import logging
import warnings

import idna
import pandas as pd
import pymysql

from dw import settings
from other.models import OtherYbStatisticNhb
from public.models import PublicMapping
from transfrom.utils.utils import query_sql, match_type

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)


# 产品原型对应说明文档：【腾讯文档】南京宁惠保四期高铁数据对应
# https://docs.qq.com/doc/DS0V3TVhHQWRHQ2JC

class YbgtNhbClaimV4():
    def __init__(self):
        self.PRODUCT_CODE = "'ninghuibao-2024-standard', 'ninghuibao-2024-upgrade'"
        # 保持取数时间节点一致，避免不同时间节点的统计数据差异，特药申请为date格式，可能会存在时间差异
        self.END_DATETIME = datetime.datetime.now().strftime('%Y-%m-%d') + ' 00:00:00'
        # self.END_DATETIME = datetime.datetime.now().strftime('%Y-%m-%d %H:00:00')
        self.DB = settings.DATABASES['nhb']
        self.data_type = 'nhb_claim_v4'

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def replace_using_dict(self, text, replacement_pairs):
        """
        用于根据提供的替换字典替换文本中的内容
        :param text: 需要替换的文本
        :param replacement_pairs: 替换字典
        :return:
        """
        for old, new in replacement_pairs.items():
            text = text.replace(old, new)
        return text

    def mask_name(self, name):
        """
        对姓名进行掩码处理。
        :param name: 需要掩码处理的姓名字符串
        :return: 掩码后的姓名字符串
        """
        if not isinstance(name, str) or not name:
            return "无效的姓名"  # 如果输入不是字符串或为空，则返回提示信息

        name_length = len(name)
        if name_length == 2:
            # 如果姓名长度为2，第二位用*替换
            return name[0] + "*"
        elif name_length > 2:
            # 如果姓名长度大于2，保留第一位和最后一位，中间用*替换
            return name[0] + "*" * (name_length - 2) + name[-1]
        else:
            # 如果姓名长度小于2，直接返回原姓名（虽然这种情况不符合常规）
            return name

    def get_total_pay_info(self, product_code, end_datetime):
        """
        获取累计赔付金额、人均赔付金额(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_pay_info = pd.read_sql(
                query_sql('SQL_TOTAL_PAY_INFO').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())
            return df_total_pay_info
        except Exception as e:
            logger.error(f"get_total_pay_info error:{e}")
            raise e

    def get_total_avg_reduce_rate(self, product_code, end_datetime):
        """
        获取平均个人减负率(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_avg_reduce_rate = pd.read_sql(
                query_sql('SQL_TOTAL_AVG_REDUCE_RATE').format(product_code=product_code,
                                                              end_datetime=end_datetime),
                self.get_connection())
            return df_total_avg_reduce_rate
        except Exception as e:
            logger.error(f"get_total_avg_reduce_rate error:{e}")
            raise e

    def get_total_max_pay_amount(self, product_code, end_datetime):
        """
        获取最大赔付金额(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_max_pay_amount = pd.read_sql(
                query_sql('SQL_TOTAL_MAX_PAY_AMOUNT').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())
            return df_total_max_pay_amount
        except Exception as e:
            logger.error(f"get_total_max_pay_amount error:{e}")
            raise e

    def get_total_max_reduce_rate(self, product_code, end_datetime):
        """
        获取最大个人减负率(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_max_reduce_rate = pd.read_sql(
                query_sql('SQL_TOTAL_MAX_REDUCE_RATE').format(product_code=product_code,
                                                              end_datetime=end_datetime),
                self.get_connection())
            return df_total_max_reduce_rate
        except Exception as e:
            logger.error(f"get_total_max_reduce_rate error:{e}")
            raise e

    def get_total_apply_num(self, product_code, end_datetime):
        """
        获取累计申请数量、理赔开通天数(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_apply_num = pd.read_sql(
                query_sql('SQL_TOTAL_APPLY_NUM').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())
            return df_total_apply_num
        except Exception as e:
            logger.error(f"get_total_apply_num error:{e}")
            raise e

    def get_total_finish_num(self, product_code, end_datetime):
        """
        获取累计完成结案数量(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间


        """
        try:
            df_total_finish_num = pd.read_sql(
                query_sql('SQL_TOTAL_FINISH_NUM').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())
            return df_total_finish_num
        except Exception as e:
            logger.error(f"get_total_finish_num error:{e}")
            raise e

    def get_total_claim_info(self, product_code, end_datetime):
        """
        获取累计理赔金额、人均赔付金额、个人负担平均降低、最高赔付获赔、个人负担最高降低、理赔通道开通天数、理赔申请数、完成结案数(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        df_total_pay_info = self.get_total_pay_info(product_code, end_datetime)
        df_total_avg_reduce_rate = self.get_total_avg_reduce_rate(product_code, end_datetime)
        df_total_max_pay_amount = self.get_total_max_pay_amount(product_code, end_datetime)
        df_total_max_reduce_rate = self.get_total_max_reduce_rate(product_code, end_datetime)
        df_total_apply_num = self.get_total_apply_num(product_code, end_datetime)
        df_total_finish_num = self.get_total_finish_num(product_code, end_datetime)
        try:
            df_total_claim_info = pd.concat([df_total_pay_info, df_total_avg_reduce_rate,
                                             df_total_max_pay_amount, df_total_max_reduce_rate,
                                             df_total_apply_num, df_total_finish_num], axis=1)
            return df_total_claim_info
        except Exception as e:
            logger.error(f"get_total_claim_info error:{e}")
            raise e

    def get_total_max_pay_amount_top10(self, product_code, end_datetime):
        """
        获取总体最高理赔金额TOP10案例，包括医疗和理赔，含身份证、总费用、统筹、个人负担、赔付金额、减负率(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_max_pay_amount_top10 = pd.read_sql(
                query_sql('SQL_TOTAL_MAX_PAY_AMOUNT_TOP10').format(product_code=product_code,
                                                                   end_datetime=end_datetime),
                self.get_connection())
            df_total_max_pay_amount_top10['product_code_set'] = (df_total_max_pay_amount_top10['product_code_set'].
            apply(
                lambda x: self.replace_using_dict(x, {'ninghuibao-2024': '宁惠保四期',
                                                      'ninghuibao-2025': '宁惠保五期',
                                                      'ninghuibao-2026': '宁惠保六期',
                                                      'ninghuibao-2027': '宁惠保七期'})))
            df_total_max_pay_amount_top10['产品名称'] = df_total_max_pay_amount_top10['product_code_set'] + \
                                                        df_total_max_pay_amount_top10['版本']
            df_total_max_pay_amount_top10.drop(['product_code_set'], axis=1, inplace=True)
            df_total_max_pay_amount_top10['序号'] = df_total_max_pay_amount_top10.index + 1
            df_total_max_pay_amount_top10['姓名'] = df_total_max_pay_amount_top10['姓名'].apply(
                lambda x: self.mask_name(x))
            df_total_max_pay_amount_top10 = df_total_max_pay_amount_top10[
                ['产品名称', '序号', '姓名', '版本', '总费用', '医保范围内费用', '基本医疗', '大病保险', '医疗救助',
                 '个人自付', '个人自费', '宁惠保报销', '多层次保障后个人减负率']]
            df_total_max_pay_amount_top10.rename(columns={'产品名称': 'product_name', '序号': 'num', '姓名': 'name',
                                                          '版本': 'product', '总费用': 'total_amount',
                                                          '医保范围内费用': 'medicare_amount',
                                                          '基本医疗': 'base_amount',
                                                          '大病保险': 'serious_illness_insurance_amount',
                                                          '医疗救助': 'medical_assistance_amount',
                                                          '个人自付': 'self_paid_amount',
                                                          '个人自费': 'self_care_amount', '宁惠保报销': 'pay_amount',
                                                          '多层次保障后个人减负率': 'reduce_rate'}, inplace=True)
            df_total_max_pay_amount_top10.fillna('', inplace=True)  # 缺失值nan转json不支持，转成空字符串
            return df_total_max_pay_amount_top10
        except Exception as e:
            logger.error(f"get_total_max_pay_amount_top10 error:{e}")
            raise e

    def get_direct_hosipital_info(self, product_code, end_datetime):
        """
        获取直赔医院数量、赔付金额、结算次数
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_direct_hosipital_num = pd.read_sql(
                query_sql('SQL_DIRECT_HOSPITAL_NUM').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())

            df_direct_hosipital_pay_info = pd.read_sql(
                query_sql('SQL_DIRECT_HOSPITAL_PAY_INFO').format(product_code=product_code,
                                                                 end_datetime=end_datetime),
                self.get_connection())
            df_direct_hosipital_info = pd.concat([df_direct_hosipital_num, df_direct_hosipital_pay_info], axis=1)
            df_direct_hosipital_info['one_stop_num'] = df_direct_hosipital_info['direct_pay_num']
            df_direct_hosipital_info['direct_pay_num'] = df_direct_hosipital_info['direct_person_num']
            df_direct_hosipital_info.drop(['direct_person_num'], axis=1, inplace=True)
            return df_direct_hosipital_info
        except Exception as e:
            logger.error(f"get_direct_hosipital_info error:{e}")
            raise e

    def get_direct_hosipital_num_top5(self, product_code, end_datetime):
        """
        获取直赔医院数量TOP5,包括序号、医院名称、赔付金额、结算次数
        :param product_code:产品代码
        :param end_datetime:截止时间
        """

        try:
            df_direct_hosipital_num_top5 = pd.read_sql(
                query_sql('SQL_DIRECT_HOSPITAL_NUM_TOP5').format(product_code=product_code,
                                                                 end_datetime=end_datetime),
                self.get_connection())
            df_direct_hosipital_num_top5['num'] = df_direct_hosipital_num_top5.index + 1
            df_direct_hosipital_num_top5 = df_direct_hosipital_num_top5[
                ['num', 'hospital_name', 'hospital_direct_amount', 'hospital_direct_pay_num']]
            return df_direct_hosipital_num_top5
        except Exception as e:
            logger.error(f"get_direct_hosipital_num_top5 error:{e}")
            raise e

    def get_total_pay_by_product(self, product_code, end_datetime):

        """
        按保障版本计算赔付件数、金额(万元)(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_pay_by_product = pd.read_sql(
                query_sql('SQL_TOTAL_PAY_BY_PRODUCT').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())
            df_total_pay_by_product['pay_num_rate'] = round(
                df_total_pay_by_product['pay_num'] / df_total_pay_by_product['pay_num'].sum(), 4)
            df_total_pay_by_product['pay_amount_rate'] = round(
                df_total_pay_by_product['pay_amount'] / df_total_pay_by_product['pay_amount'].sum(), 4)
            df_total_pay_by_product['pay_amount'] = df_total_pay_by_product['pay_amount'].apply(
                lambda x: round(x / 10000, 2))
            df_total_pay_by_product = df_total_pay_by_product[
                ['product_version', 'pay_num', 'pay_num_rate', 'pay_amount', 'pay_amount_rate']]
            return df_total_pay_by_product
        except Exception as e:
            logger.error(f"get_total_pay_by_product error:{e}")
            raise e

    def get_total_pay_by_medicare(self, product_code, end_datetime):

        """
        按参保类型计算赔付件数、金额(万元)(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_pay_by_medicare = pd.read_sql(
                query_sql('SQL_TOTAL_PAY_BY_MEDICARE').format(product_code=product_code,
                                                              end_datetime=end_datetime),
                self.get_connection())
            df_total_pay_by_medicare['pay_num_rate'] = round(
                df_total_pay_by_medicare['pay_num'] / df_total_pay_by_medicare['pay_num'].sum(), 4)
            df_total_pay_by_medicare['pay_amount_rate'] = round(
                df_total_pay_by_medicare['pay_amount'] / df_total_pay_by_medicare['pay_amount'].sum(), 4)
            df_total_pay_by_medicare['pay_amount'] = df_total_pay_by_medicare['pay_amount'].apply(
                lambda x: round(x / 10000, 2))
            df_total_pay_by_medicare = df_total_pay_by_medicare[
                ['medicare_type', 'pay_num', 'pay_num_rate', 'pay_amount', 'pay_amount_rate']]
            return df_total_pay_by_medicare
        except Exception as e:
            logger.error(f"get_total_pay_by_medicare error:{e}")
            raise e

    def get_total_pay_diagnosis_top10(self, product_code, end_datetime):
        """
        赔付病种TOP10，包括疾病名称、赔付金额、赔付件数(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_claim_disease_count = pd.read_sql(
                query_sql('SQL_TOTAL_CLAIM_DISEASE_COUNT').format(product_code=product_code,
                                                                  end_datetime=end_datetime),
                self.get_connection())
            df_total_claim_disease_count['primary_diagnosis_substr'] = \
                df_total_claim_disease_count['primary_diagnosis'].str.split(',').str[0]
            df_total_claim_disease_count['primary_diagnosis_substr'] = \
                df_total_claim_disease_count['primary_diagnosis_substr'].str.split(':').str[0]
            df_total_claim_disease_count['primary_diagnosis_substr'] = \
                df_total_claim_disease_count['primary_diagnosis_substr'].str.split('：').str[0]
            df_total_claim_disease_count['primary_diagnosis_substr'] = \
                df_total_claim_disease_count['primary_diagnosis_substr'].str.split(' ').str[0]
            df_total_claim_disease_count['primary_diagnosis_substr'] = df_total_claim_disease_count[
                'primary_diagnosis_substr'].apply(
                lambda x: x.replace('个人史', '').replace('[', '(').replace(']', ')').replace('?', '').replace(
                    '乳腺恶性肿瘤', '乳房恶性肿瘤'))
            # with open('disease_mapping.json', 'r', encoding='utf-8') as file:
            #     rules = pd.read_json(file)
            rules = pd.DataFrame(list(PublicMapping.objects.filter(type='disease').values('name', 'keywords')))
            rules.rename(columns={'keywords': 'key_words'}, inplace=True)
            # 匹配疾病大类,根据关键字处理，正则匹配，所以json文件部分格式改成正则格式，例如恶.+瘤，匹配所有含恶开头并有瘤字的疾病
            df_total_claim_disease_count['type'] = df_total_claim_disease_count['primary_diagnosis_substr'].apply(
                lambda x: match_type(x, rules))
            # print(df_total_claim_disease_count)
            df_total_pay_diagnosis_top10 = df_total_claim_disease_count.groupby(['type']).agg(
                {'amount': 'sum', 'id': 'count'}).reset_index()
            df_total_pay_diagnosis_top10 = df_total_pay_diagnosis_top10.sort_values(by=['id'],
                                                                                    ascending=False).reset_index(
                drop=True)
            df_total_pay_diagnosis_top10.rename(
                columns={'type': 'primary_diagnosis', 'id': 'pay_num', 'amount': 'pay_amount'},
                inplace=True)
            df_total_pay_diagnosis_top10['num'] = df_total_pay_diagnosis_top10.index + 1
            df_total_pay_diagnosis_top10['pay_amount'] = df_total_pay_diagnosis_top10['pay_amount'].round(2)
            df_total_pay_diagnosis_top10 = df_total_pay_diagnosis_top10[
                ['num', 'primary_diagnosis', 'pay_num', 'pay_amount']]
            return df_total_pay_diagnosis_top10.head(10)
        except Exception as e:
            logger.error(f"get_total_pay_diagnosis_top10 error:{e}")
            raise e

    def get_total_pay_age_range(self, product_code, end_datetime):

        """
        按年龄段计算赔付人数；总人数(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_pay_age_range = pd.read_sql(
                query_sql('SQL_TOTAL_PAY_AGE_RANGE').format(product_code=product_code, end_datetime=end_datetime),
                self.get_connection())
            df_total_pay_age_range['person_num_rate'] = round(
                df_total_pay_age_range['person_num'] / df_total_pay_age_range['person_num'].sum(), 4)
            df_total_pay_age_range = df_total_pay_age_range[['age_range', 'person_num', 'person_num_rate']]
            df_total_person_num = pd.DataFrame({'total_person_num': df_total_pay_age_range['person_num'].sum()},
                                               index=[0])
            return df_total_pay_age_range, df_total_person_num
        except Exception as e:
            logger.error(f"get_total_pay_age_range error:{e}")
            raise e

    def get_total_pay_hospital_top10(self, product_code, end_datetime):

        """
        赔付人员主要就诊医院TOP10，包括医院名称、就诊人次(包括特药和理赔)
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        try:
            df_total_pay_hospital_top10 = pd.read_sql(
                query_sql('SQL_TOTAL_PAY_HOSPITAL_TOP10').format(product_code=product_code,
                                                                 end_datetime=end_datetime),
                self.get_connection())
            df_total_pay_hospital_top10['num'] = df_total_pay_hospital_top10.index + 1
            df_total_pay_hospital_top10 = df_total_pay_hospital_top10[['num', 'hospital_name', 'visit_num']]
            return df_total_pay_hospital_top10
        except Exception as e:
            logger.error(f"get_total_pay_hospital_top10 error:{e}")
            raise e

    def get_total_pay_by_product_version(self, end_datetime):
        """
        按产品期计算赔付件数、最高赔付金额(万元)、赔付金额(万元)(包括特药和理赔)
        :param end_datetime: 截止时间
        """
        try:
            df_total_pay_by_product_version = pd.read_sql(
                query_sql('SQL_TOTAL_PAY_BY_PRODUCT_VERSION').format(end_datetime=end_datetime),
                self.get_connection())
            df_total_maxpay_by_product_version = pd.read_sql(
                query_sql('SQL_TOTAL_MAXPAY_BY_PRODUCT_VERSION').format(end_datetime=end_datetime),
                self.get_connection())
            df_total_pay_by_product_version = pd.merge(df_total_pay_by_product_version,
                                                       df_total_maxpay_by_product_version, on='product_code',
                                                       how='outer')
            df_total_pay_by_product_version['product_code'] = (df_total_pay_by_product_version['product_code'].apply(
                lambda x: self.replace_using_dict(x, {'ninghuibao-2021': '1期',
                                                      'ninghuibao-2022': '2期',
                                                      'ninghuibao-2023': '3期',
                                                      'ninghuibao-2024': '4期',
                                                      'ninghuibao-2025': '5期',
                                                      'ninghuibao-2026': '6期',
                                                      'ninghuibao-2027': '7期'})))
            df_total_pay_by_product_version['pay_amount'] = df_total_pay_by_product_version['pay_amount'].apply(
                lambda x: round(x / 10000, 2))
            df_total_pay_by_product_version['max_pay_amount'] = df_total_pay_by_product_version['max_pay_amount'].apply(
                lambda x: round(x / 10000, 2))
            return df_total_pay_by_product_version
        except Exception as e:
            logger.error(f"get_total_pay_by_product_version error:{e}")
            raise e

    def update_or_create_db(self, product_code, content):
        """
        尝试获取指定product_code的记录
        :param product_code: 产品代码
        :param content: 内容
        """
        try:
            claim_yb_report = OtherYbStatisticNhb.objects.get(product_code=product_code, type=self.data_type)
            # 如果记录存在，则更新content，需要转成json格式，不然解析出来会是str
            claim_yb_report.content = json.dumps(content)
            claim_yb_report.is_post = 0
            claim_yb_report.save()
        except OtherYbStatisticNhb.DoesNotExist:
            # 如果记录不存在，则新增记录，需要转成json格式，不然解析出来会是str
            OtherYbStatisticNhb.objects.create(product_code=product_code, type=self.data_type,
                                               content=json.dumps(content))

    def get_content(self, product_code, end_datetime):
        """
        获取产品内容
        :param product_code: 产品代码
        :param end_datetime: 截止时间
        """
        # 先检查数据库中的数据是否存在，如果存在，查看时间是否当日，如果是，则直接返回，否则重新计算
        try:
            latest_record = OtherYbStatisticNhb.objects.filter(product_code=product_code, type=self.data_type).order_by(
                'update_time').last()
            if latest_record and latest_record.update_time.date() == datetime.datetime.strptime(end_datetime,
                                                                                                '%Y-%m-%d %H:%M:%S').date():
                # if latest_record and latest_record.update_time.date() == datetime.datetime.strptime(end_datetime,'%Y-%m-%d %H:%M:%S').date()\
                #         and latest_record.update_time.hour == datetime.datetime.strptime(end_datetime,'%Y-%m-%d %H:%M:%S').hour:
                logger.info(f"get_content 数据已存在")
                return json.loads(latest_record.content)
            else:
                # 计算数据
                data = {}
                data['理赔总体情况'] = self.get_total_claim_info(product_code, end_datetime).to_dict(orient='records')
                data['最高赔付金额TOP10'] = self.get_total_max_pay_amount_top10(product_code, end_datetime).to_dict(
                    orient='records')
                data['一站式结算情况'] = self.get_direct_hosipital_info(product_code, end_datetime).to_dict(
                    orient='records')
                data['医院一站式结算人次TOP5'] = self.get_direct_hosipital_num_top5(product_code, end_datetime).to_dict(
                    orient='records')
                data['版本分布情况'] = self.get_total_pay_by_product(product_code, end_datetime).to_dict(
                    orient='records')
                data['参保类型分布情况'] = self.get_total_pay_by_medicare(product_code, end_datetime).to_dict(
                    orient='records')
                data['赔付病种TOP10'] = self.get_total_pay_diagnosis_top10(product_code, end_datetime).to_dict(
                    orient='records')
                df_total_pay_age_range, df_total_person_num = self.get_total_pay_age_range(product_code, end_datetime)
                data['赔付人员年龄分布'] = df_total_pay_age_range.to_dict(orient='records')
                data['总人数'] = df_total_person_num.to_dict(orient='records')
                data['赔付人员主要就诊医院TOP10'] = self.get_total_pay_hospital_top10(product_code,
                                                                                      end_datetime).to_dict(
                    orient='records')
                data['往期对比分析'] = self.get_total_pay_by_product_version(end_datetime).to_dict(orient='records')
                self.update_or_create_db(product_code, data)
                logger.info(f"get_content 数据已更新")
                return data
        except Exception as e:
            logger.error(f"get_content error:{e}")
            raise e

if __name__ == "__main__":
    yb_report = YbgtNhbClaimV4()
    print(yb_report.END_DATETIME)
    print(yb_report.get_total_claim_info(yb_report.PRODUCT_CODE, yb_report.END_DATETIME))
    # data = yb_report.get_content(yb_report.PRODUCT_CODE, yb_report.END_DATETIME)
    # print(data)
# print(type(data))
# df_total_claim_info = yb_report.get_total_pay_info(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_total_claim_info)

# df_total_max_pay_amount_top10 = yb_report.get_total_max_pay_amount_top10(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_total_max_pay_amount_top10)

    # df_direct_hosipital_info = yb_report.get_direct_hosipital_info(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
    # print(df_direct_hosipital_info)

# df_direct_hosipital_num_top5 = yb_report.get_direct_hosipital_num_top5(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_direct_hosipital_num_top5)

# df_total_pay_by_product = yb_report.get_total_pay_by_product(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_total_pay_by_product)

# df_total_pay_by_medicare = yb_report.get_total_pay_by_medicare(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_total_pay_by_medicare)

# df_total_pay_diagnosis_top10 = yb_report.get_total_pay_diagnosis_top10(yb_report.PRODUCT_CODE,
#                                                                        yb_report.END_DATETIME)
# print(df_total_pay_diagnosis_top10)

# df_total_pay_age_range, df_total_person_num = yb_report.get_total_pay_age_range(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_total_pay_age_range)
# print(df_total_person_num)

# df_total_pay_hospital_top10 = yb_report.get_total_pay_hospital_top10(yb_report.PRODUCT_CODE,yb_report.END_DATETIME)
# print(df_total_pay_hospital_top10)

# df_total_pay_by_product_version = yb_report.get_total_pay_by_product_version(yb_report.END_DATETIME)
# print(df_total_pay_by_product_version)
