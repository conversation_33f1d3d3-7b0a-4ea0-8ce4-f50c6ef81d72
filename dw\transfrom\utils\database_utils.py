#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接和配置管理工具
提供统一的数据库连接、配置优化等功能
"""

import logging
import pymysql
import idna
from dw import settings

logger = logging.getLogger(__name__)

# 默认数据库配置
DEFAULT_DB = settings.DATABASES['default']


def get_connection(db_config=None):
    """
    获取数据库连接，增加连接超时和保活配置

    Args:
        db_config (dict): 数据库配置字典,包含以下键值:
            - HOST: 数据库主机地址
            - PORT: 数据库端口
            - USER: 数据库用户名
            - PASSWORD: 数据库密码
            - NAME: 数据库名称

    Returns:
        pymysql.connections.Connection: 数据库连接对象
    """
    if db_config is None:
        db_config = DEFAULT_DB

    conn = pymysql.connect(
        host=idna.encode(db_config["HOST"]).decode("utf-8"),  # 对主机名进行IDNA编码后解码
        port=int(db_config["PORT"]),  # 端口号转为整型
        user=db_config["USER"],  # 数据库用户名
        password=db_config["PASSWORD"],  # 数据库密码
        database=db_config["NAME"],  # 数据库名称
        charset='utf8mb4',  # 字符集
        autocommit=True,  # 自动提交
        connect_timeout=60,  # 连接超时时间（秒）
        read_timeout=300,  # 读取超时时间（秒）
        write_timeout=300,  # 写入超时时间（秒）
    )
    return conn


def check_mysql_configuration(db_config=None):
    """
    检查MySQL服务器配置，确保适合大数据量操作

    Args:
        db_config (dict): 数据库配置，如果为None则使用默认配置
    """
    if db_config is None:
        db_config = DEFAULT_DB

    try:
        with get_connection(db_config) as conn:
            cursor = conn.cursor()

            # 检查关键配置参数
            config_checks = [
                "SHOW VARIABLES LIKE 'max_allowed_packet'",
                "SHOW VARIABLES LIKE 'wait_timeout'",
                "SHOW VARIABLES LIKE 'interactive_timeout'",
                "SHOW VARIABLES LIKE 'net_read_timeout'",
                "SHOW VARIABLES LIKE 'net_write_timeout'",
                "SHOW VARIABLES LIKE 'max_connections'"
            ]

            logger.info("=== MySQL配置检查 ===")
            for check_sql in config_checks:
                cursor.execute(check_sql)
                result = cursor.fetchone()
                if result:
                    logger.info(f"{result[0]}: {result[1]}")

            # 检查当前连接数
            cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
            result = cursor.fetchone()
            if result:
                logger.info(f"当前连接数: {result[1]}")

            logger.info("=== 配置检查完成 ===")

    except Exception as e:
        logger.warning(f"MySQL配置检查失败: {str(e)}")


def optimize_mysql_session(cursor):
    """
    优化MySQL会话配置，适合大数据量操作

    Args:
        cursor: 数据库游标
    """
    try:
        # 设置会话级别的超时和缓冲区配置，针对批量更新优化
        optimizations = [
            "SET SESSION wait_timeout = 3600",  # 1小时
            "SET SESSION interactive_timeout = 3600",  # 1小时
            "SET SESSION net_read_timeout = 600",  # 10分钟
            "SET SESSION net_write_timeout = 600",  # 10分钟
            "SET SESSION max_allowed_packet = 1073741824",  # 1GB
            "SET SESSION bulk_insert_buffer_size = 268435456",  # 256MB
            "SET SESSION innodb_buffer_pool_size = 1073741824",  # 1GB InnoDB缓冲池
            "SET SESSION innodb_log_buffer_size = 67108864",  # 64MB 日志缓冲区
            "SET SESSION innodb_flush_log_at_trx_commit = 2",  # 优化事务提交
            "SET SESSION autocommit = 1",  # 确保自动提交开启
        ]

        for sql in optimizations:
            try:
                cursor.execute(sql)
                logger.debug(f"执行优化配置: {sql}")
            except Exception as e:
                # 某些配置可能需要特殊权限，忽略错误
                logger.debug(f"配置 {sql} 执行失败: {str(e)}")

    except Exception as e:
        logger.warning(f"MySQL会话优化失败: {str(e)}")


class DatabaseConnectionManager:
    """
    数据库连接管理器，提供连接池和重试机制
    """

    def __init__(self, db_config=None):
        """
        初始化连接管理器

        Args:
            db_config (dict): 数据库配置，如果为None则使用默认配置
        """
        self.db_config = db_config or DEFAULT_DB

    def get_connection(self):
        """获取数据库连接，带重试机制"""
        max_retries = 3
        retry_delay = 1  # 秒

        for attempt in range(max_retries):
            try:
                conn = get_connection(self.db_config)
                # 测试连接是否有效
                conn.ping(reconnect=True)
                return conn
            except Exception as e:
                logger.warning(f"数据库连接尝试 {attempt + 1}/{max_retries} 失败: {str(e)}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    logger.error(f"数据库连接失败，已重试 {max_retries} 次")
                    raise

    def execute_with_retry(self, sql, params=None, max_retries=3):
        """
        带重试机制的SQL执行

        Args:
            sql (str): SQL语句
            params (tuple): SQL参数
            max_retries (int): 最大重试次数

        Returns:
            执行结果
        """
        retry_delay = 1

        for attempt in range(max_retries):
            try:
                with self.get_connection() as conn:
                    cursor = conn.cursor()
                    optimize_mysql_session(cursor)

                    if params:
                        cursor.execute(sql, params)
                    else:
                        cursor.execute(sql)

                    return cursor.fetchall()

            except Exception as e:
                logger.warning(f"SQL执行尝试 {attempt + 1}/{max_retries} 失败: {str(e)}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(retry_delay)
                    retry_delay *= 2
                else:
                    logger.error(f"SQL执行最终失败: {str(e)}")
                    raise

    def get_sqlalchemy_engine(self):
        """
        获取SQLAlchemy引擎，用于pandas的to_sql操作

        Returns:
            sqlalchemy.engine.Engine: SQLAlchemy引擎
        """
        from sqlalchemy import create_engine

        # 构建MySQL连接字符串
        connection_string = (
            f"mysql+pymysql://{self.db_config['USER']}:{self.db_config['PASSWORD']}"
            f"@{self.db_config['HOST']}:{self.db_config['PORT']}/{self.db_config['NAME']}"
            f"?charset=utf8mb4"
        )

        # 创建引擎
        engine = create_engine(
            connection_string,
            pool_pre_ping=True,  # 连接前检查
            pool_recycle=3600,   # 连接回收时间
            echo=False           # 不输出SQL日志
        )

        return engine


def create_sqlalchemy_engine_with_retry(db_config=None, max_retries=3):
    """
    创建SQLAlchemy引擎，带重试机制和版本兼容性处理

    Args:
        db_config (dict): 数据库配置
        max_retries (int): 最大重试次数

    Returns:
        sqlalchemy.engine.Engine: SQLAlchemy引擎
    """
    if db_config is None:
        db_config = DEFAULT_DB

    retry_delay = 1

    for attempt in range(max_retries):
        try:
            from sqlalchemy import create_engine

            # 构建MySQL连接字符串，增加超时配置
            connection_string = (
                f"mysql+pymysql://{db_config['USER']}:{db_config['PASSWORD']}"
                f"@{db_config['HOST']}:{db_config['PORT']}/{db_config['NAME']}"
                f"?charset=utf8mb4&connect_timeout=60&read_timeout=300&write_timeout=300"
            )

            # 创建引擎，配置连接池
            engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                pool_timeout=30,
                max_overflow=10,
                echo=False
            )

            # 测试连接 - 兼容不同SQLAlchemy版本
            try:
                with engine.connect() as conn:
                    # 尝试新版本语法
                    try:
                        from sqlalchemy import text
                        result = conn.execute(text("SELECT 1"))
                        # 对于SQLAlchemy 2.0+，需要获取结果
                        if hasattr(result, 'fetchone'):
                            result.fetchone()
                    except Exception:
                        # 回退到旧版本语法
                        conn.execute("SELECT 1")

                    # 提交事务（如果需要）
                    if hasattr(conn, 'commit'):
                        conn.commit()

            except Exception as test_error:
                logger.warning(f"连接测试失败，但引擎创建成功: {test_error}")
                # 即使测试失败，也返回引擎，让to_sql自己处理

            logger.info(f"SQLAlchemy引擎创建成功")
            return engine

        except Exception as e:
            logger.warning(f"SQLAlchemy引擎创建尝试 {attempt + 1}/{max_retries} 失败: {str(e)}")
            if attempt < max_retries - 1:
                import time
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                logger.error(f"SQLAlchemy引擎创建最终失败: {str(e)}")
                raise


def create_simple_sqlalchemy_engine(db_config=None):
    """
    创建简单的SQLAlchemy引擎，用于to_sql操作
    避免复杂的连接测试，让pandas.to_sql自己处理连接

    Args:
        db_config (dict): 数据库配置

    Returns:
        sqlalchemy.engine.Engine: SQLAlchemy引擎
    """
    if db_config is None:
        db_config = DEFAULT_DB

    try:
        from sqlalchemy import create_engine

        # 构建MySQL连接字符串
        connection_string = (
            f"mysql+pymysql://{db_config['USER']}:{db_config['PASSWORD']}"
            f"@{db_config['HOST']}:{db_config['PORT']}/{db_config['NAME']}"
            f"?charset=utf8mb4"
        )

        # 创建简单引擎
        engine = create_engine(
            connection_string,
            pool_pre_ping=False,  # 禁用预检查
            echo=False
        )

        logger.info(f"简单SQLAlchemy引擎创建成功")
        return engine

    except Exception as e:
        logger.error(f"简单SQLAlchemy引擎创建失败: {str(e)}")
        raise
