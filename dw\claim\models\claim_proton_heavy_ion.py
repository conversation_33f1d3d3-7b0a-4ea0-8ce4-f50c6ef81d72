from django.db import models
from common.models import BaseModel


class ClaimProtonHeavyIon(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    name = models.CharField(max_length=256, blank=True, null=True, verbose_name='姓名')
    credential_number = models.CharField(max_length=64, blank=True, null=True, verbose_name='证件号码')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    total_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='总费用')
    coordinated_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='统筹金额')
    self_burden_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='个人负担金额')
    past_symptom = models.Char<PERSON>ield(max_length=64, blank=True, null=True, verbose_name='是否既往症')

    class Meta:
        db_table = 'claim_proton_heavy_ion'
        verbose_name = '理赔-质子重离子情况'
        verbose_name_plural = verbose_name

