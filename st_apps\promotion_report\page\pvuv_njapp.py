import datetime
import warnings

import pandas as pd
import numpy as np
import streamlit as st
from st_aggrid import AgGrid

from utils.st import text_write, empty_line, query_sql
from utils.utils import simplify_replace,sum_or_combine

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')
CONNECTOR_UMAMI = st.connection('umami', type='sql')

def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        concat(
		LEFT ( ps.NAME, 5 ),
		'-',
	RIGHT ( ps.NAME,( LENGTH( ps.NAME )- 15 )/ 3 )) version,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        and ps.code not in ('ninghuibaoV1','ninghuibaoV2','ninghuibaoV3','ninghuibaoV4')
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)

    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code

def get_njapp_sale_data(product_set_code, end_datetime, conn=CONNECTOR_JKX, sql=query_sql('SQL_NJAPP_SALE')):
    """
    获取南京app销售数据
    特殊处理：njapp 算到 njapp_wdym上
    :param product_set_code: 产品集代码
    :param end_datetime: 结束时间
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    df_njapp_sale_data = conn.query(sql.format(product_set_code=product_set_code, end_datetime=end_datetime),
                                    show_spinner='查询中...', ttl=0)
    df_njapp_sale_data.loc[df_njapp_sale_data['source_code'] == 'njapp', 'source'] = '我的南京-APP我的页面'
    df_njapp_sale_data.loc[df_njapp_sale_data['source'] == '我的南京-APP我的页面', 'source_code'] = 'njapp_wdym'
    df_njapp_sale_data['date'] = df_njapp_sale_data['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_njapp_sale_data['source_code'] = df_njapp_sale_data['source_code'].apply(lambda x: x.strip())
    return df_njapp_sale_data


def get_njapp_total_sale_data(product_set_code, end_datetime, conn=CONNECTOR_JKX,
                              sql=query_sql('SQL_NJAPP_TOTAL_SALE')):
    """
    获取南京app销售数据
    特殊处理：njapp 算到 njapp_wdym上
    :param product_set_code: 产品集代码
    :param end_datetime: 结束时间
    :param conn: 数据库连接
    :param sql: 数据查询sq
    :return:
    """
    df = conn.query(sql.format(product_set_code=product_set_code, end_datetime=end_datetime),
                    show_spinner='查询中...', ttl=0)
    simplify_replace(df, 'pay_type', {'NORMAL': '自费', 'MEDICARE': '医保'})
    df['date'] = df['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df.rename(columns={'date': '日期'}, inplace=True)

    df_not_return = df[df['is_return'] == 0]
    df_is_return = df[df['is_return'] == 1]
    df_is_return_group = df_is_return.groupby(['日期', 'product_name']).agg({'count': 'sum'}).reset_index()
    df_is_return_pivot = df_is_return_group.pivot_table(index='日期', columns='product_name',
                                                    values='count', aggfunc='sum').fillna(0)
    df_is_return_pivot.rename(columns={'基础版': '退款-基础版','升级版':'退款-升级版'},inplace=True)
    df_is_return_pivot.sort_values(by=['日期'], ascending=False, inplace=True)
    df_not_return_pivot = df_not_return.pivot_table(index='日期', columns=['product_name', 'pay_type'],
                                                    values='count',aggfunc='sum').fillna(0)
    df_not_return_pivot.columns = df_not_return_pivot.columns.map(lambda x: f"{x[1]}-{x[0]}")
    df_not_return_pivot.sort_values(by=['日期'], ascending=False, inplace=True)
    df_all = pd.merge(df_not_return_pivot,df_is_return_pivot,on='日期',how='left')
    df_all['合计-基础版'] =  df_all['自费-基础版'] + df_all['医保-基础版']
    df_all['合计-升级版'] = df_all['自费-升级版'] + df_all['医保-升级版']
    df_all['合计'] = df_all['合计-基础版'] + df_all['合计-升级版']
    df_all.reset_index(inplace=True)
    number_sum = df_all.apply(lambda x: sum_or_combine(x,'总计'), axis=0).to_frame().T
    print(number_sum)
    df_all = pd.concat([number_sum,df_all], axis=0)
    df_all = df_all[['日期','自费-基础版','医保-基础版','合计-基础版','退款-基础版','自费-升级版','医保-升级版','合计-升级版','退款-升级版','合计']]
    return df_all


def get_njapp_name(conn=CONNECTOR_JKX):
    """
    获取我的南京app code和名称的匹配关系
    :return:
    """
    sql = """
    select channel_code source_code,channel_name source_name
    from marketing_channel 
    where left(channel_code,5) = 'njapp'
    """
    df_njapp_name = conn.query(sql, ttl=0, show_spinner='查询中...')
    # 去除空格
    df_njapp_name = df_njapp_name.applymap(lambda x: x.strip())
    return df_njapp_name


def get_njapp_pv_history(product_set_code, start_date='2024-09-12',
                         end_date=None, conn=CONNECTOR_DW):
    """
    获取南京app浏览量，历史数据，每日缓存，保证查询速度
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    if end_date is None:
        end_date = (datetime.date.today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
    sql = """
    select product_set_code,
    end_date date,
    source_code,
    count 
    from insure_pvuv 
    where type = 'pv'
    and source_group ='njapp'
    and product_set_code = '{product_set_code}'
    and end_date <= '{end_date}'
    and end_date >= '{start_date}'
    """
    df_njapp_pv = conn.query(sql.format(product_set_code=product_set_code, start_date=start_date, end_date=end_date),show_spinner='查询中...', ttl=0)
    df_njapp_pv.rename(columns={'count': 'pv_count'}, inplace=True)
    df_njapp_pv['date'] = df_njapp_pv['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_njapp_pv['source_code'] = df_njapp_pv['source_code'].apply(lambda x: x.split('&')[0])
    df_njapp_pv.groupby(['date', 'source_code'])['pv_count'].sum().reset_index()
    df_njapp_pv.reset_index(drop=True, inplace=True)
    return df_njapp_pv




def get_njapp_uv_history(product_set_code, start_date='2024-09-12',
                         end_date=None, conn=CONNECTOR_DW):
    """
    获取南京app访客量，历史数据，每日缓存，保证查询速度
    :param conn: 数据库连接
    :param sql: 数据查询sql
    :return:
    """
    if end_date is None:
        end_date = (datetime.date.today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
    sql = """
        select product_set_code,
        end_date date,
        source_code,
        count 
        from insure_pvuv 
        where type = 'uv'
        and source_group ='njapp'
        and product_set_code = '{product_set_code}'
        and end_date <= '{end_date}'
        and end_date >= '{start_date}'
        """
    df_njapp_uv = conn.query(sql.format(product_set_code=product_set_code, start_date=start_date, end_date=end_date),
                             show_spinner='查询中...', ttl=0)
    df_njapp_uv.rename(columns={'count': 'uv_count'}, inplace=True)
    df_njapp_uv['date'] = df_njapp_uv['date'].apply(lambda x: x.strftime('%Y-%m-%d'))
    df_njapp_uv['source_code'] = df_njapp_uv['source_code'].apply(lambda x: x.split('&')[0])
    df_njapp_uv.groupby(['date', 'source_code'])['uv_count'].sum().reset_index()
    df_njapp_uv.reset_index(drop=True, inplace=True)
    return df_njapp_uv




def get_njapp_statistic(product_set_code, yesterday_datetime):
    """
    获取南京app统计数据
    :param product_set_code:
    :param end_datetime:
    :return:
    """
    df_njapp_sale_data = get_njapp_sale_data(product_set_code, yesterday_datetime)
    df_njapp_pv = get_njapp_pv_history(product_set_code)
    df_njapp_uv = get_njapp_uv_history(product_set_code)
    print(df_njapp_pv)
    print(df_njapp_uv)
    df_njapp_name = get_njapp_name()
    df_njapp_statistic = pd.merge(df_njapp_pv, df_njapp_uv, on=['date', 'source_code'], how='left')
    df_njapp_statistic = df_njapp_statistic.groupby(['date','source_code']).agg({'pv_count': 'sum', 'uv_count': 'sum'}).reset_index()
    df_njapp_statistic = pd.merge(df_njapp_statistic, df_njapp_sale_data, on=['date', 'source_code'], how='left')
    # 匹配中文名称，直接用source的中文名称替换原来的source
    df_njapp_statistic = pd.merge(df_njapp_statistic, df_njapp_name, on=['source_code'], how='left')
    df_njapp_statistic['source'] = df_njapp_statistic['source_name']
    df_njapp_statistic.fillna(0, inplace=True)
    df_njapp_statistic.rename(
        columns={'date': '日期', 'pv_count': 'PV', 'uv_count': 'UV', 'count': '销量', 'source_name': '渠道'},
        inplace=True)
    df_njapp_statistic['渠道'] = df_njapp_statistic['渠道'].apply(
        lambda x: x.replace('我的南京-APP', '').replace('我的南京-', ''))
    # 添加日期合计项，pv、uv、销量
    df_njapp_statistic_group = df_njapp_statistic.groupby('日期').agg(
        {'PV': 'sum', 'UV': 'sum', '销量': 'sum'}).reset_index()
    df_njapp_statistic_group.sort_values(by='日期', ascending=False, inplace=True)

    df_njapp_statistic_pivot = df_njapp_statistic.pivot_table(index='日期', columns='渠道', values=['PV', 'UV', '销量'],
                                                              aggfunc='sum')
    df_njapp_statistic_pivot.columns = df_njapp_statistic_pivot.columns.map(lambda x: f"{x[1]}-{x[0]}")
    df_njapp_statistic_pivot.reset_index(inplace=True)
    df_njapp_statistic_pivot.fillna(0, inplace=True)
    # 获取列名，并排序，日期在前面
    cols = ['日期'] + sorted(list(set(df_njapp_statistic_pivot.columns) - {'日期'}))
    df_njapp_statistic_pivot = df_njapp_statistic_pivot[cols]
    df_njapp_statistic_pivot.sort_values(by='日期', ascending=False, inplace=True)
    df_njapp_statistic_pivot = pd.merge(df_njapp_statistic_group, df_njapp_statistic_pivot, on='日期', how='left')
    # df_njapp_statistic_pivot.to_excel('df_njapp_statistic_pivot.xlsx')
    return df_njapp_statistic_pivot


def main():
    st.subheader('我的南京APP统计报表')
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=product_info['product_set_name'],placeholder="请选择产品")

    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[
        0]
    st.divider()
    end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    yesterday_datetime = yesterday.strftime('%Y-%m-%d') + ' 23:59:59'
    text_write("宁惠保销售情况统计")
    df = get_njapp_total_sale_data(product_set_code, end_datetime)
    empty_line(1)
    # st.dataframe(df_njapp_statistic,use_container_width=True,hide_index=True)

    # 定义AgGrid的参数
    aggrid_options = {
        'columnDefs': [{'headerName': c, 'field': c} for c in df.columns],
        'pagination': True,  # 启用分页
        'autoHeight': True,  # 自适应高度
        'autoSizeStrategy': {
            'type': 'fitCellContents'
        },
        'defaultColDef': {
            'filter': True
        }
        # 设置底部合计行，保留2位小数，dict会精度丢失
    }
    # 使用st_aggrid显示分页表格
    AgGrid(df, gridOptions=aggrid_options)


    text_write('分页面销售情况统计')
    empty_line(1)
    df_njapp_statistic = get_njapp_statistic(product_set_code, yesterday_datetime)
    # st.dataframe(df_njapp_statistic,use_container_width=True,hide_index=True)

    # 定义AgGrid的参数
    aggrid_options = {
        'columnDefs': [{'headerName': c, 'field': c} for c in df_njapp_statistic.columns],
        'pagination': True,  # 启用分页
        'autoHeight': True,  # 自适应高度
        'autoSizeStrategy': {
            'type': 'fitCellContents'
        },
        'defaultColDef': {
            'filter': True
        }
        # 设置底部合计行，保留2位小数，dict会精度丢失
    }
    # 使用st_aggrid显示分页表格
    AgGrid(df_njapp_statistic, gridOptions=aggrid_options)




if __name__ == '__main__':
    main()
