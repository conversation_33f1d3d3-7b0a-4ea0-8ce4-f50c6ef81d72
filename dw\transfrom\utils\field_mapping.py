#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段映射和转换模块
提供字段映射、字典映射等数据转换功能
"""

import logging
import pandas as pd
from .database_utils import get_connection, DEFAULT_DB
from .data_normalization import normalize_data_types
from .field_config import get_field_mapping_config

# 导入省份配置以触发自动注册
try:
    import transfrom.tasks.spider.gjyb.pipeline.config
    import transfrom.tasks.spider.jsyb.pipeline.config
    import transfrom.tasks.spider.njyb.pipeline.config
except ImportError as e:
    logging.warning(f"部分省份配置导入失败: {e}")

logger = logging.getLogger(__name__)


class FieldMappingManager:
    """
    字段映射管理器
    负责字段映射配置的获取和应用
    """

    def __init__(self, db_config=None):
        """
        初始化字段映射管理器

        Args:
            db_config (dict): 数据库配置，如果为None则使用默认配置
        """
        self.db_config = db_config or DEFAULT_DB

        # 注意：null_to_empty_string_fields配置现在从统一配置模块获取
        # 不再在这里硬编码配置

    def get_field_mapping(self, source_table=None, target_table=None):
        """
        获取字段映射关系

        Args:
            source_table (str, optional): 来源表名，用于过滤映射关系
            target_table (str, optional): 目标表名，用于过滤映射关系

        Returns:
            pd.DataFrame: 字段映射关系数据框
        """
        sql = """
        select * from medical_field_mapping where 1=1
        """
        params = []

        if source_table:
            sql += " and source_table = %s"
            params.append(source_table)

        if target_table:
            sql += " and target_table = %s"
            params.append(target_table)

        with get_connection(self.db_config) as conn:
            df = pd.read_sql(sql, conn, params=params)
        return df

    def apply_field_mapping(self, source_df, mapping_df, target_table=None, source_table=None, province='gjyb'):
        """
        应用字段映射转换

        Args:
            source_df (pd.DataFrame): 源数据DataFrame
            mapping_df (pd.DataFrame): 字段映射关系DataFrame
            target_table (str, optional): 目标表名，用于获取特定的NULL转空字符串字段配置
            source_table (str, optional): 源表名，用于获取完整的字段配置
            province (str): 省份标识，默认为'gjyb'

        Returns:
            pd.DataFrame: 转换后的数据DataFrame
        """
        result_df = pd.DataFrame()

        # 从统一配置获取当前目标表的NULL转空字符串字段配置
        null_to_empty_fields = set()
        if target_table and source_table:
            config = get_field_mapping_config(province, source_table, target_table)
            null_to_empty_fields = config.get('null_to_empty_string_fields', set())
            logger.info(f"省份 '{province}' 源表 '{source_table}' -> 目标表 '{target_table}' 的NULL转空字符串字段: {null_to_empty_fields}")
        elif target_table:
            logger.warning(f"未指定源表，无法获取目标表 '{target_table}' 的完整字段配置")
        else:
            logger.warning("未指定目标表，无法获取NULL转空字符串字段配置")

        for _, mapping_row in mapping_df.iterrows():
            source_field = mapping_row['source_field']
            target_field = mapping_row['target_field']
            default_value = mapping_row['default']

            if source_field in source_df.columns:
                # 检查目标字段是否已经在源数据中存在（数据清洗阶段已经处理）
                if target_field in source_df.columns:
                    # 目标字段已存在，使用数据清洗阶段的值，不进行映射
                    result_df[target_field] = source_df[target_field]
                    logger.info(f"字段映射 '{source_field}' -> '{target_field}' 跳过，使用数据清洗阶段的值")
                # 检查是否需要基于description的特殊处理
                elif self._needs_description_processing(mapping_row['description']):
                    # 应用基于description的特殊处理逻辑
                    result_df[target_field] = self._apply_description_processing(
                        source_df[source_field], mapping_row['description']
                    )
                    logger.info(f"字段映射 '{source_field}' -> '{target_field}' 应用了description特殊处理: {mapping_row['description']}")
                # 检查是否需要特殊处理（针对一对多映射）
                elif self._needs_special_processing(source_field, target_field, source_table):
                    # 应用特殊处理逻辑
                    result_df[target_field] = self._apply_special_processing(
                        source_df[source_field], source_field, target_field, source_table, province
                    )
                    logger.info(f"字段映射 '{source_field}' -> '{target_field}' 应用了特殊处理")
                else:
                    # 如果源字段存在，直接映射
                    result_df[target_field] = source_df[source_field]

                # 对特定字段进行NULL到空字符串的转换
                if target_field in null_to_empty_fields:
                    result_df[target_field] = result_df[target_field].fillna('')
                    logger.info(f"字段 '{target_field}' 的NULL值已转换为空字符串")

            elif default_value is not None and default_value != '':
                # 如果源字段不存在但有默认值，使用默认值
                result_df[target_field] = default_value
            else:
                # 如果源字段不存在且无默认值，根据字段类型决定填充值
                if target_field in null_to_empty_fields:
                    # 为特定字段填充空字符串，需要确保DataFrame有正确的行数
                    result_df[target_field] = [''] * len(source_df)
                    logger.info(f"源字段 '{source_field}' 不存在，目标字段 '{target_field}' 填充为空字符串")
                else:
                    result_df[target_field] = None
                    logger.warning(f"源字段 '{source_field}' 不存在，目标字段 '{target_field}' 将填充为None")

        # 处理映射到additional_info的字段
        additional_info_mappings = mapping_df[mapping_df['target_field'] == 'additional_info']
        if not additional_info_mappings.empty and 'additional_info' in source_df.columns:
            # 如果有字段映射到additional_info，且数据清洗阶段已经创建了additional_info字段
            result_df['additional_info'] = source_df['additional_info']
            logger.info(f"保留数据清洗阶段创建的additional_info字段")
        elif not additional_info_mappings.empty:
            # 如果有字段映射到additional_info，但数据清洗阶段没有创建，则创建空的
            result_df['additional_info'] = '{}'
            logger.info(f"创建空的additional_info字段")

        return result_df

    def _needs_special_processing(self, source_field, target_field, source_table):
        """
        判断是否需要特殊处理（针对一对多映射等情况）

        Args:
            source_field (str): 源字段名
            target_field (str): 目标字段名
            source_table (str): 源表名

        Returns:
            bool: 是否需要特殊处理
        """
        # 定义需要特殊处理的映射规则
        special_mappings = {
            'spider_fuwu_service_facilities': {
                'medListCodg': ['code', 'charge_item_code']  # medListCodg需要根据目标字段进行不同处理
                # servitemName的处理已经在数据清洗阶段直接创建目标字段，不需要特殊处理
            }
        }

        if source_table in special_mappings:
            if source_field in special_mappings[source_table]:
                return target_field in special_mappings[source_table][source_field]

        return False

    def _needs_description_processing(self, description):
        """
        判断是否需要基于description的特殊处理

        Args:
            description (str): 字段描述

        Returns:
            bool: 是否需要特殊处理
        """
        if not description:
            return False

        # 定义需要特殊处理的description关键词
        special_keywords = [
            '长度不满15位，前面补0',
            '长度不满',
            '前面补0',
            '补零',
            '左补0'
        ]

        return any(keyword in description for keyword in special_keywords)

    def _apply_description_processing(self, source_series, description):
        """
        应用基于description的特殊处理逻辑

        Args:
            source_series (pd.Series): 源字段数据
            description (str): 字段描述

        Returns:
            pd.Series: 处理后的数据
        """
        if '长度不满15位，前面补0' in description:
            # 补0到15位
            return source_series.apply(lambda x: str(x).zfill(15) if pd.notna(x) and str(x).strip() else x)
        elif '长度不满' in description and '前面补0' in description:
            # 提取目标长度
            import re
            match = re.search(r'长度不满(\d+)位', description)
            if match:
                target_length = int(match.group(1))
                return source_series.apply(lambda x: str(x).zfill(target_length) if pd.notna(x) and str(x).strip() else x)

        # 默认返回原值
        return source_series

    def _apply_special_processing(self, source_series, source_field, target_field, source_table, province):
        """
        应用特殊处理逻辑

        Args:
            source_series (pd.Series): 源字段数据
            source_field (str): 源字段名
            target_field (str): 目标字段名
            source_table (str): 源表名
            province (str): 省份标识

        Returns:
            pd.Series: 处理后的数据
        """
        # 针对medListCodg字段的特殊处理
        if source_field == 'medListCodg' and source_table == 'spider_fuwu_service_facilities':
            if target_field == 'code':
                # 提取第一个'-'之前的部分
                return source_series.apply(self._extract_code_part)
            elif target_field == 'charge_item_code':
                # 提取第一个'-'之后的部分
                return source_series.apply(self._extract_charge_code_part)



        # 默认直接返回原值
        return source_series

    def _extract_code_part(self, value):
        """提取code部分（第一个'-'之前）"""
        import pandas as pd
        if pd.notna(value) and str(value).strip():
            value_str = str(value).strip()
            if '-' in value_str:
                return value_str.split('-')[0]
            else:
                return value_str
        return ''

    def _extract_charge_code_part(self, value):
        """提取charge_item_code部分（第一个'-'之后）"""
        import pandas as pd
        if pd.notna(value) and str(value).strip():
            value_str = str(value).strip()
            if '-' in value_str:
                parts = value_str.split('-', 1)  # 只分割一次
                if len(parts) > 1:
                    return parts[1]  # 返回第一个'-'后面的所有内容
                else:
                    return ''
            else:
                return ''
        return ''

    def validate_mapping(self, source_table, target_table):
        """
        验证映射配置的完整性和正确性

        Args:
            source_table (str): 源表名
            target_table (str): 目标表名

        Returns:
            dict: 验证结果
        """
        validation_result = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }

        # 获取映射配置
        mapping_df = self.get_field_mapping(source_table=source_table, target_table=target_table)

        if mapping_df.empty:
            validation_result['is_valid'] = False
            validation_result['errors'].append(f"未找到 {source_table} -> {target_table} 的映射配置")
            return validation_result

        # 检查必填字段
        for _, row in mapping_df.iterrows():
            if pd.isna(row['source_field']) or row['source_field'] == '':
                validation_result['warnings'].append(f"映射ID {row['id']} 的源字段为空")

            if pd.isna(row['target_field']) or row['target_field'] == '':
                validation_result['errors'].append(f"映射ID {row['id']} 的目标字段为空")
                validation_result['is_valid'] = False

        return validation_result


class DictMappingManager:
    """
    字典映射管理器
    负责字典映射的获取和应用
    """

    def __init__(self, db_config=None):
        """
        初始化字典映射管理器

        Args:
            db_config (dict): 数据库配置，如果为None则使用默认配置
        """
        self.db_config = db_config or DEFAULT_DB

    def get_dict_mapping(self, dict_id=None):
        """
        获取字典映射关系

        Args:
            dict_id (str, optional): 字典分类ID，用于过滤特定字典

        Returns:
            pd.DataFrame: 字典映射关系数据框
        """
        sql = """
        select `key` as dict_name, label as dict_code from system_dict_value where status = '1'
        """
        params = []

        if dict_id:
            sql += " and dict_id = %s"
            params.append(dict_id)
        else:
            sql += " and dict_id = '19'"  # 默认字典ID

        with get_connection(self.db_config) as conn:
            df = pd.read_sql(sql, conn, params=params)
        return df

    def get_custom_dict_mapping(self, dict_table, dict_code_field, dict_name_field):
        """
        获取自定义字典表的映射关系

        Args:
            dict_table (str): 字典表名
            dict_code_field (str): 字典表中的编码字段名
            dict_name_field (str): 字典表中的名称字段名

        Returns:
            pd.DataFrame: 自定义字典映射关系数据框
        """
        sql = f"""
        select `{dict_code_field}` as dict_code, `{dict_name_field}` as dict_name
        from {dict_table}
        """

        with get_connection(self.db_config) as conn:
            df = pd.read_sql(sql, conn)
        return df

    def apply_dict_mapping(self, df, mapping_row, dict_mapping_df=None):
        """
        应用字典映射转换

        Args:
            df (pd.DataFrame): 需要转换的数据DataFrame
            mapping_row (pd.Series): 单行映射配置
            dict_mapping_df (pd.DataFrame, optional): 字典映射数据，如果为None则自动获取

        Returns:
            pd.DataFrame: 转换后的数据DataFrame
        """
        target_field = mapping_row['target_field']
        dict_table = mapping_row['dict_table']
        dict_id = mapping_row['dict_id']
        dict_code_field = mapping_row['dict_code_field']
        dict_name_field = mapping_row['dict_name_field']

        if target_field not in df.columns:
            logger.warning(f"目标字段 '{target_field}' 不存在于数据中")
            return df

        # 获取字典映射数据
        if dict_mapping_df is None:
            if dict_table and dict_code_field and dict_name_field:
                # 使用自定义字典表
                dict_mapping_df = self.get_custom_dict_mapping(dict_table, dict_code_field, dict_name_field)
                code_col, name_col = 'dict_code', 'dict_name'
            elif dict_id:
                # 使用系统字典
                dict_mapping_df = self.get_dict_mapping(dict_id)
                code_col, name_col = 'dict_code', 'dict_name'
            else:
                logger.warning(f"字段 '{target_field}' 的字典映射配置不完整")
                return df
        else:
            code_col, name_col = 'dict_code', 'dict_name'

        # 智能判断映射方向：仅当dict_table有值时应用此逻辑
        if pd.notna(dict_table) and dict_table.strip():
            # 自定义字典表，根据目标字段名称智能判断映射方向
            target_field_lower = target_field.lower()

            if 'code' in target_field_lower:
                # 目标字段包含'code'，说明源数据是name值，需要获取对应的code
                mapping_dict = dict(zip(dict_mapping_df[name_col], dict_mapping_df[code_col]))
                mapping_direction = "name_to_code"
                logger.info(f"字段 '{target_field}' 智能映射: name -> code (源数据是名称，获取编码)")
            elif 'name' in target_field_lower:
                # 目标字段包含'name'，说明源数据是code值，需要获取对应的name
                mapping_dict = dict(zip(dict_mapping_df[code_col], dict_mapping_df[name_col]))
                mapping_direction = "code_to_name"
                logger.info(f"字段 '{target_field}' 智能映射: code -> name (源数据是编码，获取名称)")
            else:
                # 目标字段名称中既不包含'code'也不包含'name'，使用默认映射（code -> name）
                mapping_dict = dict(zip(dict_mapping_df[code_col], dict_mapping_df[name_col]))
                mapping_direction = "default_code_to_name"
                logger.info(f"字段 '{target_field}' 使用默认映射: code -> name")
        else:
            # 系统字典表，使用传统映射方式（code -> name）
            mapping_dict = dict(zip(dict_mapping_df[code_col], dict_mapping_df[name_col]))
            mapping_direction = "system_dict"
            logger.info(f"字段 '{target_field}' 使用系统字典映射: code -> name")

        # 应用映射，保留原值如果映射失败
        original_values = df[target_field].copy()
        mapped_values = df[target_field].map(mapping_dict)

        # 统计映射成功率
        successful_mappings = mapped_values.notna().sum()
        total_values = len(df[target_field].dropna())

        if total_values > 0:
            success_rate = (successful_mappings / total_values) * 100
            logger.info(f"字段 '{target_field}' 映射成功率: {success_rate:.2f}% ({successful_mappings}/{total_values})")

            # 如果是自定义字典表且映射成功率很低，可能是映射方向错误，尝试反向映射
            if (pd.notna(dict_table) and dict_table.strip() and
                success_rate < 20 and mapping_direction in ["name_to_code", "code_to_name"]):

                logger.warning(f"字段 '{target_field}' 映射成功率较低({success_rate:.1f}%)，尝试反向映射")

                if mapping_direction == "name_to_code":
                    # 尝试 code -> name 映射
                    reverse_mapping_dict = dict(zip(dict_mapping_df[code_col], dict_mapping_df[name_col]))
                    reverse_direction = "code_to_name"
                else:
                    # 尝试 name -> code 映射
                    reverse_mapping_dict = dict(zip(dict_mapping_df[name_col], dict_mapping_df[code_col]))
                    reverse_direction = "name_to_code"

                reverse_mapped_values = df[target_field].map(reverse_mapping_dict)
                reverse_successful = reverse_mapped_values.notna().sum()
                reverse_success_rate = (reverse_successful / total_values) * 100

                if reverse_success_rate > success_rate:
                    logger.info(f"反向映射效果更好: {reverse_successful}/{total_values} ({reverse_success_rate:.1f}%)，采用反向映射({reverse_direction})")
                    mapped_values = reverse_mapped_values
                else:
                    logger.info(f"反向映射效果不佳({reverse_success_rate:.1f}%)，保持原映射方向")

        # 使用映射结果，如果映射失败则保留原值
        df[target_field] = mapped_values.fillna(original_values)

        return df


class DataTransformer:
    """
    数据转换器
    整合字段映射、字典映射、数据类型标准化等功能
    """

    def __init__(self, db_config=None):
        """
        初始化数据转换器

        Args:
            db_config (dict): 数据库配置，如果为None则使用默认配置
        """
        self.field_mapping_manager = FieldMappingManager(db_config)
        self.dict_mapping_manager = DictMappingManager(db_config)

    def transform_data(self, source_df, source_table, target_table, province='gjyb'):
        """
        主数据转换函数，根据映射配置转换数据

        Args:
            source_df (pd.DataFrame): 源数据DataFrame
            source_table (str): 源表名
            target_table (str): 目标表名
            province (str): 省份标识，默认为'gjyb'

        Returns:
            pd.DataFrame: 转换后的数据DataFrame
        """
        logger.info(f"开始转换数据：{source_table} -> {target_table} (省份: {province})")

        # 获取字段映射配置
        mapping_df = self.field_mapping_manager.get_field_mapping(
            source_table=source_table, target_table=target_table
        )

        if mapping_df.empty:
            logger.warning(f"未找到 {source_table} -> {target_table} 的字段映射配置")
            return pd.DataFrame()

        logger.info(f"找到 {len(mapping_df)} 条字段映射配置")

        # 应用字段映射
        result_df = self.field_mapping_manager.apply_field_mapping(source_df, mapping_df, target_table, source_table, province)

        # 处理需要字典映射的字段
        dict_mappings = mapping_df[
            (mapping_df['dict_table'].notna()) |
            (mapping_df['dict_id'].notna())
        ]

        for _, mapping_row in dict_mappings.iterrows():
            target_field = mapping_row['target_field']
            source_field = mapping_row['source_field']

            # 检查目标字段是否已经在数据清洗阶段被正确处理
            # 如果字段已存在且有特殊标记，跳过字典映射
            if (target_field in source_df.columns and
                f'_{target_field}_cleaned' in source_df.columns):
                logger.info(f"字典映射 '{target_field}' 跳过，使用数据清洗阶段的正确值")
                continue

            logger.info(f"应用字典映射：{target_field}")
            result_df = self.dict_mapping_manager.apply_dict_mapping(result_df, mapping_row)

        # 应用数据类型标准化（重要：解决3.0 vs 3的问题）
        logger.info("开始数据类型标准化...")
        result_df = normalize_data_types(result_df, target_table, province)

        logger.info(f"数据转换完成，输出 {len(result_df)} 行 {len(result_df.columns)} 列")
        return result_df


# 创建全局实例，方便直接使用
default_transformer = DataTransformer()

# 提供便捷的函数接口
def transform_data(source_df, source_table, target_table, province='gjyb'):
    """
    数据转换的便捷函数

    Args:
        source_df (pd.DataFrame): 源数据DataFrame
        source_table (str): 源表名
        target_table (str): 目标表名
        province (str): 省份标识，默认为'gjyb'

    Returns:
        pd.DataFrame: 转换后的数据DataFrame
    """
    return default_transformer.transform_data(source_df, source_table, target_table, province)
