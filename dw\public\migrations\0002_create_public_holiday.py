# Generated by Django 4.2.1 on 2025-07-29 15:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("public", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PublicHoliday",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "create_time",
                    models.DateTimeField(
                        auto_now_add=True,
                        db_comment="创建时间",
                        help_text="由Django自动设置，INSERT时自动填充当前时间",
                        verbose_name="创建时间",
                    ),
                ),
                (
                    "update_time",
                    models.DateTimeField(
                        auto_now=True,
                        db_comment="更新时间",
                        help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                        verbose_name="更新时间",
                    ),
                ),
                (
                    "date",
                    models.DateField(
                        db_comment="日期，格式为YYYY-MM-DD",
                        help_text="日期，格式为YYYY-MM-DD",
                        unique=True,
                        verbose_name="日期",
                    ),
                ),
                (
                    "is_work",
                    models.BooleanField(
                        db_comment="是否工作日，True表示工作日，False表示节假日",
                        default=True,
                        help_text="是否工作日，True表示工作日，False表示节假日",
                        verbose_name="是否工作日",
                    ),
                ),
            ],
            options={
                "verbose_name": "节假日数据表",
                "verbose_name_plural": "节假日数据表",
                "db_table": "public_holiday",
                "ordering": ["-date"],
                "indexes": [
                    models.Index(fields=["date"], name="idx_pub_holiday_date"),
                    models.Index(fields=["is_work"], name="idx_pub_holiday_is_work"),
                ],
            },
        ),
    ]
