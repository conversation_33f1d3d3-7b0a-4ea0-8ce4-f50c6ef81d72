from django.db import models
from common.models import BaseModel


class OtherProduct(BaseModel):
    product_name = models.CharField(max_length=200, blank=True, null=True, verbose_name='产品名称',unique=True)
    start_date = models.DateField(blank=True, null=True, verbose_name='理赔开始日期')
    amount = models.DecimalField(max_digits=28, decimal_places=2, blank=True, null=True, verbose_name='销售金额')
    person_number = models.IntegerField(blank=True, null=True, verbose_name='销售人数')

    class Meta:
        db_table = 'other_product'
        verbose_name = '理赔-产品信息表'
        verbose_name_plural = verbose_name
