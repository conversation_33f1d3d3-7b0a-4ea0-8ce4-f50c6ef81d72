# 国家医保局爬虫数据清洗规则说明

## 概述

本文档详细说明了国家医保局爬虫数据到目标表的ETL清洗规则，包括表级别清洗、字段级别清洗和自定义清洗函数的配置。

## 支持的数据表

| 源表 | 目标表 | 描述 | 唯一性字段 |
|------|--------|------|------------|
| `spider_fuwu_fixed_hospital` | `medical_designated_providers` | 医保定点医疗机构 | `province_code`, `city_code`, `code` |
| `spider_fuwu_retail_pharmacy` | `medical_designated_providers` | 医保定点药店 | `province_code`, `city_code`, `code` |
| `spider_fuwu_service_facilities` | `medical_service_base` | 医疗服务项目 | `code`, `charge_item_code`, `admin_region_code` |
| `spider_fuwu_western_medicine` | `medical_drug_base` | 西药基础信息 | `code` |
| `spider_fuwu_chinese_prescription_medicine` | `medical_drug_base` | 中成药基础信息 | `code` |
| `spider_fuwu_tcm_herb` | `medical_chinese_herbal_drug_base` | 中草药基础信息 | `code`, `province_code` |
| `spider_fuwu_western_disease` | `medical_medicine_diagnosis` | 西医疾病诊断 | `rid` |
| `spider_fuwu_national_drug_retail_pharmacy` | `medical_national_negotiated_drug_providers` | 国谈药定点零售药店 | `rid` |
| `spider_fuwu_selfprep_medicine` | `medical_self_prepared_drug_base` | 自制药基础信息 | `code` |
| `spider_fuwu_medical_supplies` | `medical_supplies_base` | 医用耗材基础信息 | `code` |

## 清洗规则分类

### 1. 表级别清洗规则

#### 1.1 医保定点机构 (`spider_fuwu_fixed_hospital`)
- **类型验证**: 启用机构类型验证（字典ID: 24）
- **验证字段**: `medinsType` (类型代码) ↔ `medinsTypeName` (类型名称)

#### 1.2 医保定点药店 (`spider_fuwu_retail_pharmacy`)
- **类型验证**: 启用机构类型验证（字典ID: 24）
- **验证字段**: `medinsType` (类型代码) ↔ `medinsTypeName` (类型名称)
- **自定义清洗**: `businessLvOutMedOtp` 字段值转换（2→3）

#### 1.3 医疗服务项目 (`spider_fuwu_service_facilities`)
- **复杂业务逻辑处理**:
  - `medListCodg` 字段按 "-" 分割：前部分→`code`，后部分→`charge_item_code`
  - `servitemName` 字段处理：关联国家标准数据获取 `name` 和 `charge_item_name`
  - 数据过滤：只保留 `admdvs≠100000` 的数据
  - 日期字段转换：`begndate`、`enddate` 时间戳→日期格式

#### 1.4 西药信息 (`spider_fuwu_western_medicine`)
- **企业名称标准化**: `prodentpName`、`lstdLicHolder` 字段
- **占位符清理**: 多个字段的 "--" 值处理
- **用法用量智能清洗**: `eachDos` 字段问号清理和信息提取

#### 1.5 中成药信息 (`spider_fuwu_chinese_prescription_medicine`)
- **企业名称标准化**: `prodentpName`、`lstdLicHolder` 字段（与西药相同逻辑）
- **占位符清理**: 多个字段的 "--" 值处理（与西药相同逻辑）
- **用法用量智能清洗**: `eachDos` 字段问号清理和信息提取（与西药相同逻辑）
- **目标表过滤**: 写入时添加 `type_name = '中成药'` 条件

#### 1.6 中草药信息 (`spider_fuwu_tcm_herb`)
- **一对多字段映射**: `admdvs` 字段同时映射到 `province_code` 和 `province_name`
- **字典关联**: 通过字典ID=19获取省份名称
- **数据清洗**: 在清洗阶段直接创建目标字段，避免依赖medical_field_mapping表

#### 1.7 自制药信息 (`spider_fuwu_selfprep_medicine`)
- **批量无效值清洗**: 6个字段的 '无'、'--'、'-' 值统一替换为NULL
- **时间戳转换**: `aprvnoBegndate` 字段毫秒级时间戳转日期格式
- **智能清洗**: `eachDos` 字段问号清理和信息提取
- **直接字段映射**: 在清洗阶段创建23个目标字段，避免依赖medical_field_mapping表

#### 1.8 医用耗材信息 (`spider_fuwu_medical_supplies`)
- **唯一标识清洗**: 基于 `medListCodg` 字段确保数据唯一性
- **分类字段处理**: `firstMcsType`、`secondMcsType`、`thirdMcsType` 分类字段清洗
- **名称字段标准化**: `mcsName`、`hiGenname` 名称字段清理
- **规格材质清洗**: `spec`、`mcsMatl` 规格和材质字段处理
- **企业信息清洗**: `prodentpName` 生产企业名称标准化
- **去重处理**: 基于 `medListCodg` 字段的智能去重

#### 1.9 国谈药定点零售药店 (`spider_fuwu_national_drug_retail_pharmacy`)
- **唯一性标识**: 使用 `drugOptinsId` → `rid` 字段作为唯一键
- **一对多字段映射**: `province` → `province_code` + `province_name`, `region` → `city_code` + `city_name`
- **字典关联查询**: 省份和城市字段自动关联字典表获取编码信息
- **经纬度处理**: `lat`、`lnt` 字段保留小数部分，支持地理位置精确定位
- **数据清洗**: 统一使用NULL策略处理空字符串，确保数据一致性
- **数据量**: 约31万条记录，支持批量处理

### 2. 字段级别清洗规则

#### 2.1 地址字段清洗
**适用表**: `spider_fuwu_fixed_hospital`、`spider_fuwu_retail_pharmacy`
**字段**: `addr`

**清洗规则**:
1. **全角转半角转换**: 将全角字符转换为半角字符
2. **长度判断**: 根据地址长度选择清洗策略
   - 长度 > 200字符: 使用基础清洗（提取汉字、数字、常用标点）
   - 长度 ≤ 200字符: 使用增强清洗（详细规则见下）

**增强地址清洗详细规则**:

##### 地址标识符清理
- **末尾标识符**: 清除 `地址:`、`详细地址:`、`联系地址:`、`办公地址:`、`注册地址:`、`通讯地址:` 等
- **开头标识符**: 清除开头的地址标识符，包括 `址:` 等简化形式
- **中间标识符**: 清除文本中间的地址标识符

##### 网址和邮箱清理
- **网址清理**: 移除 `http://`、`https://`、`www.` 开头的网址
- **邮箱清理**: 移除标准邮箱格式（如 `<EMAIL>`）

##### 空格和标点规范化
- **特殊字符转换**: `〇` → `0`（中文零字符转数字）
- **不规范表述**: `底 商` → `底商`、`1、2 号楼` → `1、2号楼`
- **建筑空格**: 规范化建筑相关的空格表述
  - `3 层 201 室` → `3层201室`
  - `1 号 楼` → `1号楼`
  - `2 栋 3 单元` → `2栋3单元`

##### 智能空格处理
- **保护重要空格**: 保护门牌号、建筑标识之间的分隔空格
- **空格规范化**: 多个空格 → 单个空格，去除首尾空格

##### 特殊字符保护
保护有意义的特殊字符：
- 分号 `;`（地址分隔符）
- 斜杠 `/`、星号 `*`（单元号、房间范围）
- 地理坐标符号 `°′″`
- 连字符 `—`、`~`、`_`

##### 括号处理
- **清除空括号**: `()` 和 `（）`
- **清除无意义括号**: 只含标点的括号如 `(#)`、`(-)`

#### 2.2 电话字段清洗
**适用表**: `spider_fuwu_retail_pharmacy`
**字段**: `tel`

**清洗规则**:

##### 基础电话清洗
- 保留数字、`+`、`-`、`()`
- 移除其他非电话字符

##### 高级电话清洗（详细规则）

**预验证**:
- 纯数字输入长度验证
- 过滤明显无效长度（<7位或>25位）
- 特殊长度处理（如22位双手机号）

**全角转半角**:
- 全角空格(12288) → 半角空格(32)
- 全角字符(65281-65374) → 对应半角字符

**非电话内容移除**:
- 移除前缀: `联系电话:`、`联系方式:`、`电话:`、`手机:`
- 移除身份证号: 15位或18位数字
- 移除姓名: 2-4个汉字
- 移除地址信息: 包含"区"、"街道"、"路"等的长文本

**电话号码提取**:
- 手机号: `1[3-9]xxxxxxxxx`（11位）
- 固定电话: `0xxx-xxxxxxx`（带区号）
- 400/800电话: `400-xxx-xxxx`、`800-xxx-xxxx`

**分隔符标准化**:
- 全角标点 → 半角标点
- 共享区号处理: `0531-81769739/81769209` → `0531-81769739 0531-81769209`
- 斜杠分隔处理: 区分手机号和固定电话组合

**号码验证**:
- 手机号: 11位，1开头，第二位为3-9
- 固定电话: 7-12位，符合区号规则
- 400/800电话: 标准格式验证

**输出格式**:
- 多个号码用分号 `;` 分隔
- 自动去重，保持顺序

#### 2.3 全角转半角处理
**适用表**: `spider_fuwu_fixed_hospital`、`spider_fuwu_retail_pharmacy`
**字段**: `addr`

**转换规则**:
- 使用Unicode编码范围转换
- 全角空格(12288) → 半角空格(32)
- 全角字符(65281-65374) → 半角字符(减去65248)
- 保持字符完整性和准确性

#### 2.4 机构类型验证清洗
**适用表**: `spider_fuwu_fixed_hospital`、`spider_fuwu_retail_pharmacy`
**字段**: `medinsType`、`medinsTypeName`

**验证规则**:
1. **字典映射验证**: 使用字典ID 24验证机构类型代码
2. **有效性检查**: 只保留字典范围内的类型代码
3. **关联清理**: 如果类型代码无效，对应的类型名称也设为空
4. **统计记录**: 记录清洗前后的有效记录数量

**处理逻辑**:
```python
# 获取有效类型代码列表
valid_codes = ['01', '02', '03', ...]  # 从字典表获取

# 清洗类型代码
df['medinsType'] = df['medinsType'].apply(
    lambda x: x if x in valid_codes else None
)

# 清洗关联的类型名称
df.loc[df['medinsType'].isna(), 'medinsTypeName'] = None
```

#### 2.5 通用清洗规则
**适用范围**: 所有表的所有字段

**清洗规则**:
1. **空值处理**:
   - 字符串字段: 去除首尾空格
   - 空字符串和'None'字符串 → `None`
   - 特殊字段保留空字符串（根据配置）

2. **数据类型处理**:
   - 确保字符串字段为字符串类型
   - 处理混合数据类型

3. **特殊字段保护**:
   - 根据配置保留某些字段的空字符串值
   - 避免将有意义的空值转换为None

### 3. 自定义清洗函数详解

#### 3.1 西药数据清洗 (`_clean_fuwu_western_medicine_data`)

**处理字段**: `prodentpName`、`lstdLicHolder`、`natHiDruglistChrgitmLv`、`natDrugNo`、`drugGenname`、`natHiDruglistDosform`、`natHiDruglistMemo`、`eachDos`

##### 企业名称字段清洗
**字段**: `prodentpName` (生产企业名称)、`lstdLicHolder` (上市许可持有人)
**规则**:
1. 占位符处理: `--` → `Null`
2. 标点符号标准化: `，,；;` → `,`

**示例**:
```
原始值: "公司A，公司B；公司C"
清洗后: "公司A,公司B,公司C"

原始值: "--"
清洗后: null
```

##### 国家医保目录字段清洗
**字段**: `natHiDruglistChrgitmLv`、`natDrugNo`、`drugGenname`、`natHiDruglistDosform`、`natHiDruglistMemo`
**规则**: 占位符处理 `--` → `Null`

##### 用法用量智能清洗
**字段**: `eachDos` (用法用量)

**触发条件判断**:
1. **连续问号检测**: 包含3个或以上连续问号 (`???`)
2. **问号密度检测**: 问号数量占总字符数的30%以上
3. **保护机制**: 不满足触发条件的内容完全保持不变

**智能清洗策略**:

**第一步: 问号清理**
- 移除所有问号字符 (`?`)
- 清理多余空格和标点
- 多个逗号 → 单个中文逗号

**第二步: 内容验证**
- 清理后内容长度 < 3字符 → 返回空值
- 确保有实质性内容

**第三步: 信息提取模式**
使用正则表达式提取有效信息：

1. **完整用药描述**:
   - `每次\d+(?:\.\d+)?(?:ml|g|mg|μg|片|粒|袋|瓶|支|盒|包)`
   - `每[日天]\d+次`

2. **数字+单位组合**:
   - `\d+(?:\.\d+)?(?:kcal|ml|h|g|mg|μg|L|kg)(?:/\w+)?`

3. **数字范围**:
   - `\d+(?:\.\d+)?[-~]\d+(?:\.\d+)?(?:单位)?(?:/\w+)?`
   - 如: `100-125ml/h`、`1-2g`

4. **括号信息**:
   - `\([^)]*(?:\d+(?:\.\d+)?(?:单位))[^)]*\)`
   - 如: `(500ml×4)`、`(500ml 4)`

5. **营养液描述**:
   - `\d+(?:\.\d+)?kcal\s*\([^)]*\)`
   - 如: `2000kcal (500ml)`

**第四步: 结果组合**
- 去重: 移除重复的提取结果
- 排序: 保持提取顺序
- 连接: 用中文逗号 `，` 连接

**第五步: 基础清理兜底**
如果没有提取到特定模式：
- 保留中文、数字、字母和基本标点
- 移除特殊符号和无意义字符
- 长度 ≥ 3字符才保留

**清洗示例**:

```
# 示例1: 混乱营养液数据
原始值: "????????????????,???,????,?????????????????????,????????????????? 1kcal/ml,????? 100-125ml/h(???????),?????????,???????? ?????,???? 2000kcal (500ml ?4?)?????????????"
清洗后: "1kcal/ml，100-125ml/h，2000kcal，(500ml 4)，2000kcal (500ml 4)"

# 示例2: 包含有效信息的问号文本
原始值: "?????????????????????????????,????????????????????????????????????? ????  ????:???????????,????250ml,????? ???:???????????,?????,??????????????"
清洗后: "250ml"

# 示例3: 只有问号的无效数据
原始值: "???????????????????????"
清洗后: null

# 示例4: 正常用药说明（完全不处理）
原始值: "每次5ml，每日3次"
清洗后: "每次5ml，每日3次"

# 示例5: 完整用药说明（完全不处理）
原始值: "本品适合于外周静脉和中心静脉输注。成人:剂量应根据患者的个体需要进行调整..."
清洗后: (完全保持原样)
```

**性能优化**:
- 预先检查触发条件，避免不必要的处理
- 正则表达式编译优化
- 批量处理提高效率

#### 3.2 医疗服务项目清洗 (`_clean_fuwu_service_facilities_data`)

**处理字段**: `medListCodg`、`servitemName`、`admdvs`、`begndate`、`enddate`、其他映射字段

##### 数据预处理
**第一步: 国家标准数据提取**
```python
# 提取国家标准数据用于关联
national_data = df[df['admdvs'].astype(str) == '100000'].copy()
national_mapping = national_data.set_index('medListCodg')['servitemName'].to_dict()
```

**第二步: 数据过滤**
```python
# 只保留地方数据进行处理
df_filtered = df[df['admdvs'].astype(str) != '100000'].copy()
```

##### 代码字段分割处理
**字段**: `medListCodg`
**分割逻辑**:
```python
def extract_charge_code(medListCodg_value):
    if pd.notna(medListCodg_value) and str(medListCodg_value).strip():
        value_str = str(medListCodg_value).strip()
        if '-' in value_str:
            parts = value_str.split('-', 1)  # 只分割一次
            if len(parts) > 1:
                return parts[1]  # 返回第一个'-'后面的所有内容
    return ''
```

**处理结果**:
- 保持 `medListCodg` 原值不变（供字段映射阶段处理）
- 创建临时字段 `_charge_item_code_processed` 用于关联

**示例**:
```
原始值: "A001-B002-C003"
code部分: "A001" (字段映射阶段提取)
charge_item_code部分: "B002-C003" (包含所有'-'后的内容)
```

##### 名称字段关联处理
**字段**: `servitemName`

**name字段生成**:
```python
def get_name_by_code(medListCodg_value):
    # 从medListCodg提取code部分（第一个'-'之前）
    if pd.notna(medListCodg_value) and str(medListCodg_value).strip():
        medListCodg_str = str(medListCodg_value).strip()
        if '-' in medListCodg_str:
            code = medListCodg_str.split('-')[0]
        else:
            code = medListCodg_str

        # 在国家标准数据中查找对应的name
        if code and code in national_mapping:
            return national_mapping[code]
    return ''
```

**charge_item_name字段生成**:
```python
# 直接使用当前记录的servitemName值
df['charge_item_name'] = df['servitemName'].fillna('')
```

**关联成功率统计**:
- 记录name关联成功的数量和比例
- 记录charge_item_name设置成功的数量和比例

##### 行政区划字段处理
**字段**: `admdvs`
**处理逻辑**:
- 确保字段为字符串类型
- 保持原值，由字段映射阶段处理字典关联
- 记录样本值用于调试

##### 日期字段转换
**字段**: `begndate`、`enddate`
**转换逻辑**:
```python
from transfrom.utils.date import batch_convert_timestamp_to_date

# 批量转换时间戳为日期
df[date_field] = batch_convert_timestamp_to_date(df[date_field])
```

**转换规则**:
- 正数时间戳: 标准Unix时间戳转换
- 负数时间戳: 特殊处理逻辑
- 输出格式: YYYY-MM-DD
- 无效值: 转换为None

##### 其他字段映射
**直接映射字段**:
```python
field_mappings = {
    'trtItemDscr': 'treatment_item_description',    # 诊疗项目描述
    'trtItemCont': 'treatment_item_content',        # 诊疗项目内容
    'trtExctCont': 'treatment_excluded_content',    # 诊疗排除内容
    'prcunt': 'pricing_unit',                       # 计价单位
    'memo': 'remark',                               # 备注
    'rid': 'rid'                                    # 记录ID
}

# 直接复制字段值
for source_field, target_field in field_mappings.items():
    if source_field in df.columns:
        df[target_field] = df[source_field]
```

##### 数据质量保证
**验证检查**:
1. 确认国家标准数据存在性
2. 验证代码分割逻辑正确性
3. 检查关联成功率
4. 记录处理统计信息

**错误处理**:
- 缺少国家标准数据时的警告
- 字段不存在时的跳过逻辑
- 异常情况的日志记录

#### 3.3 药店数据清洗 (`_clean_fuwu_pharmacy_data`)

##### 业务级别字段转换
**字段**: `businessLvOutMedOtp`
**规则**: 值 `2` → `3`
**说明**: 业务需求的特定值转换

#### 3.4 中成药数据清洗 (`_clean_fuwu_chinese_medicine_data`)

**处理字段**: `prodentpName`、`lstdLicHolder`、`natHiDruglistChrgitmLv`、`natDrugNo`、`drugGenname`、`natHiDruglistDosform`、`natHiDruglistMemo`、`eachDos`

**清洗规则**: 完全参考西药数据清洗逻辑 (`_clean_fuwu_western_medicine_data`)

##### 企业名称字段清洗
**字段**: `prodentpName` (生产企业名称)、`lstdLicHolder` (上市许可持有人)
**规则**: 与西药相同
1. 占位符处理: `--` → `Null`
2. 标点符号标准化: `，,；;` → `,`

##### 国家医保目录字段清洗
**字段**: `natHiDruglistChrgitmLv`、`natDrugNo`、`drugGenname`、`natHiDruglistDosform`、`natHiDruglistMemo`
**规则**: 与西药相同，占位符处理 `--` → `Null`

##### 用法用量智能清洗
**字段**: `eachDos` (用法用量)
**规则**: 与西药完全相同的智能清洗逻辑
- 触发条件判断 (连续问号、问号密度)
- 五步清洗策略
- 信息提取模式
- 性能优化措施

**目标表区分**:
- 中成药数据写入 `medical_drug_base` 表时添加过滤条件 `type_name = '中成药'`
- 与西药数据在同一目标表中通过 `type_name` 字段区分

#### 3.5 中草药数据清洗 (`_clean_fuwu_tcm_herb_data`)

**处理字段**: `admdvs`、`medListCodg`、`tcmherbName` 等

##### 一对多字段映射处理
**字段**: `admdvs` (行政区划代码)

**映射逻辑**:
1. **直接映射**: `admdvs` → `province_code`
2. **字典映射**: `admdvs` → `province_name` (通过字典ID=19)

**核心代码**:
```python
# 确保字段类型
df['admdvs'] = df['admdvs'].astype(str)

# 直接映射到province_code
df['province_code'] = df['admdvs']

# 字典映射到province_name
df['province_name'] = df['admdvs'].apply(get_province_name_by_admdvs)
```

**处理机制**:
- 在数据清洗阶段直接创建目标字段
- 字段映射阶段自动跳过已存在字段
- 支持复杂的字典关联查询

##### 基础数据清洗
- 关键字段空值检查
- 数据类型转换
- 字段完整性验证

#### 3.6 自制药数据清洗 (`_clean_fuwu_selfprep_medicine_data`)

**处理字段**: 23个字段的完整映射关系

##### 批量无效值清洗
**处理字段**: `prodentpName`、`prodentpAddr`、`pacmatl`、`aprvno`、`eldPatnMedc`、`chldMedc`

**清洗规则**:
- 无效值: `'无'`、`'--'`、`'-'`
- 处理方式: 统一替换为 `NULL`
- 优化方式: 向量化批量处理

**核心代码**:
```python
invalid_value_fields = ['prodentpName', 'prodentpAddr', 'pacmatl', 'aprvno', 'eldPatnMedc', 'chldMedc']
invalid_values = ['无', '--', '-']

for field in invalid_value_fields:
    if field in df.columns:
        mask_invalid = df[field].isin(invalid_values)
        df.loc[mask_invalid, field] = None
```

##### eachDos字段智能清洗
**字段**: `eachDos` (每次剂量)

**清洗规则**: 参考西药清洗逻辑
- 触发条件: 连续问号检测、问号密度检测
- 清洗策略: 问号清理、内容验证、信息提取
- 无效值处理: `'--'`、`'-'`、`'无'` → `NULL`

##### 时间戳转换处理
**字段**: `aprvnoBegndate` (批准文号开始日期)

**转换规则**:
- 输入格式: 毫秒级时间戳 (如: 1609344000000)
- 输出格式: YYYY-MM-DD 日期格式 (如: 2020-12-31)
- 处理函数: `batch_convert_timestamp_to_date`

**核心代码**:
```python
from transfrom.utils.date import batch_convert_timestamp_to_date
df['aprvnoBegndate'] = batch_convert_timestamp_to_date(df['aprvnoBegndate'])
```

##### 直接字段映射
**映射数量**: 23个完整字段映射关系

#### 3.7 医用耗材注册信息清洗 (`clean_fuwu_medical_supplies_register_data`)

**处理字段**: `medListCodg`、`rregisterMessageDTOS`

**目标表**: `medical_supplies_register_message`

**唯一索引**: `(code, registration_number, single_product_name)`

##### 数据过滤
**第一步: 有效记录过滤**
```python
# 过滤条件：rregisterMessageDTOS 不为空且不为空字符串且不为空数组
df_filtered = df[
    df['rregisterMessageDTOS'].notna() &
    (df['rregisterMessageDTOS'] != '') &
    (df['rregisterMessageDTOS'] != '[]')
].copy()
```

##### JSON解析修复
**第二步: 转义字符处理**

**问题**: 源数据中包含无效转义字符（如 `\xad\xad`）导致JSON解析失败

**解决方案**:
```python
import re

# 修复JSON格式问题：将单引号替换为双引号
rregisterMessageDTOS = rregisterMessageDTOS.replace("'", '"')

# 修复无效的转义字符问题
# 处理类似 \xad\xad 这样的无效转义序列
rregisterMessageDTOS = re.sub(r'\\x[0-9a-fA-F]{2}', '', rregisterMessageDTOS)
# 移除其他可能的无效转义字符（除了常见的 \n, \t, \r, \", \\）
rregisterMessageDTOS = re.sub(r'\\(?![ntr"\\])', '', rregisterMessageDTOS)
```

**处理效果**:
- 原始值: `β\xad\xad-葡聚糖阴道凝胶`
- 清洗后: `β-葡聚糖阴道凝胶`

##### 一对多映射展开
**第三步: JSON数据解析和展开**

**数据格式**:
```json
{
  "specMolNum": "3",
  "regFilNo": "-",
  "sinProdName": "锁定接骨板"
},
{
  "specMolNum": "3",
  "regFilNo": "-",
  "sinProdName": "锁定接骨板-胫骨"
}
```

**展开逻辑**:
```python
for item in register_items:
    if isinstance(item, dict):
        # 提取注册信息
        regFilNo = item.get('regFilNo', '')
        sinProdName = item.get('sinProdName', '')

        # 创建展开后的记录
        expanded_record = {
            'code': medListCodg,                    # 来源于 medListCodg
            'registration_number': regFilNo,        # 来源于 regFilNo
            'single_product_name': sinProdName,     # 来源于 sinProdName
            # 保留原始字段以支持字段映射
            'medListCodg': medListCodg,
            'rregisterMessageDTOS': str(rregisterMessageDTOS),
            'specMolNum': item.get('specMolNum', ''),
        }
        expanded_records.append(expanded_record)
```

##### 数据清洗和标准化
**第四步: 字段值清洗**
```python
for field in ['code', 'registration_number', 'single_product_name']:
    if field in df_expanded.columns:
        # 清理空字符串和'-'值
        df_expanded[field] = df_expanded[field].replace(['', '-', '--'], None)
        df_expanded[field] = df_expanded[field].astype(str).str.strip()
        df_expanded[field] = df_expanded[field].replace(['', 'None', 'nan'], None)
```

##### 无效记录过滤
**第五步: 数据质量保证**

**过滤条件**: 除了code字段外，其他关键字段都为空的记录是无效的
```python
# 过滤无效记录（registration_number和single_product_name都为空的记录）
df_expanded = df_expanded[
    df_expanded['single_product_name'].notna() |  # single_product_name有值
    df_expanded['registration_number'].notna()    # 或者registration_number有值
]
```

**过滤效果**:
- 过滤前: 可能包含只有code有值的无效记录
- 过滤后: 只保留至少有产品名称或注册编码的有效记录

##### 去重处理
**第六步: 基于复合唯一索引去重**
```python
df_expanded = df_expanded.drop_duplicates(
    subset=['code', 'registration_number', 'single_product_name'],
    keep='first'
)
```

##### 处理统计
**清洗完成后的统计信息**:
- 输入记录数
- 解析出注册项数量
- 解析错误数
- 去重后记录数
- 最终输出记录数
- 有效字段统计

**示例输出**:
```
医用耗材注册信息数据清洗完成:
  - 输入记录数: 1000
  - 解析出注册项: 3500
  - 解析错误数: 5
  - 去重后记录数: 3450
  - 最终输出记录数: 3200
  - 有效code数: 3200
  - 有效registration_number数: 0
  - 有效single_product_name数: 3200
```

**主要映射**:
- 基础信息: `medListCodg→code`、`drugProdname→name`、`dosform→dosage_form`
- 医疗机构: `hospPrepAppyerEmpName→hospital_name`、`medinsConerName→hospital_contact_person`
- 生产企业: `prodentpName→production_company_name`、`prodentpAddr→production_company_address`
- 其他字段: 包装、批号、注意事项等

**处理方式**:
- 在数据清洗阶段直接创建所有目标字段
- 避免依赖 medical_field_mapping 表
- 支持完整的字段映射关系

## 配置文件位置

### 主要配置文件
- **表配置**: `config/table_configs.py`
- **数据清洗配置**: `config/data_cleaning_config.py`
- **字段映射**: 数据库表 `medical_field_mapping`

### 清洗函数注册
```python
# 在 get_custom_cleaning_functions() 中注册
{
    'clean_fuwu_western_medicine_data': self._clean_fuwu_western_medicine_data,
    'clean_fuwu_service_facilities_data': self._clean_fuwu_service_facilities_data,
    'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data,
    'clean_fuwu_chinese_medicine_data': self._clean_fuwu_chinese_medicine_data,
    # ...
}
```

### 表级别清洗配置

#### 配置结构说明

表级别清洗配置支持两种模式：

**单一清洗函数模式**：
```python
'table_name': {
    'custom_function': 'function_name'  # 所有目标表使用同一个清洗函数
}
```

**目标表级别清洗函数模式**：
```python
'table_name': {
    'custom_function_by_target': {
        'target_table_1': 'function_name_1',  # 目标表1使用特定清洗函数
        'target_table_2': 'function_name_2'   # 目标表2使用特定清洗函数
    }
}
```

#### 清洗函数详细说明

##### 1. `clean_fuwu_hospital_data` - 医保定点机构清洗
**源表**: `spider_fuwu_fixed_hospital`
**目标表**: `medical_designated_providers`
**主要功能**: 基础清洗，主要依赖字段级别清洗规则
**处理逻辑**:
- 预留地址字段增强清洗接口
- 配合字段级别的地址和电话清洗
- 机构类型验证

##### 2. `clean_fuwu_pharmacy_data` - 医保定点药店清洗
**源表**: `spider_fuwu_retail_pharmacy`
**目标表**: `medical_designated_providers`
**主要功能**: 药店特有字段处理
**处理逻辑**:
- `businessLvOutMedOtp` 字段值转换：`2` → `3`
- 配合字段级别的地址和电话清洗
- 机构类型验证

##### 3. `clean_fuwu_service_facilities_data` - 医疗服务项目清洗
**源表**: `spider_fuwu_service_facilities`
**目标表**: `medical_service_base`
**主要功能**: 复杂的一对多映射和数据关联
**处理逻辑**:
- **数据过滤**: 只保留 `admdvs != 100000` 的地方数据
- **国家标准关联**: 使用 `admdvs = 100000` 的数据进行名称关联
- **字段分割**: `medListCodg` 按 "-" 分割为 `code` 和 `charge_item_code`
- **名称映射**: 通过code关联获取标准名称
- **时间戳转换**: `begndate`、`enddate` 转换为日期格式
- **字段映射**: 创建目标字段并进行映射

##### 4. `clean_fuwu_western_medicine_data` - 西药数据清洗
**源表**: `spider_fuwu_western_medicine`
**目标表**: `medical_drug_base`
**主要功能**: 药品数据标准化和智能清洗
**处理逻辑**:
- **企业名称标准化**: `prodentpName`、`lstdLicHolder` 字段
  - `--` 值替换为 `NULL`
  - 中英文标点统一：`，,；;` → `,`
- **药品信息清洗**: `natHiDruglistChrgitmLv`、`natDrugNo`、`drugGenname` 等字段
  - `--` 值替换为 `NULL`
- **用法用量智能清洗**: `eachDos` 字段
  - 问号密度检测和清理
  - 有效信息提取
  - `--`、`无` 值处理
- **产品名称清洗**: `drugProdname`、`pacmatl` 字段
  - `无` 值替换为 `NULL`

##### 5. `clean_fuwu_chinese_medicine_data` - 中成药数据清洗
**源表**: `spider_fuwu_chinese_prescription_medicine`
**目标表**: `medical_drug_base`
**主要功能**: 参考西药清洗逻辑
**处理逻辑**: 与西药清洗完全相同的处理逻辑

##### 6. `clean_fuwu_tcm_herb_data` - 中草药数据清洗
**源表**: `spider_fuwu_tcm_herb`
**目标表**: `medical_chinese_herbal_drug_base`
**主要功能**: 中草药特有的一对多映射处理
**处理逻辑**:
- **一对多映射**: `admdvs` 字段展开为多条记录
- **省份信息关联**: 通过字典获取省份名称
- **数据去重**: 基于复合唯一索引去重

##### 7. `clean_fuwu_selfprep_medicine_data` - 自制药数据清洗
**源表**: `spider_fuwu_selfprep_medicine`
**目标表**: `medical_self_prepared_drug_base`
**主要功能**: 自制药特有字段处理
**处理逻辑**:
- **批量无效值清洗**: 6个字段的 `无`、`--`、`-` 值处理
- **用法用量清洗**: 参考西药清洗逻辑
- **时间戳转换**: `aprvnoBegndate` 字段转换为日期格式
- **电话清洗**: `medinsConerTel` 字段配合字段级别清洗

##### 8. `clean_fuwu_medical_supplies_data` - 医用耗材基础数据清洗
**源表**: `spider_fuwu_medical_supplies`
**目标表**: `medical_supplies_base`
**主要功能**: 医用耗材基础信息标准化
**处理逻辑**:
- **分类字段处理**: `firstMcsType`、`secondMcsType`、`thirdMcsType`
- **名称标准化**: `mcsName`、`hiGenname` 字段清洗
- **规格材质处理**: `spec`、`mcsMatl` 字段标准化
- **企业信息清洗**: `prodentpName` 生产企业名称处理
- **唯一性保证**: 基于 `medListCodg` 去重

##### 9. `clean_fuwu_medical_supplies_register_data` - 医用耗材注册信息清洗
**源表**: `spider_fuwu_medical_supplies`
**目标表**: `medical_supplies_register_message`
**主要功能**: JSON数据解析和一对多映射
**处理逻辑**:
- **数据过滤**: 只处理 `rregisterMessageDTOS` 有值的记录
- **JSON解析修复**: 处理无效转义字符（如 `\xad\xad`）
- **一对多展开**: JSON数组中每个对象展开为独立记录
- **字段映射**: `regFilNo` → `registration_number`, `sinProdName` → `single_product_name`
- **无效记录过滤**: 过滤除了code外其他字段都为空的记录
- **复合去重**: 基于 `(code, registration_number, single_product_name)` 去重

##### 10. `clean_fuwu_national_drug_retail_pharmacy_data` - 国谈药定点零售药店清洗
**源表**: `spider_fuwu_national_drug_retail_pharmacy`
**目标表**: `medical_national_negotiated_drug_providers`
**主要功能**: 国谈药定点零售药店数据标准化处理
**处理逻辑**:
- **唯一性标识**: 使用 `drugOptinsId` → `rid` 字段确保数据唯一性
- **一对多字段映射**: `province` → `province_code` + `province_name`, `region` → `city_code` + `city_name`
- **字典关联查询**: 省份和城市字段自动关联字典表获取编码信息，映射成功率通常为100%
- **经纬度处理**: `lat`、`lnt` 字段保留小数部分，支持地理位置精确定位
- **数据清洗**: 统一使用NULL策略处理空字符串，确保数据一致性和质量
- **批量处理**: 支持大批量数据处理（约31万条记录）
- **字段映射**: 完整的12个字段映射关系，包括药品名称、机构信息、地理位置等

#### 清洗函数复杂度和性能对比

| 清洗函数 | 复杂度 | 主要耗时操作 | 推荐批次大小 | 特殊处理 |
|----------|--------|--------------|--------------|----------|
| `clean_fuwu_hospital_data` | 低 | 基础清洗 | 5000 | 无 |
| `clean_fuwu_pharmacy_data` | 低 | 字段值转换 | 5000 | 业务逻辑转换 |
| `clean_fuwu_service_facilities_data` | 高 | 数据关联、字段分割 | 3000 | 一对多映射、国家标准关联 |
| `clean_fuwu_western_medicine_data` | 中 | 正则表达式、问号清洗 | 2000 | 智能用法用量清洗 |
| `clean_fuwu_chinese_medicine_data` | 中 | 正则表达式、问号清洗 | 2000 | 与西药相同逻辑 |
| `clean_fuwu_tcm_herb_data` | 中 | 一对多展开 | 3000 | admdvs一对多映射 |
| `clean_fuwu_selfprep_medicine_data` | 中 | 批量字段处理、时间戳转换 | 3000 | 23个字段映射 |
| `clean_fuwu_medical_supplies_data` | 中 | 分类字段处理 | 3000 | 三级分类处理 |
| `clean_fuwu_medical_supplies_register_data` | 高 | JSON解析、一对多展开 | 1000 | JSON修复、复合去重 |
| `clean_fuwu_national_drug_retail_pharmacy_data` | 中 | 字典关联、一对多映射 | 3000 | 地理位置处理、大数据量 |

#### 具体配置示例

```python
# 在 get_table_cleaning_rules() 中配置
def get_table_cleaning_rules(self) -> Dict[str, Dict[str, any]]:
    return {
        'spider_fuwu_fixed_hospital': {
            'type_validation': {
                'enabled': True,
                'dict_id': 24,
                'type_field': 'medinsType',
                'type_name_field': 'medinsTypeName'
            }
        },
        'spider_fuwu_retail_pharmacy': {
            'type_validation': {
                'enabled': True,
                'dict_id': 24,
                'type_field': 'medinsType',
                'type_name_field': 'medinsTypeName'
            },
            'custom_function': 'clean_fuwu_pharmacy_data'
        },
        'spider_fuwu_service_facilities': {
            'custom_function': 'clean_fuwu_service_facilities_data'
        },
        'spider_fuwu_western_medicine': {
            'custom_function': 'clean_fuwu_western_medicine_data'
        },
        'spider_fuwu_chinese_prescription_medicine': {
            'custom_function': 'clean_fuwu_chinese_medicine_data'
        },
        'spider_fuwu_tcm_herb': {
            'custom_function': 'clean_fuwu_tcm_herb_data'
        },
        'spider_fuwu_selfprep_medicine': {
            'custom_function': 'clean_fuwu_selfprep_medicine_data'
        },
        'spider_fuwu_medical_supplies': {
            'custom_function_by_target': {
                'medical_supplies_base': 'clean_fuwu_medical_supplies_data',  # 基础数据清洗
                'medical_supplies_register_message': 'clean_fuwu_medical_supplies_register_data'  # 注册信息清洗（一对多）
            }
        },
        'spider_fuwu_national_drug_retail_pharmacy': {
            'custom_function': 'clean_fuwu_national_drug_retail_pharmacy_data'
        }
    }
```

#### 使用建议和最佳实践

##### 选择合适的清洗模式
1. **单一清洗函数模式**: 适用于所有目标表使用相同清洗逻辑的场景
   - 示例：西药、中成药数据清洗
   - 优点：配置简单，维护方便
   - 缺点：灵活性较低

2. **目标表级别清洗函数模式**: 适用于不同目标表需要不同清洗逻辑的场景
   - 示例：医用耗材数据（基础数据 vs 注册信息）
   - 优点：灵活性高，可针对性优化
   - 缺点：配置复杂，需要维护多个函数

##### 性能优化建议
1. **批次大小设置**: 根据清洗复杂度调整批次大小
   - 高复杂度（JSON解析、一对多映射）：1000-3000
   - 中复杂度（正则表达式、字段处理）：2000-3000
   - 低复杂度（基础清洗）：5000+

2. **内存管理**:
   - 及时释放中间变量
   - 使用向量化操作替代循环
   - 避免在循环中创建大对象

3. **并行处理**:
   - 字段级别清洗可以并行执行
   - 批次处理可以多线程执行
   - 注意数据库连接池限制

##### 错误处理策略
1. **输入验证**: 检查DataFrame是否为空
2. **异常捕获**: 捕获并记录清洗过程中的异常
3. **数据保护**: 异常时返回原始数据，避免数据丢失
4. **日志记录**: 详细记录清洗前后的数据统计

##### 测试和验证
1. **单元测试**: 为每个清洗函数编写单元测试
2. **数据质量检查**: 清洗后验证数据完整性
3. **性能测试**: 监控清洗函数的执行时间
4. **回归测试**: 修改清洗逻辑后验证历史数据

### 字段级别清洗配置
```python
# 在 get_field_cleaning_rules() 中配置
def get_field_cleaning_rules(self) -> Dict[str, Dict[str, Dict[str, any]]]:
    return {
        'spider_fuwu_fixed_hospital': {
            'addr': {
                'fullwidth_to_halfwidth': {
                    'enabled': True
                },
                'address_cleaning': {
                    'enabled': True,
                    'min_length': 200
                }
            }
        },
        'spider_fuwu_retail_pharmacy': {
            'addr': {
                'fullwidth_to_halfwidth': {
                    'enabled': True
                },
                'address_cleaning': {
                    'enabled': True,
                    'min_length': 200
                }
            },
            'tel': {
                'phone_cleaning_advanced': {
                    'enabled': True
                }
            }
        }
    }
```

### 自定义函数注册配置
```python
# 在 get_custom_cleaning_functions() 中注册
def get_custom_cleaning_functions(self) -> Dict[str, Callable]:
    return {
        'clean_fuwu_hospital_data': self._clean_fuwu_hospital_data,
        'clean_fuwu_pharmacy_data': self._clean_fuwu_pharmacy_data,
        'clean_fuwu_service_facilities_data': self._clean_fuwu_service_facilities_data,
        'clean_fuwu_western_medicine_data': self._clean_fuwu_western_medicine_data,
        'clean_fuwu_chinese_medicine_data': self._clean_fuwu_chinese_medicine_data,
        'clean_fuwu_tcm_herb_data': self._clean_fuwu_tcm_herb_data,
        'clean_fuwu_selfprep_medicine_data': self._clean_fuwu_selfprep_medicine_data,
        'clean_fuwu_medical_supplies_data': self.clean_fuwu_medical_supplies_data,
        'clean_fuwu_medical_supplies_register_data': self.clean_fuwu_medical_supplies_register_data,
        'clean_fuwu_national_drug_retail_pharmacy_data': self._clean_fuwu_national_drug_retail_pharmacy_data,
        # 字段级处理函数
        'process_medListCodg_for_charge_code': self._process_medListCodg_for_charge_code,
        'process_admdvs_for_region_name': self._process_admdvs_for_region_name,
        'convert_timestamp_to_date': self._convert_timestamp_to_date,
    }
```

## 使用方法

### 自动执行（推荐）
```bash
cd dw/transfrom/tasks/spider/gjyb/pipeline
python fuwu_etl_mapping.py
```

### 编程调用
```python
from transfrom.tasks.spider.gjyb.pipeline.fuwu_etl_mapping import execute_etl_transform

# 西药数据ETL
success = execute_etl_transform(
    source_table='spider_fuwu_western_medicine',
    target_table='medical_drug_base',
    unique_fields=['code'],
    operation_mode='upsert'
)

# 医用耗材基础数据ETL
success = execute_etl_transform(
    source_table='spider_fuwu_medical_supplies',
    target_table='medical_supplies_base',
    unique_fields=['code'],
    operation_mode='upsert'
)

# 医用耗材注册信息ETL（一对多映射）
success = execute_etl_transform(
    source_table='spider_fuwu_medical_supplies',
    target_table='medical_supplies_register_message',
    unique_fields=['code', 'registration_number', 'single_product_name'],
    operation_mode='upsert'
)

# 国谈药定点零售药店ETL
success = execute_etl_transform(
    source_table='spider_fuwu_national_drug_retail_pharmacy',
    target_table='medical_national_negotiated_drug_providers',
    unique_fields=['rid'],
    operation_mode='upsert'
)
```

## 清洗效果监控

### 日志输出
ETL执行时会输出详细的清洗统计信息：
- 清洗前后的有效记录数
- 处理的特殊值数量（如 "--" 值、问号数量）
- 字段关联成功率
- 数据过滤统计

### 验证方法
```sql
-- 检查药品目标表数据质量
SELECT COUNT(*) FROM medical_drug_base WHERE code IS NOT NULL;

-- 检查药品清洗效果
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN production_company_name LIKE '%,%' THEN 1 END) as standardized_names
FROM medical_drug_base;

-- 检查医用耗材目标表数据质量
SELECT COUNT(*) FROM medical_supplies_base WHERE code IS NOT NULL;

-- 检查医用耗材清洗效果
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN first_type IS NOT NULL THEN 1 END) as with_classification,
    COUNT(CASE WHEN production_company_name IS NOT NULL THEN 1 END) as with_company
FROM medical_supplies_base;

-- 检查国谈药定点零售药店目标表数据质量
SELECT COUNT(*) FROM medical_national_negotiated_drug_providers WHERE rid IS NOT NULL;

-- 检查国谈药定点零售药店清洗效果
SELECT
    COUNT(*) as total_records,
    COUNT(CASE WHEN province_code IS NOT NULL THEN 1 END) as with_province_code,
    COUNT(CASE WHEN province_name IS NOT NULL THEN 1 END) as with_province_name,
    COUNT(CASE WHEN city_code IS NOT NULL THEN 1 END) as with_city_code,
    COUNT(CASE WHEN city_name IS NOT NULL THEN 1 END) as with_city_name,
    COUNT(CASE WHEN lat IS NOT NULL AND lnt IS NOT NULL THEN 1 END) as with_coordinates
FROM medical_national_negotiated_drug_providers;
```

## 扩展和维护

### 添加新的清洗规则
1. 在相应的清洗函数中添加处理逻辑
2. 更新配置文件中的规则定义
3. 添加相应的测试用例

### 修改现有规则
1. 修改 `data_cleaning_config.py` 中的清洗函数
2. 重新部署即可生效
3. 建议先在测试环境验证

### 性能优化建议

#### 批处理优化
```python
# 推荐的批处理大小设置
BATCH_SIZES = {
    'spider_fuwu_fixed_hospital': 5000,      # 医院数据相对简单
    'spider_fuwu_retail_pharmacy': 5000,     # 药店数据相对简单
    'spider_fuwu_service_facilities': 3000,  # 服务项目数据复杂
    'spider_fuwu_western_medicine': 2000,    # 西药数据清洗复杂
    'spider_fuwu_medical_supplies': 3000,    # 医用耗材数据中等复杂度
}
```

#### 内存优化
```python
# 大数据集处理策略
def process_large_dataset(df, batch_size=1000):
    total_rows = len(df)
    for start_idx in range(0, total_rows, batch_size):
        end_idx = min(start_idx + batch_size, total_rows)
        batch_df = df.iloc[start_idx:end_idx].copy()

        # 处理批次数据
        cleaned_batch = apply_cleaning_rules(batch_df)

        # 释放内存
        del batch_df

        yield cleaned_batch
```

#### 正则表达式优化
```python
# 预编译正则表达式
import re

class OptimizedPatterns:
    PHONE_PATTERN = re.compile(r'1[3-9]\d{9}')
    ADDRESS_PATTERN = re.compile(r'[\u4e00-\u9fa5\d\-\#\(\)（）层室号楼栋幢座]+')
    QUESTION_PATTERN = re.compile(r'\?{3,}')

    @classmethod
    def extract_phones(cls, text):
        return cls.PHONE_PATTERN.findall(text)
```

#### 并行处理
```python
# 字段级并行清洗
from concurrent.futures import ThreadPoolExecutor

def parallel_field_cleaning(df, field_rules):
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {}

        for field_name, rules in field_rules.items():
            if field_name in df.columns:
                future = executor.submit(clean_field, df[field_name], rules)
                futures[field_name] = future

        # 收集结果
        for field_name, future in futures.items():
            df[field_name] = future.result()

    return df
```

#### 缓存优化
```python
# 字典映射缓存
from functools import lru_cache

class CachedDictMapping:
    @lru_cache(maxsize=1000)
    def get_region_name(self, region_code):
        return self.dict_manager.get_region_name(region_code)

    @lru_cache(maxsize=500)
    def get_institution_type(self, type_code):
        return self.dict_manager.get_institution_type(type_code)
```

### 最佳实践

#### 清洗函数设计原则
1. **单一职责**: 每个清洗函数只处理特定类型的数据问题
2. **幂等性**: 多次执行相同清洗函数应产生相同结果
3. **容错性**: 处理异常输入，避免程序崩溃
4. **可测试性**: 提供清晰的输入输出，便于单元测试

#### 配置管理最佳实践
```python
# 使用配置类管理复杂配置
class CleaningConfig:
    # 地址清洗配置
    ADDRESS_MIN_LENGTH = 200
    ADDRESS_PATTERNS = {
        'remove_prefixes': [r'^地址[:：]?\s*', r'^详细地址[:：]?\s*'],
        'remove_suffixes': [r'地址[:：]?$', r'详细地址[:：]?$'],
    }

    # 电话清洗配置
    PHONE_PATTERNS = {
        'mobile': r'1[3-9]\d{9}',
        'landline': r'0\d{2,3}-?\d{6,8}',
        'service': r'[48]00-?\d{3}-?\d{4}',
    }

    # 用法用量清洗配置
    DOSAGE_PATTERNS = {
        'trigger_conditions': {
            'consecutive_questions': 3,
            'question_ratio': 0.3,
        },
        'extract_patterns': [
            r'每次\d+(?:\.\d+)?(?:ml|g|mg|μg|片|粒|袋|瓶|支|盒|包)',
            r'每[日天]\d+次',
            r'\d+(?:\.\d+)?(?:kcal|ml|h|g|mg|μg|L|kg)(?:/\w+)?',
        ]
    }
```

#### 错误处理和日志记录
```python
import logging

def robust_cleaning_function(df):
    """健壮的清洗函数示例"""
    logger = logging.getLogger(__name__)

    try:
        # 输入验证
        if df.empty:
            logger.warning("输入DataFrame为空")
            return df

        # 记录清洗前状态
        before_count = len(df)
        logger.info(f"开始清洗，输入记录数: {before_count}")

        # 执行清洗逻辑
        cleaned_df = apply_cleaning_logic(df)

        # 记录清洗后状态
        after_count = len(cleaned_df)
        logger.info(f"清洗完成，输出记录数: {after_count}")

        # 数据质量检查
        if after_count < before_count * 0.8:
            logger.warning(f"清洗后数据量减少超过20%，请检查清洗逻辑")

        return cleaned_df

    except Exception as e:
        logger.error(f"清洗函数执行失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        # 返回原始数据，避免数据丢失
        return df
```

#### 测试策略
```python
import unittest
import pandas as pd

class TestDataCleaning(unittest.TestCase):
    def setUp(self):
        """准备测试数据"""
        self.test_data = pd.DataFrame({
            'addr': ['北京市朝阳区', '地址：上海市浦东新区', '--'],
            'tel': ['13800138000', '010-12345678', '联系电话：13900139000'],
            'eachDos': ['每次5ml，每日3次', '???1kcal/ml???', '--']
        })

    def test_address_cleaning(self):
        """测试地址清洗"""
        result = clean_address_enhanced('地址：北京市朝阳区')
        self.assertEqual(result, '北京市朝阳区')

    def test_phone_cleaning(self):
        """测试电话清洗"""
        result = clean_phone_number_advanced('联系电话：13800138000')
        self.assertEqual(result, '13800138000')

    def test_dosage_cleaning(self):
        """测试用法用量清洗"""
        # 正常数据应保持不变
        result = clean_each_dose_content('每次5ml，每日3次')
        self.assertEqual(result, '每次5ml，每日3次')

        # 问号数据应被清洗
        result = clean_each_dose_content('???1kcal/ml???')
        self.assertIn('1kcal/ml', result)
        self.assertNotIn('?', result)
```

## 清洗规则详细配置

### 字段映射统计
| 源表 | 目标表 | 映射字段数 | 主要字段映射 |
|------|--------|------------|--------------|
| `spider_fuwu_fixed_hospital` | `medical_designated_providers` | ~25 | `medinsName→name`, `addr→address`, `tel→phone` |
| `spider_fuwu_retail_pharmacy` | `medical_designated_providers` | ~25 | `medinsName→name`, `addr→address`, `tel→phone` |
| `spider_fuwu_service_facilities` | `medical_service_base` | ~15 | `medListCodg→code`, `servitemName→charge_item_name` |
| `spider_fuwu_western_medicine` | `medical_drug_base` | ~30 | `medListCodg→code`, `regName→registered_name` |
| `spider_fuwu_chinese_prescription_medicine` | `medical_drug_base` | ~30 | `medListCodg→code`, `regName→registered_name` |
| `spider_fuwu_tcm_herb` | `medical_chinese_herbal_drug_base` | ~20 | `medListCodg→code`, `admdvs→province_code+province_name` |
| `spider_fuwu_selfprep_medicine` | `medical_self_prepared_drug_base` | 23 | `medListCodg→code`, `drugProdname→name` |
| `spider_fuwu_medical_supplies` | `medical_supplies_base` | 11 | `medListCodg→code`, `mcsName→name`, `hiGenname→general_name` |
| `spider_fuwu_medical_supplies` | `medical_supplies_register_message` | 3 | `medListCodg→code`, `rregisterMessageDTOS→registration_number+single_product_name` |
| `spider_fuwu_western_disease` | `medical_medicine_diagnosis` | ~11 | `diagCode→code`, `diagName→name`, `rid→rid` |
| `spider_fuwu_national_drug_retail_pharmacy` | `medical_national_negotiated_drug_providers` | 12 | `drugOptinsId→rid`, `drugName→drug_name`, `medinsName→name`, `province→province_code+province_name`, `region→city_code+city_name` |

### 特殊处理字段说明

#### 医疗服务项目特殊处理
1. **一对多字段映射**: `medListCodg` → `code` + `charge_item_code`
2. **关联查询**: 使用国家标准数据 (`admdvs=100000`) 进行名称关联
3. **数据过滤**: ETL结果只包含地方数据，排除国家标准数据

#### 西药信息特殊处理
1. **智能清洗**: `eachDos` 字段根据问号密度决定是否清洗
2. **标点标准化**: 企业名称字段的中英文标点统一
3. **占位符清理**: 多个字段的 "--" 值统一处理

#### 医用耗材信息特殊处理
1. **唯一标识清洗**: 基于 `medListCodg` 字段确保数据唯一性
2. **分类字段处理**: `firstMcsType`、`secondMcsType`、`thirdMcsType` 三级分类清洗
3. **名称标准化**: `mcsName`、`hiGenname` 名称字段清理和标准化
4. **规格材质清洗**: `spec`、`mcsMatl` 规格和材质字段处理
5. **企业信息清洗**: `prodentpName` 生产企业名称标准化
6. **去重处理**: 基于 `medListCodg` 字段的智能去重

#### 医用耗材注册信息特殊处理
1. **JSON解析修复**: 处理无效转义字符（如 `\xad\xad`），确保JSON解析成功
2. **一对多映射**: 将 `rregisterMessageDTOS` JSON数组展开为多条注册信息记录
3. **无效记录过滤**: 过滤除了code字段外其他关键字段都为空的无效记录
4. **数据质量保证**: 基于复合唯一索引 `(code, registration_number, single_product_name)` 去重
5. **字段映射**: `medListCodg→code`, `regFilNo→registration_number`, `sinProdName→single_product_name`
6. **错误处理**: 记录JSON解析错误，提供详细的处理统计信息

#### 西医疾病诊断特殊处理
1. **唯一性标识**: 使用 `rid` 字段作为唯一键，确保数据唯一性
2. **分类标识**: 目标表通过 `type_name = '西医疾病诊断'` 进行数据过滤
3. **层级结构**: 支持章、节、类目、亚目、细目的完整层级映射
4. **标准清洗**: 统一使用NULL策略处理空字符串，确保数据一致性
5. **字段映射**: `diagCode→code`, `diagName→name`, `rid→rid` 等11个字段映射

#### 国谈药定点零售药店特殊处理
1. **唯一性标识**: 使用 `drugOptinsId` → `rid` 字段作为唯一键，确保数据唯一性
2. **一对多字段映射**: `province` → `province_code` + `province_name`, `region` → `city_code` + `city_name`
3. **字典关联查询**: 省份和城市字段自动关联字典表获取编码信息，映射成功率通常为100%
4. **经纬度处理**: `lat`、`lnt` 字段保留小数部分，支持地理位置精确定位
5. **数据清洗**: 统一使用NULL策略处理空字符串，确保数据一致性和质量
6. **字段映射**: `drugOptinsId→rid`, `drugName→drug_name`, `medinsName→name`, `province→province_code+province_name`, `region→city_code+city_name`, `addr→address`, `lat→lat`, `lnt→lnt`, `company→production_company_name` 等12个字段映射
7. **数据量处理**: 约31万条记录，支持批处理和增量更新
8. **性能特点**: 支持upsert操作避免重复数据，使用字典缓存机制提高查询效率，自动保存操作记录到Excel文件

### 清洗执行顺序详解

```
数据源 (spider表)
    ↓
1. 表级别清洗 (自定义函数)
   - 执行复杂业务逻辑
   - 跨字段处理
   - 数据过滤和转换
    ↓
2. 字段级别清洗 (通用规则)
   - 地址清洗
   - 电话清洗
   - 全角转半角
   - 机构类型验证
    ↓
3. 通用清洗 (基础规则)
   - 空值处理
   - 字符串规范化
   - 数据类型确保
    ↓
4. 字段映射 (源→目标)
   - 按照medical_field_mapping配置
   - 字段名称映射
   - 默认值设置
    ↓
5. 数据验证和写入
   - 唯一性检查
   - 数据完整性验证
   - 写入目标表
```

#### 执行顺序说明

**第1步: 表级别清洗**
- **执行时机**: 数据转换前的第一步
- **处理范围**: 整个DataFrame
- **主要功能**:
  - 复杂业务逻辑处理
  - 一对多字段映射
  - 数据关联和过滤
  - 创建目标字段

**第2步: 字段级别清洗**
- **执行时机**: 表级别清洗后
- **处理范围**: 单个字段
- **主要功能**:
  - 地址标准化
  - 电话号码格式化
  - 字符编码转换
  - 类型验证

**第3步: 通用清洗**
- **执行时机**: 字段级别清洗后
- **处理范围**: 所有字段
- **主要功能**:
  - 空值统一处理
  - 字符串首尾空格清理
  - 数据类型确保

**第4步: 字段映射**
- **执行时机**: 清洗完成后
- **处理范围**: 源字段到目标字段
- **主要功能**:
  - 字段名称映射
  - 默认值应用
  - 数据类型转换

**第5步: 数据写入**
- **执行时机**: 映射完成后
- **处理范围**: 目标数据
- **主要功能**:
  - 唯一性约束检查
  - 数据完整性验证
  - 批量写入优化

## 故障排除

### 常见问题

#### 1. 清洗函数执行失败
**症状**: ETL过程中清洗函数报错
**排查**:
- 检查源数据是否包含预期字段
- 验证数据类型是否匹配
- 查看详细错误日志

#### 2. 字段映射失败
**症状**: 目标表字段为空或映射错误
**排查**:
- 检查 `medical_field_mapping` 表配置
- 验证源字段名称是否正确
- 确认清洗函数是否正确处理了源字段

#### 3. 关联查询失败
**症状**: 医疗服务项目的 `name` 字段为空
**排查**:
- 确认源数据包含国家标准数据 (`admdvs=100000`)
- 检查 `medListCodg` 字段格式是否正确
- 验证代码分割逻辑

#### 4. 性能问题
**症状**: ETL执行时间过长
**优化**:
- 调整批处理大小
- 优化清洗函数逻辑
- 考虑分批处理大数据集

### 调试方法

#### 启用详细日志
```python
import logging
logging.getLogger('transfrom.tasks.spider.gjyb.pipeline').setLevel(logging.DEBUG)
```

#### 测试单个清洗函数
```python
from transfrom.tasks.spider.gjyb.pipeline.config.data_cleaning_config import gjyb_data_cleaning_config
import pandas as pd

# 创建测试数据
test_df = pd.DataFrame({'eachDos': ['每次5ml，每日3次', '???1kcal/ml???']})

# 测试清洗函数
result = gjyb_data_cleaning_config._clean_fuwu_western_medicine_data(test_df)
print(result)
```

## 版本历史

### v1.0 (初始版本)
- 基础ETL框架
- 医保定点机构和药店数据清洗
- 地址和电话字段清洗

### v1.1 (医疗服务项目)
- 添加医疗服务项目数据清洗
- 实现一对多字段映射
- 国家标准数据关联逻辑

### v1.2 (西药信息)
- 添加西药数据清洗
- 企业名称标点符号标准化
- 占位符清理功能

### v1.3 (智能清洗)
- 用法用量字段智能清洗
- 问号密度检测算法
- 信息提取和格式标准化

### v1.4 (中草药支持)
- 添加中草药数据清洗支持
- 实现一对多字段映射 (admdvs → province_code + province_name)
- 字典关联查询功能

### v1.5 (自制药支持)
- 添加自制药数据清洗支持
- 批量无效值清洗优化
- 时间戳转日期功能
- 直接字段映射机制

### v1.6 (医用耗材支持)
- 添加医用耗材数据清洗支持
- 实现三级分类字段处理
- 唯一标识清洗和去重功能
- 规格材质字段标准化
- 企业信息清洗优化
- 完整的字段映射关系 (11个字段)

### v1.7 (医用耗材注册信息支持)
- 添加医用耗材注册信息数据清洗支持 (`clean_fuwu_medical_supplies_register_data`)
- 实现JSON解析修复，处理无效转义字符（如 `\xad\xad`）
- 实现一对多映射：`rregisterMessageDTOS` JSON数组展开为多条记录
- 添加无效记录过滤：过滤除了code字段外其他关键字段都为空的记录
- 支持复合唯一索引：`(code, registration_number, single_product_name)`
- 完整的字段映射关系：`medListCodg→code`, `regFilNo→registration_number`, `sinProdName→single_product_name`
- 增强错误处理和统计信息输出
- 支持目标表级别的自定义清洗函数配置 (`custom_function_by_target`)

### v1.8 (西医疾病诊断支持)
- 添加西医疾病诊断数据ETL支持 (`spider_fuwu_western_disease → medical_medicine_diagnosis`)
- 使用 `rid` 字段作为唯一键，确保数据唯一性
- 支持完整的疾病层级结构映射：章、节、类目、亚目、细目
- 实现分类过滤：通过 `type_name = '西医疾病诊断'` 区分数据类型
- 完整的字段映射关系：11个字段映射配置
- 统一使用NULL策略进行数据清洗，确保数据一致性

### v1.9 (国谈药定点零售药店支持)
- 添加国谈药定点零售药店数据ETL支持 (`spider_fuwu_national_drug_retail_pharmacy → medical_national_negotiated_drug_providers`)
- 使用 `drugOptinsId` → `rid` 字段作为唯一键，确保数据唯一性
- 实现一对多字段映射：`province` → `province_code` + `province_name`, `region` → `city_code` + `city_name`
- 支持字典关联查询：省份和城市字段自动关联字典表获取编码信息
- 经纬度字段特殊处理：保留小数部分，支持地理位置精确定位
- 完整的字段映射关系：12个字段映射配置
- 统一使用NULL策略进行数据清洗，确保数据一致性

## 注意事项

1. **数据完整性**: 清洗过程优先保护数据完整性
2. **向后兼容**: 修改清洗规则时考虑向后兼容性
3. **测试验证**: 新增或修改规则后进行充分测试
4. **日志监控**: 关注清洗过程中的异常和警告信息
5. **备份策略**: 重要数据清洗前建议备份原始数据
6. **性能考虑**: 大数据集处理时注意内存和时间消耗
7. **字段映射**: 确保 `medical_field_mapping` 表配置完整准确

#### 3. 医用耗材注册信息JSON解析失败
**症状**: 出现 `Invalid \escape` 或 `JSONDecodeError` 错误
**原因**: 源数据包含无效转义字符（如 `\xad\xad`）
**解决方案**:
- 检查 `clean_fuwu_medical_supplies_register_data` 函数是否正确处理转义字符
- 验证正则表达式修复逻辑：`re.sub(r'\\x[0-9a-fA-F]{2}', '', text)`
- 查看具体的错误记录和medListCodg值

#### 4. 医用耗材注册信息无效记录过多
**症状**: 大量记录被过滤，最终输出记录数远少于预期
**原因**: `registration_number` 和 `single_product_name` 都为空
**排查**:
- 检查源数据 `rregisterMessageDTOS` 字段的JSON格式
- 验证 `regFilNo` 和 `sinProdName` 字段是否存在
- 确认过滤逻辑是否过于严格

#### 5. 医用耗材注册信息重复数据
**症状**: 目标表出现重复记录
**解决方案**:
- 确认复合唯一索引已创建：`(code, registration_number, single_product_name)`
- 检查去重逻辑是否正确执行
- 验证ETL操作模式是否为 `upsert`

#### 6. 国谈药定点零售药店字典映射失败
**症状**: `province_name` 或 `city_name` 字段为空
**原因**: 省份或城市字典数据不完整，或源数据格式不匹配
**排查**:
- 检查省份和城市字典表数据是否完整
- 验证源数据中的 `province` 和 `region` 字段格式
- 确认字典关联逻辑是否正确

#### 7. 国谈药定点零售药店唯一性冲突
**症状**: 出现 `rid` 字段重复错误
**原因**: 源数据中 `drugOptinsId` 字段有重复值
**解决方案**:
- 检查源数据中 `drugOptinsId` 字段是否有重复
- 验证目标表中 `rid` 字段的唯一性约束
- 确认ETL操作模式设置正确

#### 8. 国谈药定点零售药店经纬度数据异常
**症状**: 地理位置显示异常或超出合理范围
**排查**:
- 检查源数据中 `lat`、`lnt` 字段的数据格式
- 验证数值范围是否合理（纬度：-90到90，经度：-180到180）
- 确认小数位数保留是否正确
