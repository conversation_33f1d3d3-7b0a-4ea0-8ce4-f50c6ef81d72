import streamlit as st
import streamlit_authenticator as stauth
from utils.st import empty_line
import yaml
from yaml.loader import SafeLoader
import datetime
from pathlib import Path
st.set_page_config(
    page_title="我的南京APP统计报表",
    layout="wide",
    initial_sidebar_state="expanded",
    page_icon="📊"
)


def main():
    path_name = Path.cwd().joinpath('user').joinpath("config.yaml")
    with open(path_name, 'r') as file:
        config = yaml.load(file, Loader=SafeLoader)

    stauth.Hasher.hash_passwords(config['credentials'])
    authenticator = stauth.Authenticate(
        config['credentials'],
        config['cookie']['name'],
        config['cookie']['key'],
        config['cookie']['expiry_days']
    )

    name, authentication_status, username = authenticator.login(fields = {'Form name':'我的南京APP统计报表', 'Username':'账号', 'Password':'密码',
                      'Login':'登录', 'Captcha':'Captcha'},max_login_attempts=10)
    # 检查登录状态
    if authentication_status:
        with st.columns([1,1,1,0.3])[3]:
            authenticator.logout('退出登录', 'main')
        # 导入并运行指定页面
        try:
            from page.pvuv_njapp import main as pvuv_njapp
            pvuv_njapp()
        except Exception as e:
            pass

    elif authentication_status == False:
        st.error('用户名/密码不正确')
    elif authentication_status is None:
        st.warning('请输入您的用户名和密码')

if __name__ == '__main__':
    main()