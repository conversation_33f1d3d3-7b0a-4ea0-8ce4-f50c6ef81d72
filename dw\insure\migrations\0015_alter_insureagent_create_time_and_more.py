# Generated by Django 4.2.1 on 2025-06-11 09:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("insure", "0014_alter_insureagent_create_time_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="insureagent",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureagent",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureagesex",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureagesex",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insurearea",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insurearea",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insuregroup",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insuregroup",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insuremobile",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insuremobile",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureonline",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insureonline",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="create_time",
            field=models.DateTimeField(
                auto_now_add=True,
                db_comment="创建时间",
                help_text="由Django自动设置，INSERT时自动填充当前时间",
                verbose_name="创建时间",
            ),
        ),
        migrations.AlterField(
            model_name="insurepvuv",
            name="update_time",
            field=models.DateTimeField(
                auto_now=True,
                db_comment="更新时间",
                help_text="由Django自动维护，INSERT和UPDATE时自动更新为当前时间",
                verbose_name="更新时间",
            ),
        ),
    ]
