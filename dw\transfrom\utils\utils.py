import json
import requests
import os
import re
import shutil
import datetime
import sys
import numpy as np
import pandas as pd
from public.models import PublicSqlTemplate,PublicIndicatorMain
from django.core.exceptions import ObjectDoesNotExist
from django.conf import settings


def replace_using_dict(text, replacement_pairs):
    """
    用于根据提供的替换字典替换文本中的内容
    :param text: 需要替换的文本
    :param replacement_pairs: 替换字典
    :return:
    """
    for old, new in replacement_pairs.items():
        text = text.replace(old, new)
    return text


def sum_or_combine(serie, name='合计'):
    """
    对数值列求和，对字符串列返回'合计'
    :param serie: series对象
    :param name: 非数值列的显示名称
    :return:
    """
    if pd.api.types.is_numeric_dtype(serie):
        return serie.sum()
    else:
        return name


def sum_or_combine_v1(df, name='合计', drop_columns=None, drop_statistic=None, default_statistic='sum'):
    """
    对DataFrame的数值列求和或计算均值，对字符串列返回'合计'。
    非数值列或指定剔除的列将返回None，除非在drop_statistic中指定了统计方法。

    :param df: DataFrame对象
    :param drop_columns: 需要从统计中剔除的列的列表
    :param drop_statistic: 一个字典，指定需要计算合计或均值的字段，键为列名，值为'sum'或'mean'
    :param default_statistic: 默认的统计方法，当字段没有在drop_statistic中指定时使用，'sum'或'mean'
    :return: 一个字典，包含统计结果
    """
    if drop_columns is None:
        drop_columns = []
    if drop_statistic is None:
        drop_statistic = {}

    result = {}
    for column in df.columns:
        if column in drop_columns:
            # 指定剔除的列返回None
            result[column] = None
        elif pd.api.types.is_numeric_dtype(df[column]):
            # 数值列根据drop_statistic字典或default_statistic参数求和或计算均值
            statistic_method = drop_statistic.get(column, default_statistic)
            if statistic_method == 'sum':
                result[column] = df[column].sum()
            elif statistic_method == 'mean':
                result[column] = df[column].mean()
            else:
                raise ValueError(f"Unsupported statistic method: {statistic_method}")
        else:
            # 非数值列返回'合计'
            result[column] = name

    return result


def join_list_to_string(lst, quote_char="'"):
    """
    将列表转换为字符串，并用指定字符包裹
    :param lst: 需要转化的list
    :param quote_char: 用什么字符包裹
    :return:
    """
    return f"{','.join([quote_char + item + quote_char for item in lst if item != ''])}"


def amount_group(df, amount_col, bins=None, labels=None):
    """
    按金额分组
    :param df:需要处理的dataframe
    :param amount_col:需要进行分组的列名
    :param bins:分组的金额列表
    :param labels:分组的标签列表
    :return:
    """
    if labels is None:
        labels = ['0-5000', '5000-10000', '10000-20000', '20000-50000', '50000-100000', '100000-200000', '200000以上']
    if bins is None:
        bins = [0, 5000, 10000, 20000, 50000, 100000, 200000, float('inf')]
    df['amount_group'] = pd.cut(df[amount_col], bins=bins, labels=labels, right=False)
    return df


def age_group(df, age_col, bins=None, labels=None):
    """
    按年龄分组
    :param df:需要处理的dataframe
    :param age_col:需要进行分组的列名
    :param bins:分组的年龄列表
    :param labels:分组的标签列表
    :return:
    """
    if labels is None:
        labels = ['0-17岁', '18-40岁', '41-65岁', '66岁及以上']
    if bins is None:
        bins = [0, 18, 41, 66, float('inf')]
    df['age_group'] = pd.cut(df[age_col], bins=bins, labels=labels, right=False)
    return df


def move_numbered_json_files(target_directory, source_directory=os.getcwd()):
    """
    将源目录下的纯数字命名的JSON文件移动到目标目录。
    :param target_directory: 目标目录的路径
    :param source_directory: 源目录的路径,默认为根目录
    """
    for filename in os.listdir(source_directory):
        if re.match(r'\d+\.json', filename):
            print(filename)
            shutil.move(os.path.join(source_directory, filename), target_directory)


def match_type(name, rules_df):
    """
    根据名称和规则数据框匹配类型(匹配的文件限制了名称为name，关键字为key_words的规则)
    参数:
    name: 待匹配的名称
    rules_df: 包含匹配规则的数据框
    返回:
    匹配到的类型名称，若未匹配到则返回原名称
    """
    for rule_index, rule_row in rules_df.iterrows():
        keywords = rule_row['key_words'].split('|')  # 将关键字按竖线分隔为列表
        for keyword in keywords:
            pattern = re.compile(keyword)  # 使用正则表达式进行匹配
            if pattern.search(name):  # 若匹配成功则返回规则中的名称
                return rule_row['name']
    return name  # 若未匹配到任何规则，则返回原名称


def simplify_replace(df, column_name, replacements, default_value='其他'):
    """
    简化替换DataFrame中指定列的值，将所有其他值替换为"其他"。

    参数:
    df : pandas.DataFrame
        要处理的DataFrame。
    column_name : str
        要替换值的列名。
    replacements : dict
        一个字典，包含要替换的旧值到新值的映射。
    default_value : str, optional
        未匹配到的默认值。默认值为'其他'。
    返回:
    pandas.DataFrame
        替换后的DataFrame。
    """
    # 将指定的替换应用到列
    df[column_name] = df[column_name].replace(replacements)

    # 将所有剩余的值替换为"其他"
    df[column_name] = np.where(df[column_name].isin(replacements.values()), df[column_name], default_value)


def query_sql(sql_name_en):
    """
    查询SQL语句，并返回结果
    :param sql_name_en: SQL语句英文名称
    :return: 结果
    """
    record = PublicSqlTemplate.objects.get(name_en=sql_name_en)
    sql = record.template
    return sql




def custom_update_or_create(model_class, defaults=None, exclude_fields=None, **kwargs):
    """
    自定义update_or_create行为，如果记录完全一致则不做处理,避免重复更新记录，影响效率。
    添加了exclude_fields参数，允许指定哪些字段不参与值的比较。

    :param model_class: 要操作的模型类。
    :param defaults: 如果不存在匹配项时用于创建新实例的默认值字典。
    :param exclude_fields: 一个列表或元组，包含不应参与值的比较的字段名称。
    :param kwargs: 用于查询和/或更新的键值对。
    :return: 查询到的对象或新创建的对象。
    """
    try:
        # 尝试根据kwargs获取数据库中的对象
        obj = model_class.objects.get(**kwargs)

        # 检查defaults（如果提供）与数据库中对象的值是否一致
        if defaults:
            # 构建一个字典，只包含需要比较的字段
            compare_defaults = {key: value for key, value in defaults.items() if key not in (exclude_fields or [])}

            # 使用类型转换和异常处理来比较值
            if any(
                    try_compare(getattr(obj, key), value)
                    for key, value in compare_defaults.items()
            ):
                # 如果有不一致的地方，则更新对象
                for key, value in defaults.items():
                    setattr(obj, key, value)
                obj.save(update_fields=defaults.keys())
        else:
            # 数据完全一致，直接返回对象不做处理
            pass

    except ObjectDoesNotExist:
        # 如果没有找到匹配的对象，则根据defaults创建新对象
        obj, created = model_class.objects.get_or_create(**kwargs, defaults=defaults)

    return obj


def try_compare(value1, value2):
    """
    尝试比较两个值，如果类型不匹配则尝试转换。
    如果转换失败则返回 False。
    """
    try:
        # 尝试直接比较
        return value1 != value2
    except TypeError:
        # 如果类型不匹配，则尝试转换为字符串再比较
        return str(value1) != str(value2)



def send_feishu_message(message, app_id=settings.FEISHU_APP_ID):
    """
    发送消息到飞书机器人。
    参数:
    message -- 要发送的消息内容，格式为字典，包含消息类型和内容
    """
    # 消息内容编码
    msg = {
        "msg_type": "text",
        "content": {"text": message}
    }
    msg_encode = json.dumps(msg, ensure_ascii=True).encode("utf-8")

    # 请求头设置
    headers = {
        "Content-type": "application/json",
        "charset": "utf-8"
    }

    # 发送POST请求
    response = requests.post(
        url="https://open.feishu.cn/open-apis/bot/v2/hook/%s" % app_id,
        data=msg_encode,
        headers=headers
    )
    return response.status_code



def query_indicator_code(df):
    # 查看列名中是否存在code列，如果不存在则添加
    if 'code' not in df.columns:
        df['code'] = None
    # 获取code
    for i in range(len(df)):
        indic_name = df.loc[i, 'indic_name']
        indicator = PublicIndicatorMain.objects.filter(name=indic_name).first()
        if indicator:
            df.loc[i, 'code'] = indicator.code
        else:
            df.loc[i, 'code'] = None  # 或者设置为其他默认值
            send_feishu_message(f"{indic_name} 指标不存在".format(indic_name=indic_name))



def query_indicator_code_v1(df):
    """
    对于批量用这个函数，避免遍历查询效率慢
    """
    # 查看列名中是否存在code列，如果不存在则添加
    if 'code'  in df.columns:
        df.drop(columns=['code'], inplace=True)
    # 获取code
    # 优化代码：取出其中的所有indic_name的唯一值，组成一个df，在遍历查询，最后与原来的df合并
    df_indicator_name = df['indic_name'].unique()
    df_indicator_name = pd.DataFrame({'indic_name': df_indicator_name})
    df_indicator_name['code'] = None
    for i in range(len(df_indicator_name)):
        indic_name = df_indicator_name.loc[i, 'indic_name']
        indicator = PublicIndicatorMain.objects.filter(name=indic_name).first()
        if indicator:
            df_indicator_name.loc[i, 'code'] = indicator.code
        else:
            df_indicator_name.loc[i, 'code'] = None  # 或者设置为其他默认值
            send_feishu_message(f"{indic_name} 指标不存在".format(indic_name=indic_name))
    df = pd.merge(df, df_indicator_name, on='indic_name', how='left')
    return df

def df_to_dict(df, key_column, value_columns):
    """
    将DataFrame转换为字典

    参数:
    - df: 输入的DataFrame
    - key_column: 用作字典键的列名
    - value_columns: 用作字典值的列名列表

    返回:
    - 一个字典，其中键是key_column指定的列的值，值是由value_columns指定的列组成的字典
    """
    # 创建空字典
    dict_data = {}

    # 迭代DataFrame的每一行
    for _, row in df.iterrows():
        # 获取键
        key = row[key_column]

        # 如果键不在字典中，则添加一个空列表
        if key not in dict_data:
            dict_data[key] = []

        # 添加值
        value = {col: row[col] for col in value_columns}
        dict_data[key].append(value)

    return dict_data


def get_week_start_end(date):
    """
    获取指定日期所在周的周一和周日的日期。

    :param date: 输入的日期 (datetime.date 或 datetime.datetime 对象)
    :return: (周一的日期, 周日的日期) 元组
    """
    # 将输入转换为 datetime.date 对象，如果输入是 datetime.datetime 对象
    if isinstance(date, datetime.datetime):
        date = date.date()
    else:
        date = datetime.datetime.strptime(str(date), '%Y-%m-%d').date()

    # 获取输入日期是周几 (0=Monday, 6=Sunday)
    weekday = date.weekday()

    # 计算本周周一的日期
    start_of_week = date - datetime.timedelta(days=weekday)

    # 计算本周周日的日期
    end_of_week = start_of_week + datetime.timedelta(days=6)

    return start_of_week, end_of_week


def get_day_of_week(date):
    """
    获取指定日期是当周的第几天。

    :param date: 输入的日期 (datetime.date 或 datetime.datetime 对象)
    :return: 当周的第几天 (1=Monday, 7=Sunday)
    """
    # 将输入转换为 datetime.date 对象，如果输入是 datetime.datetime 对象
    if isinstance(date, datetime.datetime):
        date = date.date()
    else:
        date = datetime.datetime.strptime(str(date), '%Y-%m-%d').date()

    # 获取输入日期是周几 (0=Monday, 6=Sunday)
    weekday = date.weekday()

    # 将 0-6 转换为 1-7
    day_of_week = weekday + 1

    return day_of_week


# def get_reversed_week_number(date_str):
#     """
#     根据给定的日期字符串，返回该日期所在的周在本年的倒序周数。
#
#     :param date_str: 日期字符串，格式为 'YYYY-MM-DD'
#     :return: 该日期所在的周在本年的倒序周数
#     """
#     from datetime import datetime, timedelta
#
#     # 将日期字符串转换为 datetime 对象
#     date_obj = datetime.strptime(date_str, '%Y-%m-%d')
#
#     # 获取当前日期所在的年份的第一天（1月1日）
#     first_day_of_year = datetime(year=date_obj.year, month=1, day=1)
#
#     # 调整第一天和给定日期到它们所在的周的周一
#     first_monday = first_day_of_year + timedelta(days=(7 - first_day_of_year.weekday()) % 7)
#     current_monday = date_obj + timedelta(days=(7 - date_obj.weekday()) % 7)
#
#     # 如果当前周一晚于年份的第一个周一，则减去一周以确保我们计算的是完整的周
#     if current_monday > first_monday:
#         current_monday -= timedelta(days=7)
#
#     # 计算从年份的第一个周一到当前周一有多少个完整的周
#     full_weeks_passed = (current_monday - first_monday).days // 7
#
#     # 获取本年的总周数（考虑不完整的最后一周）
#     last_day_of_year = datetime(year=date_obj.year, month=12, day=31)
#     total_weeks_in_year = (last_day_of_year - first_monday).days // 7 + 1
#
#     # 计算倒序周数
#     reversed_week_number = total_weeks_in_year - full_weeks_passed
#
#     return reversed_week_number


def get_reversed_week_number(date_str):
    """
    根据给定的日期字符串，返回该日期所在的周在本年的倒序周数。

    :param date_str: 日期字符串，格式为 'YYYY-MM-DD'
    :return: 该日期所在的周在本年的倒序周数
    """
    from datetime import datetime, timedelta

    # 将日期字符串转换为 datetime 对象
    date_obj = datetime.strptime(date_str, '%Y-%m-%d')

    # 获取当前日期所在的年份的第一天（1月1日）
    first_day_of_year = datetime(year=date_obj.year, month=1, day=1)

    # 调整第一天到它所在的周的周一
    first_monday = first_day_of_year - timedelta(days=first_day_of_year.weekday())

    # 调整给定日期到它所在的周的周一
    current_monday = date_obj - timedelta(days=date_obj.weekday())

    # 计算从年份的第一个周一到当前周一有多少个完整的周
    full_weeks_passed = (current_monday - first_monday).days // 7

    # 获取本年的总周数（考虑不完整的最后一周）
    last_day_of_year = datetime(year=date_obj.year, month=12, day=31)
    last_monday = last_day_of_year - timedelta(days=last_day_of_year.weekday())
    total_weeks_in_year = (last_monday - first_monday).days // 7 + 1

    # 计算倒序周数
    reversed_week_number = total_weeks_in_year - full_weeks_passed

    return reversed_week_number


def clean_orphaned_data(model_class, new_data_df, unique_fields, key_field='id',
                       filter_conditions=None, verbose=True):
    """
    通用的数据清理函数，删除数据库中存在但新数据中不存在的记录
    类似于您原来的sqldf逻辑：
    delete_df = sqldf("select a.id from df_db_person_group a left join df_person_group b on a.key = b.is_personal where b.is_personal is null")

    使用示例：
    # 示例1：基本用法
    deleted_count = clean_orphaned_data(
        model_class=InsurePvUv,
        new_data_df=df_new_data,
        unique_fields=['product_set_code', 'end_date', 'source_group', 'type'],
        verbose=True
    )

    # 示例2：带过滤条件
    deleted_count = clean_orphaned_data(
        model_class=PublicStatistics,
        new_data_df=df_person_group,
        unique_fields=['is_personal'],  # 对应原来的key字段
        filter_conditions={'product_code': 'ABC123'},
        verbose=True
    )

    :param model_class: Django模型类
    :param new_data_df: 新数据的DataFrame
    :param unique_fields: 用于比较的唯一字段列表
    :param key_field: 用于删除的主键字段名，默认为'id'
    :param filter_conditions: 额外的过滤条件，用于限制清理范围
    :param verbose: 是否打印详细信息
    :return: 删除的记录数量
    """
    try:
        # 构建查询条件，获取数据库中的现有数据
        db_queryset = model_class.objects.all()

        # 如果有过滤条件，应用过滤条件
        if filter_conditions:
            db_queryset = db_queryset.filter(**filter_conditions)

        # 获取数据库中的数据，包含唯一字段和主键
        db_fields = unique_fields + [key_field]
        db_data = list(db_queryset.values(*db_fields))

        if not db_data:
            if verbose:
                print("数据库中没有现有数据，无需清理")
            return 0

        # 转换为DataFrame
        df_db = pd.DataFrame(db_data)

        if verbose:
            print(f"数据库中现有记录数: {len(df_db)}")
            print(f"新数据记录数: {len(new_data_df)}")

        # 创建用于比较的复合键
        def create_composite_key(df, fields):
            """创建复合键用于比较"""
            # 确保所有字段都存在
            missing_fields = [field for field in fields if field not in df.columns]
            if missing_fields:
                raise ValueError(f"DataFrame中缺少字段: {missing_fields}")

            if len(fields) == 1:
                return df[fields[0]].astype(str)
            else:
                # 对每个字段进行标准化处理
                standardized_df = df[fields].copy()
                for field in fields:
                    if field == 'end_date':
                        # 标准化日期格式
                        standardized_df[field] = pd.to_datetime(standardized_df[field]).dt.strftime('%Y-%m-%d')
                    else:
                        # 其他字段转为字符串
                        standardized_df[field] = standardized_df[field].astype(str)

                return standardized_df.apply(lambda x: '|'.join(x), axis=1)

        # 为数据库数据和新数据创建复合键
        try:
            db_composite_key = create_composite_key(df_db, unique_fields)
            new_composite_key = create_composite_key(new_data_df, unique_fields)

            if verbose:
                print(f"复合键匹配数量: {db_composite_key.isin(new_composite_key).sum()}")

        except Exception as e:
            print(f"创建复合键时出错: {e}")
            if verbose:
                print("数据库字段:", df_db.columns.tolist())
                print("新数据字段:", new_data_df.columns.tolist())
                print("数据库前3行:", df_db.head(3).to_dict('records'))
                print("新数据前3行:", new_data_df.head(3).to_dict('records'))
            return 0

        # 找出数据库中存在但新数据中不存在的记录
        orphaned_mask = ~db_composite_key.isin(new_composite_key)
        orphaned_records = df_db[orphaned_mask]

        if len(orphaned_records) > 0:
            # 获取需要删除的ID列表
            delete_ids = orphaned_records[key_field].tolist()

            if verbose:
                print(f"发现 {len(delete_ids)} 条多余数据需要删除")
                print(f"删除的记录ID: {delete_ids[:10]}{'...' if len(delete_ids) > 10 else ''}")

            # 执行删除操作
            deleted_count = model_class.objects.filter(**{f"{key_field}__in": delete_ids}).delete()[0]

            if verbose:
                print(f"成功删除 {deleted_count} 条多余数据")

            return deleted_count
        else:
            if verbose:
                print("没有发现多余数据，无需删除")
            return 0

    except Exception as e:
        error_msg = f"清理数据时出错: {e}"
        if verbose:
            print(error_msg)
        return 0


def clean_old_data_by_date(model_class, date_field, cutoff_date,
                          additional_conditions=None, verbose=True):
    """
    根据日期清理旧数据

    使用示例：
    # 示例1：删除30天前的数据
    from datetime import date, timedelta
    cutoff_date = date.today() - timedelta(days=30)
    deleted_count = clean_old_data_by_date(
        model_class=InsurePvUv,
        date_field='end_date',
        cutoff_date=cutoff_date,
        verbose=True
    )

    # 示例2：带额外条件的清理
    deleted_count = clean_old_data_by_date(
        model_class=InsurePvUv,
        date_field='created_date',
        cutoff_date=cutoff_date,
        additional_conditions={'status': 'processed', 'product_set_code': 'old_product'},
        verbose=True
    )

    :param model_class: Django模型类
    :param date_field: 日期字段名
    :param cutoff_date: 截止日期，早于此日期的数据将被删除
    :param additional_conditions: 额外的过滤条件
    :param verbose: 是否打印详细信息
    :return: 删除的记录数量
    """
    try:
        conditions = {f"{date_field}__lt": cutoff_date}

        if additional_conditions:
            conditions.update(additional_conditions)

        # 先查询要删除的记录数量
        delete_count = model_class.objects.filter(**conditions).count()

        if delete_count > 0:
            if verbose:
                print(f"根据日期条件将删除 {delete_count} 条记录")
                print(f"删除条件: {conditions}")

            # 执行删除
            deleted_count = model_class.objects.filter(**conditions).delete()[0]

            if verbose:
                print(f"成功删除 {deleted_count} 条记录")

            return deleted_count
        else:
            if verbose:
                print("没有符合日期条件的记录需要删除")
            return 0

    except Exception as e:
        error_msg = f"根据日期清理数据时出错: {e}"
        if verbose:
            print(error_msg)
        return 0
