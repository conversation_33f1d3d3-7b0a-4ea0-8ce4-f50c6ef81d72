import warnings
import json

import numpy as np
import pandas as pd
from pathlib import Path
import streamlit as st
from streamlit_echarts import st_pyecharts, st_echarts
import plotly.graph_objects as go
from utils.st import text_write, empty_line

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')
CONNECTOR_UMAMI = st.connection('umami', type='sql')

website_id = 'c73e45a2-073b-402c-8e83-4caa1f8a60ca'


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        concat(
		LEFT ( ps.NAME, 5 ),
		'-',
	RIGHT ( ps.NAME,( LENGTH( ps.NAME )- 15 )/ 3 )) version,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        and ps.code not in ('ninghuibaoV1','ninghuibaoV2','ninghuibaoV3','ninghuibaoV4')
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)

    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code

@st.cache_resource(ttl=60 * 60)
def get_person_count(website_id):
    """
    获取访客量
    :param website_id:
    :return:
    """
    sql = """
    SELECT count(DISTINCT session_id ) as person_count
    FROM
    website_event 
    WHERE
        website_id = '{website_id}'
        AND event_type = '1';
    """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['person_count']


@st.cache_resource(ttl=60 * 60)
def get_daily_uv(website_id):
    """
    获取日度访客量
    :param website_id:
    :return:
    """
    sql = """
    SELECT date(created_at) date,
	count(distinct session_id) as person_count
    FROM
    website_event 
    WHERE
        website_id = '{website_id}'
        AND event_type = '1'
    group by date(created_at);
    """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)
    df.rename(columns={'date': '日期', 'person_count': '访客量'}, inplace=True)
    return df


@st.cache_resource(ttl=60 * 60)
def get_view_count(website_id, start_time):
    """
    获取浏览量数据
    :param website_id:
    :return:
    """
    sql = """
    select count(*) view_num from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    and created_at >='{start_time}'
    """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id, start_time=start_time), show_spinner='查询中...',
                               ttl=0)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['view_num']


@st.cache_resource(ttl=60 * 60)
def get_daily_pv(website_id):
    """
    获取日度浏览量数据
    :param website_id:
    :return:
    """
    sql = """
    select date(created_at) date,count(*) view_num from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    group by date(created_at)
    """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)
    df.rename(columns={'date': '日期', 'view_num': '浏览量'}, inplace=True)
    return df


@st.cache_resource(ttl=60 * 60)
def get_visit_count(website_id):
    """
    获取访问次数据
    """
    sql = """
        select count(distinct visit_id) as visit_count from website_event
        where website_id = '{website_id}'
        and event_type = 1;
        """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)
    if df.empty:
        return 0
    else:
        return df.iloc[0]['visit_count']


@st.cache_resource(ttl=60 * 60)
def get_counce_rate(website_id):
    """
    获取跳出率数据
    """
    sql = """
    # 只访问了单个页面，除总访问次数，计算跳出率
    select sum(one_page_count) one_page_count from (
    select visit_id,count(*) one_page_count  from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    group by visit_id
    HAVING count(*) =1) a
    """

    one_page_count = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)
    if one_page_count.empty:
        one_page_count = 0
    else:
        one_page_count = one_page_count.iloc[0]['one_page_count']

    visit_count = get_visit_count(website_id)
    if visit_count == 0:
        return '0%'
    else:
        return str(int(round(one_page_count / visit_count * 100, 0))) + '%'


def convert_seconds_to_hms(seconds):
    """
    将秒数转换为小时、分钟和秒的格式。

    参数:
    seconds (int): 总秒数

    返回:
    tuple: (小时, 分钟, 秒)
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return hours, minutes, seconds


@st.cache_resource(ttl=60 * 60)
def get_visit_duration(website_id):
    """
    获取平均访问时长数据
    """
    sql = """
    select avg(seconds) avg_seconds from 
    (select visit_id,TIMESTAMPDIFF(SECOND,min(created_at),max(created_at)) seconds  from website_event
    where website_id = '{website_id}'
    and event_type = '1'
    group by visit_id) a
            """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)
    if df.empty:
        avg_seconds = 0
    else:
        avg_seconds = df.iloc[0]['avg_seconds']

    hours, minutes, seconds = convert_seconds_to_hms(avg_seconds)
    if hours > 0:
        return f"{hours}小时{minutes}钟{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分{seconds}秒"
    else:
        return f"{seconds}秒"


@st.cache_resource(ttl=60 * 60)
def get_beacon_data(website_id):
    """
    获取埋点数据
    """
    sql = """
    select event_name,count(*) num from website_event
    where website_id = '{website_id}'
    and event_type = '2'
    and created_at >='2024-09-14 15:00:00'
    group by event_name
    """
    df = CONNECTOR_UMAMI.query(sql.format(website_id=website_id), show_spinner='查询中...', ttl=0)

    sql_mapping = """
    select `key` as name,label as event_name from system_dict_value
    where dict_id = '14'
    and status = '1'
    """
    df_mapping = CONNECTOR_DW.query(sql_mapping, show_spinner='查询中...', ttl=0)

    df = pd.merge(df, df_mapping, on='event_name', how='left')
    df.rename(columns={'name': '事件名称', 'num': '事件数量'}, inplace=True)
    print(df)
    # 浏览量
    view_count = int(get_view_count(website_id, '2024-09-14 15:00:00'))
    vist_count = int(df[df['事件名称'] == '进入产品详情页']['事件数量'].sum())
    add_person_count = int(df[df['事件名称'] == '添加参保人信息']['事件数量'].sum())
    pay_count = int(df[(df['事件名称'] == '医保个账支付') | (df['事件名称'] == '自费支付')]['事件数量'].sum())
    pay_success_count = int(df[df['事件名称'] == '进入支付成功页']['事件数量'].sum())
    beacon_list = [view_count, vist_count, add_person_count, pay_count, pay_success_count]
    beacon_ratio_list = [100, round(vist_count / view_count * 100, 2), round(add_person_count / view_count * 100, 2),
                         round(pay_count / view_count * 100, 2), round(pay_success_count / view_count * 100, 2)]
    return beacon_list, beacon_ratio_list



def format_duration_from_seconds(total_seconds):
    """
    将秒数转换为可读的时长格式

    :param total_seconds: 总秒数
    :return: 格式化的时长字符串
    """
    if total_seconds is None or total_seconds < 0:
        return "0秒"

    hours = int(total_seconds // 3600)
    minutes = int((total_seconds % 3600) // 60)
    seconds = int(total_seconds % 60)

    if hours > 0:
        return f"{hours}小时{minutes}分{seconds}秒"
    elif minutes > 0:
        return f"{minutes}分{seconds}秒"
    else:
        return f"{seconds}秒"

def app_pv_uv(product_set_code):
    """
    显示PV/UV数据和图表

    Args:
        product_set_code: 产品编码

    Returns:
        dict: 包含埋点数据的字典，用于漏斗图显示
    """
    # person_count = get_person_count(website_id)
    # view_count = get_view_count(website_id, '2000-01-01 00:00:00')
    # visit_count = get_visit_count(website_id)
    # visit_duration = get_visit_duration(website_id)
    # counce_rate = get_counce_rate(website_id)
    # print(person_count, view_count, visit_count, visit_duration, counce_rate)
    sql = f"""
        select * from insure_website_statistics
        where product_set_code = '{product_set_code}'
        order by statistics_date desc
        limit 1
        """
    beacon_data = CONNECTOR_DW.query(sql.format(product_set_code=product_set_code), show_spinner='查询中...', ttl=60)
    if beacon_data.empty:
        beacon_data = pd.DataFrame({'total_page_views': [0],'total_visits': [0],'total_visitors': [0],'bounce_rate': [0],'avg_visit_duration': [0]})
    text_write("APP访问概览")
    empty_line(1)
    cols = st.columns(5)
    with cols[0]:
        st.metric("浏览量", beacon_data['total_page_views'])
    with cols[1]:
        st.metric("访问次数", beacon_data['total_visits'])
    with cols[2]:
        st.metric("访客数", beacon_data['total_visitors'])
    with cols[3]:
        bounce_rate = str(int(beacon_data['bounce_rate']*100))+'%'
        st.metric("跳出率", bounce_rate)
    with cols[4]:
        avg_visit_duration = format_duration_from_seconds(beacon_data['avg_visit_duration'].values[0])
        st.metric("平均访问时间", avg_visit_duration)

    # df_daily_pv = get_daily_pv(website_id)
    # df_daily_uv = get_daily_uv(website_id)
    #
    # # 转换为 DataFrame
    # sub_data_df = pd.merge(df_daily_pv, df_daily_uv, on='日期', how='outer')
    # sub_data_df = sub_data_df.fillna(0)
    # sub_data_df.sort_values(by='日期', ascending=True, inplace=True)
    # sub_data_df.reset_index(drop=True, inplace=True)
    sql = f"""
    select end_date as 日期,type,count from insure_pvuv
    where product_set_code = '{product_set_code}'
    and source_group = 'total'
    order by end_date,type
    """
    df = CONNECTOR_DW.query(sql, show_spinner='查询中...', ttl=60)

    # 根据type进行拆分，将值为pv的设为列浏览量，uv设为列访客量
    if not df.empty:
        # 使用pivot将type列转换为列名
        sub_data_df = df.pivot(index='日期', columns='type', values='count').reset_index()

        # 重命名列
        if 'pv' in sub_data_df.columns:
            sub_data_df.rename(columns={'pv': '浏览量'}, inplace=True)
        if 'uv' in sub_data_df.columns:
            sub_data_df.rename(columns={'uv': '访客量'}, inplace=True)

        # 填充缺失值为0
        sub_data_df = sub_data_df.fillna(0)

        # 确保数据类型为整数
        if '浏览量' in sub_data_df.columns:
            sub_data_df['浏览量'] = sub_data_df['浏览量'].astype(int)
        if '访客量' in sub_data_df.columns:
            sub_data_df['访客量'] = sub_data_df['访客量'].astype(int)

        # 按日期排序
        sub_data_df.sort_values(by='日期', ascending=True, inplace=True)
        sub_data_df.reset_index(drop=True, inplace=True)
    else:
        # 如果没有数据，创建空的DataFrame
        sub_data_df = pd.DataFrame(columns=['日期', '浏览量', '访客量'])
    # 绘制堆叠条形图
    fig = go.Figure()

    # 检查数据是否存在
    if not sub_data_df.empty and len(sub_data_df) > 0:
        # 添加访客量系列
        if '访客量' in sub_data_df.columns:
            fig.add_trace(go.Bar(
                x=sub_data_df["日期"],
                y=sub_data_df["访客量"],
                name="访客量",
                text=sub_data_df["访客量"],
                textposition='outside',
                texttemplate='%{text:.2s}',
                showlegend=True,
                hovertemplate='日期: %{x}<br>访客量: %{y:.0f}'
            ))

        # 添加浏览量系列
        if '浏览量' in sub_data_df.columns:
            fig.add_trace(go.Bar(
                x=sub_data_df["日期"],
                y=sub_data_df["浏览量"],
                name="浏览量",
                text=sub_data_df["浏览量"],
                textposition='outside',
                texttemplate='%{text:.2s}',
                showlegend=True,
                hovertemplate='日期: %{x}<br>浏览量: %{y:.0f}'
            ))
    else:
        # 如果没有数据，显示空图表
        fig.add_trace(go.Bar(
            x=[],
            y=[],
            name="暂无数据"
        ))
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    # 设置堆叠模式
    fig.update_layout(
        barmode='stack',
        title='',
        xaxis_title='',
        yaxis_title='',
        xaxis_rangeslider_visible=True,  # 添加 X 轴的缩略坐标
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=0.5
        ),
        showlegend=True,
        height=600
    )
    fig.update_xaxes(tickformat="%m-%d")
    # 取消值的显示
    fig.update_traces(text='', textposition='none')

    # 显示图表
    st.plotly_chart(fig)


def app_funnel(product_set_code):
    import pyecharts.options as opts
    from pyecharts.charts import Funnel
    # y_data, beacon_ratio_list = get_beacon_data(website_id)
    # 获取埋点数据用于漏斗图
    sql_beacon = f"""
    select step_name,conversion_rate*100 as conversion_rate from insure_beacon
    where product_set_code = '{product_set_code}'
    order by statistics_date desc, step_order
    limit 5
    """
    beacon_df = CONNECTOR_DW.query(sql_beacon.format(product_set_code=product_set_code), show_spinner='查询埋点数据...', ttl=60)

    if beacon_df.empty:
        # 提取转化率数据
        beacon_df = pd.DataFrame(columns=['step_name', 'conversion_rate'])
        beacon_df['step_name'] = ['点击', '访问', '点击参保', '生成订单', '支付成功']
        beacon_df['conversion_rate'] = [0, 0, 0, 0, 0]


    beacon_ratio_list = beacon_df['conversion_rate'].tolist()

    text_write("用户转换情况")
    x_data = beacon_df['step_name'].tolist()

    funnel = Funnel()
    funnel.add("行为", [list(z) for z in zip(x_data, beacon_ratio_list)],
               tooltip_opts=opts.TooltipOpts(trigger="item", formatter="{b} : {c}%"), )
    # funnel.add("行为", [list(z) for z in zip(Faker.choose(), Faker.values())])
    st_pyecharts(funnel, height="500px")


def main():
    # 取数据改成从文件读取，提高效率
    st.subheader("埋点分析")
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=
    product_info[product_info['product_set_code'] != 'ninghuibaoV4']['product_set_name'],
                                                  placeholder="请选择产品")

    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[
        0]
    st.divider()
    # 显示PV/UV数据并获取埋点数据
    app_pv_uv(product_set_code)

    # 显示漏斗图
    app_funnel(product_set_code)


if __name__ == '__main__':
    main()
