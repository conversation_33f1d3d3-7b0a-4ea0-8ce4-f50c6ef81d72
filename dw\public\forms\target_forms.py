from django import forms
from public.models import PublicTarget, SystemDictValue
from django.forms.models import ModelChoiceField


class TargetChoiceField(ModelChoiceField):
    # 重写label_from_instance方法，使得下拉框显示中文
    def label_from_instance(self, obj):
        return obj.key


class TargetForm(forms.ModelForm):
    class Meta:
        model = PublicTarget
        fields = '__all__'

    # 字段与需要替换的字段一致
    type = TargetChoiceField(
        queryset=SystemDictValue.objects.filter(dict_id=7),
        to_field_name='label',
        label='分类',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
