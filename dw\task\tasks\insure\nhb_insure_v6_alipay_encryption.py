import hashlib
import multiprocessing

import redis
from concurrent.futures import ThreadPoolExecutor
from dw import settings
import pymysql
import pandas as pd
import idna
import datetime
import warnings
from transfrom.utils.utils import send_feishu_message

warnings.filterwarnings('ignore')

DB = settings.DATABASES['jkx']
DB_DW = settings.DATABASES['default']


def get_connection(DB):
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn


def get_total_credential_number():
    """
    获取数据库中手机号的总数
    :return: 身份证状态
    """
    # 其他渠道的手机号
    sql = """
    select distinct credential_number, mobile other_mobile
     from insure_mobile where product_short_code = 'ninghuibao'
      and length(credential_number) = 18
    """
    df_other = pd.read_sql(sql, get_connection(DB_DW))

    # 宁惠保手机号，是否已经购买
    sql_buy = """
    SELECT
        u2.credential_number,u2.mobile,0 not_buy
    FROM
        `order` o
        JOIN order_item oi ON oi.order_id = o.id
        JOIN order_item_client oic ON oic.order_item_id = oi.id 
        AND oic.is_return = 0
        JOIN product_set ps ON ps.id = o.product_set_id 
        AND ps.CODE = 'ninghuibaoV6'
        JOIN product p ON oi.product_id = p.id  and p.main = 1
        JOIN `user` u2 ON oic.client_id = u2.id
    WHERE
        o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
        AND o.delete_time IS NULL
    """
    df_buy = pd.read_sql(sql_buy, get_connection(DB))
    # 读取user表的全量数据
    sql1 = """
    SELECT
    distinct u.credential_number, u.mobile
    FROM
    	`order` o
    	JOIN order_item oi ON oi.order_id = o.id
    	JOIN order_item_client oic ON oic.order_item_id = oi.id 
    	AND oic.is_return = 0
    	JOIN product_set ps ON ps.id = o.product_set_id 
    	AND ps.CODE in ('ninghuibaoV5','ninghuibaoV4','ninghuibaoV3','ninghuibaoV2','ninghuibaoV1')
    	JOIN product p ON oi.product_id = p.id and p.main=1
    	join `user` u on u.id = oic.client_id
    WHERE
    	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
    and o.delete_time is null
    """
    df_nhb = pd.read_sql(sql1, get_connection(DB))


    # 所有已经发送过短信的手机号
    sql_sms = """
    select distinct phone mobile,1 is_sms from jkx_tele_sales.tele_sales_template tst join jkx_tele_sales.tele_sales_task t 
    on tst.id = t.template_id
    join jkx_tele_sales.tele_sales_item tsi on t.id = tsi.task_id
    where t.delete_time is  null 
    and tst.business_type in ('SMS')
    AND tst.product_set_code = 'ninghuibaoV6'
    and tsi.back_status = 'SUCCESS'
    """
    df_sms = pd.read_sql(sql_sms, get_connection(DB))
    # 合并数据，查看哪些用户已经购买，哪些已经发送过短信
    df_nhb = pd.merge(df_nhb, df_other, on=['credential_number'], how='outer')
    # 如果mobile为空，则取other_mobile
    df_nhb.loc[df_nhb['mobile'].isnull(), 'mobile'] = df_nhb['other_mobile']
    # 去掉other_mobile列
    df_nhb.drop(['other_mobile'], axis=1, inplace=True)

    df_all = pd.merge(df_nhb, df_buy, on=['credential_number', 'mobile'], how='left')
    df_all = pd.merge(df_all, df_sms, on=['mobile'], how='left')
    # 赋值默认 2 未投保 未发短信
    df_all['status'] = 2
    # 未投保 已发短信
    df_all.loc[df_all['is_sms'].notnull(), 'status'] = 1
    # 已购买，最后兜底
    df_all.loc[df_all['not_buy'].notnull(), 'status'] = 0
    df_all = df_all[['credential_number', 'status']]
    df_all['credential_number'] = df_all['credential_number'].apply(get_sha256_string_with_salt)
    df_all.reset_index(drop=True, inplace=True)
    return df_all


def get_sha256_string(message):
    """
    计算SHA256哈希值
    :param message: 需要哈希的消息
    :return: SHA256哈希值
    """
    sha256 = hashlib.sha256()
    sha256.update(message.encode('utf-8'))
    return sha256.hexdigest()

def get_sha256_string_with_salt(message, salt='36a3cb766684fbdf1ef9aff3ec653dc9'):
    """
    计算带有盐值的SHA256哈希值
    :param message: 需要哈希的消息
    :param salt: 盐值
    :return: 带有盐值的SHA256哈希值
    """
    sha256_message = get_sha256_string(message)
    salted_message = sha256_message + salt
    salted_sha256 = get_sha256_string(salted_message)
    return salted_sha256

def connect_redis(password):
    """
    连接到Redis服务器
    :param password: Redis密码
    :return: Redis连接对象
    """
    return redis.StrictRedis(host='127.0.0.1', port=6378, db=0, password=password, decode_responses=True,
                             socket_timeout=3600,  # 操作超时时间，单位为秒
                             socket_connect_timeout=3600  # 连接超时时间，单位为秒
                             )



def alipay_main():
    """
    主函数，用于处理数据并查询缓存
    """
    df = get_total_credential_number()
    batch_size = 1000  # 每批处理100条数据
    password = "Abc12356"  # Redis 密码

    # 将 DataFrame 转换为字典
    result_dict = df.set_index('credential_number')['status'].to_dict()
    # result_dict 拆分分根据batch_size分批处理,但是最后的结果还是字典
    items = list(result_dict.items())
    batch = [dict(items[i:i + batch_size]) for i in range(0, len(items), batch_size)]
    r = connect_redis(password)
    for i in batch:
        r.hmset('alipay_encryption', i)
        r.expire('alipay_encryption', 7 * 24 * 60 * 60)
    # send_feishu_message("宁惠保V5支付宝加密任务执行完成", )



# if __name__ == "__main__":
#     start_time = datetime.datetime.now()
#     # alipay_main()
#     # # 测试根据身份证号码获取数据
#     credential_number = get_sha256_string_with_salt("650121199111040849","36a3cb766684fbdf1ef9aff3ec653dc9")  # 示例身份证号码
#     password = "Abc12356"  # Redis 密码
#     r = connect_redis(password)
#     print(r.hget('alipay_encryption', credential_number))
#     # r.flushdb()
#     print(r.dbsize())
#     # result = query_cache(credential_number, r)
#     # print(result)
#     # print(f"Result for hash credential number {credential_number}: {result}")
#     end_time = datetime.datetime.now()
#     print(f"Time used: {end_time - start_time}")
