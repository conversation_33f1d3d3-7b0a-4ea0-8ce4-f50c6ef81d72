import datetime
from pprint import pprint

import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
from pandasql import sqldf
from plotly.subplots import make_subplots
from utils.st import query_sql, text_write, empty_line, sub_text_write
from utils.utils import number_to_chinese, simplify_replace
from pyecharts import options as opts
from pyecharts.charts import Sankey
from streamlit_echarts import st_pyecharts
import warnings
import plotly.figure_factory as ff
import matplotlib.pyplot as plt

import sys
import venn

warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.2f' % x)
pd.set_option('display.max_rows', None)

CONNECTOR_JKX = st.connection('jkx', type='sql')
CONNECTOR_DW = st.connection('dw', type='sql')
# sales_start_date = '2024-09-12'


def get_product_code():
    """
    获取产品集代码、名称
    :return:
    """
    SQL_PRODUCT_CODE = '''
    SELECT
    distinct 
        ps.NAME product_set_name,
        ps.CODE product_set_code,
        ps.prev_code prev_product_set_code,
        ifnull(p.pre_sale_from,sale_from)  sale_start_time,
        ifnull(p.delay_sale_until,p.sale_until) sale_end_time
    FROM
        product_set ps
        JOIN product p ON p.product_set_id = ps.id and p.main=1
    WHERE
        ps.code like 'ninghuibao%'
        and ps.code !='ninghuibaoV1'
    ORDER BY
        ps.CODE DESC
        '''
    df_product_code = CONNECTOR_JKX.query(SQL_PRODUCT_CODE, show_spinner='查询中...', ttl=600)
    df_product_code['sale_start_time'] = df_product_code['sale_start_time'].astype(str)
    df_product_code['sale_start_time'] = np.where(
        df_product_code['product_set_code'] == 'ninghuibaoV4',
        '2023-09-18 00:00:00',
        df_product_code['sale_start_time']
    )
    df_product_code['sale_start_time'] = pd.to_datetime(df_product_code['sale_start_time'])
    return df_product_code


def get_daily_seller_forward(product_set_code, sale_start_datetime):
    """
    获取保司物料的传播情况，包括阅读和转发
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售开始时间
    :return:
    """
    sql = """
    SELECT
    date( m.create_time ) create_time,
        s.short_name AS 保司,
        m.type AS 动作,
        count(*) AS 数量 
    FROM
        material_operating_trajectory m
        join material mt on m.material_id = mt.id
        JOIN employee e ON m.agent_id = e.id
        JOIN seller s ON e.organization_id = s.id
    WHERE
         mt.product_set = '{product_set_code}' 
         and m.create_time >= '{sale_start_datetime}'
    GROUP BY
    date( m.create_time ),
        m.type,
        s.short_name
    order by date( m.create_time)
        """
    df = CONNECTOR_JKX.query(sql.format(product_set_code=product_set_code, sale_start_datetime=sale_start_datetime),ttl=600)
    if df.empty:
        return pd.DataFrame(columns=['create_time', '保司', '动作', '数量']), pd.DataFrame(columns=['create_time', '保司', '转发', '阅读', '合计'])
    simplify_replace(df, '动作', {'FORWARD': '转发', 'PLAY': '阅读'})
    pivot_df = df.pivot_table(index=['create_time', '保司'], columns='动作', values='数量', aggfunc=sum).reset_index()
    # 将空值替换为 0
    pivot_df['转发'] = pivot_df['转发'].fillna(0)
    pivot_df['阅读'] = pivot_df['阅读'].fillna(0)
    pivot_df['合计'] = pivot_df['转发'] + pivot_df['阅读']
    return df, pivot_df


def get_compare_data(product_set_code, sale_start_datetime,sale_end_datetime, product_set_code_prev, sale_start_datetime_prev):
    """
    获取两期产品的传播情况对比，数据需要对齐
    :param product_set_code:产品集编码
    :param sale_start_datetime:销售起始时间 零点开始
    :param product_set_code_prev:前期产品集编码
    :param sale_start_datetime_prev:前期产品销售起始时间 零点开始
    :return:
    """
    sales_start_date = datetime.datetime.strftime(pd.to_datetime(sale_start_datetime), '%Y-%m-%d')

    end_date = min(datetime.datetime.now().date(), datetime.datetime.strptime(sale_end_datetime, '%Y-%m-%d %H:%M:%S').date())
    df_current, df_current_pivot = get_daily_seller_forward(product_set_code, sale_start_datetime)
    df_prev, df_prev_pivot = get_daily_seller_forward(product_set_code_prev, sale_start_datetime_prev)
    # 两期数据都添加一列顺序，以便后面对齐，拿数据中的最小日期作为基准，其他日期减去该日期
    df_current_pivot['date'] = pd.to_datetime(df_current_pivot['create_time'])
    df_prev_pivot['date'] = pd.to_datetime(df_prev_pivot['create_time'])
    # 历史素材还有转发等数据，所以要有截止日，这里为本期的开始日期
    df_prev_pivot = df_prev_pivot[df_prev_pivot['create_time'] < datetime.datetime.strptime(sales_start_date, '%Y-%m-%d').date()]
    # df_current_pivot['date_order'] = (df_current_pivot['date'] - df_current_pivot['date'].min()).dt.days
    # df_prev_pivot['date_order'] = (df_prev_pivot['date'] - df_prev_pivot['date'].min()).dt.days
    df_current_pivot['day'] = df_current_pivot['date'].dt.strftime('%m-%d')
    df_prev_pivot['day'] = df_prev_pivot['date'].dt.strftime('%m-%d')
    # 合并数据
    df_merge = pd.merge(df_current_pivot, df_prev_pivot, on=['day', '保司'],how='left', suffixes=('_本期', '_上期'))
    # print(df_merge)
    # 获取完整的日期范围，补充缺失日期sales_start_date
    full_date_range = pd.date_range(start=sales_start_date, end=end_date, freq='D')
    df_combinations = pd.MultiIndex.from_product(
        [full_date_range,
         ['中国人保', '国寿财险', '中国人寿', '利安人寿', '中华联合', '中银保险', '紫金保险', '阳光财险', '太保产险']],
        names=['create_time_本期', '保司'])
    df_full_combinations = pd.DataFrame(index=df_combinations).reset_index()
    df_full_combinations['create_time_本期'] = df_full_combinations['create_time_本期'].dt.date
    # print(df_full_combinations)
    df_merge = pd.merge(df_full_combinations, df_merge, on=['create_time_本期', '保司'], how='left')
    df_merge['转发_本期'].fillna(0, inplace=True)
    df_merge['转发_上期'].fillna(0, inplace=True)
    df_merge['阅读_本期'].fillna(0, inplace=True)
    df_merge['阅读_上期'].fillna(0, inplace=True)
    df_merge['合计_本期'].fillna(0, inplace=True)
    df_merge['合计_上期'].fillna(0, inplace=True)
    df_merge.rename(columns={'create_time_本期': '日期'}, inplace=True)
    df_merge = df_merge[['日期', '保司', '阅读_本期', '阅读_上期', '转发_本期', '转发_上期', '合计_本期', '合计_上期']]
    return df_merge


def main():
    st.subheader('保司素材分享')
    product_info = get_product_code()
    product_set_name = st.columns(3)[0].selectbox("请选择产品", options=product_info['product_set_name'],
                                                  placeholder="请选择产品")
    product_set_code = product_info[product_info['product_set_name'] == product_set_name]['product_set_code'].values[0]
    prev_product_set_code = product_info[product_info['product_set_name'] == product_set_name]['prev_product_set_code'].values[
        0]
    sales_start_datetime = product_info[product_info['product_set_name'] == product_set_name]['sale_start_time'].values[
        0]
    sales_start_date = datetime.datetime.strftime(pd.to_datetime(sales_start_datetime), '%Y-%m-%d')
    sales_start_datetime = datetime.datetime.strftime(pd.to_datetime(sales_start_datetime), '%Y-%m-%d %H:%M:%S')
    sale_end_date = datetime.datetime.strftime(
        pd.to_datetime(product_info[product_info['product_set_name'] == product_set_name]['sale_end_time'].values[0]),
        '%Y-%m-%d')
    sale_end_datetime = datetime.datetime.strftime(
        pd.to_datetime(product_info[product_info['product_set_name'] == product_set_name]['sale_end_time'].values[0]),
        '%Y-%m-%d %H:%M:%S')
    sale_start_datetime_prev = product_info[product_info['product_set_code'] == prev_product_set_code]['sale_start_time'].values[
        0]
    sale_start_datetime_prev = datetime.datetime.strftime(
        pd.to_datetime(sale_start_datetime_prev), '%Y-%m-%d %H:%M:%S')


    st.divider()
    df_compare_data = get_compare_data(product_set_code, sales_start_datetime,sale_end_datetime, prev_product_set_code, sale_start_datetime_prev)

    text_write('素材分享总览')
    empty_line(1)
    sub_text_write('保司素材分享情况')
    empty_line(1)
    df_compare_group = df_compare_data.groupby(['保司']).agg(
        {'阅读_本期': sum, '阅读_上期': sum, '转发_本期': sum, '转发_上期': sum, '合计_本期': sum,
         '合计_上期': sum}).reset_index()
    st.dataframe(df_compare_group, hide_index=True, use_container_width=True)
    sub_text_write('保司日度素材分享情况')
    empty_line(1)
    st.dataframe(df_compare_data, hide_index=True, use_container_width=True)

    text_write('素材分享趋势')
    empty_line(1)
    sub_text_write('保司素材分享趋势')
    # 绘制折线图，根据日期、产品名称、单量绘制
    fig = px.bar(df_compare_data, x='日期', y='合计_本期', color='保司', barmode='stack', height=500)
    # 更新文本位置和样式
    fig.update_traces(
    )
    # 隐藏x轴和y轴的名称
    fig.update_xaxes(title_text='')  # 隐藏x轴名称
    fig.update_yaxes(title_text='')  # 隐藏y轴名称
    fig.update_xaxes(tickformat="%Y-%m-%d")
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    st.plotly_chart(fig, use_container_width=True)

    sub_text_write('保司素材阅读趋势')
    # 绘制折线图，根据日期、产品名称、单量绘制
    fig = px.bar(df_compare_data, x='日期', y='阅读_本期', color='保司', barmode='stack', height=500)
    # 更新文本位置和样式
    fig.update_traces(
    )
    # 隐藏x轴和y轴的名称
    fig.update_xaxes(title_text='')  # 隐藏x轴名称
    fig.update_yaxes(title_text='')  # 隐藏y轴名称
    fig.update_xaxes(tickformat="%Y-%m-%d")
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    st.plotly_chart(fig, use_container_width=True)

    sub_text_write('保司素材转发趋势')
    # 绘制折线图，根据日期、产品名称、单量绘制
    fig = px.bar(df_compare_data, x='日期', y='转发_本期', color='保司', barmode='stack', height=500)
    # 更新文本位置和样式
    fig.update_traces(
    )
    # 隐藏x轴和y轴的名称
    fig.update_xaxes(title_text='')  # 隐藏x轴名称
    fig.update_yaxes(title_text='')  # 隐藏y轴名称
    fig.update_xaxes(tickformat="%Y-%m-%d")
    # 更新 y 轴格式为整数形式
    fig.update_yaxes(tickformat=".0f")
    st.plotly_chart(fig, use_container_width=True)

    # # 占比趋势，看今年与去年的表现情况
    # df_compare_ratio = df_compare_data.copy()
    # df_compare_ratio['阅读同比'] = df_compare_ratio.apply(
    #     lambda x: (x['阅读_本期'] - x['阅读_上期']) / x['阅读_上期'] * 100 if x['阅读_上期'] != 0 else 100 if x[
    #                                                                                                               '阅读_本期'] > 0 else 0,
    #     axis=1)
    # df_compare_ratio['转发同比'] = df_compare_ratio.apply(
    #     lambda x: (x['转发_本期'] - x['转发_上期']) / x['转发_上期'] * 100 if x['转发_上期'] != 0 else 100 if x[
    #                                                                                                               '转发_本期'] > 0 else 0,
    #     axis=1)
    # df_compare_ratio['合计同比'] = df_compare_ratio.apply(
    #     lambda x: (x['合计_本期'] - x['合计_上期']) / x['合计_上期'] * 100 if x['合计_上期'] != 0 else 100 if x[
    #                                                                                                               '合计_本期'] > 0 else 0,
    #     axis=1)
    # text_write('素材分享往期对比')
    # empty_line(1)
    # sub_text_write('保司素材分享往期同比')
    # # 绘制折线图，根据日期、产品名称、单量绘制
    # fig = px.line(df_compare_ratio, x='日期', y='合计同比', color='保司', height=500)
    # # 更新文本位置和样式
    # fig.update_traces(
    #     cliponaxis=False
    # )
    # # 隐藏x轴和y轴的名称
    # fig.update_xaxes(title_text='')  # 隐藏x轴名称
    # fig.update_yaxes(title_text='')  # 隐藏y轴名称
    # fig.update_xaxes(tickformat="%Y-%m-%d")
    # st.plotly_chart(fig, use_container_width=True)
    #
    # sub_text_write('保司素材阅读往期同比')
    # # 绘制折线图，根据日期、产品名称、单量绘制
    # fig = px.line(df_compare_ratio, x='日期', y='阅读同比', color='保司', height=500)
    # # 更新文本位置和样式
    # fig.update_traces(
    #     cliponaxis=False
    # )
    # # 隐藏x轴和y轴的名称
    # fig.update_xaxes(title_text='')  # 隐藏x轴名称
    # fig.update_yaxes(title_text='')  # 隐藏y轴名称
    # fig.update_xaxes(tickformat="%Y-%m-%d")
    # st.plotly_chart(fig, use_container_width=True)
    #
    # sub_text_write('保司素材转发往期同比')
    # # 绘制折线图，根据日期、产品名称、单量绘制
    # fig = px.line(df_compare_ratio, x='日期', y='转发同比', color='保司', height=500)
    # # 更新文本位置和样式
    # fig.update_traces(
    #     cliponaxis=False
    # )
    # # 隐藏x轴和y轴的名称
    # fig.update_xaxes(title_text='')  # 隐藏x轴名称
    # fig.update_yaxes(title_text='')  # 隐藏y轴名称
    # fig.update_xaxes(tickformat="%Y-%m-%d")
    # st.plotly_chart(fig, use_container_width=True)


if __name__ == '__main__':
    main()
