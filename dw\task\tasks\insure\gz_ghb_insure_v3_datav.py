import sys
import os

current_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
)
sys.path.append(current_dir)
import datetime
import logging
import re
import warnings
from pprint import pprint

import idna
import numpy as np
import pandas as pd
import pymysql

from dw import settings
from insure.models import InsureArea, InsureAgeSex, InsureOnline, InsureAgent
from public.models import PublicStatistics, PublicAreaBaseInsure, PublicMapping
from transfrom.utils.utils import query_sql
from task.utils.cache_manager import CacheManager

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)


class DataVGhbInsureV3(CacheManager):
    def __init__(self):
        super().__init__()
        self.DB = settings.DATABASES['default']  # dw数据数据库
        self.product_set_code = 'guihuibaoV3'  # 产品集编码
        self.previous_product_set_code = 'guihuibaoV2'  # 上一期的产品集编码
        self.version = '贵州惠民保-三期'  # 产品期，用于指标名称标准化
        self.title_name = '贵惠保三期'
        self.type = 'insure'  # 统计大类
        self.sale_start_date = '2024-10-31'
        self.sale_start_time = '2024-10-31 16:00:00'
        self.sale_start_time_zero = '2024-10-31 00:00:00'  # 取当日，需要从0点开始
        #去年的销售开始时间
        self.sale_start_time_prev = '2023-12-27 00:00:00'  # 用0点时间

        self.end_datetime = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        # self.end_datetime = '2024-01-04 15:00:00'
        # 24小时前的时间
        # self.start_datetime = (datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
        #     hours=24)).strftime('%Y-%m-%d %H:%M:%S')
        self.start_datetime = (datetime.datetime.now() - datetime.timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S')

        self.today = datetime.datetime.today().strftime('%Y-%m-%d')
        # self.today = '2024-01-04'
        # 昨天的时间
        # self.yesterday = '2024-01-03'
        self.yesterday = (datetime.datetime.today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')
        # 前天的时间
        self.two_days_ago = (datetime.datetime.today() - datetime.timedelta(days=2)).strftime('%Y-%m-%d')
        # 15日前的时间
        # self.fifteen_days_ago = (
        #         datetime.datetime.strptime(self.end_datetime, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
        #     days=15)).strftime('%Y-%m-%d %H:%M:%S')
        self.fifteen_days_ago = (datetime.datetime.today() - datetime.timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')
        self.fourteen_days_ago = (datetime.datetime.today() - datetime.timedelta(days=14)).strftime('%Y-%m-%d %H:%M:%S')
        self.end_time = self.today + ' 00:00:00'
        self.yesterday_time = self.yesterday + ' 00:00:00'
        self.two_days_ago_time = self.two_days_ago + ' 00:00:00'
        self.city_name_list = ['贵阳市', '遵义市', '毕节市', '黔南市', '铜仁市', '黔东南市', '六盘水市', '黔西南市',
                               '安顺市', '省本级']
        self.age_labels = ['90及以上', '80-90', '70-80', '60-70', '50-60', '40-50', '30-40', '20-30', '10-20', '0-10']

    def get_connection(self):
        self.conn = pymysql.connect(host=idna.encode(self.DB["HOST"]).decode('utf-8'), port=int(self.DB["PORT"]),
                                    user=self.DB["USER"],
                                    password=self.DB["PASSWORD"], database=self.DB["NAME"])
        return self.conn

    def extract_city(self, data_string, type_pattern='人数'):
        """
        匹配城市名称
        :param data_string: 数据字符串
        :param type_pattern: 匹配关键字，默认为'人数'
        """
        match = re.search(rf'-{type_pattern}-([^-]+)-', data_string)
        if match:
            # 如果找到了城市名，返回该城市名
            return match.group(1)
        else:
            # 如果没有找到具体的城市名，则返回"全市"
            if f'-{type_pattern}-' in data_string:
                return '省本级'
            else:
                # 如果没有 '-人数-' 模式，则不进行提取
                return None

    def df_to_dict(self, df, key_column, value_columns):
        """
        将DataFrame转换为字典

        参数:
        - df: 输入的DataFrame
        - key_column: 用作字典键的列名
        - value_columns: 用作字典值的列名列表

        返回:
        - 一个字典，其中键是key_column指定的列的值，值是由value_columns指定的列组成的字典
        """
        # 创建空字典
        dict_data = {}

        # 迭代DataFrame的每一行
        for _, row in df.iterrows():
            # 获取键
            key = row[key_column]

            # 如果键不在字典中，则添加一个空列表
            if key not in dict_data:
                dict_data[key] = []

            # 添加值
            value = {col: row[col] for col in value_columns}
            dict_data[key].append(value)

        return dict_data

    def get_area_mapping(self):
        """
        获取地区映射表
        """
        # 获取编码与城市的对应关系
        area_mapping = PublicMapping.objects.filter(type='area')
        df_mapping = pd.DataFrame(list(area_mapping.values()))[['name', 'keywords']]
        df_mapping.rename(columns={'name': 'city_name', 'keywords': 'city_code'}, inplace=True)
        return df_mapping

    def get_seller_group_report_data(self):
        """
        获取保司上传的团单数据
        """
        with self.get_connection() as conn:
            df_department_info = pd.read_sql(
                query_sql('SQL_GROUP_REPORT').format(product_set_code=self.product_set_code), conn)
            max_publish_time_indices = df_department_info.groupby('code')['publish_time'].idxmax()
            max_publish_time_df = df_department_info.loc[max_publish_time_indices]

            max_publish_time_df['publish_time'] = max_publish_time_df['publish_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['end_time'] = max_publish_time_df['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            max_publish_time_df['id'] = max_publish_time_df['id'].astype(str)
            max_publish_time_df['value'] = max_publish_time_df['value'].astype(int)
            max_publish_time_df['short_name'] = max_publish_time_df['name'].apply(lambda x: x.split('-')[-2])
            max_publish_time_df['version'] = max_publish_time_df['name'].apply(
                lambda x: '合计' if x.split('-')[-3] == '团单' else x.split('-')[-3])
        return max_publish_time_df

    def get_insure_data(self):
        """
        概览列表(累计参保、累计保费、今日参保、昨日参保（较上一日比较）、今日保费、昨日保费（较上一日比较）)
        """
        # 1、累计参保
        with self.get_connection() as conn:
            df_total_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='单'), conn)

            # 过滤数据
            df_total_count = df_total_count[df_total_count['name'].str.contains('销量') & ~df_total_count[
                'name'].str.contains('线下') & ~df_total_count[
                'name'].str.contains('线上') & ~df_total_count[
                'name'].str.contains('省本级')]

            df_total_count['city_name'] = df_total_count['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            df_total_count['value'].fillna(0, inplace=True)

            # 这边是保司上传的数据，保险公司的团单数据，省本级需要加上团单数据，其他不用，与历史一致
            df_offline_seller_group = self.get_seller_group_report_data()
            group_seller = df_offline_seller_group[df_offline_seller_group['version'] == '合计']
            group_seller.rename(columns={'value': 'group_count', 'short_name': 'seller'}, inplace=True)
            group_count = group_seller['group_count'].sum()
            # city_name为省本级的value加上团单数量
            df_total_count.loc[df_total_count['city_name'] == '省本级', 'value'] = df_total_count.loc[df_total_count[
                                                                                                          'city_name'] == '省本级', 'value'] + group_count
            if df_total_count.empty:
                df_total_count = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            df_total_count = df_total_count[['city_name', 'value']]
            # 最终以json输出，转成dict
            dict_total_person_count = self.df_to_dict(df_total_count, 'city_name', ['value'])

            # 2、累计保费
            df_total_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='累计值', unit='元'), conn)

            df_total_amount = df_total_amount[df_total_amount['name'].str.contains('销售额') & ~df_total_amount[
                'name'].str.contains('线下') & ~df_total_amount['name'].str.contains('线上') & ~df_total_amount[
                'name'].str.contains('省本级')]
            df_total_amount['city_name'] = df_total_amount['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销售额'))
            df_total_amount['value'].fillna(0, inplace=True)
            if df_total_amount.empty:
                df_total_amount = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            dict_total_amount = self.df_to_dict(df_total_amount, 'city_name', ['value'])

            # 3、今日参保
            df_today_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.end_time), conn)
            df_today_count = df_today_count[
                df_today_count['name'].str.contains('-销量-') & ~df_today_count['name'].str.contains('小时') & ~
                df_today_count['name'].str.contains('线下') & ~df_today_count['name'].str.contains('线上') & ~
                df_today_count['name'].str.contains('省本级')]
            df_today_count['city_name'] = df_today_count['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            df_today_count['value'].fillna(0, inplace=True)

            if df_today_count.empty:
                df_today_count = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            dict_today_count = self.df_to_dict(df_today_count, 'city_name', ['value'])

            # 4、今日保费
            df_today_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.end_time), conn)
            df_today_amount = df_today_amount[
                df_today_amount['name'].str.contains('-销售额-') & ~df_today_amount[
                    'name'].str.contains('小时') & ~df_today_amount['name'].str.contains('线下') & ~df_today_amount[
                    'name'].str.contains('线上') & ~df_today_amount['name'].str.contains('省本级')]
            df_today_amount['city_name'] = df_today_amount['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销售额'))
            df_today_amount['value'].fillna(0, inplace=True)
            if df_today_amount.empty:
                df_today_amount = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            dict_today_amount = self.df_to_dict(df_today_amount, 'city_name', ['value'])

            # 5、昨日参保
            df_yesterday_count = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='单',
                                                                  end_time=self.yesterday_time), conn)

            df_yesterday_count = df_yesterday_count[
                df_yesterday_count['name'].str.contains('-销量-') & ~df_yesterday_count[
                    'name'].str.contains('小时') & ~df_yesterday_count['name'].str.contains('线下') & ~
                df_yesterday_count[
                    'name'].str.contains('线上') & ~df_yesterday_count['name'].str.contains('省本级')]

            df_yesterday_count['city_name'] = df_yesterday_count['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            df_yesterday_count['value'].fillna(0, inplace=True)
            if df_yesterday_count.empty:
                df_yesterday_count = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})
            dict_yesterday_count = self.df_to_dict(df_yesterday_count, 'city_name', ['value'])

            # 6、昨日保费
            df_yesterday_amount = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_ONE_DAY_DATA').format(product_set_code=self.product_set_code,
                                                                  statistical_type='当期值', unit='元',
                                                                  end_time=self.yesterday_time), conn)

            df_yesterday_amount = df_yesterday_amount[
                df_yesterday_amount['name'].str.contains('-销售额-') & ~df_yesterday_amount[
                    'name'].str.contains('小时') & ~df_yesterday_amount['name'].str.contains('线下') & ~
                df_yesterday_amount[
                    'name'].str.contains('线上') & ~df_yesterday_amount['name'].str.contains('省本级')]

            df_yesterday_amount['city_name'] = df_yesterday_amount['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销售额'))
            df_yesterday_amount['value'].fillna(0, inplace=True)
            if df_yesterday_amount.empty:
                df_yesterday_amount = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list)})

            dict_yesterday_amount = self.df_to_dict(df_yesterday_amount, 'city_name', ['value'])

        return dict_total_person_count, dict_total_amount, dict_today_count, dict_today_amount, dict_yesterday_count, dict_yesterday_amount

    def get_sale_info(self):
        """
        产品销售情况
        """
        with self.get_connection() as conn:
            # 1、完成率，只有一个总的，没有分城市的
            df_complete_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_complete_rate = df_complete_rate[df_complete_rate['name'].str.contains('完成率-当期值')][
                ['value']].rename(columns={'value': 'percent'})
            # 如果结果为None，则说明没有数据，则返回0
            if df_complete_rate.empty:
                df_complete_rate = pd.DataFrame({'percent': [0]})
            if df_complete_rate['percent'].values[0] is None:
                df_complete_rate['percent'] = 0
            df_complete_rate['value'] = df_complete_rate['percent']
            df_complete_rate['percent'] = df_complete_rate['percent'].apply(lambda x: round(x / 100, 4))
            df_complete_rate['actual'] = df_complete_rate['percent']
            df_complete_rate['aims'] = 1

            dict_complete_rate = df_complete_rate.to_dict(orient='records')
            # 2、续约占比
            df_continue_rate = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='当期值', unit='百分比'), conn)
            df_continue_rate = df_continue_rate[
                df_continue_rate['name'].str.contains('续保占比-') & ~df_continue_rate['name'].str.contains(
                    '省本级') & ~df_continue_rate['name'].str.contains('续约率')]
            # 将value为None的置为0
            df_continue_rate['value'].fillna(0, inplace=True)

            df_continue_rate.rename(columns={'value': 'percent'}, inplace=True)
            df_continue_rate['value'] = df_continue_rate['percent']
            df_continue_rate['percent'] = df_continue_rate['percent'].apply(lambda x: round(x / 100, 4))
            df_continue_rate['city_name'] = df_continue_rate['name'].apply(
                lambda x: self.extract_city(x, type_pattern='续保占比'))
            df_continue_rate.fillna(0, inplace=True)
            if df_continue_rate.empty:
                df_continue_rate = pd.DataFrame(
                    {'city_name': self.city_name_list, 'value': [0] * len(self.city_name_list),
                     'percent': [0] * len(self.city_name_list), 'actual': [0] * len(self.city_name_list),
                     'aims': [1] * len(self.city_name_list)})
            df_continue_rate['actual'] = df_continue_rate['percent']
            df_continue_rate['aims'] = 1

            dict_continue_rate = self.df_to_dict(df_continue_rate, 'city_name', ['value', 'percent','actual','aims'])

            # # 2.1、续保率
            # df_continue_rate_prev = pd.read_sql(
            #     query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.previous_product_set_code,
            #                                                      statistical_type='当期值', unit='百分比'), conn)
            # df_continue_rate_prev = df_continue_rate_prev[df_continue_rate_prev['name'].str.contains('-续保率-当期值')][
            #     ['value']].rename(columns={'value': 'percent'})
            # df_continue_rate_prev['value'] = df_continue_rate_prev['percent']
            # df_continue_rate_prev['percent'] = df_continue_rate_prev['percent'].apply(lambda x: round(x / 100, 4))
            #
            # dict_continue_rate_prev = df_continue_rate_prev.to_dict(orient='records')

        # # 3、产品类型分布
        # product_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
        #                                                statistical_type='product')
        # df_product_data = pd.DataFrame(list(product_data.values('key', 'value'))).rename(columns={'key': 'type'})
        # if df_product_data.empty:
        #     df_product_data = pd.DataFrame()
        # else:
        #     df_product_data['value'] = df_product_data['value'].astype(int)
        #
        # dict_product_data = df_product_data.to_dict(orient='records')

        # 4、个单团单分布
        personal_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='personal')
        df_personal_data = pd.DataFrame(list(personal_data.values('key', 'value', 'additional_info'))).rename(
            columns={'key': 'type', 'additional_info': 'city_name'})

        if df_personal_data.empty:
            df_personal_data = pd.DataFrame()
        else:
            df_personal_data['city_name'].fillna('省本级', inplace=True)
            df_personal_data['value'] = df_personal_data['value'].astype(int)
        dict_personal_data = self.df_to_dict(df_personal_data, 'city_name', ['value', 'type'])
        # 5、线上线下分布
        isonline_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='isonline')
        df_isonline_data = pd.DataFrame(list(isonline_data.values('key', 'value', 'additional_info'))).rename(
            columns={'key': 'type', 'additional_info': 'city_name'})

        if df_isonline_data.empty:
            df_isonline_data = pd.DataFrame()
        else:
            df_isonline_data['city_name'].fillna('省本级', inplace=True)
            df_isonline_data['value'] = df_isonline_data['value'].astype(int)
        dict_isonline_data = self.df_to_dict(df_isonline_data, 'city_name', ['value', 'type'])

        # 6、自费个账分布
        pay_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                   statistical_type='pay')
        df_pay_data = pd.DataFrame(list(pay_data.values('key', 'value', 'additional_info'))).rename(
            columns={'key': 'type', 'additional_info': 'city_name'})

        if df_pay_data.empty:
            df_pay_data = pd.DataFrame()
        else:
            df_pay_data['city_name'].fillna('省本级', inplace=True)
            df_pay_data['value'] = df_pay_data['value'].astype(int)
        dict_pay_data = self.df_to_dict(df_pay_data, 'city_name', ['value', 'type'])

        # 8、险种分布
        medicare_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                        statistical_type='medicare')
        df_medicare_data = pd.DataFrame(list(medicare_data.values('key', 'value', 'additional_info'))).rename(
            columns={'key': 'type', 'additional_info': 'city_name'})

        if df_medicare_data.empty:
            df_medicare_data = pd.DataFrame()
        else:
            df_medicare_data['city_name'].fillna('省本级', inplace=True)
            df_medicare_data['value'] = df_medicare_data['value'].astype(int)
        dict_medicare_data = self.df_to_dict(df_medicare_data, 'city_name', ['value', 'type'])

        return dict_complete_rate, dict_continue_rate, dict_personal_data, dict_isonline_data, dict_pay_data, dict_medicare_data

    def get_last_24_hour_data(self):
        """
        最近24小时销量数据
        """
        with self.get_connection() as conn:
            # 如果过去24小时的起始小于销售起始时间，则取销售起始时间
            start_datetime = max(
                datetime.datetime.strptime(self.start_datetime, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time, '%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='小时', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_24_hour_data = df_last_24_hour_data[df_last_24_hour_data['name'].str.contains('销量-')][
                ['end_time', 'value', 'name']]
            df_last_24_hour_data['city_name'] = df_last_24_hour_data['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            df_last_24_hour_data['end_time'] = df_last_24_hour_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d %H:%M:%S'))
            df_last_24_hour_data['value'] = df_last_24_hour_data['value'].astype(int)
            df_last_24_hour_data.rename(columns={'end_time': 'x', 'value': 'y'}, inplace=True)
            dict_last_24_hour_data = self.df_to_dict(df_last_24_hour_data, 'city_name', ['x', 'y'])
            return dict_last_24_hour_data

    def get_last_15_days_data(self):
        """
        最近15日销量数据
        """
        with self.get_connection() as conn:
            start_datetime = max(
                datetime.datetime.strptime(self.fifteen_days_ago, '%Y-%m-%d %H:%M:%S'),
                datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S'))
            df_last_15_days_data = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_last_15_days_data = df_last_15_days_data[
                df_last_15_days_data['name'].str.contains('销量-') & ~df_last_15_days_data['name'].str.contains(
                    '线下') & ~df_last_15_days_data['name'].str.contains('线上')][
                ['end_time', 'value', 'name']]
            df_last_15_days_data['city_name'] = df_last_15_days_data['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            df_last_15_days_data['end_time'] = df_last_15_days_data['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            df_last_15_days_data['value'] = df_last_15_days_data['value'].astype(int)
            df_last_15_days_data.rename(columns={'end_time': 'x', 'value': 'y'}, inplace=True)
            dict_last_15_days_data = self.df_to_dict(df_last_15_days_data, 'city_name', ['x', 'y'])
            return dict_last_15_days_data

    def get_area_data(self):
        """
        参保地统计报表（排名、参保地、占比、总单数、今日参保、昨日参保、参保率）
        """

        area_data = InsureArea.objects.filter(product_set_code=self.product_set_code)
        df_area_data = pd.DataFrame(list(
            area_data.values('position', 'name', 'ratio', 'total_count', 'today_count', 'yesterday_count',
                             'insure_ratio', 'additional_info')))
        # 按照排序先确定顺序，并对合计的顺序置为-
        if df_area_data.empty:
            df_area_data = pd.DataFrame()
        else:
            df_area_data.sort_values(by='position', inplace=True)
            df_area_data.reset_index(inplace=True)
            # df_area_data['position'] = df_area_data.index + 1
            df_area_data[['insure_ratio', 'ratio']] = df_area_data[['insure_ratio', 'ratio']].astype(float)
            df_area_data.loc[df_area_data['name'] == '合计', 'position'] = '-'
            df_area_data['insure_ratio'] = df_area_data['insure_ratio'].apply(lambda x: str(round(x * 100, 1)) + '%')
            df_area_data.rename(columns={'additional_info': 'city_name'}, inplace=True)
            # 将参保率转成百分比
            # 如果city_name不为市本级，则将insure_ratio 赋值-
            df_area_data['city_name'].fillna('省本级', inplace=True)
            df_area_data.loc[df_area_data['city_name'] != '省本级', 'insure_ratio'] = '-'
        dict_area_data = self.df_to_dict(df_area_data, 'city_name',
                                         ['position', 'name', 'ratio', 'total_count', 'today_count', 'yesterday_count',
                                          'insure_ratio'])
        return dict_area_data

    def get_agent_data(self):
        """
        获取线下保司销售数据（排名、保司、占比、代理人数、人均出单、个单、团单、目标、完成率）
        """
        agent_data = InsureAgent.objects.filter(product_set_code=self.product_set_code)
        df_agent_data = pd.DataFrame(list(agent_data.values('position', 'name', 'insure_ratio', 'employee_count',
                                                            'average_count', 'personal_count', 'group_count', 'target',
                                                            'target_ratio', 'today_count', 'yesterday_count',
                                                            'additional_info')))
        if df_agent_data.empty:
            df_agent_data = pd.DataFrame()
        else:
            # 按照排序先确定顺序，并对合计的顺序置为-
            df_agent_data.sort_values(by=['position'], inplace=True)
            df_agent_data[['insure_ratio', 'target_ratio', 'average_count']] = df_agent_data[
                ['insure_ratio', 'target_ratio', 'average_count']].astype(
                float)
            df_agent_data.loc[df_agent_data['name'] == '合计', 'position'] = '-'
            df_agent_data['average_count'] = df_agent_data['average_count'].round(1).astype(float)
            df_agent_data.rename(columns={'additional_info': 'city_name'}, inplace=True)
            df_agent_data['city_name'].fillna('省本级', inplace=True)
            # 将完成率转成百分比
            df_agent_data['target_ratio'] = df_agent_data['target_ratio'].apply(lambda x: str(round(x * 100, 1)) + '%')
            df_agent_data.loc[df_agent_data['city_name'] != '省本级', ['target', 'target_ratio', 'employee_count', 'average_count']] = '-'
        dict_agent_data = self.df_to_dict(df_agent_data, 'city_name',
                                          ['position', 'name', 'insure_ratio', 'employee_count', 'average_count',
                                           'personal_count',
                                           'group_count', 'target', 'target_ratio', 'today_count', 'yesterday_count'])
        return dict_agent_data

    # 人群特征
    def get_age_data(self):
        """
        平均年龄、年龄中位数
        """
        with self.get_connection() as conn:
            df_avg_age = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='平均值', unit='岁'), conn)

            df_avg_age = df_avg_age[
                df_avg_age['name'].str.contains('年龄-') & ~df_avg_age['name'].str.contains('省本级')]
            df_avg_age['city_name'] = df_avg_age['name'].apply(lambda x: self.extract_city(x, type_pattern='年龄'))
            df_avg_age['name'] = '平均年龄'
            df_avg_age['value'] = df_avg_age['value'].apply(lambda x: round(float(x),1))
            df_median_age = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_LATEST_DATA').format(product_set_code=self.product_set_code,
                                                                 statistical_type='中位值', unit='岁'), conn)
            df_median_age = df_median_age[
                df_median_age['name'].str.contains('年龄-') & ~df_median_age['name'].str.contains('省本级')]
            df_median_age['city_name'] = df_median_age['name'].apply(
                lambda x: self.extract_city(x, type_pattern='年龄'))
            df_median_age['name'] = '年龄中位数'
            df_median_age['value'] = df_median_age['value'].apply(lambda x: round(float(x),1))
            dict_avg_age = self.df_to_dict(df_avg_age, 'city_name', ['name', 'value'])
            dict_median_age = self.df_to_dict(df_median_age, 'city_name', ['name', 'value'])
            return dict_avg_age, dict_median_age

    def get_age_gender_data(self):
        """
        年龄性别分布
        """
        age_sex_data = InsureAgeSex.objects.filter(product_set_code=self.product_set_code).order_by('age_distribution',
                                                                                                    'sex')
        df_age_sex_data = pd.DataFrame(list(
            age_sex_data.values('sex', 'age_distribution', 'value', 'additional_info')))
        if df_age_sex_data.empty:
            df_age_sex_data = pd.DataFrame()
            df_sex = pd.DataFrame()
        else:
            df_age_sex_data['additional_info'].fillna('省本级', inplace=True)
            df_sex = df_age_sex_data.groupby(['sex', 'additional_info'])['value'].sum().reset_index()
            df_age_sex_data.rename(columns={'additional_info': 'city_name'}, inplace=True)
            df_age_sex_data.rename(columns={'age_distribution': 'x', 'sex': 's', 'value': 'y'}, inplace=True)
            df_sex.rename(columns={'additional_info': 'city_name', 'sex': 'type'}, inplace=True)
        dict_age_sex_data = self.df_to_dict(df_age_sex_data, 'city_name', ['x', 's', 'y'])
        dict_sex_data = self.df_to_dict(df_sex, 'city_name', ['type', 'value'])
        return dict_age_sex_data, dict_sex_data

    def get_age_distribution_data(self):
        """
        年龄段分布
        """
        age_distribution_data = PublicStatistics.objects.filter(product_set_code=self.product_set_code, type=self.type,
                                                                statistical_type='age_ratio').order_by('key')

        df_age_distribution_data = pd.DataFrame(list(age_distribution_data.values('key', 'value', 'additional_info')))
        if df_age_distribution_data.empty:
            df_age_distribution_data = pd.DataFrame()
        else:
            df_age_distribution_data['additional_info'].fillna('省本级', inplace=True)
            df_age_distribution_data.rename(columns={'additional_info': 'city_name'}, inplace=True)
            df_age_distribution_data['value'] = df_age_distribution_data['value'].astype(float)

            # 强制排序
            age_mapping = {age: i for i, age in enumerate(self.age_labels)}
            df_age_distribution_data['age_order'] = df_age_distribution_data['key'].map(age_mapping)
            df_age_distribution_data = df_age_distribution_data.sort_values(by=['age_order'])
            df_age_distribution_data = df_age_distribution_data.drop('age_order', axis=1).reset_index(drop=True)

            df_age_distribution_data.rename(columns={'key': 'age'}, inplace=True)
        dict_age_distribution_data = self.df_to_dict(df_age_distribution_data, 'city_name', ['age', 'value'])
        return dict_age_distribution_data

    def get_trend_data(self):
        """
        往期对比-全量（当期值） 本期累计值、往期累计、累计同比
        往期对比-线上（当期值） 本期累计值、往期累计、累计同比
        往期对比-线下（当期值） 本期累计值、往期累计、累计同比
        """
        # 往期对比-全量（当期值） 本期累计值、往期累计、累计同比
        start_datetime = datetime.datetime.strptime(self.sale_start_time_zero, '%Y-%m-%d %H:%M:%S')
        with self.get_connection() as conn:
            df_count_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)

            df_count = df_count_total[
                df_count_total['name'].str.contains('销量-当期值') & ~df_count_total['name'].str.contains('小时') & ~
                df_count_total[
                    'name'].str.contains('线上') & ~df_count_total['name'].str.contains('线下') & ~df_count_total[
                    'name'].str.contains('省本级')][['end_time', 'value', 'name']]

            df_count['value'] = df_count['value'].astype(int)
            df_count['city_name'] = df_count['name'].apply(lambda x: self.extract_city(x, type_pattern='销量'))

            # 往期对比，当日销量对比
            df_count_prev_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.previous_product_set_code,
                                                          statistical_type='当期值', unit='单',
                                                          freq='日', start_datetime=self.sale_start_time_prev,
                                                          end_datetime=self.end_datetime), conn)
            df_count_prev = df_count_prev_total[
                df_count_prev_total['name'].str.contains('销量-当期值') & ~df_count_prev_total['name'].str.contains(
                    '小时') & ~
                df_count_prev_total['name'].str.contains('线上') & ~df_count_prev_total['name'].str.contains('线下') & ~
                df_count_prev_total['name'].str.contains('省本级')][['end_time', 'value', 'name']]
            df_count_prev['city_name'] = df_count_prev['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            # df_count_prev['end_time'] = df_count_prev['end_time'].apply(lambda x: x.strftime('%Y-%m-%d'))
            df_count_prev['value'] = df_count_prev['value'].astype(int)
            if pd.to_datetime(self.sale_start_time_zero) > pd.to_datetime(self.end_datetime):
                df_count['date_order'] = np.nan
            else:
                df_count['date_order'] = (df_count['end_time'] - df_count['end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
            df_count_prev['date_order'] = (
                    df_count_prev['end_time'] - df_count_prev['end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联

            df_count_prev['month_day'] = df_count_prev['end_time'].apply(lambda x: x.strftime('%Y-%m-%d')[-5:])
            df_count['month_day'] = df_count['end_time'].apply(lambda x: x.strftime('%Y-%m-%d')[-5:])
            # 如果改成月-日关联，需要调整这里的关联关系
            df_count_compare_dqz = pd.merge(df_count[['end_time', 'value', 'city_name', 'date_order']],
                                            df_count_prev[['value', 'city_name', 'date_order']],
                                            on=['city_name', 'date_order'], how='left')

            full_date_range_total = pd.date_range(start=self.sale_start_date,
                                                  end=datetime.datetime.strptime(self.end_datetime,
                                                                                 '%Y-%m-%d %H:%M:%S').date(), freq='D')
            # 补全日期范围
            df_count_compare_dqz = (
                df_count_compare_dqz.set_index('end_time')
                .reindex(full_date_range_total)
                .fillna(0)
                .reset_index()
                .rename(columns={'index': 'end_time'})
            )
            df_count_compare_melted = pd.melt(df_count_compare_dqz, id_vars=['end_time', 'city_name'],
                                              value_vars=['value_x', 'value_y'], var_name='s',
                                              value_name='y').rename(columns={'end_time': 'x'})
            df_count_compare_melted['s'] = df_count_compare_melted['s'].map({'value_x': '本期', 'value_y': '上期'})
            df_count_compare_melted['x'] = df_count_compare_melted['x'].apply(lambda x: x.strftime('%Y-%m-%d'))
            # 如果有多个城市的，这边需要用df_to_dict处理
            dict_count_compare_total_dqz = df_count_compare_melted.to_dict(orient='records')

            # 本期累计值
            df_count_cumsum_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.product_set_code,
                                                          statistical_type='累计值', unit='单',
                                                          freq='日', start_datetime=start_datetime,
                                                          end_datetime=self.end_datetime), conn)
            df_count_cumsum = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-累计值') & ~df_count_cumsum_total['name'].str.contains(
                    '小时')][
                ['end_time', 'value', 'name']]
            df_count_cumsum['city_name'] = df_count_cumsum['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            df_count_cumsum['value'] = df_count_cumsum['value'].astype(int)
            df_count_cumsum.rename(columns={'value': 'total_value'}, inplace=True)

            # 往期累计值、累计同比
            df_count_cumsum_previous_total = pd.read_sql(
                query_sql('SQL_DW_INDICATOR_DATA').format(product_set_code=self.previous_product_set_code,
                                                          statistical_type='累计值', unit='单',
                                                          freq='日', start_datetime=self.sale_start_time_prev,
                                                          end_datetime=self.end_datetime), conn)

            df_count_cumsum_previous = df_count_cumsum_previous_total[
                df_count_cumsum_previous_total['name'].str.contains('销量-累计值') & ~df_count_cumsum_previous_total[
                    'name'].str.contains('小时')][
                ['end_time', 'value', 'name']]
            df_count_cumsum_previous['city_name'] = df_count_cumsum_previous['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量'))
            # 日期强制对齐
            if pd.to_datetime(self.sale_start_time_zero) > pd.to_datetime(self.end_datetime):
                df_count_cumsum['date_order'] = np.nan
            else:
                df_count_cumsum['date_order'] = (
                        df_count_cumsum['end_time'] - df_count_cumsum['end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
            df_count_cumsum_previous['date_order'] = (
                    df_count_cumsum_previous['end_time'] - df_count_cumsum_previous[
                'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联

            df_count_cumsum_previous['value'] = df_count_cumsum_previous['value'].astype(int)
            df_count_cumsum_previous.rename(columns={'value': 'previous_total_value'}, inplace=True)

            df_count_cumsum['month_day'] = df_count_cumsum['end_time'].apply(lambda x: x.strftime('%Y-%m-%d')[-5:])
            df_count_cumsum_previous['month_day'] = df_count_cumsum_previous['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            # # 如果销售期差别很大，会有问题，比如一个是上半年的，一个是下半年的——本次强制调平，因为出现了销售期差别很大的情况
            df_count_compare = pd.merge(df_count_cumsum[['end_time', 'date_order', 'city_name', 'total_value']],
                                        df_count_cumsum_previous[['date_order', 'city_name', 'previous_total_value']],
                                        on=['city_name', 'date_order'],
                                        how='left')
            df_count_compare.fillna(0, inplace=True)
            # 取最后一条记录，根据city_name分组，取最后一条记录
            df_count_compare = df_count_compare.groupby('city_name').apply(lambda x: x.iloc[-1])
            df_count_compare.reset_index(inplace=True, drop=True)
            df_count_compare['yoy'] = df_count_compare.apply(
                lambda row: (row['total_value'] - row['previous_total_value']) / row['previous_total_value'] *100 if
                row['previous_total_value'] != 0 else 100, axis=1)
            dict_count_cumsum = df_count_compare[['city_name', 'total_value']].rename(
                columns={'total_value': 'value'}).to_dict(orient='records')
            dict_count_yoy = df_count_compare[['city_name', 'yoy']].rename(columns={'yoy': 'value'}).to_dict(
                orient='records')
            dict_count_cumsum_prev = df_count_compare[['city_name', 'previous_total_value']].rename(
                columns={'previous_total_value': 'value'}).to_dict(orient='records')
            # 往期对比-线上（当期值） 本期累计值、往期累计、累计同比
            df_count_online = df_count_total[
                df_count_total['name'].str.contains('销量-线上-当期值') & ~df_count_total['name'].str.contains(
                    '小时') & ~df_count_total['name'].str.contains('线下') & ~df_count_total[
                    'name'].str.contains('省本级')][['end_time', 'value', 'name']]
            df_count_online['value'] = df_count_online['value'].astype(int)
            df_count_online['city_name'] = df_count_online['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线上'))
            df_count_prev_online = df_count_prev_total[
                df_count_prev_total['name'].str.contains('销量-线上-当期值') & ~df_count_prev_total[
                    'name'].str.contains(
                    '小时') & ~df_count_prev_total['name'].str.contains('线下') & ~
                df_count_prev_total['name'].str.contains('省本级')][['end_time', 'value', 'name']]
            df_count_prev_online['city_name'] = df_count_prev_online['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线上'))
            df_count_prev_online['value'] = df_count_prev_online['value'].astype(int)
            if pd.to_datetime(self.sale_start_time_zero) > pd.to_datetime(self.end_datetime):
                df_count_online['date_order'] = np.nan
            else:
                df_count_online['date_order'] = (
                            df_count_online['end_time'] - df_count_online['end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
            df_count_prev_online['date_order'] = (
                    df_count_prev_online['end_time'] - df_count_prev_online[
                'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联

            df_count_prev_online['month_day'] = df_count_prev_online['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            df_count_online['month_day'] = df_count_online['end_time'].apply(lambda x: x.strftime('%Y-%m-%d')[-5:])
            # 如果改成月-日关联，需要调整这里的关联关系
            df_count_compare_online = pd.merge(df_count_online[['end_time', 'value', 'city_name', 'date_order']],
                                               df_count_prev_online[['value', 'city_name', 'date_order']],
                                               on=['city_name', 'date_order'], how='left')

            full_date_range_online = pd.date_range(start=self.sale_start_date,
                                                   end=datetime.datetime.strptime(self.end_datetime,
                                                                                  '%Y-%m-%d %H:%M:%S').date(), freq='D')
            # 补全日期范围
            df_count_compare_online = (
                df_count_compare_online.set_index('end_time')
                .reindex(full_date_range_online)
                .fillna(0)
                .reset_index()
                .rename(columns={'index': 'end_time'})
            )
            df_count_compare_online_melted = pd.melt(df_count_compare_online, id_vars=['end_time', 'city_name'],
                                                     value_vars=['value_x', 'value_y'], var_name='s',
                                                     value_name='y').rename(columns={'end_time': 'x'})
            df_count_compare_online_melted['s'] = df_count_compare_online_melted['s'].map(
                {'value_x': '本期', 'value_y': '上期'})
            df_count_compare_online_melted['x'] = df_count_compare_online_melted['x'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            # 如果有多个城市的，这边需要用df_to_dict处理
            dict_count_compare_online = df_count_compare_online_melted.to_dict(orient='records')

            # 往期累计值、累计同比线上
            df_count_cumsum_online = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-线上-累计值') & ~df_count_cumsum_total[
                    'name'].str.contains(
                    '小时')][
                ['end_time', 'value', 'name']]
            df_count_cumsum_online['city_name'] = df_count_cumsum_online['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线上'))
            df_count_cumsum_online['value'] = df_count_cumsum_online['value'].astype(int)
            df_count_cumsum_online.rename(columns={'value': 'total_value'}, inplace=True)

            df_count_cumsum_previous_online = df_count_cumsum_previous_total[
                df_count_cumsum_previous_total['name'].str.contains('销量-线上-累计值') & ~
                df_count_cumsum_previous_total[
                    'name'].str.contains('小时')][
                ['end_time', 'value', 'name']]
            df_count_cumsum_previous_online['city_name'] = df_count_cumsum_previous_online['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线上'))
            # 日期强制对齐
            if pd.to_datetime(self.sale_start_time_zero) > pd.to_datetime(self.end_datetime):
                df_count_cumsum_online['date_order'] = np.nan
            else:
                df_count_cumsum_online['date_order'] = (
                        df_count_cumsum_online['end_time'] - df_count_cumsum_online[
                    'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
            df_count_cumsum_previous_online['date_order'] = (
                    df_count_cumsum_previous_online['end_time'] - df_count_cumsum_previous_online[
                'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联

            df_count_cumsum_previous_online['value'] = df_count_cumsum_previous_online['value'].astype(int)
            df_count_cumsum_previous_online.rename(columns={'value': 'previous_total_value'}, inplace=True)

            df_count_cumsum_online['month_day'] = df_count_cumsum_online['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            df_count_cumsum_previous_online['month_day'] = df_count_cumsum_previous_online['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            # # 如果销售期差别很大，会有问题，比如一个是上半年的，一个是下半年的——本次强制调平，因为出现了销售期差别很大的情况
            df_count_compare_online = pd.merge(
                df_count_cumsum_online[['end_time', 'date_order', 'city_name', 'total_value']],
                df_count_cumsum_previous_online[['date_order', 'city_name', 'previous_total_value']],
                on=['city_name', 'date_order'],
                how='left')
            df_count_compare_online.fillna(0, inplace=True)
            # 取最后一条记录，根据city_name分组，取最后一条记录
            df_count_compare_online = df_count_compare_online.groupby('city_name').apply(lambda x: x.iloc[-1])
            df_count_compare_online.reset_index(inplace=True, drop=True)
            df_count_compare_online['yoy'] = df_count_compare_online.apply(
                lambda row: (row['total_value'] - row['previous_total_value']) / row['previous_total_value'] *100 if
                row['previous_total_value'] != 0 else 100, axis=1)
            dict_count_cumsum_online = df_count_compare_online[['city_name', 'total_value']].rename(
                columns={'total_value': 'value'}).to_dict(orient='records')
            dict_count_yoy_online = df_count_compare_online[['city_name', 'yoy']].rename(
                columns={'yoy': 'value'}).to_dict(
                orient='records')
            dict_count_cumsum_prev_online = df_count_compare_online[['city_name', 'previous_total_value']].rename(
                columns={'previous_total_value': 'value'}).to_dict(orient='records')

            # 往期对比-线下（当期值） 本期累计值、往期累计、累计同比
            df_count_offline = df_count_total[
                df_count_total['name'].str.contains('销量-线下-当期值') & ~df_count_total['name'].str.contains(
                    '小时') & ~df_count_total['name'].str.contains('省本级')][['end_time', 'value', 'name']]
            df_count_offline['value'] = df_count_offline['value'].astype(int)
            df_count_offline['city_name'] = df_count_offline['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线下'))
            df_count_prev_offline = df_count_prev_total[
                df_count_prev_total['name'].str.contains('销量-线下-当期值') & ~df_count_prev_total[
                    'name'].str.contains('小时') & ~df_count_prev_total['name'].str.contains('省本级')][
                ['end_time', 'value', 'name']]
            df_count_prev_offline['city_name'] = df_count_prev_offline['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线下'))
            df_count_prev_offline['value'] = df_count_prev_offline['value'].astype(int)
            if pd.to_datetime(self.sale_start_time_zero) > pd.to_datetime(self.end_datetime):
                df_count_offline['date_order'] = np.nan
            else:
                df_count_offline['date_order'] = (
                        df_count_offline['end_time'] - df_count_offline['end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
            df_count_prev_offline['date_order'] = (
                    df_count_prev_offline['end_time'] - df_count_prev_offline[
                'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联

            df_count_prev_offline['month_day'] = df_count_prev_offline['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            df_count_offline['month_day'] = df_count_offline['end_time'].apply(lambda x: x.strftime('%Y-%m-%d')[-5:])
            # 如果改成月-日关联，需要调整这里的关联关系
            df_count_compare_offline = pd.merge(df_count_offline[['end_time', 'value', 'city_name', 'date_order']],
                                                df_count_prev_offline[['value', 'city_name', 'date_order']],
                                                on=['city_name', 'date_order'], how='left')

            full_date_range_offline = pd.date_range(start=self.sale_start_date,
                                                    end=datetime.datetime.strptime(self.end_datetime,
                                                                                   '%Y-%m-%d %H:%M:%S').date(),
                                                    freq='D')
            # 补全日期范围
            df_count_compare_offline = (
                df_count_compare_offline.set_index('end_time')
                .reindex(full_date_range_offline)
                .fillna(0)
                .reset_index()
                .rename(columns={'index': 'end_time'})
            )
            df_count_compare_offline_melted = pd.melt(df_count_compare_offline, id_vars=['end_time', 'city_name'],
                                                      value_vars=['value_x', 'value_y'], var_name='s',
                                                      value_name='y').rename(columns={'end_time': 'x'})
            df_count_compare_offline_melted['s'] = df_count_compare_offline_melted['s'].map(
                {'value_x': '本期', 'value_y': '上期'})
            df_count_compare_offline_melted['x'] = df_count_compare_offline_melted['x'].apply(
                lambda x: x.strftime('%Y-%m-%d'))
            # 如果有多个城市的，这边需要用df_to_dict处理
            dict_count_compare_offline = df_count_compare_offline_melted.to_dict(orient='records')

            # 往期累计值、累计同比线下
            df_count_cumsum_offline = df_count_cumsum_total[
                df_count_cumsum_total['name'].str.contains('销量-线下-累计值') & ~df_count_cumsum_total[
                    'name'].str.contains(
                    '小时')][
                ['end_time', 'value', 'name']]
            df_count_cumsum_offline['city_name'] = df_count_cumsum_offline['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线下'))
            df_count_cumsum_offline['value'] = df_count_cumsum_offline['value'].astype(int)
            df_count_cumsum_offline.rename(columns={'value': 'total_value'}, inplace=True)

            df_count_cumsum_previous_offline = df_count_cumsum_previous_total[
                df_count_cumsum_previous_total['name'].str.contains('销量-线下-累计值') & ~
                df_count_cumsum_previous_total[
                    'name'].str.contains('小时')][
                ['end_time', 'value', 'name']]
            df_count_cumsum_previous_offline['city_name'] = df_count_cumsum_previous_offline['name'].apply(
                lambda x: self.extract_city(x, type_pattern='销量-线下'))
            # 日期强制对齐
            if pd.to_datetime(self.sale_start_time_zero) > pd.to_datetime(self.end_datetime):
                df_count_cumsum_offline['date_order'] = np.nan
            else:
                df_count_cumsum_offline['date_order'] = (
                        df_count_cumsum_offline['end_time'] - df_count_cumsum_offline[
                    'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联
            df_count_cumsum_previous_offline['date_order'] = (
                    df_count_cumsum_previous_offline['end_time'] - df_count_cumsum_previous_offline[
                'end_time'].min()).dt.days  # 如果不关日期，按照第一天排序，用这个关联

            df_count_cumsum_previous_offline['value'] = df_count_cumsum_previous_offline['value'].astype(int)
            df_count_cumsum_previous_offline.rename(columns={'value': 'previous_total_value'}, inplace=True)

            df_count_cumsum_offline['month_day'] = df_count_cumsum_offline['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            df_count_cumsum_previous_offline['month_day'] = df_count_cumsum_previous_offline['end_time'].apply(
                lambda x: x.strftime('%Y-%m-%d')[-5:])
            # # 如果销售期差别很大，会有问题，比如一个是上半年的，一个是下半年的——本次强制调平，因为出现了销售期差别很大的情况
            df_count_compare_offline = pd.merge(
                df_count_cumsum_offline[['end_time', 'date_order', 'city_name', 'total_value']],
                df_count_cumsum_previous_offline[['date_order', 'city_name', 'previous_total_value']],
                on=['city_name', 'date_order'],
                how='left')
            df_count_compare_offline.fillna(0, inplace=True)
            # 取最后一条记录，根据city_name分组，取最后一条记录
            df_count_compare_offline = df_count_compare_offline.groupby('city_name').apply(lambda x: x.iloc[-1])
            df_count_compare_offline.reset_index(inplace=True, drop=True)
            df_count_compare_offline['yoy'] = df_count_compare_offline.apply(
                lambda row: (row['total_value'] - row['previous_total_value']) / row['previous_total_value'] *100 if
                row['previous_total_value'] != 0 else 100, axis=1)
            dict_count_cumsum_offline = df_count_compare_offline[['city_name', 'total_value']].rename(
                columns={'total_value': 'value'}).to_dict(orient='records')
            dict_count_yoy_offline = df_count_compare_offline[['city_name', 'yoy']].rename(
                columns={'yoy': 'value'}).to_dict(
                orient='records')
            dict_count_cumsum_prev_offline = df_count_compare_offline[['city_name', 'previous_total_value']].rename(
                columns={'previous_total_value': 'value'}).to_dict(orient='records')
            print(df_count_compare_offline)
            return dict_count_compare_total_dqz, dict_count_cumsum, dict_count_yoy, dict_count_cumsum_prev, dict_count_compare_online, dict_count_cumsum_online, dict_count_yoy_online, dict_count_cumsum_prev_online, dict_count_compare_offline, dict_count_cumsum_offline, dict_count_yoy_offline, dict_count_cumsum_prev_offline

    def get_content(self):
        """
        获取全部数据
        """
        data = {}
        dict_total_person_count, dict_total_amount, dict_today_count, dict_today_amount, dict_yesterday_count, dict_yesterday_amount = self.get_insure_data()
        data['累计参保'] = dict_total_person_count
        data['累计保费'] = dict_total_amount
        data['今日参保'] = dict_today_count
        data['今日保费'] = dict_today_amount
        data['昨日参保'] = dict_yesterday_count
        data['昨日保费'] = dict_yesterday_amount
        dict_complete_rate, dict_continue_rate, dict_personal_data, dict_isonline_data, dict_pay_data, dict_medicare_data = self.get_sale_info()
        data['完成率'] = dict_complete_rate
        data['续约占比'] = dict_continue_rate
        # data['续保率'] = dict_continue_rate_prev
        # data['产品类型'] = dict_product_data
        data['个单团单'] = dict_personal_data
        data['线上线下'] = dict_isonline_data
        data['自费个账'] = dict_pay_data
        data['险种'] = dict_medicare_data
        data['近24小时投保量'] = self.get_last_24_hour_data()
        data['近15日投保量'] = self.get_last_15_days_data()
        data['参保地区'] = self.get_area_data()
        data['渠道参保'] = self.get_agent_data()
        dict_avg_age, dict_median_age = self.get_age_data()
        data['平均年龄'] = dict_avg_age
        data['年龄中位数'] = dict_median_age
        dict_age_sex_data, dict_sex_data = self.get_age_gender_data()
        data['年龄性别分布'] = dict_age_sex_data
        data['性别分布'] = dict_sex_data
        data['年龄段分布'] = self.get_age_distribution_data()
        dict_count_compare_total_dqz, dict_count_cumsum, dict_count_yoy, dict_count_cumsum_prev, dict_count_compare_online, dict_count_cumsum_online, dict_count_yoy_online, dict_count_cumsum_prev_online, dict_count_compare_offline, dict_count_cumsum_offline, dict_count_yoy_offline, dict_count_cumsum_prev_offline = self.get_trend_data()
        data['本期累计投保'] = dict_count_cumsum
        data['往期累计投保'] = dict_count_cumsum_prev
        data['本期累计同比'] = dict_count_yoy
        data['往期对比线上'] = dict_count_compare_online
        data['本期线上累计投保'] = dict_count_cumsum_online
        data['往期线上累计投保'] = dict_count_cumsum_prev_online
        data['本期线上累计同比'] = dict_count_yoy_online
        data['往期对比线下'] = dict_count_compare_offline
        data['本期线下累计投保'] = dict_count_cumsum_offline
        data['本期线下累计同比'] = dict_count_yoy_offline
        data['往期线下累计投保'] = dict_count_cumsum_prev_offline
        data['往期对比全量'] = dict_count_compare_total_dqz
        data['标题'] = self.title_name
        return data

    def cache_content(self):
        """
        缓存全部数据
        """
        try:
            df_content = self.get_content()
            # 使用 Django 的数据库连接将 DataFrame 写入redis缓存,主动推送，保证数据的实时性
            self.update_cache('get_content', df_content)
            return df_content
        except Exception as e:
            logger.error(f'{type(self).__name__} cache_content error:{e}')
            raise ValueError(f'{type(self).__name__} cache_content error:{e}')

    def get_datav_data(self):
        """
        获取数据
        """
        data = self.get_from_cache('get_content')
        return data


if __name__ == '__main__':
    data = DataVGhbInsureV3()
    # product_set_code = data.product_set_code
    # data.cache_area_code()
    # data.get_insure_data()
    # data.get_last_24_hour_data()
    # data.get_last_7_days_data()
    # data.get_school_data()
    # data.get_area_data()
    # data.get_person_data()
    # df = data.get_content()
    # pprint(df)
    # df_age_gender_data = data.get_age_gender_data()
    # print(df_age_gender_data)
    data.cache_content()
    datav = data.get_datav_data()
    pprint(datav)
    # pprint(data.get_trend_data())
