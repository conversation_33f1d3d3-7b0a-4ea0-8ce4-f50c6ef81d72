import logging
import time

from transfrom.tasks.insure.nhb_insure_v5 import NhbInsureV5
from transfrom.utils.utils import send_feishu_message

logger = logging.getLogger(__name__)


def normal_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = NhbInsureV5()
    for method in [
        'get_target_ratio',
        'get_online_source_info',
        'get_offline_seller_info',
        'get_offline_seller',
        'get_area_info',
        'get_cumulative_yoy',
        'get_online_cumulative_yoy',
        'get_offline_cumulative_yoy',
        'get_personal_cumulative_yoy',
        'get_offline_personal_cumulative_yoy',
        'get_daily_online_count', #医保局要，保持数据一致，所以统一时间调度，防止历史不能及时调整，下同
        'get_daily_offline_count',
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'nhb_insure_v5_todb normal_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'nhb_insure_v5_todb normal_to_db: {method} cost:{_cost} ms')


def fast_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = NhbInsureV5()
    for method in [
        'get_last_24_hour_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'nhb_insure_v5_todb fast_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'nhb_insure_v5_todb fast_to_db: {method} cost:{_cost} ms')


def main_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = NhbInsureV5()
    for method in [
        'get_total_count',
        'get_today_count',
        'get_yesterday_count',
        'get_total_amount',
        'get_today_amount',
        'get_total_personal_count',
        'get_yesterday_amount',
        'get_person_group_count',
        'get_online_offline_count',
        'get_pay_type_count',
        'get_product_count',
        'get_insure_place_count',
        'get_medicare_type_count',
        'get_pay_type_amount',
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'nhb_insure_v5_todb main_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'nhb_insure_v5_todb main_to_db: {method} cost:{_cost} ms')


def slow_to_db():
    """
    图表中需要用到的数据存入数据库
    """
    source = NhbInsureV5()
    for method in [
        'get_age_gender',
        'get_age_range',
        'get_renewal_ratio' #        'get_target_weekly_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'nhb_insure_v5_todb slow_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'nhb_insure_v5_todb slow_to_db: {method} cost:{_cost} ms')


def history_to_db():
    """
    补充历史数据，并保证历史数据的准确性，图表数据由于调度，不一定可以获取准确的当日数据
    """
    source = NhbInsureV5()
    for method in [
        'get_medicare_person_count',
        'get_daily_count',
        'get_daily_cumsum',
        'get_daily_personal_cumsum',
        'get_daily_online_cumsum',
        'get_daily_offline_cumsum',
        'get_daily_offline_personal_cumsum',
        'get_daily_amount',
        'get_daily_amount_cumsum'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'nhb_insure_v5_todb history_to_db: {method} error: {e}')
            continue
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'nhb_insure_v5_todb history_to_db: {method} cost:{_cost} ms')


def weekly_to_db():
    """
    周度数据，并保证历史数据的准确性，图表数据由于调度，不一定可以获取准确的当日数据
    """
    source = NhbInsureV5()
    for method in [
        'get_dynamic_weekly_count'
    ]:
        _start = time.time()
        try:
            getattr(source, method)()
        except Exception as e:
            logger.error('error: {e}'.format(e=e))
            send_feishu_message(f'nhb_insure_v5_todb weekly_to_db: {method} error: {e}')
            raise e
        _cost = int((time.time() - _start) * 1000)
        logger.info(f'nhb_insure_v5_todb weekly_to_db: {method} cost:{_cost} ms')