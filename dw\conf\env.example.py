USER = ''
PASSWORD = ''
DEFAULT_PASSWORD = ''

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',  # 数据库引擎
        'NAME': 'dw',  # 数据库名称
        'HOST': 'rm-uf6t66r6m37g71192.mysql.rds.aliyuncs.com',  # 数据库地址
        'PORT': 3306,  # 端口
        'USER': USER,  # 数据库用户名
        'PASSWORD': PASSWORD,  # 数据库密码
    },
    'jkx': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'jkx_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'nhb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'nhb-claim_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'jkx_ghb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'jkx_slave',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-2zev32g9b4fvb27o3fo.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'ghb': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'insurance-claim-prod',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'jkx-ghb-public.mysql.polardb.rds.aliyuncs.com',
        "PORT": '3306',
    },
    'umami': {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'umami',
        "USER": USER,
        "PASSWORD": PASSWORD,
        "HOST": 'rm-uf6t66r6m37g71192.mysql.rds.aliyuncs.com',
        "PORT": '3306',
    }
}

# ================================================= #
# ******** redis配置，用于缓存数据  ******** #
# ================================================= #
REDIS_PASSWORD = ''
REDIS_HOST = '127.0.0.1'
REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:6379/1'
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
        }
    }
}

###----Celery redis 配置-----###
# Broker配置，使用Redis作为消息中间件
CELERY_BROKER_BACKEND = 'redis'
# redis設置密碼的
CELERY_REDIS_PASSWORD = ''
CELERY_REDIS_HOST = '127.0.0.1'
CELERY_BROKER_URL = f'redis://:{CELERY_REDIS_PASSWORD or ""}@{CELERY_REDIS_HOST}:6379/0'