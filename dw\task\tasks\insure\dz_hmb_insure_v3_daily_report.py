import datetime
import logging
import warnings
from decimal import Decimal
import os
import idna
import numpy as np
import pandas as pd
import pymysql
from django.db import transaction
from django.db.models import Q
from pandasql import sqldf
from django.core.mail import EmailMessage

from dw import settings
from insure.models import InsureOnline, InsureAgent, InsureArea, InsureAgeSex
from public.models import PublicIndicatorData, PublicIndicatorMain, PublicStatistics, PublicAreaBaseInsure, PublicTarget
from task.utils.cache_manager import CacheManager
from transfrom.utils.utils import simplify_replace, query_sql, sum_or_combine, age_group, custom_update_or_create, \
    send_feishu_message, query_indicator_code

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 200)
pd.set_option('display.expand_frame_repr', False)
pd.set_option('display.float_format', lambda x: '%.6f' % x)

DB = settings.DATABASES['jkx']
end_time = datetime.datetime.now().strftime('%Y-%m-%d 00:00:00')
today = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S')
yesterday = datetime.datetime.strptime(end_time, '%Y-%m-%d %H:%M:%S') - datetime.timedelta(
    days=1)

TO = ['<EMAIL>']
# TO = ['<EMAIL>']
CC= ['<EMAIL>']

def get_connection(DB):
    """
    获取数据库连接
    """
    conn = pymysql.connect(host=idna.encode(DB["HOST"]).decode('utf-8'), port=int(DB["PORT"]),
                           user=DB["USER"],
                           password=DB["PASSWORD"], database=DB["NAME"])
    return conn

def get_daily_report():
    sql1 = """
    -- 汇总查询
SELECT
	a.*,
	ifnull( b.代理人总保单量, 0 ) 代理人总保单量（个单）
FROM
	(
	SELECT
		e.organization_name 所属保司,
		e.id,
		e.NAME 代理人姓名,
		e.phone 手机号,
	CASE
			
			WHEN count( DISTINCT ei.invitee_id ) != 1 THEN
			count( DISTINCT ei.invitee_id ) - 1 ELSE count( DISTINCT ei.invitee_id ) 
		END AS 团队总人数 
	FROM
		( SELECT invitee_id, invitee_name, root_inviter_id, invitee_level FROM employee_invitation UNION ALL SELECT id, NAME, id, 1 FROM employee WHERE is_root = 0 ) ei
		JOIN employee e ON ei.root_inviter_id = e.id
		JOIN seller s ON s.id = e.organization_id
		JOIN seller_product_set sps ON s.id = sps.seller_id
		JOIN product_set ps ON ps.id = sps.product_set_id 
		AND ps.CODE = 'dezhou_hmbV3' 
		AND ( e.STATUS IS NULL OR e.STATUS = 0 ) 
	GROUP BY
		ei.root_inviter_id 
	) a
	LEFT JOIN (-- 汇总查询
	SELECT
		e.organization_name 所属保司,
		e.NAME 代理人姓名,
		e.id,
		e.phone 手机号,
		count( DISTINCT oic.client_id ) 代理人总保单量 
	FROM
		`order` o
		JOIN order_item oi ON oi.order_id = o.id
		JOIN order_item_client oic ON oic.order_item_id = oi.id 
		AND oic.is_return = 0
		JOIN product_set ps ON ps.id = o.product_set_id 
		AND ps.CODE = 'dezhou_hmbV3'
		JOIN product p ON oi.product_id = p.id
		JOIN marketing_channel mc ON mc.channel_id = o.source_id
		JOIN seller ON seller.id = o.seller_id
		JOIN order_agent oa ON o.id = oa.order_id
		JOIN ( SELECT invitee_id, invitee_name, root_inviter_id, invitee_level FROM employee_invitation UNION ALL SELECT id, NAME, id, 1 FROM employee WHERE is_root = 0 ) ei ON oa.agent_id = ei.invitee_id
		JOIN employee e ON ei.root_inviter_id = e.id 
	WHERE
		o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
		AND o.delete_time IS NULL 
		AND o.is_personal = 1 
		AND o.is_online = 0 
		AND o.create_time <= '{end_time}' 
	GROUP BY
	e.id 
	) b ON a.id = b.id
	where a.所属保司 !='德州-线上渠道';
    """


    sql2 = """
        -- 汇总查询
    SELECT
        e.id,
        count( DISTINCT oic.client_id ) 代理人当日单量
    FROM
        `order` o
        JOIN order_item oi ON oi.order_id = o.id
        JOIN order_item_client oic ON oic.order_item_id = oi.id 
        AND oic.is_return = 0
        JOIN product_set ps ON ps.id = o.product_set_id 
        AND ps.CODE = 'dezhou_hmbV3'
        JOIN product p ON oi.product_id = p.id
        JOIN marketing_channel mc ON mc.channel_id = o.source_id
        JOIN seller ON seller.id = o.seller_id
        JOIN order_agent oa ON o.id = oa.order_id
        JOIN (select invitee_id,invitee_name,root_inviter_id,invitee_level from employee_invitation
        union all 
        select id,name,id,1 from employee where is_root=0) ei ON oa.agent_id = ei.invitee_id
        JOIN employee e ON ei.root_inviter_id = e.id 
    WHERE
        o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
        AND o.delete_time IS NULL 
        AND o.is_personal = 1 
        AND o.is_online = 0 
        AND date(o.create_time) = date('{end_time}') 
    GROUP BY
        e.id;
        """
    sql3 = """
    SELECT
	a.*,
	ifnull( b.代理人总保单量, 0 ) 代理人总保单量（个单）
FROM
	(
	SELECT
		s.short_name AS seller,
		s.NAME AS seller_name,
		e.id,
		e.NAME 代理人姓名,
		ei.invitee_level 代理人级别,
		e.phone 手机号,
		( SELECT NAME FROM employee e1 WHERE e1.id = ei.root_inviter_id ) 上级代理人姓名,
		( SELECT phone FROM employee e1 WHERE e1.id = ei.root_inviter_id ) 上级代理人手机号,
		( SELECT organization_name FROM employee e1 WHERE e1.id = ei.root_inviter_id ) 上级代理人所属保司 
	FROM
		( SELECT invitee_id, invitee_name, root_inviter_id, invitee_level FROM employee_invitation UNION ALL SELECT id, NAME, id, 1 FROM employee WHERE is_root = 0 ) ei
		JOIN employee e ON ei.invitee_id = e.id
		JOIN seller s ON s.id = e.organization_id
		JOIN seller_product_set sps ON s.id = sps.seller_id
		JOIN product_set ps ON ps.id = sps.product_set_id 
		AND ps.CODE = 'dezhou_hmbV3' 
		AND ( e.STATUS IS NULL OR e.STATUS = 0 ) 
	) a
	LEFT JOIN (
	SELECT
		e.id,
		e.NAME 代理人姓名,
		e.phone 手机号,
		ei.invitee_level 代理人级别,
		count( DISTINCT oic.client_id ) 代理人总保单量 
	FROM
		`order` o
		JOIN order_item oi ON oi.order_id = o.id
		JOIN order_item_client oic ON oic.order_item_id = oi.id 
		AND oic.is_return = 0
		JOIN product_set ps ON ps.id = o.product_set_id 
		AND ps.CODE = 'dezhou_hmbV3'
		JOIN product p ON oi.product_id = p.id
		JOIN marketing_channel mc ON mc.channel_id = o.source_id
		JOIN seller ON seller.id = o.seller_id
		JOIN order_agent oa ON o.id = oa.order_id
		JOIN ( SELECT invitee_id, invitee_name, root_inviter_id, invitee_level FROM employee_invitation UNION ALL SELECT id, NAME, id, 1 FROM employee WHERE is_root = 0 ) ei ON oa.agent_id = ei.invitee_id
		JOIN employee e ON ei.invitee_id = e.id 
	WHERE
		o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
		AND o.delete_time IS NULL 
		AND o.is_personal = 1 
		AND o.is_online = 0 
		AND o.create_time <= '{end_time}' 
	GROUP BY
	ei.invitee_id 
	) b ON a.id = b.id
		where a.上级代理人所属保司 !='德州-线上渠道'
    """

    sql4 = """
        SELECT
    	e.id,
    	count( DISTINCT oic.client_id ) 当日单量

    FROM
    	`order` o
    	JOIN order_item oi ON oi.order_id = o.id
    	JOIN order_item_client oic ON oic.order_item_id = oi.id 
    	AND oic.is_return = 0
    	JOIN product_set ps ON ps.id = o.product_set_id 
    	AND ps.CODE = 'dezhou_hmbV3'
    	JOIN product p ON oi.product_id = p.id
    	JOIN marketing_channel mc ON mc.channel_id = o.source_id
    	JOIN seller ON seller.id = o.seller_id
    	JOIN order_agent oa ON o.id = oa.order_id
    	JOIN (select invitee_id,invitee_name,root_inviter_id,invitee_level from employee_invitation
    	union all 
    	select id,name,id,1 from employee where is_root=0) ei ON oa.agent_id = ei.invitee_id
    	JOIN employee e ON ei.invitee_id = e.id 
    WHERE
    	o.order_status IN ( 'PAID_SUCCESS', 'WAIT_DEDUCT' ) 
    	AND o.delete_time IS NULL 
    	AND o.is_personal = 1 
    	AND o.is_online = 0 
    	AND date(o.create_time) = date('{end_time}') 
    GROUP BY
    	ei.invitee_id
        """
    # 获取前一日的日期
    end_time = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime('%Y-%m-%d')+' 23:59:59'
    df_total = pd.read_sql(sql1.format(end_time=end_time),get_connection(DB))
    df_total_today = pd.read_sql(sql2.format(end_time=end_time), get_connection(DB))
    df_detail = pd.read_sql(sql3.format(end_time=end_time), get_connection(DB))
    df_detail_today = pd.read_sql(sql4.format(end_time=end_time), get_connection(DB))
    df = pd.merge(df_total, df_total_today, on=['id'], how='outer')
    df1 = pd.merge(df_detail, df_detail_today, on=['id'], how='outer')
    df.drop(columns=['id'], inplace=True)
    df1.drop(columns=['id','seller','seller_name'], inplace=True)
    df.fillna(0, inplace=True)
    df1.fillna(0, inplace=True)
    return df,df1




def email_dz_hmb():
    date = datetime.datetime.now().strftime('%Y%m%d')
    path = os.path.join(os.path.dirname(__file__), f'德州惠民保三期-代理人销量统计_{date}.xlsx')
    df,df1 = get_daily_report()
    # 使用 ExcelWriter 将多个 DataFrame 写入同一个 Excel 文件的不同工作表
    with pd.ExcelWriter(path) as writer:
        df.to_excel(writer, sheet_name='汇总', index=False)
        df1.to_excel(writer, sheet_name='明细', index=False)

    mail = EmailMessage(
        subject=f'德州惠民保三期-代理人销量统计_{date}',
        body='见附件',
        to=TO,
        cc=CC,
    )
    mail.attach_file(path)
    mail.send()

    os.remove(path)


