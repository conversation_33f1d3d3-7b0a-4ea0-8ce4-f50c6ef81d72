from django.db import models
from common.models import BaseModel


class ClaimSellerPay(BaseModel):
    product_set_code = models.CharField(max_length=32, blank=True, null=True, verbose_name='产品集编码')
    is_provincial = models.IntegerField(blank=True, null=True, verbose_name='是否省级数据')
    city = models.CharField(max_length=32, blank=True, null=True, verbose_name='地市')
    channel_name = models.CharField(max_length=32, blank=True, null=True, verbose_name='渠道名称')
    pay_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人次')
    pay_number_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付人次占比')

    pay_person_number = models.IntegerField(blank=True, null=True, verbose_name='赔付人数')
    pay_person_number_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付人数占比')
    pay_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付金额')
    pay_amount_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True,verbose_name='赔付金额占比')
    premium_amount = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='保费')
    claim_rate = models.DecimalField(max_digits=20, decimal_places=4, blank=True, null=True, verbose_name='赔付率') 

    class Meta:
        db_table = 'claim_seller_pay'
        verbose_name = '理赔-保司赔付情况'
        verbose_name_plural = verbose_name

