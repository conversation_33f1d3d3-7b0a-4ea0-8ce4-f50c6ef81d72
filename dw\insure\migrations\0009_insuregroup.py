# Generated by Django 3.2.12 on 2024-12-06 10:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('insure', '0008_insuremobile_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='InsureGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('update_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('product_set_code', models.CharField(blank=True, max_length=32, null=True, verbose_name='产品集编码')),
                ('company_name', models.CharField(blank=True, max_length=256, null=True, verbose_name='公司名称')),
                ('name', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=256, null=True, verbose_name='被保人姓名')),
                ('credential_number', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=64, null=True, verbose_name='证件号码')),
            ],
            options={
                'verbose_name': '健康险团单数据',
                'verbose_name_plural': '健康险团单数据',
                'db_table': 'insure_group',
            },
        ),
    ]
